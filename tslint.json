{"extends": "tslint:recommended", "rules": {"interface-name": false, "no-reference": false, "no-string-literal": false, "no-var-requires": false, "no-bitwise": false, "object-literal-sort-keys": false, "ordered-imports": false, "object-literal-key-quotes": false, "member-ordering": false, "max-classes-per-file": false, "object-literal-shorthand": false, "arrow-parens": false, "array-type": false, "only-arrow-functions": false, "trailing-comma": false, "no-empty-interface": false, "no-unused-expression": false}, "linterOptions": {"format": "verbose"}}