<!doctype html>
<html>
<head>
    <title>Socket.IO chat</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font: 13px Helvetica, Arial; }
        form { background: #000; padding: 3px; position: fixed; bottom: 0; width: 100%; }
        form input { border: 0; padding: 10px; width: 80%; margin-right: .5%; }
        form button { width: 19%; background: rgb(130, 224, 255); border: none; padding: 10px; }
        #messages { list-style-type: none; margin: 0; padding: 0; }
        #messages li { padding: 5px 10px; }
        #messages li:nth-child(odd) { background: #eee; }
    </style>
</head>
<body>
<ul id="messages"></ul>
<form id="login">
    <input id="access_token" autocomplete="off" placeholder="Access Token"/><button>Login</button>
</form>
<form id="enter-room" style="display: none;">
    <input id="room-type" autocomplete="off" placeholder="Room Type"/><button>Enter Room</button>
</form>
<form id="action-form" style="display: none;">
    <input id="message" autocomplete="off" placeholder="Action"/><button>Submit</button>
</form>

<script src="/socket.io/socket.io.js"></script>
<script src="https://code.jquery.com/jquery-1.11.1.js"></script>
<script>
    var requestId = 1;
    var socket = io(null, {
        extraHeaders: {
            Authorization: "Bearer authorization_token_here"
        }
    });
    $('#login').submit(function() {
        var access_token = $('#login #access_token').val();
        var token = access_token ? access_token : {
            playerCode: 'DEMO-USER',
            gameCode: 'sw-fufish',
            brandId: 0,
            currency: 'USD'
        };
        socket.emit('init', {request: 'init', startGameToken: token, gameId: "sw-fufish", deviceId: "web"});
        $('#login').hide();
        return false;
    });
    $('#enter-room').submit(function() {
        var room_type = $('#enter-room #room-type').val();
        var trajectories = {
        };
        for (var pos=0; pos<=25; pos++) {
            trajectories["item_type" + pos] = {main: [1, 2, 3, 4, 5], multiplier: [1, 2, 3, 4, 5]};
        }
        socket.emit('play', {request: 'room-enter', requestId: requestId++, type: room_type, trajectories: trajectories});
        $('#enter-room').hide();
        return false;
    });
    $('#action-form').submit(function() {
        var message = $('#action-form #message').val();
        var items = message.split(' ');
        var action = items[0];
        if (action === 'fire') {
            let hit_items = JSON.parse(items[1]);

            var request = {
                request: 'fire',
                requestId: requestId++,
                bet_amount: 1,
                locked: false,
                timestamp: new Date().getTime(),
                hit_items: hit_items,
                is_bomb: false
            };

            socket.emit('play', request);
        } else if (action === 'bomb') {
            let hit_items = JSON.parse(items[1]);

            var request = {
                request: 'bomb',
                requestId: requestId++,
                bet_amount: 1,
                locked: false,
                timestamp: new Date().getTime(),
                hit_items: hit_items,
                is_bomb: true
            };

            socket.emit('play', request);
        } else if (action === 'cheat') {
            socket.emit('play', {
                request: 'cheat',
                type: +(items[1]),
                timestamp: new Date().getTime()
            });
        } else if (action === 'transfer-in') {
            socket.emit('transfer-in', {
                request: 'transfer-in',
                amount: JSON.parse(items[1]),
                timestamp: new Date().getTime()
            });
        } else if (action === 'transfer-out') {
            socket.emit('transfer-out', {
                request: 'transfer-out',
                amount: JSON.parse(items[1]),
                timestamp: new Date().getTime()
            });
        } else if (action === 'ticker') {
            socket.emit('jp-ticker', {
                request: 'jp-ticker'
            });
        }
        $('#m').val('');
        return false;
    });

    socket.on('init', function(response){
        $('#messages').append($('<li>').text('init: ' + JSON.stringify(response)));
        socket.emit('play', {request: 'list-rooms', requestId: requestId++})
    });
    socket.on('play', function(response){
        const req = response.result.request;
        switch (req) {
            case 'list-rooms':
                $('#messages').append($('<li>').text('list-rooms: ' + JSON.stringify(response)));
                $('#enter-room').show();
                break;
            case 'room-enter':
                $('#messages').append($('<li>').text('room-enter: ' + JSON.stringify(response)));
                $('#action-form').show();
                break;
            default:
                $('#messages').append($('<li>').text(req + ': ' + JSON.stringify(response)));
        }
    });
    socket.on('transfer-in', function(response){
        const req = response.result.request;
        $('#messages').append($('<li>').text(req + ': ' + JSON.stringify(response)));
    });
    socket.on('transfer-out', function(response){
        const req = response.result.request;
        $('#messages').append($('<li>').text(req + ': ' + JSON.stringify(response)));
    });
    socket.on('jp-ticker', function(response){
        $('#messages').append($('<li>').text('jp-ticker: ' + JSON.stringify(response)));
    });
    socket.on('event', function(event){
        $('#messages').append($('<li>').text(event.type + ': ' + JSON.stringify(event)));
    });
    socket.on('game-error', function(response){
        $('#messages').append($('<li>').text('error: ' + JSON.stringify(response)));
    });
</script>
</body>
</html>