# Skywind Game Server

## Environment variables

### Environment

| Variable                    | Description                                                   |
|-----------------------------|---------------------------------------------------------------|
|`NODE_ENV_ID`                | Current environment id to validate start game token (optional)|
|`NODE_ENV`                   | Env mode (default: development)                               |
|`GAMESERVER_PREFIX`          | Game server prefix (default: games)                           |
|`APP_PORT`                   | Game server port. Default is 4000                             |
|`APP_INTERNAL_PORT`          | Game server internal port. Default is 4004                    |
|`CLUSTER_NAME`               | Cluster name. (default: default)                              |
|`HTTP_SERVER_TIMEOUT`            | Server timeout. (default: 12000)                          |
|`HTTP_SERVER_KEEP_ALIVE_TIMEOUT` | TTL. (default: 0)                                         |
|`ENGINE_GAME_ENTRY_POINT`        | Engine game entry point. (default: /casino/game2)         |

### Player history

| Variable                     | Description                                                   |
|----------------------------- |---------------------------------------------------------------|
|`PLAYER_HISTOTY_PERIOD_MS`    | History period (default: 7 * 24 * 60 * 60 * 1000)             |
|`PLAYER_HISTORY_RECORDS_LIMIT`| History records limit (default: 200)                          |

### Redis

| Variable                         | Description                                                                       |
|----------------------------------|-----------------------------------------------------------------------------------|
|`REDIS_HOST`                      | Redis hostname (default: redis)                                                   |
|`REDIS_PORT`                      | Redis port (default: 6379)                                                        |
|`REDIS_PASSWORD`                  | Redis password                                                                    |
|`REDIS_SENTINELS`                 | JSON encoded list of sentinels(default is null)                                   |
|`REDIS_CLUSTER_NAME`              | Redis cluster name default is redis-ha                                            |
|`REDIS_CONNECTION_TIMEOUT`        | Redis connection timeout (default is 1000 ms)                                     |
|`REDIS_MIN_CONNECTIONS`           | Min number of connection in the pool (default: 2)                                 |
|`REDIS_MAX_CONNECTIONS`           | Max number of connection in the pool (default: 10)                                |
|`REDIS_MAX_IDLE_TIME_MS`          | Timeout to close idle connections (default: 30000)                                |
|`REDIS_REPLICATION_FACTOR`        | Sync replication factor (default is 0 async)                                      |
|`REDIS_REPLICATION_TIMEOUT`       | Max timeout for waiting slave approval (default 100ms)                            |
|`REDIS_MAX_RETRIERS_PER_REQUEST`  | In case of connection problems how many times to repeat the command (default is 0 |

### Postgres

| Variable              | Description                                        |
|-----------------------|----------------------------------------------------|
|`PGHOST`               | Postgres hostname (default: localhost)             |
|`PGPORT`               | Postgres port (default: 5432)                      |
|`PGUSER`               | Postgres user                                      |
|`PGPASSWORD`           | Password                                           |
|`PGDATABASE`           | Database to connect to (default: swgameserver)     |
|`PG_CA_CERT`           | Postgre CA certificate path                        |
|`PG_SECURE_CONNECTION` | Postgre secure connection switch true/false        |
|`PG_MAX_CONNECTIONS`   | Max number of connection in the pool (default: 10) |
|`PG_MAX_IDLE_TIME_MS`  | Timeout to close idle connections (default: 30000) |
|`SYNC_ON_START`        | Use this param if you initialize app the first time (optional, default false) |

### New Relic

| Variable          | Description                                       |
|-------------------|---------------------------------------------------|
|`NEWRELIC_APP_NAME`| New Relic app name (default: SW-GAME-SERVER)      |
|`NEWRELIC_KEY`     | New Relic key                                     |
|`NEWRELIC_ENV_NAME`| New Relic environment name (default: development) |

### Management interaction

| Variable                    | Description                                                            |
|-----------------------------|------------------------------------------------------------------------|
|`MANAGEMENT_API`             | Management interaction url (default: http://api:3006/)                 |
|`MANAGEMENT_API_INTERNAL_URL`| Management url for cross-server interaction (default: http://api:4004) |

### Game session token

| Variable                       | Description                                                   |
|--------------------------------|---------------------------------------------------------------|
|`GAME_SESSION_TOKEN_ALGORITHM`  | Game session token algorithm  (default: HS512)                |
|`GAME_SESSION_TOKEN_ISSUER`     | Game session token issuer (default: skywindgroup) |
|`GAME_SESSION_TOKEN_SECRET`     | Game session token secret                                     |

### Wallet access type

| Variable            | Description                         |
|---------------------|-------------------------------------|
|`WALLET_THROUGH_API` | Wallet through API (default: false) |

### Management API Game history

| Variable                     | Description                         |
|------------------------------|-------------------------------------|
|`MANAGEMENT_API_GAME_HISTORY` | Management game history API (default: http://api:3006/) |

### JPN Server

| Variable                     | Description                         |
|------------------------------|-------------------------------------|
|`JPN_SERVER_API` | JPN server API (default: http://jackpot:5000)    |

### Game Context

| Variable                       | Description                                         |
|--------------------------------|-----------------------------------------------------|
|`GAME_CONTEXT_PREFIX`           | Game context prefix (default: context)              |
|`GAME_FUN_CONTEXT_PREFIX`       | Game FUN context prefix (default: context)          |
|`GAME_BNS_CONTEXT_PREFIX`       | Game BNS context prefix (default: bns)              |
|`GAME_PLAY_MONEY_CONTEXT_PREFIX`| Game PLAY MONEY context prefix (default: play_money)|
|`GAME_FUN_BONUS_CONTEXT_PREFIX` | Game FUN BONUS context prefix (default: fun_bonus)  |

### Context

| Variable                       | Description                                                        |
|--------------------------------|--------------------------------------------------------------------|
|`PLAYER_CONTEXT_PREFIX`         | Player context prefix (default: player)                            |
|`GAME_HISTORY_PREFIX`           | Game History context prefix (default: history)                     |
|`ROUND_HISTORY_PREFIX`          | Round history prefix (default: rounds)                             |
|`SESSION_HISTORY_PREFIX`        | Session history prefix (default: sessions)                         |
|`CLEANUP_GAME_CONTEXT_PREFIX`   | Clean game context prefix (default: cleanup/players-cleanup)       |
|`ANALYTICS_PREFIX`              | Analytics prefix (default: analytics)                              |
|`LAST_GAME_ACTIVITY_KEY`        | Last game activity key prefix (default: last-activity)             |
|`FORCE_CLEANUP_JOB`             | Force cleanup job prefix (default: force-cleanup)                  |
|`MERCHANT_SESSION_LINK`         | Merchant session link prefix (default: merchant-session)           |

### Fun game mode

| Variable                   | Description                                                                                                               |
|----------------------------|---------------------------------------------------------------------------------------------------------------------------|
|`FUN_GAME_START_AMOUNT`     | Start amount for fun game mode (default: 500)                                                                             |
|`FUN_GAME_START_GAME_TOKEN_REQUIRED` | Indicates if possible to run fun game anonymously without start game token                                       |
|`FUN_GAME_STD_TTL`          | The standard ttl as number in seconds for every generated cache element (wallet account) (default: 900,  // 15 min)       |
|`FUN_KEYS_PREFIX`           | Key prefix for all records which is associated with fun game                                                              |
|`FUN_REDIS_HOST`            | Redis hostname (default: redis)                                                                                           |
|`FUN_REDIS_PORT`            | Redis port (default: 6379)                                                                                                |
|`FUN_REDIS_PASSWORD`        | Redis password                                                                                                            |
|`FUN_REDIS_MIN_CONNECTIONS` | Min number of connection in the pool (default: 2)                                                                         |
|`FUN_REDIS_MAX_CONNECTIONS` | Max number of connection in the pool (default: 10)                                                                        |
|`FUN_REDIS_MAX_IDLE_TIME_MS`| Timeout to close idle connections (default: 30000)                                                                        |
|`FUN_REDIS_CONNECTION_TIMEOUT`| Redis connection timeout (default is 1000 ms)                                                                             |

### Clean up job

| Variable                          | Description                                                                                |
|-----------------------------------|--------------------------------------------------------------------------------------------|
|`CLEAN_UP_EXPIRE_IN`               | Game context will be considered 'cold' after this interval(minutes) (default: 30)          |
|`CLEAN_UP_CHECK_INTERVAL`          | How often to check expired contexts(seconds) default is 10                                 |
|`CLEAN_UP_BATCH_SIZE`              | Max element which will be queried and processed in one job tick (default: 100)             |
|`FORCE_CLENUP_REPAIR_INTERVAL`     | Force cleanup repair interval (default: 10 * 60 * 1000)                                    |
|`CLEAN_UP_PLAYERS_EXPIRE_IN`       | Players context will be considered 'cold' after this interval(minutes) (default: 40)       |
|`CLEAN_UP_CHECK_PLAYERS_INTERVAL`  | How often to check expired players contexts seconds (default is 60)                        |
|`CLEAN_UP_PLAYERS_BATCH_SIZE`      | Max element which will be queried and processed in one job tick (default: 100)             |

### Unloader

| Variable                          | Description                                                                   |
|-----------------------------------|-------------------------------------------------------------------------------|
|`MAX_ORPHAN_TTL`                   | Interval in minutes for checking inactive workers (default: 15)               |
|`REPAIR_WORKER_CRON`               | Cron for running checking of inactive workers (default: "0/20 * * * *")       |
|`UNLOADER_MAX_HISTORY_BATCH_SIZE`  | Max event history in batch (default: 1000)                                    |
|`UNLOADER_MAX_ROUND_BATCH_SIZE`    | Max round history in batch (default: 100)                                     |
|`UNLOADER_HISTORY_CONSUMERS`       | List of event consumers: "events", "alooma", "redshift", "gamehistory"        |
|`MAX_SPIN_POP_DURATION`            | Max interval for awaiting bulk items (default: 500)                           |
|`SPIN_POP_SLEEP_DURATION`          | Sleep interval in spin pop procedure (default: 100)                           |
|`SESSION_HISTORY_SKIP_THRESHOLD`   | threshold in days, oldest sessions will be skipped by unloader (default 120)  |

#### Event consumer

It's used only for test/dev environment or if we want to store events directly in Redshift 

| Variable                             | Description                                        |
|--------------------------------------|----------------------------------------------------|
|`EVENT_CONSUMER_PGHOST`               | Postgres hostname (default: localhost)             |
|`EVENT_CONSUMER_PGPORT`               | Postgres port (default: 5432)                      |
|`EVENT_CONSUMER_PGUSER`               | Postgres user                                      |
|`EVENT_CONSUMER_PGPASSWORD`           | Password                                           |
|`EVENT_CONSUMER_PGDATABASE`           | Database to connect to (default: swgameserver)     |
|`EVENT_CONSUMER_PG_SECURE_CONNECTION` | Postgre secure connection switch true/false        |
|`EVENT_CONSUMER_PG_MAX_CONNECTIONS`   | Max number of connection in the pool (default: 10) |
|`EVENT_CONSUMER_PG_MAX_IDLE_TIME_MS`  | Timeout to close idle connections (default: 30000) |

#### Alooma consumer

It sends events to www.alooma.com service

| Variable                       | Description                                             |
|--------------------------------|---------------------------------------------------------|
|`ALLOMA_TOKEN`                  | Access token to alooma service                          |
|`ALLOMA_RETRIES`                | Number of retries if we got an error (default: 10)      |

### Allow set positions by client 

| Variable                      | Description                                                    |
|-------------------------------|----------------------------------------------------------------|
|`ALLOW_SET_POSITIONS_BY_CLIENT`| Allow set positions by client, only for debug (default: false) |

### Minimal logging level

| Variable                        | Description                                                    |
|---------------------------------|----------------------------------------------------------------|
|`LOG_LEVEL`                      | error, warn, info, debug (default: "info")                     |

### Graylog

| Variable                        | Description                                                    |
|---------------------------------|----------------------------------------------------------------|
|`GRAYLOG_HOST`                   | Hostname of Graylog server (optional)                          |
|`GRAYLOG_PORT`                   | Graylog port               (optional)                          |

### NPM

| Variable                           | Description                                                                                                          |
|------------------------------------|----------------------------------------------------------------------------------------------------------------------|
|`NPM_USER`                          | Npm login user                                                                                                       |
|`NPM_PASSWORD`                      | Npm login password                                                                                                   |
|`NPM_EMAIL`                         | Npm login email                                                                                                      |
|`NPM_MODULE_GROUP`                  | Npm module group (default: @skywind-group)                                                                           |
|`NPM_GAME_MODULES`                  | Comma separated list of game modules to be pre-loaded, e.g. @skywind-group/sw_sod@0.1.13,@skywind-group/sw_gol@0.2.6 |

### CORS and Site Whitelisting

| Variable                           | Description                                                                                                              |
|------------------------------------|--------------------------------------------------------------------------------------------------------------------------|
|`GAME_CORS_WHITELIST`               | Comma-separated list of allowed origins for /game endpoint requests                                                      |                                              |

### Log slow request

| Variable                        | Description                                                    |
|---------------------------------|----------------------------------------------------------------|
|`SLOW_REQUEST_LOG_THRESHOLD_MS`  | Threshold for log slow request (default: 1000)                 |
|`POSTGRES_QUERY_LOGGING`         | logging all POSTGRES queries to console (default: false)       |

### Measures

| Variable                        | Description                                                                      |
|---------------------------------|----------------------------------------------------------------------------------|
|`MEASURES_INCLUDE_DEBUG_ONLY`    | Turns on debug mode for gathering more measures (default: false)                 |
|`LOG_UNLOAD_HISTORY_INTERVAL`    | Interval of logging  metrics in history unload job (default: 5 * 60 * 1000)      |

### Generators

| Variable               | Description                                                                                          |
|------------------------|------------------------------------------------------------------------------------------------------|
|`ROUND_ID_MIN`          | Min value of roundId. The first round starts with this value (default: 0)                            |
|`ROUND_ID_MAX`          | Max value of roundId. If we reached this value we will get error for every next roundId attempt      |
|`SESSION_ID_MIN`        | Min value of sessionId. The first round starts with this value (default: 0)                          |
|`SESSION_ID_MAX`        | Max value of sessionId. If we reached this value we will get error for every next sessionId attempt  |

### Keep-alive
| Variable               | Description                                                                                          |
|------------------------|------------------------------------------------------------------------------------------------------|
|`JPN_KEEP_ALIVE_FREE_SOCKET_COUNT`              | Keep alive pool for JPN (default is 100)                                     |
|`JPN_KEEP_ALIVE_TIMEOUT`                        | Keep alive of free socket for JPN (default is 30000 ms)                      |
|`JPN_SOCKET_ACTIVE_TTL`                         | Time to live of active socket for MAPI(default is 60000 ms)                  |
|`MANAGEMENT_API_KEEP_ALIVE_FREE_SOCKET_COUNT`   | Keep alive pool for MAPI (default is 100)                                    |
|`MANAGEMENT_API_KEEP_ALIVE_TIMEOUT`             | Keep alive of free socket for for JPN (default is 30000 ms)                  |
|`MANAGEMENT_API_SOCKET_ACTIVE_TTL`              | Time to live of active socket for MAPI(default is 60000 ms)                  |

### Game context service to load/store/delete game context
| Variable                     | Description                                                                                          |
|------------------------------|------------------------------------------------------------------------------------------------------|
|`OFFLINE_STORAGE_USE_REMOTE`  | Should we use remote service to load/update/remove contexts                                          |
|`OFFLINE_STORAGE_URL`         | URL for remote service (default is http://context-storage:4006/v1                                    |
|`OFFLINE_STORAGE_PORT`        | Storage Port (default is 4006                                                                        |

### Remove expired offline game context job configuration
| Variable                     | Description                                                                                          |
|------------------------------|------------------------------------------------------------------------------------------------------|
| EXPIRE_JOB_BATCH             | Batch size, default is 10. To turn the job off should be equal to 0                                 |
| EXPIRE_JOB_CHECK_INTERVAL    | How often to start searching expired contexts, default 1 minute                                      |
| POSTPONE_EXPIRATION_INTERVAL | Prolong expiration marker if we could't expire the context(minutes). Default is 10                   |

### API request size limits
| Variable                 | Description                                                            |
|--------------------------|------------------------------------------------------------------------|
|`BODY_PARSER_JSON_LIMIT`  | Max size of request json body (default: 5242880)                       |
|`BODY_PARSER_URL_LIMIT`   | Max size of request url (default: 5242880)                             |
|`COMPRESSION_THRESHOLD`   | Compression threshold (default: 1024)                                  |

### Kafka configuration
| Variable                     | Description                                                                  |
|------------------------------|------------------------------------------------------------------------------|
|`GAME_HISTORY_KAFKA_UNLOADER_ENABLED`   | Enable kafka for game history events                               |
|`GAME_HISTORY_TOPIC_NAME`               | Game history event kafka topic name                                |
|`GAME_HISTORY_KAFKA_BROKER_HOSTNAMES`   | Kafka hosts for game events                                        |
|`ROUND_HISTORY_KAFKA_UNLOADER_ENABLED`  | Enable kafka for round history events                              |
|`ROUND_HISTORY_TOPIC_NAME`              | Rounds history topic name                                          |
|`ROUND_HISTORY_KAFKA_BROKER_HOSTNAMES`  | Kafka hosts for round history                                      |
|`SESSION_HISTORY_KAFKA_UNLOADER_ENABLED`| Enable kafka for session history events                            |
|`SESSION_HISTORY_TOPIC_NAME`            | Session history topic name                                         |
|`SESSION_HISTORY_KAFKA_BROKER_HOSTNAMES`| Kafka hosts for session history                                    |
|`KAFKA_REQUIRE_ACK`                     | Acknowledgement type. Default is -1  (from all nodes)              |
|`KAFKA_ACK_TIMEOUT`                     | Acknowledge timeout. Default is 1000 ms                            |
|`KAFKA_CONNECT_TIMEOUT`                 | Kafka connection timeout. Default is 60000 ms                      |
|`KAFKA_REQUEST_TIMEOUT`                 | Kafka request timeout. Default is 5000 ms                          |
|`KAFKA_PARTITIONER_TYPE`                | Kafka partitioner type. Default is 0 (cyclic)                      |
|`KAFKA_MAX_SEND_ATTEMPT_TIMEOUT`        | Max timeout before send attempt (exponential back off)(default: 10000)|

### Settings for public id generation

| Variable                                | Description                                            |
|-----------------------------------------|--------------------------------------------------------|
|`PCID_PASSWORD`                          | Salt for generation                                    |
|`PCID_MIN_HASH_LENGTH`                   | Minimal length of hash (default: 8)                    |

### New rounds consumer

| Variable                                | Description                                            |
|-----------------------------------------|--------------------------------------------------------|
|`NEW_ROUNDS_CONSUMER`                    | Enable new rounds consumer (default: false)            |

### Internal server token (recovery, history, forcefinish, etc)

| Variable                              | Description                                                   |
|---------------------------------------|---------------------------------------------------------------|
|`INTERNAL_SERVER_TOKEN_ALGORITHM`      | Internal token algorithm  (default: HS512)                    |
|`INTERNAL_SERVER_TOKEN_ISSUER`         | Internal token issuer (default: skywindgroup)                 |
|`INTERNAL_SERVER_SECRET`               | Internal token secret                                         |
|`INTERNAL_SERVER_TOKEN_EXPIRES_IN`     | Internal token ttl(default is 300 seconds)                    |

### Retries for JPN/MAPI

| Variable                    | Description                                                                    |
|-----------------------------|--------------------------------------------------------------------------------|
|`RETRIES_SLEEP_TIMEOUT`      | Initial attempt timeout(default 50 ms). Every next timeout is twice larger     |
|`RETRIES_MAX_TIMEOUT`        | Retry until total spent time has reached this value (default is 1000 ms)       |

### BI game event integration
| Variable                                         | Description                                                        |
|--------------------------------------------------|--------------------------------------------------------------------|
|`ANALYTICS_SEND_ONLINE`                           | Send event in the game flow or by unloader (default is unloader)   |
|`ANALYTICS_TOPIC_NAME`                            | BI event kafka topic name                                          |
|`ANALYTICS_KAFKA_BROKER_HOSTNAMES`                | Kafka hosts for game events                                        |
|`ANALYTICS_KAFKA_REQUIRE_ACK`                     | Acknowledgement type. Default is -1  (from all nodes)              |
|`ANALYTICS_KAFKA_ACK_TIMEOUT`                     | Acknowledge timeout. Default is 1000 ms                            |
|`ANALYTICS_KAFKA_CONNECT_TIMEOUT`                 | Kafka connection timeout. Default is 60000 ms                      |
|`ANALYTICS_KAFKA_REQUEST_TIMEOUT`                 | Kafka request timeout. Default is 5000 ms                          |
|`ANALYTICS_KAFKA_PARTITIONER_TYPE`                | Kafka partitioner type. Default is 2 (cyclic)                      |
|`ANALYTICS_KAFKA_MAX_SEND_ATTEMPT_TIMEOUT`        | Max timeout before send attempt  (exponential back off)            |


### Unloader from kafka to PG

| Variable                                             | Description                                                    |
|------------------------------------------------------|----------------------------------------------------------------|
|`UNLOADER_TYPE`                                       | Types of unloader: "redis", "kafka". Default is "redis"        |
|`KAFKA_TO_PG_GAME_HISTORY_CONSUMER_GROUP_ID`          | Group id for game event group consumer                         |
|`KAFKA_TO_PG_GAME_HISTORY_CONSUMER_SESSION_TIMEOUT`   | Session timeout for game event group consumer                  |
|`KAFKA_TO_PG_GAME_HISTORY_CONSUMER_FETCH_MAX_BYTES`   | Max bytes to fetch  for  game event group consumer             |
|`KAFKA_TO_PG_GAME_HISTORY_CONSUMER_FETCH_MAX_WAITS`   | Max await ts for game event group consumer                     |
|`KAFKA_TO_PG_GAME_HISTORY_CONSUMER_BUFFER_SIZE`       | Internal cache buffer  for game event group consumer           |
|`KAFKA_TO_PG_ROUND_CONSUMER_GROUP_ID`                 | Group id for round group consumer                              |
|`KAFKA_TO_PG_ROUND_CONSUMER_SESSION_TIMEOUT`          | Session timeout for round group consumer                       |
|`KAFKA_TO_PG_ROUND_CONSUMER_FETCH_MAX_BYTES`          | Max bytes to fetch  for round group consumer                   |
|`KAFKA_TO_PG_ROUND_CONSUMER_FETCH_MAX_WAITS`          | Max await ts for round group consumer                          |
|`KAFKA_TO_PG_ROUND_CONSUMER_BUFFER_SIZE`              | Internal cache buffer round group consumer                     |
|`KAFKA_TO_PG_SESSION_CONSUMER_GROUP_ID`               | Group id for session group consumer                            |
|`KAFKA_TO_PG_SESSION_CONSUMER_SESSION_TIMEOUT`        | Session timeout for session group consumer                     |
|`KAFKA_TO_PG_SESSION_CONSUMER_FETCH_MAX_BYTES`        | Max bytes to fetch  for session group consumer                 |
|`KAFKA_TO_PG_SESSION_CONSUMER_FETCH_MAX_WAITS`        | Max await ts for session group consumer                        |
|`KAFKA_TO_PG_SESSION_CONSUMER_BUFFER_SIZE`            | Internal cache buffer for session group consumer               |


### Deployment route validation
| Variable                                         | Description                                                        |
|--------------------------------------------------|--------------------------------------------------------------------|
|`ROUTING_ENABLE_ROUTE_VALIDATION`                 | Enable route validation on init request (default is false)         |
|`ROUTING_ALLOWED_ROUTE`                           | Сan be used in an emergency to change route (default undefined)    |


## Games Loading

Prerequisites:
- each game module should have a default version, e.g. `stable` - the default version is used for fun mode
- multiple different versions of the same game module can be installed on the server - the game version is derived from the game token
- if no version specified in the game token, the default version will be used

There are two ways of how to load games into game-server:

### Pre-loading
To make it work add the game module and its possible versions to `games.json` file in the format (the default version will be added automatically):
```
{
  "@skywind-group/sw_sod": ["0.1.12", "0.1.13"],
  "@skywind-group/sw_gol": []  // just default version will be installed
}
```

Example: The simplest game release process can look like this:
- add game to `games.json`:
```
...
  "@skywind-group/<game>": []  // just default version will be installed
...
```
- move the default version tag to a new game version
```
npm dist-tag add @skywind-group/<game>@x.y.z stable
```
- rebuild and redeploy game-server

### Dynamic loading (may not work on "readonly" docker containers)
To load games dynamically - define proper module version for the game in management-api in EntityGame and restart the game with a new game token.
The game module will be loaded automatically on the first touch.

### Achievements

| Variable                            | Description                                    |
|-------------------------------------|------------------------------------------------|
|`ACHIEVEMENTS_NATS_URL`              | NATS connection url (required)                 |
|`ACHIEVEMENTS_ENABLE`                | Switcher achievements feature                  |
|`ACHIEVEMENTS_NATS_RECOVERY_INTERVAL`| Nats recovery interval (default 60000)         |
|`ACHIEVEMENTS_NATS_ENV`              | Achievements nats channel preifx (default dev) |

### SM_RESULT

| Variable                            | Description                                    |
|-------------------------------------|------------------------------------------------|
|`SM_RESULT_ENABLED`                  | Switcher for sm_result feature                 |
|`SM_RESULT_MAX_LENGTH`               | Max length of string for sm_result             |
