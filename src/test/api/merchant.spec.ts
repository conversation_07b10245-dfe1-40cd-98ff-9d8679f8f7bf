import { expect } from "chai";
import * as makeRequest from "supertest";
import { testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;
import status200 = testing.status200;
import status400 = testing.status400;
import { getApplication } from "../../skywind/bootstrap";

describe("Merchant API", () => {
    let server;
    let request: RequestMock;

    before(async () => {
        const app = getApplication();
        await app.ready();
        server = app.server;
        request = requestMock(superagent);
    });

    beforeEach(() => {
        request.clearRoutes();
    });

    after(() => {
        request.unmock(superagent);
    });

    it("Redirects to game url", async () => {
        request.post("http://game-auth-api:3007//v1/merchants/game/url", status200({
            url: "http://game.test?param=value",
            token: "test_token",
        }));
        const res = await makeRequest(server).get("/v1/ipm/game")
            .query({
                merchantCode: "xxx",
                gameCode: "yyy",
                ticket: "zzz",
            })
            .redirects(0);
        expect(res.statusCode).to.be.equal(302);
        expect(res.header.location).to.be.equal("http://game.test/?param=value&startGameToken=test_token");
    });

    it("Not redirects to game url", async () => {
        request.post("http://game-auth-api:3007//v1/merchants/game/url", status200({
            url: "http://game.test?param=value",
            token: "test_token",
        }));
        const res = await makeRequest(server).get("/v1/ipm/game")
            .query({
                merchantCode: "xxx",
                gameCode: "yyy",
                ticket: "zzz",
                notRedirect: true
            });
        expect(res.statusCode).to.be.equal(200);
    });

    it("Redirects to game url with encoded params", async () => {
        request.post("http://game-auth-api:3007//v1/merchants/game/url", () => {
            return {
                status: 200,
                body: {
                    url: "http://game.test?param=value&lobby=http%3A%2F%2Flobby.test%2Fgame",
                    token: "test_token",
                }
            };
        });
        const query = {
            merchantCode: "xxx",
            gameCode: "yyy",
            ticket: "zzz",
            ip: "127.0.0.1"
        };
        const res = await makeRequest(server).get("/v1/ipm/game")
            .query(query)
            .redirects(0);
        expect(res.statusCode).to.be.equal(302);
        expect(res.header.location).to.be.equal(
            "http://game.test/?param=value&lobby=http%3A%2F%2Flobby.test%2Fgame&startGameToken=test_token");
    });

    it("Fails to redirect if merchant params are not valid", async () => {
        const apiError = {
            code: 506,
            message: "Error during interaction with merchant",
        };
        request.post("http://game-auth-api:3007//v1/merchants/game/url", status400(apiError));
        const res = await makeRequest(server).get("/v1/ipm/game")
            .query({
                merchantCode: "xxx",
                gameCode: "yyy",
                ticket: "zzz",
            });
        expect(res.statusCode).to.be.equal(400);
        expect(res.body).deep.equal(apiError);
    });

    it("Validate cors OPTION", async () => {
        const origin = "http://chebureck-egor.com";
        const res = await makeRequest(server).options("/v1/ipm/game")
            .set("Access-Control-Request-Headers", "access-control-allow-origin")
            .set("Origin", origin);

        expect(res.headers["access-control-allow-origin"]).to.be.equal(origin);
        expect(res.headers["access-control-allow-headers"]).to.be.contain("Access-Control-Allow-Origin");
        expect(res.statusCode).to.be.equal(204);
    });

    it("Validate cors POST", async () => {
        const origin = "http://chebureck-egor.com";
        request.post("http://game-auth-api:3007//v1/merchants/game/url", status200({
            url: "http://game.test?param=value",
            token: "test_token",
        }));
        const res = await makeRequest(server).get("/v1/ipm/game")
            .set("Access-Control-Request-Headers", "access-control-allow-origin")
            .set("Origin", origin)
            .query({
                merchantCode: "xxx",
                gameCode: "yyy",
                ticket: "zzz",
                notRedirect: true
            });

        expect(res.headers["access-control-allow-origin"]).to.be.equal(origin);
        expect(res.headers["access-control-allow-headers"]).to.be.contain("Access-Control-Allow-Origin");
        expect(res.statusCode).to.be.equal(200);
    });

});
