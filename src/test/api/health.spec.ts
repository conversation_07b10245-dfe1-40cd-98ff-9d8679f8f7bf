import { expect } from "chai";
import * as request from "supertest";
import { getApplication } from "../../skywind/bootstrap";

describe("Health check service API", () => {
    let server;
    const healthStatusOk = 200;

    describe("Positive flow", () => {
        const makeGetSuccess = async (path: string, expectStatus: number) => {
            const res = await request(server)
                .get(path)
                .send();
            expect(res.status).to.be.equal(expectStatus);
            expect(res.body).to.be.deep.equal({
                serverName: "GameServer"
            });
            expect(res.headers["access-control-allow-origin"]).to.be.a("string");
        };

        /**
         * staring and stopping server for this test suite
         */
        before(async () => {
            const app = getApplication();
            await app.ready();
            server = app.server;
        });

        it("checks /health", async () => {
            await makeGetSuccess("/v1/health", healthStatusOk);
        });
    });
});
