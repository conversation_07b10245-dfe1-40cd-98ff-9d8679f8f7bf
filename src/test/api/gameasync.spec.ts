import { expect } from "chai";
import { EventEmitter } from "events";
import { SinonStub, stub } from "sinon";
import {
    FunStartGameToken,
    GameEvent,
    GameInitResponse,
    GamePlayResponse,
    PaymentInfo
} from "@skywind-group/sw-game-core";
import * as GameService from "../../skywind/services/game/game";
import { flushAll, TestLoadResult } from "../helper";
import { generateStartGameToken } from "../../skywind/services/tokens";
import { AsyncTestGame } from "../testGames";
import { testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import config from "../../skywind/config";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;
import status200 = testing.status200;
import { setRandomGeneratorFactory } from "../../index";
import { RandomGeneratorFactoryImpl } from "../testRandomFactory";
import * as _ from "lodash";
import { IoServerV2 } from "../../skywind/io-versions";

const multiplier = 10;
const defaultAmount = config.funGame.defaultFunGameStartAmount * multiplier;

const io = {
    emitters: new Map<string, EventEmitter>(),
    use: () => {
        // empty function
    },

    of: function(name: string) {
        if (!this.emitters.get(name)) {
            this.emitters.set(name, new EventEmitter());
        }

        return this.emitters.get(name);
    },
    sockets: {
        on: () => {
            // empty
        }
    }
};

IoServerV2.useIO(io as any);

export class Socket {

    public request = {
        headers: {
            "user-agent": "Test",
            "x-forwarded-for": "127.0.0.1"
        }
    };
    private serverConnection = new EventEmitter();
    private clientConnection = new EventEmitter();

    public on(event, handler) {
        this.serverConnection.on(event, handler);
    }

    public emit(event, data) {
        this.clientConnection.emit(event, data);
    }

    public clientEmit(event, data) {
        this.serverConnection.emit(event, data);
    }

    public clientOn(event, handler) {
        this.clientConnection.on(event, handler);
    }

    // tslint:disable-next-line:no-empty
    public disconnect() {
        this.emit("disconnect", {});
    }

    public async waitOnCalled(event) {
        const isOnCalled = () => this.serverConnection.listenerCount(event) > 0;
        const retry = (fn, n, resolve, reject) => {
            if (n < 0) {
                return reject(new Error("Failed to wait"));
            }
            if (fn()) {
                return resolve();
            } else {
                setImmediate(() => retry(fn, n--, resolve, reject));
            }
        };
        return new Promise((resolve, reject) => {
            retry(isOnCalled, 5, resolve, reject);
        });
    }
}

describe("Game Async", () => {
    const testPlays = 10;
    let loadGame: SinonStub;
    let socket;
    let request: RequestMock;

    before(async () => {
        // await syncModels();
        loadGame = stub(GameService, "load");
        request = requestMock(superagent);
        setRandomGeneratorFactory(new RandomGeneratorFactoryImpl());
    });

    after(() => {
        loadGame.restore();
        request.unmock(superagent);
    });

    beforeEach(async () => {
        await flushAll();

        const events: GameEvent[] = [];
        const payments: PaymentInfo[] = [];
        const playResponses: GamePlayResponse[] = [];
        for (let i = 1; i <= testPlays; i += 1) {
            events.push({ type: "async", request: { request: "test", requestId: i } } as GameEvent);
            payments.push({ bet: i * multiplier, win: (i - 1) * multiplier });
            playResponses.push({ request: "test", requestId: i } as GamePlayResponse);
        }
        loadGame.returns(Promise.resolve(new TestLoadResult(new AsyncTestGame(
            [{ request: "init" } as GameInitResponse], [], events, payments, [], playResponses, 10))));

        const token = await generateStartGameToken(
            {
                gameCode: "test_slot",
                brandId: 1,
                playerCode: "PL001",
                currency: "USD",
                playmode: "fun"
            }
        );
        request.post("http://api:3006//v2/play/fun/startgame", status200({
            gameToken: token,
            limits: {},
            settings: {},
            test: true,
            jrsdSettings: { a: 1 },
            jurisdictionCode: "GB",
            playedFromCountry: "GB",
        }));
        request.post("http://game-auth-api:3007//v1/game/url", status200({
            token: "test",
            url: "http://test.com"
        }));

        socket = new Socket();
        io.of("/").emit("connection", socket);
        await socket.waitOnCalled("init");
        await socket.waitOnCalled("play");
    });

    afterEach(() => {
        loadGame.resetBehavior();
        request.clearRoutes();

        socket.clientEmit("disconnect", null);
    });

    it("fails to process invalid 'init' request", (done) => {
        request.post("http://api:3006//v2/play/startgame", () => {
            throw new Error();
        });
        socket.clientOn("game-error", err => {
            expect(err).to.exist;
            done();
        });

        socket.clientEmit("init", { startGameToken: "invalid" });
    });

    describe("positive", () => {

        it("processes 'init' request", (done) => {
            socket.clientOn("init", (response) => {
                delete (response.gameSession);
                expect(response).to.deep.equal(
                    {
                        balance:
                            {
                                currency: "USD",
                                amount: defaultAmount,
                                real: { amount: defaultAmount },
                                bonus: { amount: 0 }
                            },
                        result: { request: "init" },
                        gameSettings: undefined,
                        brandSettings: undefined,
                        jrsdSettings: { a: 1 },
                        jurisdictionCode: "GB",
                        playedFromCountry: "GB",
                        roundEnded: true,
                        renderType: undefined,
                        brandInfo: undefined,
                    });
                expect(request.args[0].headers["x-forwarded-for"]).to.equal("127.0.0.1");
                done();
            });

            socket.clientEmit("init", {
                request: "init",
                name: "test",
                gameId: "test",
                deviceId: "test",
                startGameToken: {
                    playerCode: "test_player",
                    gameCode: "test",
                    brandId: 1,
                    currency: "USD",
                } as FunStartGameToken,
            });
        });
    });

    describe("play", () => {

        beforeEach(done => {
            socket.clientOn("init", (response) => {
                done();
            });

            socket.clientEmit("init", {
                request: "init",
                name: "test",
                gameId: "test",
                deviceId: "test",
                startGameToken: {
                    playerCode: "test_player",
                    gameCode: "test",
                    brandId: 1,
                    currency: "USD",
                } as FunStartGameToken,
            });
        });

        it("processes 'play' request", done => {
            const expectEventsDone = expectDone(2, done);

            socket.clientOn("event", event => {
                expect(event).to.deep.equal({
                    type: "async",
                    request: {
                        request: "test",
                        requestId: 1
                    }
                });
                expectEventsDone();
            });

            socket.clientOn("play", response => {
                expect(response).to.deep.equal({
                    "balance": {
                        "amount": defaultAmount - multiplier,
                        "bonus": {
                            "amount": 0,
                        },
                        "currency": "USD",
                        "real": {
                            "amount": defaultAmount - multiplier,
                        },
                    },
                    extraData: undefined,
                    "gameSession": undefined,
                    "requestId": 1,
                    "result": {
                        "request": "test",
                        "requestId": 1,
                    },
                    "roundEnded": false
                });
                expectEventsDone();
            });

            socket.clientEmit("play", {
                request: "play",
                requestId: 1,
            });
        });

        it("processes sequential 'play' requests in proper order", done => {
            const expectEventsDone = expectDone(testPlays * 2, done);
            let expectedEventRequestId = 0;
            let expectedRequestId = 0;
            let expectedBalance = defaultAmount;

            socket.clientOn("event", event => {
                expectedEventRequestId += 1;
                expect(event).to.deep.equal({
                    type: "async",
                    request: {
                        request: "test",
                        requestId: expectedEventRequestId,
                    }
                });
                expectEventsDone();
            });

            socket.clientOn("play", response => {
                expectedRequestId += 1;
                expectedBalance -= multiplier;
                expect(_.omit(response, "roundTotalBet", "roundTotalWin")).to.deep.equal({
                    "balance": {
                        "amount": expectedBalance,
                        "bonus": {
                            "amount": 0,
                        },
                        "currency": "USD",
                        "real": {
                            "amount": expectedBalance,
                        },
                    },
                    extraData: undefined,
                    "gameSession": undefined,
                    "requestId": expectedRequestId,
                    "result": {
                        "request": "test",
                        "requestId": expectedRequestId,
                    },
                    "roundEnded": false
                });
                expectEventsDone();
            });

            for (let i = 1; i <= testPlays; i += 1) {
                socket.clientEmit("play", {
                    request: "play",
                    requestId: i,
                });
            }
        });

        it("fails to process concurrent 'play' request", done => {
            socket.clientOn("game-error", error => {
                expect(error.code).to.be.equal(11);
            });

            socket.clientEmit("play", {
                request: "play",
                requestId: 1,
            });
            socket.clientEmit("play", {
                request: "play",
                requestId: 1,
            });

            socket.clientOn("disconnect", () => {
                done();
            });
        });

        it("disconnects", async () => {
            await socket.clientEmit("disconnect");
        });

        function expectDone(expectedEvents, done) {
            let events = 0;
            return function() {
                events += 1;
                if (events === expectedEvents) {
                    done();
                } else if (events > expectedEvents) {
                    done(new Error("Too many events received"));
                }
            };
        }
    });
});
