import { RandomGeneratorFactory } from "../skywind/services/random";
import { RandomGenerator, CSRandomGenerator } from "@skywind-group/sw-random-cs";

let CheatingRandomGenerator;
function initCheatingRandomGenerator() {
    if (!CheatingRandomGenerator) {
        CheatingRandomGenerator = require("@skywind-group/sw-random-cs-cheating").CheatingRandomGenerator;
    }
}

const cheatsConfig = require("../../resources/testRandomFactory/cheatsConfig.json");

const globalRandomGenerator = new CSRandomGenerator();

export class RandomGeneratorFactoryImpl implements RandomGeneratorFactory {

    public createRandomGenerator(req: any): RandomGenerator {
        let positions;
        if (req && req.positions) {
            positions = req.positions;
            req.positions = undefined;
        }
        if (positions && cheatsConfig.allowSetPositionsByClient) {
            initCheatingRandomGenerator();
            return new CheatingRandomGenerator(positions);
        }
        return globalRandomGenerator;
    }

    public getCheats(rng: RandomGenerator): number[] {
        if (cheatsConfig.allowSetPositionsByClient) {
            initCheatingRandomGenerator();
            if (rng instanceof CheatingRandomGenerator) {
                const cheats = (rng as any).cheatingValues;
                return cheats && cheats.length > 0 ? cheats : undefined;
            }
        }
        return undefined;
    }

    public isAllowedSetPositionsByClient(): boolean {
        return cheatsConfig.allowSetPositionsByClient;
    }
}
