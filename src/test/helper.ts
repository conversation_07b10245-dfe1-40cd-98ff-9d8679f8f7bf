import { Redis as RedisClient } from "ioredis";
import { <PERSON><PERSON>ed<PERSON>, Redis } from "../skywind/storage/redis";
import * as jwt from "jsonwebtoken";
import { GameTokenData } from "../skywind/services/tokens";
import { SomeGame } from "@skywind-group/sw-game-core";
import {
    ANALYTICS_QUEUE_NAME,
    CLEAN_GAME_CONTEXT_QUEUE_NAME,
    CLEAN_PLAYER_CONTEXT_QUEUE_NAME,
    GAME_HISTORY_LOG,
    ROUND_HISTORY_LOG,
    SESSION_HISTORY_LOG
} from "../skywind/services/queue/redisQueue";
import { CleanupContextRequest, GameEventHistory, RoundHistory, SessionHistory } from "../skywind/history/history";
import { ModuleName } from "../skywind/services/game/gamemodule";
import { LoadedGameResult } from "../skywind/services/game/game";
import { getGameInitSettingsModel } from "../skywind/services/gameInitSettings";
import { setSessionIdGenerator } from "../skywind/services/gameSession";
import * as _ from "lodash";
import config from "../skywind/config";
import db from "../skywind/storage/db";

import {
    getArchiveContextModel,
    getGameContextModel,
    getPlayerContextModel
} from "../skywind/services/offlinestorage/models";
import { setGenerators } from "../skywind/services/onlinestorage/gameContextCommands";
import { AnalyticsData } from "../skywind/services/analytics/analyticsService";
import { getManager } from "../skywind/wallet";
import { getGameHistoryModels } from "../skywind/history/model/gameHistory";
import { getSessionHistoryModels } from "../skywind/history/model/sessionHistory";
import { getRoundsHistoryModels } from "../skywind/history/model/roundHistory";
import { HiLoIdGenerator, redis } from "@skywind-group/sw-utils";

export const TEST_MODULE_NAME: ModuleName = {
    nameWithVersion: "skywind-group/sw_test@0.1.0",
    name: "skywind-group/sw_test",
    id: "sw_test",
    version: "0.1.0"
};

export async function getGameHistoryItem(): Promise<any> {
    const client: RedisClient = await Redis.get().get();
    try {
        const response = await client.lpop(GAME_HISTORY_LOG);
        return response ? JSON.parse(response) : undefined;
    } finally {
        await Redis.get().release(client);
    }
}

export function getGameHistory(from: number, to: number): Promise<GameEventHistory[]> {
    return getRange(GAME_HISTORY_LOG, from, to);
}

export async function getGameHistoryDuplicates(from: number, to: number): Promise<GameEventHistory[]> {
    return getGameHistoryModels().duplicates.findAll({ offset: from, limit: to + from })
        .then((data) => data.map((i: any) => i.get()) as any);
}

export function getRoundHistory(from: number, to: number): Promise<RoundHistory[]> {
    return getRange(ROUND_HISTORY_LOG, from, to);
}

export async function getRoundHistoryDuplicates(from: number, to: number): Promise<RoundHistory[]> {
    return getRoundsHistoryModels().duplicates.findAll({ offset: from, limit: to + from })
        .then((data) => data.map((i: any) => i.get()) as any);
}

export function getSessionHistory(from: number, to: number): Promise<SessionHistory[]> {
    return getRange(SESSION_HISTORY_LOG, from, to);
}

export async function getSessionHistoryDuplicates(from: number, to: number): Promise<SessionHistory[]> {
    return getSessionHistoryModels().duplicates.findAll({ offset: from, limit: to + from })
        .then((data) => data.map((i: any) => i.get()) as any);
}

export function getCleanupContextRequests(from: number, to: number): Promise<CleanupContextRequest[]> {
    return getRange(CLEAN_GAME_CONTEXT_QUEUE_NAME, from, to);
}

export function getCleanupPlayerContextRequests(from: number, to: number): Promise<CleanupContextRequest[]> {
    return getRange(CLEAN_PLAYER_CONTEXT_QUEUE_NAME, from, to);
}

export function getAnalytics(from: number, to: number): Promise<AnalyticsData[]> {
    return getRange(ANALYTICS_QUEUE_NAME, from, to);
}

export async function activityListCount() {
    const client = await Redis.get().get();
    try {
        return await client.zcard(config.namespaces.lastGameActivityKey);
    } finally {
        await Redis.get().release(client);
    }
}

export async function activityList(withScores: boolean = true): Promise<string[]> {
    const client = await Redis.get().get();
    try {
        const count = await client.zcard(config.namespaces.lastGameActivityKey);
        return withScores ?
               await client.zrange(config.namespaces.lastGameActivityKey, 0, count, "WITHSCORES") :
               await client.zrange(config.namespaces.lastGameActivityKey, 0, count);
    } finally {
        await Redis.get().release(client);
    }
}

export async function playersActivityListCount() {
    const client = await Redis.get().get();
    try {
        return await client.zcard(config.namespaces.lastPlayerActivityPrefix);
    } finally {
        await Redis.get().release(client);
    }
}

export async function playersActivityList(withScore: boolean = true): Promise<string[]> {
    const client = await Redis.get().get();
    try {
        const count = await client.zcard(config.namespaces.lastPlayerActivityPrefix);
        return withScore ?
               await client.zrange(config.namespaces.lastPlayerActivityPrefix, 0, count, "WITHSCORES") :
               await client.zrange(config.namespaces.lastPlayerActivityPrefix, 0, count);
    } finally {
        await Redis.get().release(client);
    }
}

export async function getRange(listName: string, from: number, to: number): Promise<any[]> {
    const client = await Redis.get().get();
    try {
        const response = await client.lrange(listName, from, to);
        return response.map(item => JSON.parse(item));
    } finally {
        await Redis.get().release(client);
    }
}

async function flushRedis(redisPool: redis.RedisPool<RedisClient>): Promise<void> {
    const rc: RedisClient = await redisPool.get();
    try {
        await rc.flushall();
    } finally {
        await redisPool.release(rc);
    }
}

export async function hGetAll(key: string): Promise<any> {
    const rc: RedisClient = await Redis.get().get();
    try {
        return await rc.hgetall(key);
    } finally {
        await Redis.get().release(rc);
    }
}

export async function getKey(key: string): Promise<any> {
    const rc: RedisClient = await Redis.get().get();
    try {
        return await rc.get(key);
    } finally {
        await Redis.get().release(rc);
    }
}

export async function flushAll(): Promise<void> {
    // flush gameContext round counter
    setGenerators(new HiLoIdGenerator("testRoundId", 1000, Redis),
        new HiLoIdGenerator("fun:testRoundId", 1000, FunRedis));
    setSessionIdGenerator(new HiLoIdGenerator("testSessionId", 1000, Redis));
    await flushRedis(Redis.get());
    await flushRedis(FunRedis.get());
}

export async function syncModels(): Promise<void> {
    await getManager();
    await db.get().sync();
    await getGameContextModel().sync();
    await getArchiveContextModel().sync();
    await getGameInitSettingsModel().sync();
    await getPlayerContextModel().sync();
}

export async function createGameToken(gameTokenData: GameTokenData): Promise<string> {
    return await jwt.sign(gameTokenData, "SOMESECRETKEY", { noTimestamp: true });
}

export class TestLoadResult extends LoadedGameResult {
    constructor(game: SomeGame, noWinResult?: any) {
        super(game, TEST_MODULE_NAME, noWinResult);
    }
}

export function omitUndefined(obj): any {
    if (_.isArray(obj)) {
        return _.map(obj, omitUndefined);
    } else if (_.isObject(obj)) {
        return _.omitBy(_.mapValues(obj, omitUndefined), _.isUndefined);
    } else {
        return obj;
    }
}
