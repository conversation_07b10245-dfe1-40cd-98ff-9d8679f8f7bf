import { PaymentOperation } from "../skywind/services/wallet";
import { expect, should, use } from "chai";
import { PendingModification, SpecialState } from "../skywind/services/context/gamecontext";
import { GameContextID, PlayerContextID } from "../skywind/services/contextIds";
import { syncModels } from "./helper";
import { GameContextPersistencePolicy } from "@skywind-group/sw-game-core";
import { getGameContextDecoder, getGameContextEncoder } from "../skywind/services/encoder/contextEncoding";
import { GameSession } from "../skywind/services/gameSession";
import { deepClone } from "../skywind/utils/cloner";
import { BNSGameData } from "../skywind/services/auth";
import {
    getArchiveContextModel,
    getGameContextModel,
    getPlayerContextModel
} from "../skywind/services/offlinestorage/models";
import * as _ from "lodash";
import { PlayerContextImpl } from "../skywind/services/playercontext/playerContext";
import { GameFlowContextImpl } from "../skywind/services/context/gameContextImpl";
import { getGameFlowContextManager } from "../skywind/services/contextmanager/contextManagerImpl";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

describe("GameContextManager: working with offline entities", () => {
    const context: GameFlowContextImpl = new GameFlowContextImpl(GameContextID.createFromString(
        "sw-slot-engine:context:1:playerId:gameId:deviceId"));
    beforeEach(async () => {
        await syncModels();
        await getGameContextModel().truncate();
        await getArchiveContextModel().truncate();
        await getPlayerContextModel().truncate();

        context.gameContext = {
            scenesState: {},
            currentScene: "scene",
            nextScene: "next",
            history: ["event1", "event2"],
            stake: {
                bet: 10,
                win: 1000
            }
        };

        context.session = GameSession.create("sessionID");
        context.lastRequestId = 1;
        context.gameSerialNumber = 1;
        context.totalEventId = 1;

        context.settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: 0,
            stakeAll: [1, 2, 3, 4],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 100,
            transferEnabled: false,
        };
        context.gameData = {
            limits: {},
            gameId: "gameId",
            gameTokenData: {
                playerCode: "playerId",
                gameCode: "gameCode",
                brandId: 1,
                currency: "USD",
                token: "SOMETOKEN",
                playmode: "real",
                merchantSessionId: "test_merchant_session_id"
            }
        };
        context.pendingModification = {
            request: { request: "req", requestId: 1 },
            history: {
                type: "slot",
                version: 1,
                roundEnded: true,
                data: { positions: [4, 3, 2, 1] }
            },
            walletOperation: {
                operation: "payment",
                transactionId: "trxId",
                currency: "USD",
                bet: 100,
                win: 1000,
            } as PaymentOperation,
            newState: {
                nextScene: "SOMESCENE"
            }
        } as PendingModification;
        context.roundEnded = false;
        context.roundId = "1";
        context.specialState = SpecialState.BROKEN_INTEGRATION;
    });

    const getEngineContext = async (ctx: GameFlowContextImpl) => {
        return JSON.parse(JSON.stringify(await getGameContextEncoder().encode(ctx)));
    };

    it("saves game contexts", async () => {
        await getGameFlowContextManager().saveGameContextOffline([context]);
        const result = await getGameFlowContextManager().findOfflineGameContext(context.id);
        expect(result.data).deep.equal(await getEngineContext(context));
        expect(result.specialState).equal(SpecialState.BROKEN_INTEGRATION);
        expect(result.merchantSessionId).equal(context.gameData.gameTokenData.merchantSessionId);
    });

    it("saves game contexts with expiration for bns", async () => {
        const data = deepClone(context.gameData);
        const now = Date.now();
        try {
            context.gameData.gameTokenData.playmode = "bns";
            (context.gameData as BNSGameData).bnsPromotion = {
                promoId: "1",
                expireAt: now,
                exchangeRate: 1
            };
            await getGameFlowContextManager().saveGameContextOffline([context]);
            const result = await getGameFlowContextManager().findOfflineGameContext(context.id);
            expect(result.data).deep.equal(await getEngineContext(context));
            const contextDB = await getGameContextModel().findByPk(context.id.asString());
            expect(contextDB.expireAt.toISOString()).equal(new Date(now).toISOString());
        } finally {
            context.gameData = data;
        }
    });

    it("updates created game context", async () => {
        await getGameFlowContextManager().saveGameContextOffline([context]);
        let result = await getGameFlowContextManager().findOfflineGameContext(context.id);
        expect(result.data).deep.equal(await getEngineContext(context));

        // update wallet operation
        context.pendingModification = null;
        context.session = GameSession.create("NEWSESSIONID");
        context.persistencePolicy = GameContextPersistencePolicy.LONG_TERM;
        await getGameFlowContextManager().saveGameContextOffline([context]);
        result = await getGameFlowContextManager().findOfflineGameContext(context.id);
        expect(result.data).deep.equal(await getEngineContext(context));

    });

    it("finds game context", async () => {
        let result = await getGameFlowContextManager().findOfflineGameContext(context.id);
        expect(result).is.undefined;

        await getGameFlowContextManager().saveGameContextOffline([context]);
        result = await getGameFlowContextManager().findOfflineGameContext(context.id);
        expect(result.data).deep.equal(await getEngineContext(context));
    });

    it("remove game context", async () => {
        let result = await getGameFlowContextManager().findOfflineGameContext(context.id);
        expect(result).is.undefined;
        await getGameFlowContextManager().saveGameContextOffline([context]);
        result = await getGameFlowContextManager().findOfflineGameContext(context.id);
        expect(result.data).deep.equal(await getEngineContext(context));
        await getGameFlowContextManager().removeGameContextOffline([context.id]);
        result = await getGameFlowContextManager().findOfflineGameContext(context.id);
        expect(result).is.undefined;
    });

    it("finds expired", async () => {
        const ts = new Date();
        expect(await getGameFlowContextManager().findExpiredGameContext(ts, 1)).deep.equals([]);
        const id = "games:context:25:AUTO_PLAYER_2121200175:sw_mrmnky:web";
        const gameContextId = GameContextID.createFromString(id);

        const item = {
            id,
            brandId: gameContextId.brandId,
            playerCode: gameContextId.playerCode,
            gameCode: gameContextId.gameCode,
            gameId: gameContextId.gameCode,
            deviceId: gameContextId.deviceId,
            roundId: "1",
            // tslint:disable-next-line:max-line-length
            data: "{\"dataVersion\":\"2\",\"currentSessionId\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOjMzOTMyOSwiaWQiOiJnYW1lczpjb250ZXh0OjI1OkFVVE9fUExBWUVSXzIxMjEyMDAxNzU6c3dfbXJtbmt5OndlYiIsImlhdCI6MTUwNzcyNTA1NywiaXNzIjoic2t5d2luZGdyb3VwIn0.syF_8A29OEHw4zMEW7ydXZl6xk-M1BRt30NtlBISplduFkaUm0dwBKBCyRSA_O04gExWWzb4VRNax05a89rnog\",\"requestId\":\"3\",\"historyCounter\":\"3\",\"version\":\"8\",\"state\":\"{\\\"gameData\\\":{\\\"gameTokenData\\\":{\\\"gameCode\\\":\\\"sw_mrmnky\\\",\\\"brandId\\\":25,\\\"playerCode\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"currency\\\":\\\"USD\\\",\\\"test\\\":true,\\\"transferEnabled\\\":true,\\\"iat\\\":1507725057,\\\"iss\\\":\\\"skywindgroup\\\",\\\"token\\\":\\\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJnYW1lQ29kZSI6InN3X21ybW5reSIsImJyYW5kSWQiOjI1LCJwbGF5ZXJDb2RlIjoiQVVUT19QTEFZRVJfMjEyMTIwMDE3NSIsImN1cnJlbmN5IjoiVVNEIiwidGVzdCI6dHJ1ZSwidHJhbnNmZXJFbmFibGVkIjp0cnVlLCJpYXQiOjE1MDc3MjUwNTcsImlzcyI6InNreXdpbmRncm91cCJ9.p_stFZuaxYFF3u6sNbWEiXr9ygeujPYN-dTHJR4gBxC_hfduscpP1LJ9h34r0Mx2awnAVHspn6fk3lvCGaOQlg\\\"},\\\"balance\\\":{\\\"main\\\":50000},\\\"player\\\":{\\\"code\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"id\\\":\\\"LB0n1dql\\\",\\\"status\\\":\\\"normal\\\",\\\"firstName\\\":\\\"AUTO_PLAYER_FIRSTNAME_2121200175\\\",\\\"lastName\\\":\\\"AUTO_PLAYER_LASTNAME_2121200175\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"currency\\\":\\\"USD\\\",\\\"country\\\":\\\"BY\\\",\\\"language\\\":\\\"en\\\",\\\"agentId\\\":null,\\\"isTest\\\":true,\\\"lastLogin\\\":\\\"2017-10-11T12:30:57.842Z\\\",\\\"customData\\\":null,\\\"createdAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"brandId\\\":\\\"GJqNWRkX\\\",\\\"brandTitle\\\":null},\\\"limits\\\":{\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200},\\\"settings\\\":{\\\"transferEnabled\\\":true}},\\\"gameVersion\\\":\\\"0.3.0\\\",\\\"gameContext\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"scenesState\\\":{\\\"main\\\":{\\\"multiplier\\\":1,\\\"behaviorsState\\\":{\\\"regularSlotSceneBehavior\\\":null}},\\\"freeSpins\\\":{\\\"multiplier\\\":3,\\\"behaviorsState\\\":{\\\"freeGamesSlotSceneBehavior\\\":{\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2}}}},\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"history\\\":[{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"scene\\\":\\\"main\\\",\\\"multiplier\\\":1,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"main\\\",\\\"positions\\\":[36,0,0,3,0],\\\"view\\\":[[13,6,4,4,7],[2,1,6,13,10],[5,2,13,2,2],[3,7,3,6,3]]},\\\"rewards\\\":[{\\\"reward\\\":\\\"scatter\\\",\\\"positions\\\":[[0,0],[1,3],[2,2]],\\\"payout\\\":2,\\\"paytable\\\":[6,2]}],\\\"events\\\":[{\\\"id\\\":\\\"freeSpinsStart\\\",\\\"amount\\\":12,\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[46,0,20,21,23],\\\"view\\\":[[1,4,2,1,1],[5,1,4,4,4],[2,9,6,8,2],[7,6,12,3,8]]},\\\"triggeredSceneId\\\":\\\"freeSpins\\\",\\\"triggerSymbols\\\":[[0,0],[1,3],[2,2]]}],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}],\\\"currentRoundWin\\\":2,\\\"nextScene\\\":\\\"\\\",\\\"previousProcessSceneResult\\\":{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0,\\\"totalWin\\\":0,\\\"scene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsWin\\\":0,\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[36,20,11,21,36],\\\"view\\\":[[2,8,9,1,4],[7,4,4,4,5],[8,1,5,8,9],[5,11,6,3,3]]},\\\"rewards\\\":[],\\\"events\\\":[],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}},\\\"settings\\\":{\\\"transferEnabled\\\":true,\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200,\\\"defaultCoin\\\":1,\\\"coins\\\":[1],\\\"currencyMultiplier\\\":100},\\\"roundId\\\":11980748,\\\"roundEnded\\\":false,\\\"round\\\":{\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"totalEvents\\\":3,\\\"balanceBefore\\\":50000,\\\"startedAt\\\":\\\"2017-10-11T12:30:58.244Z\\\",\\\"broken\\\":true},\\\"requestContext\\\":{\\\"ip\\\":\\\"::ffff:************\\\",\\\"language\\\":\\\"en\\\",\\\"country\\\":\\\"BY\\\"},\\\"createdAt\\\":\\\"2017-10-11T12:30:58.008Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:58.609Z\\\"}\"}",
            createdAt: new Date("2017-11-09 10:40:17.5"),
            updatedAt: new Date("2017-11-09 10:40:17.5"),
            expireAt: ts,
            broken: true,
            brokenPayment: true,
            requireLogout: true,
            requireTransferOut: false,
            requireCompletion: false,
            specialState: null,
            merchantSessionId: "merchant_session",
            retryAttempts: 1
        };
        await getGameContextModel().create(item);

        expect((await getGameFlowContextManager().findExpiredGameContext(ts, 1)).length).equals(1);
    });

    it("finds expired - skip wrong policy of wrong special state", async () => {
        const ts = new Date();
        expect(await await getGameFlowContextManager().findExpiredGameContext(ts, 1)).deep.equals([]);
        const id1 = "games:context:25:AUTO_PLAYER_2121200175:sw_mrmnky:web";
        const id2 = "games:context:25:AUTO_PLAYER_2121200175:sw_mrmnky:mobile";
        const id3 = "games:context:25:AUTO_PLAYER_2121200175_1:sw_mrmnky:mobile";
        const id4 = "games:context:25:AUTO_PLAYER_2121200175_2:sw_mrmnky:mobile";
        const id5 = "games:context:25:AUTO_PLAYER_2121200175_3:sw_mrmnky:mobile";
        const gameContextId1 = GameContextID.createFromString(id1);
        const gameContextId2 = GameContextID.createFromString(id2);
        const gameContextId3 = GameContextID.createFromString(id3);
        const gameContextId4 = GameContextID.createFromString(id4);
        const gameContextId5 = GameContextID.createFromString(id5);

        await getGameContextModel().create( {
            id: id1,
            brandId: gameContextId1.brandId,
            playerCode: gameContextId1.playerCode,
            gameCode: gameContextId1.gameCode,
            gameId: gameContextId1.gameCode,
            deviceId: gameContextId1.deviceId,
            roundId: "1",
            // tslint:disable-next-line:max-line-length
            data: "{\"dataVersion\":\"2\",\"currentSessionId\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOjMzOTMyOSwiaWQiOiJnYW1lczpjb250ZXh0OjI1OkFVVE9fUExBWUVSXzIxMjEyMDAxNzU6c3dfbXJtbmt5OndlYiIsImlhdCI6MTUwNzcyNTA1NywiaXNzIjoic2t5d2luZGdyb3VwIn0.syF_8A29OEHw4zMEW7ydXZl6xk-M1BRt30NtlBISplduFkaUm0dwBKBCyRSA_O04gExWWzb4VRNax05a89rnog\",\"requestId\":\"3\",\"historyCounter\":\"3\",\"version\":\"8\",\"state\":\"{\\\"gameData\\\":{\\\"gameTokenData\\\":{\\\"gameCode\\\":\\\"sw_mrmnky\\\",\\\"brandId\\\":25,\\\"playerCode\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"currency\\\":\\\"USD\\\",\\\"test\\\":true,\\\"transferEnabled\\\":true,\\\"iat\\\":1507725057,\\\"iss\\\":\\\"skywindgroup\\\",\\\"token\\\":\\\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJnYW1lQ29kZSI6InN3X21ybW5reSIsImJyYW5kSWQiOjI1LCJwbGF5ZXJDb2RlIjoiQVVUT19QTEFZRVJfMjEyMTIwMDE3NSIsImN1cnJlbmN5IjoiVVNEIiwidGVzdCI6dHJ1ZSwidHJhbnNmZXJFbmFibGVkIjp0cnVlLCJpYXQiOjE1MDc3MjUwNTcsImlzcyI6InNreXdpbmRncm91cCJ9.p_stFZuaxYFF3u6sNbWEiXr9ygeujPYN-dTHJR4gBxC_hfduscpP1LJ9h34r0Mx2awnAVHspn6fk3lvCGaOQlg\\\"},\\\"balance\\\":{\\\"main\\\":50000},\\\"player\\\":{\\\"code\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"id\\\":\\\"LB0n1dql\\\",\\\"status\\\":\\\"normal\\\",\\\"firstName\\\":\\\"AUTO_PLAYER_FIRSTNAME_2121200175\\\",\\\"lastName\\\":\\\"AUTO_PLAYER_LASTNAME_2121200175\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"currency\\\":\\\"USD\\\",\\\"country\\\":\\\"BY\\\",\\\"language\\\":\\\"en\\\",\\\"agentId\\\":null,\\\"isTest\\\":true,\\\"lastLogin\\\":\\\"2017-10-11T12:30:57.842Z\\\",\\\"customData\\\":null,\\\"createdAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"brandId\\\":\\\"GJqNWRkX\\\",\\\"brandTitle\\\":null},\\\"limits\\\":{\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200},\\\"settings\\\":{\\\"transferEnabled\\\":true}},\\\"gameVersion\\\":\\\"0.3.0\\\",\\\"gameContext\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"scenesState\\\":{\\\"main\\\":{\\\"multiplier\\\":1,\\\"behaviorsState\\\":{\\\"regularSlotSceneBehavior\\\":null}},\\\"freeSpins\\\":{\\\"multiplier\\\":3,\\\"behaviorsState\\\":{\\\"freeGamesSlotSceneBehavior\\\":{\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2}}}},\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"history\\\":[{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"scene\\\":\\\"main\\\",\\\"multiplier\\\":1,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"main\\\",\\\"positions\\\":[36,0,0,3,0],\\\"view\\\":[[13,6,4,4,7],[2,1,6,13,10],[5,2,13,2,2],[3,7,3,6,3]]},\\\"rewards\\\":[{\\\"reward\\\":\\\"scatter\\\",\\\"positions\\\":[[0,0],[1,3],[2,2]],\\\"payout\\\":2,\\\"paytable\\\":[6,2]}],\\\"events\\\":[{\\\"id\\\":\\\"freeSpinsStart\\\",\\\"amount\\\":12,\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[46,0,20,21,23],\\\"view\\\":[[1,4,2,1,1],[5,1,4,4,4],[2,9,6,8,2],[7,6,12,3,8]]},\\\"triggeredSceneId\\\":\\\"freeSpins\\\",\\\"triggerSymbols\\\":[[0,0],[1,3],[2,2]]}],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}],\\\"currentRoundWin\\\":2,\\\"nextScene\\\":\\\"\\\",\\\"previousProcessSceneResult\\\":{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0,\\\"totalWin\\\":0,\\\"scene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsWin\\\":0,\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[36,20,11,21,36],\\\"view\\\":[[2,8,9,1,4],[7,4,4,4,5],[8,1,5,8,9],[5,11,6,3,3]]},\\\"rewards\\\":[],\\\"events\\\":[],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}},\\\"settings\\\":{\\\"transferEnabled\\\":true,\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200,\\\"defaultCoin\\\":1,\\\"coins\\\":[1],\\\"currencyMultiplier\\\":100},\\\"roundId\\\":11980748,\\\"roundEnded\\\":false,\\\"round\\\":{\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"totalEvents\\\":3,\\\"balanceBefore\\\":50000,\\\"startedAt\\\":\\\"2017-10-11T12:30:58.244Z\\\",\\\"broken\\\":true},\\\"requestContext\\\":{\\\"ip\\\":\\\"::ffff:************\\\",\\\"language\\\":\\\"en\\\",\\\"country\\\":\\\"BY\\\"},\\\"createdAt\\\":\\\"2017-10-11T12:30:58.008Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:58.609Z\\\"}\"}",
            createdAt: new Date("2017-11-09 10:40:17.5"),
            updatedAt: new Date("2017-11-09 10:40:17.5"),
            expireAt: ts,
            broken: true,
            brokenPayment: true,
            requireLogout: true,
            requireTransferOut: false,
            requireCompletion: false,
            specialState: null,
            merchantSessionId: "merchant_session",
            retryAttempts: 1,
            policy: GameContextPersistencePolicy.LONG_TERM
        });

        await getGameContextModel().create( {
            id: id2,
            brandId: gameContextId2.brandId,
            playerCode: gameContextId2.playerCode,
            gameCode: gameContextId2.gameCode,
            gameId: gameContextId2.gameCode,
            deviceId: gameContextId2.deviceId,
            roundId: "1",
            // tslint:disable-next-line:max-line-length
            data: "{\"dataVersion\":\"2\",\"currentSessionId\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOjMzOTMyOSwiaWQiOiJnYW1lczpjb250ZXh0OjI1OkFVVE9fUExBWUVSXzIxMjEyMDAxNzU6c3dfbXJtbmt5OndlYiIsImlhdCI6MTUwNzcyNTA1NywiaXNzIjoic2t5d2luZGdyb3VwIn0.syF_8A29OEHw4zMEW7ydXZl6xk-M1BRt30NtlBISplduFkaUm0dwBKBCyRSA_O04gExWWzb4VRNax05a89rnog\",\"requestId\":\"3\",\"historyCounter\":\"3\",\"version\":\"8\",\"state\":\"{\\\"gameData\\\":{\\\"gameTokenData\\\":{\\\"gameCode\\\":\\\"sw_mrmnky\\\",\\\"brandId\\\":25,\\\"playerCode\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"currency\\\":\\\"USD\\\",\\\"test\\\":true,\\\"transferEnabled\\\":true,\\\"iat\\\":1507725057,\\\"iss\\\":\\\"skywindgroup\\\",\\\"token\\\":\\\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJnYW1lQ29kZSI6InN3X21ybW5reSIsImJyYW5kSWQiOjI1LCJwbGF5ZXJDb2RlIjoiQVVUT19QTEFZRVJfMjEyMTIwMDE3NSIsImN1cnJlbmN5IjoiVVNEIiwidGVzdCI6dHJ1ZSwidHJhbnNmZXJFbmFibGVkIjp0cnVlLCJpYXQiOjE1MDc3MjUwNTcsImlzcyI6InNreXdpbmRncm91cCJ9.p_stFZuaxYFF3u6sNbWEiXr9ygeujPYN-dTHJR4gBxC_hfduscpP1LJ9h34r0Mx2awnAVHspn6fk3lvCGaOQlg\\\"},\\\"balance\\\":{\\\"main\\\":50000},\\\"player\\\":{\\\"code\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"id\\\":\\\"LB0n1dql\\\",\\\"status\\\":\\\"normal\\\",\\\"firstName\\\":\\\"AUTO_PLAYER_FIRSTNAME_2121200175\\\",\\\"lastName\\\":\\\"AUTO_PLAYER_LASTNAME_2121200175\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"currency\\\":\\\"USD\\\",\\\"country\\\":\\\"BY\\\",\\\"language\\\":\\\"en\\\",\\\"agentId\\\":null,\\\"isTest\\\":true,\\\"lastLogin\\\":\\\"2017-10-11T12:30:57.842Z\\\",\\\"customData\\\":null,\\\"createdAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"brandId\\\":\\\"GJqNWRkX\\\",\\\"brandTitle\\\":null},\\\"limits\\\":{\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200},\\\"settings\\\":{\\\"transferEnabled\\\":true}},\\\"gameVersion\\\":\\\"0.3.0\\\",\\\"gameContext\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"scenesState\\\":{\\\"main\\\":{\\\"multiplier\\\":1,\\\"behaviorsState\\\":{\\\"regularSlotSceneBehavior\\\":null}},\\\"freeSpins\\\":{\\\"multiplier\\\":3,\\\"behaviorsState\\\":{\\\"freeGamesSlotSceneBehavior\\\":{\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2}}}},\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"history\\\":[{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"scene\\\":\\\"main\\\",\\\"multiplier\\\":1,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"main\\\",\\\"positions\\\":[36,0,0,3,0],\\\"view\\\":[[13,6,4,4,7],[2,1,6,13,10],[5,2,13,2,2],[3,7,3,6,3]]},\\\"rewards\\\":[{\\\"reward\\\":\\\"scatter\\\",\\\"positions\\\":[[0,0],[1,3],[2,2]],\\\"payout\\\":2,\\\"paytable\\\":[6,2]}],\\\"events\\\":[{\\\"id\\\":\\\"freeSpinsStart\\\",\\\"amount\\\":12,\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[46,0,20,21,23],\\\"view\\\":[[1,4,2,1,1],[5,1,4,4,4],[2,9,6,8,2],[7,6,12,3,8]]},\\\"triggeredSceneId\\\":\\\"freeSpins\\\",\\\"triggerSymbols\\\":[[0,0],[1,3],[2,2]]}],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}],\\\"currentRoundWin\\\":2,\\\"nextScene\\\":\\\"\\\",\\\"previousProcessSceneResult\\\":{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0,\\\"totalWin\\\":0,\\\"scene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsWin\\\":0,\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[36,20,11,21,36],\\\"view\\\":[[2,8,9,1,4],[7,4,4,4,5],[8,1,5,8,9],[5,11,6,3,3]]},\\\"rewards\\\":[],\\\"events\\\":[],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}},\\\"settings\\\":{\\\"transferEnabled\\\":true,\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200,\\\"defaultCoin\\\":1,\\\"coins\\\":[1],\\\"currencyMultiplier\\\":100},\\\"roundId\\\":11980748,\\\"roundEnded\\\":false,\\\"round\\\":{\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"totalEvents\\\":3,\\\"balanceBefore\\\":50000,\\\"startedAt\\\":\\\"2017-10-11T12:30:58.244Z\\\",\\\"broken\\\":true},\\\"requestContext\\\":{\\\"ip\\\":\\\"::ffff:************\\\",\\\"language\\\":\\\"en\\\",\\\"country\\\":\\\"BY\\\"},\\\"createdAt\\\":\\\"2017-10-11T12:30:58.008Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:58.609Z\\\"}\"}",
            createdAt: new Date("2017-11-09 10:40:17.5"),
            updatedAt: new Date("2017-11-09 10:40:17.5"),
            expireAt: ts,
            broken: true,
            brokenPayment: true,
            requireLogout: true,
            requireTransferOut: false,
            requireCompletion: false,
            specialState: null,
            merchantSessionId: "merchant_session",
            retryAttempts: 1,
            policy: GameContextPersistencePolicy.SHORT_TERM
        });

        await getGameContextModel().create( {
            id: id3,
            brandId: gameContextId3.brandId,
            playerCode: gameContextId3.playerCode,
            gameCode: gameContextId3.gameCode,
            gameId: gameContextId3.gameCode,
            deviceId: gameContextId3.deviceId,
            roundId: "1",
            // tslint:disable-next-line:max-line-length
            data: "{\"dataVersion\":\"2\",\"currentSessionId\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOjMzOTMyOSwiaWQiOiJnYW1lczpjb250ZXh0OjI1OkFVVE9fUExBWUVSXzIxMjEyMDAxNzU6c3dfbXJtbmt5OndlYiIsImlhdCI6MTUwNzcyNTA1NywiaXNzIjoic2t5d2luZGdyb3VwIn0.syF_8A29OEHw4zMEW7ydXZl6xk-M1BRt30NtlBISplduFkaUm0dwBKBCyRSA_O04gExWWzb4VRNax05a89rnog\",\"requestId\":\"3\",\"historyCounter\":\"3\",\"version\":\"8\",\"state\":\"{\\\"gameData\\\":{\\\"gameTokenData\\\":{\\\"gameCode\\\":\\\"sw_mrmnky\\\",\\\"brandId\\\":25,\\\"playerCode\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"currency\\\":\\\"USD\\\",\\\"test\\\":true,\\\"transferEnabled\\\":true,\\\"iat\\\":1507725057,\\\"iss\\\":\\\"skywindgroup\\\",\\\"token\\\":\\\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJnYW1lQ29kZSI6InN3X21ybW5reSIsImJyYW5kSWQiOjI1LCJwbGF5ZXJDb2RlIjoiQVVUT19QTEFZRVJfMjEyMTIwMDE3NSIsImN1cnJlbmN5IjoiVVNEIiwidGVzdCI6dHJ1ZSwidHJhbnNmZXJFbmFibGVkIjp0cnVlLCJpYXQiOjE1MDc3MjUwNTcsImlzcyI6InNreXdpbmRncm91cCJ9.p_stFZuaxYFF3u6sNbWEiXr9ygeujPYN-dTHJR4gBxC_hfduscpP1LJ9h34r0Mx2awnAVHspn6fk3lvCGaOQlg\\\"},\\\"balance\\\":{\\\"main\\\":50000},\\\"player\\\":{\\\"code\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"id\\\":\\\"LB0n1dql\\\",\\\"status\\\":\\\"normal\\\",\\\"firstName\\\":\\\"AUTO_PLAYER_FIRSTNAME_2121200175\\\",\\\"lastName\\\":\\\"AUTO_PLAYER_LASTNAME_2121200175\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"currency\\\":\\\"USD\\\",\\\"country\\\":\\\"BY\\\",\\\"language\\\":\\\"en\\\",\\\"agentId\\\":null,\\\"isTest\\\":true,\\\"lastLogin\\\":\\\"2017-10-11T12:30:57.842Z\\\",\\\"customData\\\":null,\\\"createdAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"brandId\\\":\\\"GJqNWRkX\\\",\\\"brandTitle\\\":null},\\\"limits\\\":{\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200},\\\"settings\\\":{\\\"transferEnabled\\\":true}},\\\"gameVersion\\\":\\\"0.3.0\\\",\\\"gameContext\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"scenesState\\\":{\\\"main\\\":{\\\"multiplier\\\":1,\\\"behaviorsState\\\":{\\\"regularSlotSceneBehavior\\\":null}},\\\"freeSpins\\\":{\\\"multiplier\\\":3,\\\"behaviorsState\\\":{\\\"freeGamesSlotSceneBehavior\\\":{\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2}}}},\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"history\\\":[{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"scene\\\":\\\"main\\\",\\\"multiplier\\\":1,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"main\\\",\\\"positions\\\":[36,0,0,3,0],\\\"view\\\":[[13,6,4,4,7],[2,1,6,13,10],[5,2,13,2,2],[3,7,3,6,3]]},\\\"rewards\\\":[{\\\"reward\\\":\\\"scatter\\\",\\\"positions\\\":[[0,0],[1,3],[2,2]],\\\"payout\\\":2,\\\"paytable\\\":[6,2]}],\\\"events\\\":[{\\\"id\\\":\\\"freeSpinsStart\\\",\\\"amount\\\":12,\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[46,0,20,21,23],\\\"view\\\":[[1,4,2,1,1],[5,1,4,4,4],[2,9,6,8,2],[7,6,12,3,8]]},\\\"triggeredSceneId\\\":\\\"freeSpins\\\",\\\"triggerSymbols\\\":[[0,0],[1,3],[2,2]]}],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}],\\\"currentRoundWin\\\":2,\\\"nextScene\\\":\\\"\\\",\\\"previousProcessSceneResult\\\":{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0,\\\"totalWin\\\":0,\\\"scene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsWin\\\":0,\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[36,20,11,21,36],\\\"view\\\":[[2,8,9,1,4],[7,4,4,4,5],[8,1,5,8,9],[5,11,6,3,3]]},\\\"rewards\\\":[],\\\"events\\\":[],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}},\\\"settings\\\":{\\\"transferEnabled\\\":true,\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200,\\\"defaultCoin\\\":1,\\\"coins\\\":[1],\\\"currencyMultiplier\\\":100},\\\"roundId\\\":11980748,\\\"roundEnded\\\":false,\\\"round\\\":{\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"totalEvents\\\":3,\\\"balanceBefore\\\":50000,\\\"startedAt\\\":\\\"2017-10-11T12:30:58.244Z\\\",\\\"broken\\\":true},\\\"requestContext\\\":{\\\"ip\\\":\\\"::ffff:************\\\",\\\"language\\\":\\\"en\\\",\\\"country\\\":\\\"BY\\\"},\\\"createdAt\\\":\\\"2017-10-11T12:30:58.008Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:58.609Z\\\"}\"}",
            createdAt: new Date("2017-11-09 10:40:17.5"),
            updatedAt: new Date("2017-11-09 10:40:17.5"),
            expireAt: ts,
            broken: true,
            brokenPayment: true,
            requireLogout: true,
            requireTransferOut: false,
            requireCompletion: false,
            specialState: SpecialState.BROKEN_INTEGRATION,
            merchantSessionId: "merchant_session",
            retryAttempts: 1,
            policy: GameContextPersistencePolicy.NORMAL
        });

        await getGameContextModel().create( {
            id: id4,
            brandId: gameContextId3.brandId,
            playerCode: gameContextId4.playerCode,
            gameCode: gameContextId4.gameCode,
            gameId: gameContextId4.gameCode,
            deviceId: gameContextId4.deviceId,
            roundId: "1",
            // tslint:disable-next-line:max-line-length
            data: "{\"dataVersion\":\"2\",\"currentSessionId\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOjMzOTMyOSwiaWQiOiJnYW1lczpjb250ZXh0OjI1OkFVVE9fUExBWUVSXzIxMjEyMDAxNzU6c3dfbXJtbmt5OndlYiIsImlhdCI6MTUwNzcyNTA1NywiaXNzIjoic2t5d2luZGdyb3VwIn0.syF_8A29OEHw4zMEW7ydXZl6xk-M1BRt30NtlBISplduFkaUm0dwBKBCyRSA_O04gExWWzb4VRNax05a89rnog\",\"requestId\":\"3\",\"historyCounter\":\"3\",\"version\":\"8\",\"state\":\"{\\\"gameData\\\":{\\\"gameTokenData\\\":{\\\"gameCode\\\":\\\"sw_mrmnky\\\",\\\"brandId\\\":25,\\\"playerCode\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"currency\\\":\\\"USD\\\",\\\"test\\\":true,\\\"transferEnabled\\\":true,\\\"iat\\\":1507725057,\\\"iss\\\":\\\"skywindgroup\\\",\\\"token\\\":\\\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJnYW1lQ29kZSI6InN3X21ybW5reSIsImJyYW5kSWQiOjI1LCJwbGF5ZXJDb2RlIjoiQVVUT19QTEFZRVJfMjEyMTIwMDE3NSIsImN1cnJlbmN5IjoiVVNEIiwidGVzdCI6dHJ1ZSwidHJhbnNmZXJFbmFibGVkIjp0cnVlLCJpYXQiOjE1MDc3MjUwNTcsImlzcyI6InNreXdpbmRncm91cCJ9.p_stFZuaxYFF3u6sNbWEiXr9ygeujPYN-dTHJR4gBxC_hfduscpP1LJ9h34r0Mx2awnAVHspn6fk3lvCGaOQlg\\\"},\\\"balance\\\":{\\\"main\\\":50000},\\\"player\\\":{\\\"code\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"id\\\":\\\"LB0n1dql\\\",\\\"status\\\":\\\"normal\\\",\\\"firstName\\\":\\\"AUTO_PLAYER_FIRSTNAME_2121200175\\\",\\\"lastName\\\":\\\"AUTO_PLAYER_LASTNAME_2121200175\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"currency\\\":\\\"USD\\\",\\\"country\\\":\\\"BY\\\",\\\"language\\\":\\\"en\\\",\\\"agentId\\\":null,\\\"isTest\\\":true,\\\"lastLogin\\\":\\\"2017-10-11T12:30:57.842Z\\\",\\\"customData\\\":null,\\\"createdAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"brandId\\\":\\\"GJqNWRkX\\\",\\\"brandTitle\\\":null},\\\"limits\\\":{\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200},\\\"settings\\\":{\\\"transferEnabled\\\":true}},\\\"gameVersion\\\":\\\"0.3.0\\\",\\\"gameContext\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"scenesState\\\":{\\\"main\\\":{\\\"multiplier\\\":1,\\\"behaviorsState\\\":{\\\"regularSlotSceneBehavior\\\":null}},\\\"freeSpins\\\":{\\\"multiplier\\\":3,\\\"behaviorsState\\\":{\\\"freeGamesSlotSceneBehavior\\\":{\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2}}}},\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"history\\\":[{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"scene\\\":\\\"main\\\",\\\"multiplier\\\":1,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"main\\\",\\\"positions\\\":[36,0,0,3,0],\\\"view\\\":[[13,6,4,4,7],[2,1,6,13,10],[5,2,13,2,2],[3,7,3,6,3]]},\\\"rewards\\\":[{\\\"reward\\\":\\\"scatter\\\",\\\"positions\\\":[[0,0],[1,3],[2,2]],\\\"payout\\\":2,\\\"paytable\\\":[6,2]}],\\\"events\\\":[{\\\"id\\\":\\\"freeSpinsStart\\\",\\\"amount\\\":12,\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[46,0,20,21,23],\\\"view\\\":[[1,4,2,1,1],[5,1,4,4,4],[2,9,6,8,2],[7,6,12,3,8]]},\\\"triggeredSceneId\\\":\\\"freeSpins\\\",\\\"triggerSymbols\\\":[[0,0],[1,3],[2,2]]}],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}],\\\"currentRoundWin\\\":2,\\\"nextScene\\\":\\\"\\\",\\\"previousProcessSceneResult\\\":{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0,\\\"totalWin\\\":0,\\\"scene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsWin\\\":0,\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[36,20,11,21,36],\\\"view\\\":[[2,8,9,1,4],[7,4,4,4,5],[8,1,5,8,9],[5,11,6,3,3]]},\\\"rewards\\\":[],\\\"events\\\":[],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}},\\\"settings\\\":{\\\"transferEnabled\\\":true,\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200,\\\"defaultCoin\\\":1,\\\"coins\\\":[1],\\\"currencyMultiplier\\\":100},\\\"roundId\\\":11980748,\\\"roundEnded\\\":false,\\\"round\\\":{\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"totalEvents\\\":3,\\\"balanceBefore\\\":50000,\\\"startedAt\\\":\\\"2017-10-11T12:30:58.244Z\\\",\\\"broken\\\":true},\\\"requestContext\\\":{\\\"ip\\\":\\\"::ffff:************\\\",\\\"language\\\":\\\"en\\\",\\\"country\\\":\\\"BY\\\"},\\\"createdAt\\\":\\\"2017-10-11T12:30:58.008Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:58.609Z\\\"}\"}",
            createdAt: new Date("2017-11-09 10:40:17.5"),
            updatedAt: new Date("2017-11-09 10:40:17.5"),
            expireAt: ts,
            broken: true,
            brokenPayment: true,
            requireLogout: true,
            requireTransferOut: false,
            requireCompletion: false,
            specialState: SpecialState.BROKEN_GAME_CONTEXT,
            merchantSessionId: "merchant_session",
            retryAttempts: 1,
            policy: GameContextPersistencePolicy.NORMAL
        });

        await getGameContextModel().create( {
            id: id5,
            brandId: gameContextId5.brandId,
            playerCode: gameContextId5.playerCode,
            gameCode: gameContextId5.gameCode,
            gameId: gameContextId5.gameCode,
            deviceId: gameContextId5.deviceId,
            roundId: "1",
            // tslint:disable-next-line:max-line-length
            data: "{\"dataVersion\":\"2\",\"currentSessionId\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOjMzOTMyOSwiaWQiOiJnYW1lczpjb250ZXh0OjI1OkFVVE9fUExBWUVSXzIxMjEyMDAxNzU6c3dfbXJtbmt5OndlYiIsImlhdCI6MTUwNzcyNTA1NywiaXNzIjoic2t5d2luZGdyb3VwIn0.syF_8A29OEHw4zMEW7ydXZl6xk-M1BRt30NtlBISplduFkaUm0dwBKBCyRSA_O04gExWWzb4VRNax05a89rnog\",\"requestId\":\"3\",\"historyCounter\":\"3\",\"version\":\"8\",\"state\":\"{\\\"gameData\\\":{\\\"gameTokenData\\\":{\\\"gameCode\\\":\\\"sw_mrmnky\\\",\\\"brandId\\\":25,\\\"playerCode\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"currency\\\":\\\"USD\\\",\\\"test\\\":true,\\\"transferEnabled\\\":true,\\\"iat\\\":1507725057,\\\"iss\\\":\\\"skywindgroup\\\",\\\"token\\\":\\\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJnYW1lQ29kZSI6InN3X21ybW5reSIsImJyYW5kSWQiOjI1LCJwbGF5ZXJDb2RlIjoiQVVUT19QTEFZRVJfMjEyMTIwMDE3NSIsImN1cnJlbmN5IjoiVVNEIiwidGVzdCI6dHJ1ZSwidHJhbnNmZXJFbmFibGVkIjp0cnVlLCJpYXQiOjE1MDc3MjUwNTcsImlzcyI6InNreXdpbmRncm91cCJ9.p_stFZuaxYFF3u6sNbWEiXr9ygeujPYN-dTHJR4gBxC_hfduscpP1LJ9h34r0Mx2awnAVHspn6fk3lvCGaOQlg\\\"},\\\"balance\\\":{\\\"main\\\":50000},\\\"player\\\":{\\\"code\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"id\\\":\\\"LB0n1dql\\\",\\\"status\\\":\\\"normal\\\",\\\"firstName\\\":\\\"AUTO_PLAYER_FIRSTNAME_2121200175\\\",\\\"lastName\\\":\\\"AUTO_PLAYER_LASTNAME_2121200175\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"currency\\\":\\\"USD\\\",\\\"country\\\":\\\"BY\\\",\\\"language\\\":\\\"en\\\",\\\"agentId\\\":null,\\\"isTest\\\":true,\\\"lastLogin\\\":\\\"2017-10-11T12:30:57.842Z\\\",\\\"customData\\\":null,\\\"createdAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"brandId\\\":\\\"GJqNWRkX\\\",\\\"brandTitle\\\":null},\\\"limits\\\":{\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200},\\\"settings\\\":{\\\"transferEnabled\\\":true}},\\\"gameVersion\\\":\\\"0.3.0\\\",\\\"gameContext\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"scenesState\\\":{\\\"main\\\":{\\\"multiplier\\\":1,\\\"behaviorsState\\\":{\\\"regularSlotSceneBehavior\\\":null}},\\\"freeSpins\\\":{\\\"multiplier\\\":3,\\\"behaviorsState\\\":{\\\"freeGamesSlotSceneBehavior\\\":{\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2}}}},\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"history\\\":[{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"scene\\\":\\\"main\\\",\\\"multiplier\\\":1,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"main\\\",\\\"positions\\\":[36,0,0,3,0],\\\"view\\\":[[13,6,4,4,7],[2,1,6,13,10],[5,2,13,2,2],[3,7,3,6,3]]},\\\"rewards\\\":[{\\\"reward\\\":\\\"scatter\\\",\\\"positions\\\":[[0,0],[1,3],[2,2]],\\\"payout\\\":2,\\\"paytable\\\":[6,2]}],\\\"events\\\":[{\\\"id\\\":\\\"freeSpinsStart\\\",\\\"amount\\\":12,\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[46,0,20,21,23],\\\"view\\\":[[1,4,2,1,1],[5,1,4,4,4],[2,9,6,8,2],[7,6,12,3,8]]},\\\"triggeredSceneId\\\":\\\"freeSpins\\\",\\\"triggerSymbols\\\":[[0,0],[1,3],[2,2]]}],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}],\\\"currentRoundWin\\\":2,\\\"nextScene\\\":\\\"\\\",\\\"previousProcessSceneResult\\\":{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0,\\\"totalWin\\\":0,\\\"scene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsWin\\\":0,\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[36,20,11,21,36],\\\"view\\\":[[2,8,9,1,4],[7,4,4,4,5],[8,1,5,8,9],[5,11,6,3,3]]},\\\"rewards\\\":[],\\\"events\\\":[],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}},\\\"settings\\\":{\\\"transferEnabled\\\":true,\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200,\\\"defaultCoin\\\":1,\\\"coins\\\":[1],\\\"currencyMultiplier\\\":100},\\\"roundId\\\":11980748,\\\"roundEnded\\\":false,\\\"round\\\":{\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"totalEvents\\\":3,\\\"balanceBefore\\\":50000,\\\"startedAt\\\":\\\"2017-10-11T12:30:58.244Z\\\",\\\"broken\\\":true},\\\"requestContext\\\":{\\\"ip\\\":\\\"::ffff:************\\\",\\\"language\\\":\\\"en\\\",\\\"country\\\":\\\"BY\\\"},\\\"createdAt\\\":\\\"2017-10-11T12:30:58.008Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:58.609Z\\\"}\"}",
            createdAt: new Date("2017-11-09 10:40:17.5"),
            updatedAt: new Date("2017-11-09 10:40:17.5"),
            expireAt: ts,
            broken: true,
            brokenPayment: true,
            requireLogout: true,
            requireTransferOut: false,
            requireCompletion: false,
            specialState: SpecialState.CANNOT_COMPLETE_PAYMENT,
            merchantSessionId: "merchant_session",
            retryAttempts: 1,
            policy: GameContextPersistencePolicy.NORMAL
        });
        // For now set it to 1, as Long Term context is found and I'm note sure we want to set a special state for it
        expect((await getGameFlowContextManager().findExpiredGameContext(ts, 100)).length).equals(0);
    });

    it("finds expired - normal policy and specific games", async () => {
        const ts = new Date();
        expect(await await getGameFlowContextManager().findExpiredGameContext(ts, 1)).deep.equals([]);
        const id1 = "games:context:25:AUTO_PLAYER_2121200175:sw_cl_965:web";
        const id2 = "games:context:25:AUTO_PLAYER_2121200175:sw_cl_910:mobile";
        const id3 = "games:context:25:AUTO_PLAYER_2121200175_1:sw_cl_945:mobile";
        const id4 = "games:context:25:AUTO_PLAYER_2121200175_2:sw_hl_965:mobile";
        const id5 = "games:context:25:AUTO_PLAYER_2121200175_3:sw_hl_910:mobile";
        const id6 = "games:context:25:AUTO_PLAYER_2121200175_3:sw_hl_945:mobile";
        const id7 = "games:context:25:AUTO_PLAYER_2121200175_3:sw_mrmnky:mobile";
        const id8 = "games:context:25:AUTO_PLAYER_2121200175:sw_mrmnky:web";
        const gameContextId1 = GameContextID.createFromString(id1);
        const gameContextId2 = GameContextID.createFromString(id2);
        const gameContextId3 = GameContextID.createFromString(id3);
        const gameContextId4 = GameContextID.createFromString(id4);
        const gameContextId5 = GameContextID.createFromString(id5);
        const gameContextId6 = GameContextID.createFromString(id6);
        const gameContextId7 = GameContextID.createFromString(id7);
        const gameContextId8 = GameContextID.createFromString(id8);

        await getGameContextModel().create( {
            id: id1,
            brandId: gameContextId1.brandId,
            playerCode: gameContextId1.playerCode,
            gameCode: gameContextId1.gameCode,
            gameId: gameContextId1.gameCode,
            deviceId: gameContextId1.deviceId,
            roundId: "1",
            // tslint:disable-next-line:max-line-length
            data: "{\"dataVersion\":\"2\",\"currentSessionId\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOjMzOTMyOSwiaWQiOiJnYW1lczpjb250ZXh0OjI1OkFVVE9fUExBWUVSXzIxMjEyMDAxNzU6c3dfbXJtbmt5OndlYiIsImlhdCI6MTUwNzcyNTA1NywiaXNzIjoic2t5d2luZGdyb3VwIn0.syF_8A29OEHw4zMEW7ydXZl6xk-M1BRt30NtlBISplduFkaUm0dwBKBCyRSA_O04gExWWzb4VRNax05a89rnog\",\"requestId\":\"3\",\"historyCounter\":\"3\",\"version\":\"8\",\"state\":\"{\\\"gameData\\\":{\\\"gameTokenData\\\":{\\\"gameCode\\\":\\\"sw_mrmnky\\\",\\\"brandId\\\":25,\\\"playerCode\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"currency\\\":\\\"USD\\\",\\\"test\\\":true,\\\"transferEnabled\\\":true,\\\"iat\\\":1507725057,\\\"iss\\\":\\\"skywindgroup\\\",\\\"token\\\":\\\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJnYW1lQ29kZSI6InN3X21ybW5reSIsImJyYW5kSWQiOjI1LCJwbGF5ZXJDb2RlIjoiQVVUT19QTEFZRVJfMjEyMTIwMDE3NSIsImN1cnJlbmN5IjoiVVNEIiwidGVzdCI6dHJ1ZSwidHJhbnNmZXJFbmFibGVkIjp0cnVlLCJpYXQiOjE1MDc3MjUwNTcsImlzcyI6InNreXdpbmRncm91cCJ9.p_stFZuaxYFF3u6sNbWEiXr9ygeujPYN-dTHJR4gBxC_hfduscpP1LJ9h34r0Mx2awnAVHspn6fk3lvCGaOQlg\\\"},\\\"balance\\\":{\\\"main\\\":50000},\\\"player\\\":{\\\"code\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"id\\\":\\\"LB0n1dql\\\",\\\"status\\\":\\\"normal\\\",\\\"firstName\\\":\\\"AUTO_PLAYER_FIRSTNAME_2121200175\\\",\\\"lastName\\\":\\\"AUTO_PLAYER_LASTNAME_2121200175\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"currency\\\":\\\"USD\\\",\\\"country\\\":\\\"BY\\\",\\\"language\\\":\\\"en\\\",\\\"agentId\\\":null,\\\"isTest\\\":true,\\\"lastLogin\\\":\\\"2017-10-11T12:30:57.842Z\\\",\\\"customData\\\":null,\\\"createdAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"brandId\\\":\\\"GJqNWRkX\\\",\\\"brandTitle\\\":null},\\\"limits\\\":{\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200},\\\"settings\\\":{\\\"transferEnabled\\\":true}},\\\"gameVersion\\\":\\\"0.3.0\\\",\\\"gameContext\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"scenesState\\\":{\\\"main\\\":{\\\"multiplier\\\":1,\\\"behaviorsState\\\":{\\\"regularSlotSceneBehavior\\\":null}},\\\"freeSpins\\\":{\\\"multiplier\\\":3,\\\"behaviorsState\\\":{\\\"freeGamesSlotSceneBehavior\\\":{\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2}}}},\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"history\\\":[{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"scene\\\":\\\"main\\\",\\\"multiplier\\\":1,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"main\\\",\\\"positions\\\":[36,0,0,3,0],\\\"view\\\":[[13,6,4,4,7],[2,1,6,13,10],[5,2,13,2,2],[3,7,3,6,3]]},\\\"rewards\\\":[{\\\"reward\\\":\\\"scatter\\\",\\\"positions\\\":[[0,0],[1,3],[2,2]],\\\"payout\\\":2,\\\"paytable\\\":[6,2]}],\\\"events\\\":[{\\\"id\\\":\\\"freeSpinsStart\\\",\\\"amount\\\":12,\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[46,0,20,21,23],\\\"view\\\":[[1,4,2,1,1],[5,1,4,4,4],[2,9,6,8,2],[7,6,12,3,8]]},\\\"triggeredSceneId\\\":\\\"freeSpins\\\",\\\"triggerSymbols\\\":[[0,0],[1,3],[2,2]]}],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}],\\\"currentRoundWin\\\":2,\\\"nextScene\\\":\\\"\\\",\\\"previousProcessSceneResult\\\":{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0,\\\"totalWin\\\":0,\\\"scene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsWin\\\":0,\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[36,20,11,21,36],\\\"view\\\":[[2,8,9,1,4],[7,4,4,4,5],[8,1,5,8,9],[5,11,6,3,3]]},\\\"rewards\\\":[],\\\"events\\\":[],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}},\\\"settings\\\":{\\\"transferEnabled\\\":true,\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200,\\\"defaultCoin\\\":1,\\\"coins\\\":[1],\\\"currencyMultiplier\\\":100},\\\"roundId\\\":11980748,\\\"roundEnded\\\":false,\\\"round\\\":{\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"totalEvents\\\":3,\\\"balanceBefore\\\":50000,\\\"startedAt\\\":\\\"2017-10-11T12:30:58.244Z\\\",\\\"broken\\\":true},\\\"requestContext\\\":{\\\"ip\\\":\\\"::ffff:************\\\",\\\"language\\\":\\\"en\\\",\\\"country\\\":\\\"BY\\\"},\\\"createdAt\\\":\\\"2017-10-11T12:30:58.008Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:58.609Z\\\"}\"}",
            createdAt: new Date("2017-11-09 10:40:17.5"),
            updatedAt: new Date("2017-11-09 10:40:17.5"),
            expireAt: ts,
            broken: false,
            brokenPayment: false,
            requireLogout: false,
            requireTransferOut: false,
            requireCompletion: false,
            specialState: null,
            merchantSessionId: "merchant_session",
            retryAttempts: 1,
            policy: GameContextPersistencePolicy.LONG_TERM
        });

        await getGameContextModel().create( {
            id: id2,
            brandId: gameContextId2.brandId,
            playerCode: gameContextId2.playerCode,
            gameCode: gameContextId2.gameCode,
            gameId: gameContextId2.gameCode,
            deviceId: gameContextId2.deviceId,
            roundId: "1",
            // tslint:disable-next-line:max-line-length
            data: "{\"dataVersion\":\"2\",\"currentSessionId\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOjMzOTMyOSwiaWQiOiJnYW1lczpjb250ZXh0OjI1OkFVVE9fUExBWUVSXzIxMjEyMDAxNzU6c3dfbXJtbmt5OndlYiIsImlhdCI6MTUwNzcyNTA1NywiaXNzIjoic2t5d2luZGdyb3VwIn0.syF_8A29OEHw4zMEW7ydXZl6xk-M1BRt30NtlBISplduFkaUm0dwBKBCyRSA_O04gExWWzb4VRNax05a89rnog\",\"requestId\":\"3\",\"historyCounter\":\"3\",\"version\":\"8\",\"state\":\"{\\\"gameData\\\":{\\\"gameTokenData\\\":{\\\"gameCode\\\":\\\"sw_mrmnky\\\",\\\"brandId\\\":25,\\\"playerCode\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"currency\\\":\\\"USD\\\",\\\"test\\\":true,\\\"transferEnabled\\\":true,\\\"iat\\\":1507725057,\\\"iss\\\":\\\"skywindgroup\\\",\\\"token\\\":\\\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJnYW1lQ29kZSI6InN3X21ybW5reSIsImJyYW5kSWQiOjI1LCJwbGF5ZXJDb2RlIjoiQVVUT19QTEFZRVJfMjEyMTIwMDE3NSIsImN1cnJlbmN5IjoiVVNEIiwidGVzdCI6dHJ1ZSwidHJhbnNmZXJFbmFibGVkIjp0cnVlLCJpYXQiOjE1MDc3MjUwNTcsImlzcyI6InNreXdpbmRncm91cCJ9.p_stFZuaxYFF3u6sNbWEiXr9ygeujPYN-dTHJR4gBxC_hfduscpP1LJ9h34r0Mx2awnAVHspn6fk3lvCGaOQlg\\\"},\\\"balance\\\":{\\\"main\\\":50000},\\\"player\\\":{\\\"code\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"id\\\":\\\"LB0n1dql\\\",\\\"status\\\":\\\"normal\\\",\\\"firstName\\\":\\\"AUTO_PLAYER_FIRSTNAME_2121200175\\\",\\\"lastName\\\":\\\"AUTO_PLAYER_LASTNAME_2121200175\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"currency\\\":\\\"USD\\\",\\\"country\\\":\\\"BY\\\",\\\"language\\\":\\\"en\\\",\\\"agentId\\\":null,\\\"isTest\\\":true,\\\"lastLogin\\\":\\\"2017-10-11T12:30:57.842Z\\\",\\\"customData\\\":null,\\\"createdAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"brandId\\\":\\\"GJqNWRkX\\\",\\\"brandTitle\\\":null},\\\"limits\\\":{\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200},\\\"settings\\\":{\\\"transferEnabled\\\":true}},\\\"gameVersion\\\":\\\"0.3.0\\\",\\\"gameContext\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"scenesState\\\":{\\\"main\\\":{\\\"multiplier\\\":1,\\\"behaviorsState\\\":{\\\"regularSlotSceneBehavior\\\":null}},\\\"freeSpins\\\":{\\\"multiplier\\\":3,\\\"behaviorsState\\\":{\\\"freeGamesSlotSceneBehavior\\\":{\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2}}}},\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"history\\\":[{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"scene\\\":\\\"main\\\",\\\"multiplier\\\":1,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"main\\\",\\\"positions\\\":[36,0,0,3,0],\\\"view\\\":[[13,6,4,4,7],[2,1,6,13,10],[5,2,13,2,2],[3,7,3,6,3]]},\\\"rewards\\\":[{\\\"reward\\\":\\\"scatter\\\",\\\"positions\\\":[[0,0],[1,3],[2,2]],\\\"payout\\\":2,\\\"paytable\\\":[6,2]}],\\\"events\\\":[{\\\"id\\\":\\\"freeSpinsStart\\\",\\\"amount\\\":12,\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[46,0,20,21,23],\\\"view\\\":[[1,4,2,1,1],[5,1,4,4,4],[2,9,6,8,2],[7,6,12,3,8]]},\\\"triggeredSceneId\\\":\\\"freeSpins\\\",\\\"triggerSymbols\\\":[[0,0],[1,3],[2,2]]}],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}],\\\"currentRoundWin\\\":2,\\\"nextScene\\\":\\\"\\\",\\\"previousProcessSceneResult\\\":{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0,\\\"totalWin\\\":0,\\\"scene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsWin\\\":0,\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[36,20,11,21,36],\\\"view\\\":[[2,8,9,1,4],[7,4,4,4,5],[8,1,5,8,9],[5,11,6,3,3]]},\\\"rewards\\\":[],\\\"events\\\":[],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}},\\\"settings\\\":{\\\"transferEnabled\\\":true,\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200,\\\"defaultCoin\\\":1,\\\"coins\\\":[1],\\\"currencyMultiplier\\\":100},\\\"roundId\\\":11980748,\\\"roundEnded\\\":false,\\\"round\\\":{\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"totalEvents\\\":3,\\\"balanceBefore\\\":50000,\\\"startedAt\\\":\\\"2017-10-11T12:30:58.244Z\\\",\\\"broken\\\":true},\\\"requestContext\\\":{\\\"ip\\\":\\\"::ffff:************\\\",\\\"language\\\":\\\"en\\\",\\\"country\\\":\\\"BY\\\"},\\\"createdAt\\\":\\\"2017-10-11T12:30:58.008Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:58.609Z\\\"}\"}",
            createdAt: new Date("2017-11-09 10:40:17.5"),
            updatedAt: new Date("2017-11-09 10:40:17.5"),
            expireAt: ts,
            broken: false,
            brokenPayment: false,
            requireLogout: false,
            requireTransferOut: false,
            requireCompletion: false,
            specialState: null,
            merchantSessionId: "merchant_session",
            retryAttempts: 1,
            policy: GameContextPersistencePolicy.LONG_TERM
        });

        await getGameContextModel().create( {
            id: id3,
            brandId: gameContextId3.brandId,
            playerCode: gameContextId3.playerCode,
            gameCode: gameContextId3.gameCode,
            gameId: gameContextId3.gameCode,
            deviceId: gameContextId3.deviceId,
            roundId: "1",
            // tslint:disable-next-line:max-line-length
            data: "{\"dataVersion\":\"2\",\"currentSessionId\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOjMzOTMyOSwiaWQiOiJnYW1lczpjb250ZXh0OjI1OkFVVE9fUExBWUVSXzIxMjEyMDAxNzU6c3dfbXJtbmt5OndlYiIsImlhdCI6MTUwNzcyNTA1NywiaXNzIjoic2t5d2luZGdyb3VwIn0.syF_8A29OEHw4zMEW7ydXZl6xk-M1BRt30NtlBISplduFkaUm0dwBKBCyRSA_O04gExWWzb4VRNax05a89rnog\",\"requestId\":\"3\",\"historyCounter\":\"3\",\"version\":\"8\",\"state\":\"{\\\"gameData\\\":{\\\"gameTokenData\\\":{\\\"gameCode\\\":\\\"sw_mrmnky\\\",\\\"brandId\\\":25,\\\"playerCode\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"currency\\\":\\\"USD\\\",\\\"test\\\":true,\\\"transferEnabled\\\":true,\\\"iat\\\":1507725057,\\\"iss\\\":\\\"skywindgroup\\\",\\\"token\\\":\\\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJnYW1lQ29kZSI6InN3X21ybW5reSIsImJyYW5kSWQiOjI1LCJwbGF5ZXJDb2RlIjoiQVVUT19QTEFZRVJfMjEyMTIwMDE3NSIsImN1cnJlbmN5IjoiVVNEIiwidGVzdCI6dHJ1ZSwidHJhbnNmZXJFbmFibGVkIjp0cnVlLCJpYXQiOjE1MDc3MjUwNTcsImlzcyI6InNreXdpbmRncm91cCJ9.p_stFZuaxYFF3u6sNbWEiXr9ygeujPYN-dTHJR4gBxC_hfduscpP1LJ9h34r0Mx2awnAVHspn6fk3lvCGaOQlg\\\"},\\\"balance\\\":{\\\"main\\\":50000},\\\"player\\\":{\\\"code\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"id\\\":\\\"LB0n1dql\\\",\\\"status\\\":\\\"normal\\\",\\\"firstName\\\":\\\"AUTO_PLAYER_FIRSTNAME_2121200175\\\",\\\"lastName\\\":\\\"AUTO_PLAYER_LASTNAME_2121200175\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"currency\\\":\\\"USD\\\",\\\"country\\\":\\\"BY\\\",\\\"language\\\":\\\"en\\\",\\\"agentId\\\":null,\\\"isTest\\\":true,\\\"lastLogin\\\":\\\"2017-10-11T12:30:57.842Z\\\",\\\"customData\\\":null,\\\"createdAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"brandId\\\":\\\"GJqNWRkX\\\",\\\"brandTitle\\\":null},\\\"limits\\\":{\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200},\\\"settings\\\":{\\\"transferEnabled\\\":true}},\\\"gameVersion\\\":\\\"0.3.0\\\",\\\"gameContext\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"scenesState\\\":{\\\"main\\\":{\\\"multiplier\\\":1,\\\"behaviorsState\\\":{\\\"regularSlotSceneBehavior\\\":null}},\\\"freeSpins\\\":{\\\"multiplier\\\":3,\\\"behaviorsState\\\":{\\\"freeGamesSlotSceneBehavior\\\":{\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2}}}},\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"history\\\":[{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"scene\\\":\\\"main\\\",\\\"multiplier\\\":1,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"main\\\",\\\"positions\\\":[36,0,0,3,0],\\\"view\\\":[[13,6,4,4,7],[2,1,6,13,10],[5,2,13,2,2],[3,7,3,6,3]]},\\\"rewards\\\":[{\\\"reward\\\":\\\"scatter\\\",\\\"positions\\\":[[0,0],[1,3],[2,2]],\\\"payout\\\":2,\\\"paytable\\\":[6,2]}],\\\"events\\\":[{\\\"id\\\":\\\"freeSpinsStart\\\",\\\"amount\\\":12,\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[46,0,20,21,23],\\\"view\\\":[[1,4,2,1,1],[5,1,4,4,4],[2,9,6,8,2],[7,6,12,3,8]]},\\\"triggeredSceneId\\\":\\\"freeSpins\\\",\\\"triggerSymbols\\\":[[0,0],[1,3],[2,2]]}],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}],\\\"currentRoundWin\\\":2,\\\"nextScene\\\":\\\"\\\",\\\"previousProcessSceneResult\\\":{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0,\\\"totalWin\\\":0,\\\"scene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsWin\\\":0,\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[36,20,11,21,36],\\\"view\\\":[[2,8,9,1,4],[7,4,4,4,5],[8,1,5,8,9],[5,11,6,3,3]]},\\\"rewards\\\":[],\\\"events\\\":[],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}},\\\"settings\\\":{\\\"transferEnabled\\\":true,\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200,\\\"defaultCoin\\\":1,\\\"coins\\\":[1],\\\"currencyMultiplier\\\":100},\\\"roundId\\\":11980748,\\\"roundEnded\\\":false,\\\"round\\\":{\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"totalEvents\\\":3,\\\"balanceBefore\\\":50000,\\\"startedAt\\\":\\\"2017-10-11T12:30:58.244Z\\\",\\\"broken\\\":true},\\\"requestContext\\\":{\\\"ip\\\":\\\"::ffff:************\\\",\\\"language\\\":\\\"en\\\",\\\"country\\\":\\\"BY\\\"},\\\"createdAt\\\":\\\"2017-10-11T12:30:58.008Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:58.609Z\\\"}\"}",
            createdAt: new Date("2017-11-09 10:40:17.5"),
            updatedAt: new Date("2017-11-09 10:40:17.5"),
            expireAt: ts,
            broken: false,
            brokenPayment: false,
            requireLogout: false,
            requireTransferOut: false,
            requireCompletion: false,
            specialState: null,
            merchantSessionId: "merchant_session",
            retryAttempts: 1,
            policy: GameContextPersistencePolicy.LONG_TERM
        });

        await getGameContextModel().create( {
            id: id4,
            brandId: gameContextId3.brandId,
            playerCode: gameContextId4.playerCode,
            gameCode: gameContextId4.gameCode,
            gameId: gameContextId4.gameCode,
            deviceId: gameContextId4.deviceId,
            roundId: "1",
            // tslint:disable-next-line:max-line-length
            data: "{\"dataVersion\":\"2\",\"currentSessionId\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOjMzOTMyOSwiaWQiOiJnYW1lczpjb250ZXh0OjI1OkFVVE9fUExBWUVSXzIxMjEyMDAxNzU6c3dfbXJtbmt5OndlYiIsImlhdCI6MTUwNzcyNTA1NywiaXNzIjoic2t5d2luZGdyb3VwIn0.syF_8A29OEHw4zMEW7ydXZl6xk-M1BRt30NtlBISplduFkaUm0dwBKBCyRSA_O04gExWWzb4VRNax05a89rnog\",\"requestId\":\"3\",\"historyCounter\":\"3\",\"version\":\"8\",\"state\":\"{\\\"gameData\\\":{\\\"gameTokenData\\\":{\\\"gameCode\\\":\\\"sw_mrmnky\\\",\\\"brandId\\\":25,\\\"playerCode\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"currency\\\":\\\"USD\\\",\\\"test\\\":true,\\\"transferEnabled\\\":true,\\\"iat\\\":1507725057,\\\"iss\\\":\\\"skywindgroup\\\",\\\"token\\\":\\\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJnYW1lQ29kZSI6InN3X21ybW5reSIsImJyYW5kSWQiOjI1LCJwbGF5ZXJDb2RlIjoiQVVUT19QTEFZRVJfMjEyMTIwMDE3NSIsImN1cnJlbmN5IjoiVVNEIiwidGVzdCI6dHJ1ZSwidHJhbnNmZXJFbmFibGVkIjp0cnVlLCJpYXQiOjE1MDc3MjUwNTcsImlzcyI6InNreXdpbmRncm91cCJ9.p_stFZuaxYFF3u6sNbWEiXr9ygeujPYN-dTHJR4gBxC_hfduscpP1LJ9h34r0Mx2awnAVHspn6fk3lvCGaOQlg\\\"},\\\"balance\\\":{\\\"main\\\":50000},\\\"player\\\":{\\\"code\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"id\\\":\\\"LB0n1dql\\\",\\\"status\\\":\\\"normal\\\",\\\"firstName\\\":\\\"AUTO_PLAYER_FIRSTNAME_2121200175\\\",\\\"lastName\\\":\\\"AUTO_PLAYER_LASTNAME_2121200175\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"currency\\\":\\\"USD\\\",\\\"country\\\":\\\"BY\\\",\\\"language\\\":\\\"en\\\",\\\"agentId\\\":null,\\\"isTest\\\":true,\\\"lastLogin\\\":\\\"2017-10-11T12:30:57.842Z\\\",\\\"customData\\\":null,\\\"createdAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"brandId\\\":\\\"GJqNWRkX\\\",\\\"brandTitle\\\":null},\\\"limits\\\":{\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200},\\\"settings\\\":{\\\"transferEnabled\\\":true}},\\\"gameVersion\\\":\\\"0.3.0\\\",\\\"gameContext\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"scenesState\\\":{\\\"main\\\":{\\\"multiplier\\\":1,\\\"behaviorsState\\\":{\\\"regularSlotSceneBehavior\\\":null}},\\\"freeSpins\\\":{\\\"multiplier\\\":3,\\\"behaviorsState\\\":{\\\"freeGamesSlotSceneBehavior\\\":{\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2}}}},\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"history\\\":[{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"scene\\\":\\\"main\\\",\\\"multiplier\\\":1,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"main\\\",\\\"positions\\\":[36,0,0,3,0],\\\"view\\\":[[13,6,4,4,7],[2,1,6,13,10],[5,2,13,2,2],[3,7,3,6,3]]},\\\"rewards\\\":[{\\\"reward\\\":\\\"scatter\\\",\\\"positions\\\":[[0,0],[1,3],[2,2]],\\\"payout\\\":2,\\\"paytable\\\":[6,2]}],\\\"events\\\":[{\\\"id\\\":\\\"freeSpinsStart\\\",\\\"amount\\\":12,\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[46,0,20,21,23],\\\"view\\\":[[1,4,2,1,1],[5,1,4,4,4],[2,9,6,8,2],[7,6,12,3,8]]},\\\"triggeredSceneId\\\":\\\"freeSpins\\\",\\\"triggerSymbols\\\":[[0,0],[1,3],[2,2]]}],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}],\\\"currentRoundWin\\\":2,\\\"nextScene\\\":\\\"\\\",\\\"previousProcessSceneResult\\\":{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0,\\\"totalWin\\\":0,\\\"scene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsWin\\\":0,\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[36,20,11,21,36],\\\"view\\\":[[2,8,9,1,4],[7,4,4,4,5],[8,1,5,8,9],[5,11,6,3,3]]},\\\"rewards\\\":[],\\\"events\\\":[],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}},\\\"settings\\\":{\\\"transferEnabled\\\":true,\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200,\\\"defaultCoin\\\":1,\\\"coins\\\":[1],\\\"currencyMultiplier\\\":100},\\\"roundId\\\":11980748,\\\"roundEnded\\\":false,\\\"round\\\":{\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"totalEvents\\\":3,\\\"balanceBefore\\\":50000,\\\"startedAt\\\":\\\"2017-10-11T12:30:58.244Z\\\",\\\"broken\\\":true},\\\"requestContext\\\":{\\\"ip\\\":\\\"::ffff:************\\\",\\\"language\\\":\\\"en\\\",\\\"country\\\":\\\"BY\\\"},\\\"createdAt\\\":\\\"2017-10-11T12:30:58.008Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:58.609Z\\\"}\"}",
            createdAt: new Date("2017-11-09 10:40:17.5"),
            updatedAt: new Date("2017-11-09 10:40:17.5"),
            expireAt: ts,
            broken: false,
            brokenPayment: false,
            requireLogout: false,
            requireTransferOut: false,
            requireCompletion: false,
            specialState: null,
            merchantSessionId: "merchant_session",
            retryAttempts: 1,
            policy: GameContextPersistencePolicy.LONG_TERM
        });

        await getGameContextModel().create( {
            id: id5,
            brandId: gameContextId5.brandId,
            playerCode: gameContextId5.playerCode,
            gameCode: gameContextId5.gameCode,
            gameId: gameContextId5.gameCode,
            deviceId: gameContextId5.deviceId,
            roundId: "1",
            // tslint:disable-next-line:max-line-length
            data: "{\"dataVersion\":\"2\",\"currentSessionId\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOjMzOTMyOSwiaWQiOiJnYW1lczpjb250ZXh0OjI1OkFVVE9fUExBWUVSXzIxMjEyMDAxNzU6c3dfbXJtbmt5OndlYiIsImlhdCI6MTUwNzcyNTA1NywiaXNzIjoic2t5d2luZGdyb3VwIn0.syF_8A29OEHw4zMEW7ydXZl6xk-M1BRt30NtlBISplduFkaUm0dwBKBCyRSA_O04gExWWzb4VRNax05a89rnog\",\"requestId\":\"3\",\"historyCounter\":\"3\",\"version\":\"8\",\"state\":\"{\\\"gameData\\\":{\\\"gameTokenData\\\":{\\\"gameCode\\\":\\\"sw_mrmnky\\\",\\\"brandId\\\":25,\\\"playerCode\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"currency\\\":\\\"USD\\\",\\\"test\\\":true,\\\"transferEnabled\\\":true,\\\"iat\\\":1507725057,\\\"iss\\\":\\\"skywindgroup\\\",\\\"token\\\":\\\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJnYW1lQ29kZSI6InN3X21ybW5reSIsImJyYW5kSWQiOjI1LCJwbGF5ZXJDb2RlIjoiQVVUT19QTEFZRVJfMjEyMTIwMDE3NSIsImN1cnJlbmN5IjoiVVNEIiwidGVzdCI6dHJ1ZSwidHJhbnNmZXJFbmFibGVkIjp0cnVlLCJpYXQiOjE1MDc3MjUwNTcsImlzcyI6InNreXdpbmRncm91cCJ9.p_stFZuaxYFF3u6sNbWEiXr9ygeujPYN-dTHJR4gBxC_hfduscpP1LJ9h34r0Mx2awnAVHspn6fk3lvCGaOQlg\\\"},\\\"balance\\\":{\\\"main\\\":50000},\\\"player\\\":{\\\"code\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"id\\\":\\\"LB0n1dql\\\",\\\"status\\\":\\\"normal\\\",\\\"firstName\\\":\\\"AUTO_PLAYER_FIRSTNAME_2121200175\\\",\\\"lastName\\\":\\\"AUTO_PLAYER_LASTNAME_2121200175\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"currency\\\":\\\"USD\\\",\\\"country\\\":\\\"BY\\\",\\\"language\\\":\\\"en\\\",\\\"agentId\\\":null,\\\"isTest\\\":true,\\\"lastLogin\\\":\\\"2017-10-11T12:30:57.842Z\\\",\\\"customData\\\":null,\\\"createdAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"brandId\\\":\\\"GJqNWRkX\\\",\\\"brandTitle\\\":null},\\\"limits\\\":{\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200},\\\"settings\\\":{\\\"transferEnabled\\\":true}},\\\"gameVersion\\\":\\\"0.3.0\\\",\\\"gameContext\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"scenesState\\\":{\\\"main\\\":{\\\"multiplier\\\":1,\\\"behaviorsState\\\":{\\\"regularSlotSceneBehavior\\\":null}},\\\"freeSpins\\\":{\\\"multiplier\\\":3,\\\"behaviorsState\\\":{\\\"freeGamesSlotSceneBehavior\\\":{\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2}}}},\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"history\\\":[{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"scene\\\":\\\"main\\\",\\\"multiplier\\\":1,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"main\\\",\\\"positions\\\":[36,0,0,3,0],\\\"view\\\":[[13,6,4,4,7],[2,1,6,13,10],[5,2,13,2,2],[3,7,3,6,3]]},\\\"rewards\\\":[{\\\"reward\\\":\\\"scatter\\\",\\\"positions\\\":[[0,0],[1,3],[2,2]],\\\"payout\\\":2,\\\"paytable\\\":[6,2]}],\\\"events\\\":[{\\\"id\\\":\\\"freeSpinsStart\\\",\\\"amount\\\":12,\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[46,0,20,21,23],\\\"view\\\":[[1,4,2,1,1],[5,1,4,4,4],[2,9,6,8,2],[7,6,12,3,8]]},\\\"triggeredSceneId\\\":\\\"freeSpins\\\",\\\"triggerSymbols\\\":[[0,0],[1,3],[2,2]]}],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}],\\\"currentRoundWin\\\":2,\\\"nextScene\\\":\\\"\\\",\\\"previousProcessSceneResult\\\":{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0,\\\"totalWin\\\":0,\\\"scene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsWin\\\":0,\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[36,20,11,21,36],\\\"view\\\":[[2,8,9,1,4],[7,4,4,4,5],[8,1,5,8,9],[5,11,6,3,3]]},\\\"rewards\\\":[],\\\"events\\\":[],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}},\\\"settings\\\":{\\\"transferEnabled\\\":true,\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200,\\\"defaultCoin\\\":1,\\\"coins\\\":[1],\\\"currencyMultiplier\\\":100},\\\"roundId\\\":11980748,\\\"roundEnded\\\":false,\\\"round\\\":{\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"totalEvents\\\":3,\\\"balanceBefore\\\":50000,\\\"startedAt\\\":\\\"2017-10-11T12:30:58.244Z\\\",\\\"broken\\\":true},\\\"requestContext\\\":{\\\"ip\\\":\\\"::ffff:************\\\",\\\"language\\\":\\\"en\\\",\\\"country\\\":\\\"BY\\\"},\\\"createdAt\\\":\\\"2017-10-11T12:30:58.008Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:58.609Z\\\"}\"}",
            createdAt: new Date("2017-11-09 10:40:17.5"),
            updatedAt: new Date("2017-11-09 10:40:17.5"),
            expireAt: ts,
            broken: false,
            brokenPayment: false,
            requireLogout: false,
            requireTransferOut: false,
            requireCompletion: false,
            specialState: null,
            merchantSessionId: "merchant_session",
            retryAttempts: 1,
            policy: GameContextPersistencePolicy.LONG_TERM
        });

        await getGameContextModel().create( {
            id: id6,
            brandId: gameContextId6.brandId,
            playerCode: gameContextId6.playerCode,
            gameCode: gameContextId6.gameCode,
            gameId: gameContextId6.gameCode,
            deviceId: gameContextId6.deviceId,
            roundId: "1",
            // tslint:disable-next-line:max-line-length
            data: "{\"dataVersion\":\"2\",\"currentSessionId\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOjMzOTMyOSwiaWQiOiJnYW1lczpjb250ZXh0OjI1OkFVVE9fUExBWUVSXzIxMjEyMDAxNzU6c3dfbXJtbmt5OndlYiIsImlhdCI6MTUwNzcyNTA1NywiaXNzIjoic2t5d2luZGdyb3VwIn0.syF_8A29OEHw4zMEW7ydXZl6xk-M1BRt30NtlBISplduFkaUm0dwBKBCyRSA_O04gExWWzb4VRNax05a89rnog\",\"requestId\":\"3\",\"historyCounter\":\"3\",\"version\":\"8\",\"state\":\"{\\\"gameData\\\":{\\\"gameTokenData\\\":{\\\"gameCode\\\":\\\"sw_mrmnky\\\",\\\"brandId\\\":25,\\\"playerCode\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"currency\\\":\\\"USD\\\",\\\"test\\\":true,\\\"transferEnabled\\\":true,\\\"iat\\\":1507725057,\\\"iss\\\":\\\"skywindgroup\\\",\\\"token\\\":\\\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJnYW1lQ29kZSI6InN3X21ybW5reSIsImJyYW5kSWQiOjI1LCJwbGF5ZXJDb2RlIjoiQVVUT19QTEFZRVJfMjEyMTIwMDE3NSIsImN1cnJlbmN5IjoiVVNEIiwidGVzdCI6dHJ1ZSwidHJhbnNmZXJFbmFibGVkIjp0cnVlLCJpYXQiOjE1MDc3MjUwNTcsImlzcyI6InNreXdpbmRncm91cCJ9.p_stFZuaxYFF3u6sNbWEiXr9ygeujPYN-dTHJR4gBxC_hfduscpP1LJ9h34r0Mx2awnAVHspn6fk3lvCGaOQlg\\\"},\\\"balance\\\":{\\\"main\\\":50000},\\\"player\\\":{\\\"code\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"id\\\":\\\"LB0n1dql\\\",\\\"status\\\":\\\"normal\\\",\\\"firstName\\\":\\\"AUTO_PLAYER_FIRSTNAME_2121200175\\\",\\\"lastName\\\":\\\"AUTO_PLAYER_LASTNAME_2121200175\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"currency\\\":\\\"USD\\\",\\\"country\\\":\\\"BY\\\",\\\"language\\\":\\\"en\\\",\\\"agentId\\\":null,\\\"isTest\\\":true,\\\"lastLogin\\\":\\\"2017-10-11T12:30:57.842Z\\\",\\\"customData\\\":null,\\\"createdAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"brandId\\\":\\\"GJqNWRkX\\\",\\\"brandTitle\\\":null},\\\"limits\\\":{\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200},\\\"settings\\\":{\\\"transferEnabled\\\":true}},\\\"gameVersion\\\":\\\"0.3.0\\\",\\\"gameContext\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"scenesState\\\":{\\\"main\\\":{\\\"multiplier\\\":1,\\\"behaviorsState\\\":{\\\"regularSlotSceneBehavior\\\":null}},\\\"freeSpins\\\":{\\\"multiplier\\\":3,\\\"behaviorsState\\\":{\\\"freeGamesSlotSceneBehavior\\\":{\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2}}}},\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"history\\\":[{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"scene\\\":\\\"main\\\",\\\"multiplier\\\":1,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"main\\\",\\\"positions\\\":[36,0,0,3,0],\\\"view\\\":[[13,6,4,4,7],[2,1,6,13,10],[5,2,13,2,2],[3,7,3,6,3]]},\\\"rewards\\\":[{\\\"reward\\\":\\\"scatter\\\",\\\"positions\\\":[[0,0],[1,3],[2,2]],\\\"payout\\\":2,\\\"paytable\\\":[6,2]}],\\\"events\\\":[{\\\"id\\\":\\\"freeSpinsStart\\\",\\\"amount\\\":12,\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[46,0,20,21,23],\\\"view\\\":[[1,4,2,1,1],[5,1,4,4,4],[2,9,6,8,2],[7,6,12,3,8]]},\\\"triggeredSceneId\\\":\\\"freeSpins\\\",\\\"triggerSymbols\\\":[[0,0],[1,3],[2,2]]}],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}],\\\"currentRoundWin\\\":2,\\\"nextScene\\\":\\\"\\\",\\\"previousProcessSceneResult\\\":{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0,\\\"totalWin\\\":0,\\\"scene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsWin\\\":0,\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[36,20,11,21,36],\\\"view\\\":[[2,8,9,1,4],[7,4,4,4,5],[8,1,5,8,9],[5,11,6,3,3]]},\\\"rewards\\\":[],\\\"events\\\":[],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}},\\\"settings\\\":{\\\"transferEnabled\\\":true,\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200,\\\"defaultCoin\\\":1,\\\"coins\\\":[1],\\\"currencyMultiplier\\\":100},\\\"roundId\\\":11980748,\\\"roundEnded\\\":false,\\\"round\\\":{\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"totalEvents\\\":3,\\\"balanceBefore\\\":50000,\\\"startedAt\\\":\\\"2017-10-11T12:30:58.244Z\\\",\\\"broken\\\":true},\\\"requestContext\\\":{\\\"ip\\\":\\\"::ffff:************\\\",\\\"language\\\":\\\"en\\\",\\\"country\\\":\\\"BY\\\"},\\\"createdAt\\\":\\\"2017-10-11T12:30:58.008Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:58.609Z\\\"}\"}",
            createdAt: new Date("2017-11-09 10:40:17.5"),
            updatedAt: new Date("2017-11-09 10:40:17.5"),
            expireAt: ts,
            broken: false,
            brokenPayment: false,
            requireLogout: false,
            requireTransferOut: false,
            requireCompletion: false,
            specialState: null,
            merchantSessionId: "merchant_session",
            retryAttempts: 1,
            policy: GameContextPersistencePolicy.LONG_TERM
        });

        await getGameContextModel().create( {
            id: id7,
            brandId: gameContextId7.brandId,
            playerCode: gameContextId7.playerCode,
            gameCode: gameContextId7.gameCode,
            gameId: gameContextId7.gameCode,
            deviceId: gameContextId7.deviceId,
            roundId: "1",
            // tslint:disable-next-line:max-line-length
            data: "{\"dataVersion\":\"2\",\"currentSessionId\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOjMzOTMyOSwiaWQiOiJnYW1lczpjb250ZXh0OjI1OkFVVE9fUExBWUVSXzIxMjEyMDAxNzU6c3dfbXJtbmt5OndlYiIsImlhdCI6MTUwNzcyNTA1NywiaXNzIjoic2t5d2luZGdyb3VwIn0.syF_8A29OEHw4zMEW7ydXZl6xk-M1BRt30NtlBISplduFkaUm0dwBKBCyRSA_O04gExWWzb4VRNax05a89rnog\",\"requestId\":\"3\",\"historyCounter\":\"3\",\"version\":\"8\",\"state\":\"{\\\"gameData\\\":{\\\"gameTokenData\\\":{\\\"gameCode\\\":\\\"sw_mrmnky\\\",\\\"brandId\\\":25,\\\"playerCode\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"currency\\\":\\\"USD\\\",\\\"test\\\":true,\\\"transferEnabled\\\":true,\\\"iat\\\":1507725057,\\\"iss\\\":\\\"skywindgroup\\\",\\\"token\\\":\\\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJnYW1lQ29kZSI6InN3X21ybW5reSIsImJyYW5kSWQiOjI1LCJwbGF5ZXJDb2RlIjoiQVVUT19QTEFZRVJfMjEyMTIwMDE3NSIsImN1cnJlbmN5IjoiVVNEIiwidGVzdCI6dHJ1ZSwidHJhbnNmZXJFbmFibGVkIjp0cnVlLCJpYXQiOjE1MDc3MjUwNTcsImlzcyI6InNreXdpbmRncm91cCJ9.p_stFZuaxYFF3u6sNbWEiXr9ygeujPYN-dTHJR4gBxC_hfduscpP1LJ9h34r0Mx2awnAVHspn6fk3lvCGaOQlg\\\"},\\\"balance\\\":{\\\"main\\\":50000},\\\"player\\\":{\\\"code\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"id\\\":\\\"LB0n1dql\\\",\\\"status\\\":\\\"normal\\\",\\\"firstName\\\":\\\"AUTO_PLAYER_FIRSTNAME_2121200175\\\",\\\"lastName\\\":\\\"AUTO_PLAYER_LASTNAME_2121200175\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"currency\\\":\\\"USD\\\",\\\"country\\\":\\\"BY\\\",\\\"language\\\":\\\"en\\\",\\\"agentId\\\":null,\\\"isTest\\\":true,\\\"lastLogin\\\":\\\"2017-10-11T12:30:57.842Z\\\",\\\"customData\\\":null,\\\"createdAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"brandId\\\":\\\"GJqNWRkX\\\",\\\"brandTitle\\\":null},\\\"limits\\\":{\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200},\\\"settings\\\":{\\\"transferEnabled\\\":true}},\\\"gameVersion\\\":\\\"0.3.0\\\",\\\"gameContext\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"scenesState\\\":{\\\"main\\\":{\\\"multiplier\\\":1,\\\"behaviorsState\\\":{\\\"regularSlotSceneBehavior\\\":null}},\\\"freeSpins\\\":{\\\"multiplier\\\":3,\\\"behaviorsState\\\":{\\\"freeGamesSlotSceneBehavior\\\":{\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2}}}},\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"history\\\":[{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"scene\\\":\\\"main\\\",\\\"multiplier\\\":1,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"main\\\",\\\"positions\\\":[36,0,0,3,0],\\\"view\\\":[[13,6,4,4,7],[2,1,6,13,10],[5,2,13,2,2],[3,7,3,6,3]]},\\\"rewards\\\":[{\\\"reward\\\":\\\"scatter\\\",\\\"positions\\\":[[0,0],[1,3],[2,2]],\\\"payout\\\":2,\\\"paytable\\\":[6,2]}],\\\"events\\\":[{\\\"id\\\":\\\"freeSpinsStart\\\",\\\"amount\\\":12,\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[46,0,20,21,23],\\\"view\\\":[[1,4,2,1,1],[5,1,4,4,4],[2,9,6,8,2],[7,6,12,3,8]]},\\\"triggeredSceneId\\\":\\\"freeSpins\\\",\\\"triggerSymbols\\\":[[0,0],[1,3],[2,2]]}],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}],\\\"currentRoundWin\\\":2,\\\"nextScene\\\":\\\"\\\",\\\"previousProcessSceneResult\\\":{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0,\\\"totalWin\\\":0,\\\"scene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsWin\\\":0,\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[36,20,11,21,36],\\\"view\\\":[[2,8,9,1,4],[7,4,4,4,5],[8,1,5,8,9],[5,11,6,3,3]]},\\\"rewards\\\":[],\\\"events\\\":[],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}},\\\"settings\\\":{\\\"transferEnabled\\\":true,\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200,\\\"defaultCoin\\\":1,\\\"coins\\\":[1],\\\"currencyMultiplier\\\":100},\\\"roundId\\\":11980748,\\\"roundEnded\\\":false,\\\"round\\\":{\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"totalEvents\\\":3,\\\"balanceBefore\\\":50000,\\\"startedAt\\\":\\\"2017-10-11T12:30:58.244Z\\\",\\\"broken\\\":true},\\\"requestContext\\\":{\\\"ip\\\":\\\"::ffff:************\\\",\\\"language\\\":\\\"en\\\",\\\"country\\\":\\\"BY\\\"},\\\"createdAt\\\":\\\"2017-10-11T12:30:58.008Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:58.609Z\\\"}\"}",
            createdAt: new Date("2017-11-09 10:40:17.5"),
            updatedAt: new Date("2017-11-09 10:40:17.5"),
            expireAt: ts,
            broken: false,
            brokenPayment: false,
            requireLogout: false,
            requireTransferOut: false,
            requireCompletion: false,
            specialState: null,
            merchantSessionId: "merchant_session",
            retryAttempts: 1,
            policy: GameContextPersistencePolicy.NORMAL
        });

        await getGameContextModel().create( {
            id: id8,
            brandId: gameContextId8.brandId,
            playerCode: gameContextId8.playerCode,
            gameCode: gameContextId8.gameCode,
            gameId: gameContextId8.gameCode,
            deviceId: gameContextId8.deviceId,
            roundId: "1",
            // tslint:disable-next-line:max-line-length
            data: "{\"dataVersion\":\"2\",\"currentSessionId\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOjMzOTMyOSwiaWQiOiJnYW1lczpjb250ZXh0OjI1OkFVVE9fUExBWUVSXzIxMjEyMDAxNzU6c3dfbXJtbmt5OndlYiIsImlhdCI6MTUwNzcyNTA1NywiaXNzIjoic2t5d2luZGdyb3VwIn0.syF_8A29OEHw4zMEW7ydXZl6xk-M1BRt30NtlBISplduFkaUm0dwBKBCyRSA_O04gExWWzb4VRNax05a89rnog\",\"requestId\":\"3\",\"historyCounter\":\"3\",\"version\":\"8\",\"state\":\"{\\\"gameData\\\":{\\\"gameTokenData\\\":{\\\"gameCode\\\":\\\"sw_mrmnky\\\",\\\"brandId\\\":25,\\\"playerCode\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"currency\\\":\\\"USD\\\",\\\"test\\\":true,\\\"transferEnabled\\\":true,\\\"iat\\\":1507725057,\\\"iss\\\":\\\"skywindgroup\\\",\\\"token\\\":\\\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJnYW1lQ29kZSI6InN3X21ybW5reSIsImJyYW5kSWQiOjI1LCJwbGF5ZXJDb2RlIjoiQVVUT19QTEFZRVJfMjEyMTIwMDE3NSIsImN1cnJlbmN5IjoiVVNEIiwidGVzdCI6dHJ1ZSwidHJhbnNmZXJFbmFibGVkIjp0cnVlLCJpYXQiOjE1MDc3MjUwNTcsImlzcyI6InNreXdpbmRncm91cCJ9.p_stFZuaxYFF3u6sNbWEiXr9ygeujPYN-dTHJR4gBxC_hfduscpP1LJ9h34r0Mx2awnAVHspn6fk3lvCGaOQlg\\\"},\\\"balance\\\":{\\\"main\\\":50000},\\\"player\\\":{\\\"code\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"id\\\":\\\"LB0n1dql\\\",\\\"status\\\":\\\"normal\\\",\\\"firstName\\\":\\\"AUTO_PLAYER_FIRSTNAME_2121200175\\\",\\\"lastName\\\":\\\"AUTO_PLAYER_LASTNAME_2121200175\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"currency\\\":\\\"USD\\\",\\\"country\\\":\\\"BY\\\",\\\"language\\\":\\\"en\\\",\\\"agentId\\\":null,\\\"isTest\\\":true,\\\"lastLogin\\\":\\\"2017-10-11T12:30:57.842Z\\\",\\\"customData\\\":null,\\\"createdAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"brandId\\\":\\\"GJqNWRkX\\\",\\\"brandTitle\\\":null},\\\"limits\\\":{\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200},\\\"settings\\\":{\\\"transferEnabled\\\":true}},\\\"gameVersion\\\":\\\"0.3.0\\\",\\\"gameContext\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"scenesState\\\":{\\\"main\\\":{\\\"multiplier\\\":1,\\\"behaviorsState\\\":{\\\"regularSlotSceneBehavior\\\":null}},\\\"freeSpins\\\":{\\\"multiplier\\\":3,\\\"behaviorsState\\\":{\\\"freeGamesSlotSceneBehavior\\\":{\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2}}}},\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"history\\\":[{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"scene\\\":\\\"main\\\",\\\"multiplier\\\":1,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"main\\\",\\\"positions\\\":[36,0,0,3,0],\\\"view\\\":[[13,6,4,4,7],[2,1,6,13,10],[5,2,13,2,2],[3,7,3,6,3]]},\\\"rewards\\\":[{\\\"reward\\\":\\\"scatter\\\",\\\"positions\\\":[[0,0],[1,3],[2,2]],\\\"payout\\\":2,\\\"paytable\\\":[6,2]}],\\\"events\\\":[{\\\"id\\\":\\\"freeSpinsStart\\\",\\\"amount\\\":12,\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[46,0,20,21,23],\\\"view\\\":[[1,4,2,1,1],[5,1,4,4,4],[2,9,6,8,2],[7,6,12,3,8]]},\\\"triggeredSceneId\\\":\\\"freeSpins\\\",\\\"triggerSymbols\\\":[[0,0],[1,3],[2,2]]}],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}],\\\"currentRoundWin\\\":2,\\\"nextScene\\\":\\\"\\\",\\\"previousProcessSceneResult\\\":{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0,\\\"totalWin\\\":0,\\\"scene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsWin\\\":0,\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[36,20,11,21,36],\\\"view\\\":[[2,8,9,1,4],[7,4,4,4,5],[8,1,5,8,9],[5,11,6,3,3]]},\\\"rewards\\\":[],\\\"events\\\":[],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}},\\\"settings\\\":{\\\"transferEnabled\\\":true,\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200,\\\"defaultCoin\\\":1,\\\"coins\\\":[1],\\\"currencyMultiplier\\\":100},\\\"roundId\\\":11980748,\\\"roundEnded\\\":false,\\\"round\\\":{\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"totalEvents\\\":3,\\\"balanceBefore\\\":50000,\\\"startedAt\\\":\\\"2017-10-11T12:30:58.244Z\\\",\\\"broken\\\":true},\\\"requestContext\\\":{\\\"ip\\\":\\\"::ffff:************\\\",\\\"language\\\":\\\"en\\\",\\\"country\\\":\\\"BY\\\"},\\\"createdAt\\\":\\\"2017-10-11T12:30:58.008Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:58.609Z\\\"}\"}",
            createdAt: new Date("2017-11-09 10:40:17.5"),
            updatedAt: new Date("2017-11-09 10:40:17.5"),
            expireAt: ts,
            broken: false,
            brokenPayment: false,
            requireLogout: false,
            requireTransferOut: false,
            requireCompletion: false,
            specialState: null,
            merchantSessionId: "merchant_session",
            retryAttempts: 1,
            policy: GameContextPersistencePolicy.LONG_TERM
        });
        const ltContexts = await getGameFlowContextManager().findExpiredGameContext(ts, 100);
        expect(ltContexts.filter(e => e.expireAt == null).length).equals(0);
        expect(ltContexts.filter(e => e.expireAt != null).length).equals(7);
        expect(ltContexts.length).equals(7);
    });

    it("old format offline storage", async () => {
        const id = "games:context:25:AUTO_PLAYER_2121200175:sw_mrmnky:web";
        const gameContextId = GameContextID.createFromString(id);
        await getGameContextModel().create({
            id,
            brandId: gameContextId.brandId,
            playerCode: gameContextId.playerCode,
            gameCode: gameContextId.gameCode,
            gameId: gameContextId.gameCode,
            deviceId: gameContextId.deviceId,
            roundId: "1",
            // tslint:disable-next-line:max-line-length
            data: "{\"dataVersion\":\"2\",\"currentSessionId\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOjMzOTMyOSwiaWQiOiJnYW1lczpjb250ZXh0OjI1OkFVVE9fUExBWUVSXzIxMjEyMDAxNzU6c3dfbXJtbmt5OndlYiIsImlhdCI6MTUwNzcyNTA1NywiaXNzIjoic2t5d2luZGdyb3VwIn0.syF_8A29OEHw4zMEW7ydXZl6xk-M1BRt30NtlBISplduFkaUm0dwBKBCyRSA_O04gExWWzb4VRNax05a89rnog\",\"requestId\":\"3\",\"historyCounter\":\"3\",\"version\":\"8\",\"state\":\"{\\\"gameData\\\":{\\\"gameId\\\": \\\"sw_mrmnky\\\",\\\"gameTokenData\\\":{\\\"gameCode\\\":\\\"sw_mrmnky\\\",\\\"brandId\\\":25,\\\"playerCode\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"currency\\\":\\\"USD\\\",\\\"test\\\":true,\\\"transferEnabled\\\":true,\\\"iat\\\":1507725057,\\\"iss\\\":\\\"skywindgroup\\\",\\\"token\\\":\\\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJnYW1lQ29kZSI6InN3X21ybW5reSIsImJyYW5kSWQiOjI1LCJwbGF5ZXJDb2RlIjoiQVVUT19QTEFZRVJfMjEyMTIwMDE3NSIsImN1cnJlbmN5IjoiVVNEIiwidGVzdCI6dHJ1ZSwidHJhbnNmZXJFbmFibGVkIjp0cnVlLCJpYXQiOjE1MDc3MjUwNTcsImlzcyI6InNreXdpbmRncm91cCJ9.p_stFZuaxYFF3u6sNbWEiXr9ygeujPYN-dTHJR4gBxC_hfduscpP1LJ9h34r0Mx2awnAVHspn6fk3lvCGaOQlg\\\"},\\\"balance\\\":{\\\"main\\\":50000},\\\"player\\\":{\\\"code\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"id\\\":\\\"LB0n1dql\\\",\\\"status\\\":\\\"normal\\\",\\\"firstName\\\":\\\"AUTO_PLAYER_FIRSTNAME_2121200175\\\",\\\"lastName\\\":\\\"AUTO_PLAYER_LASTNAME_2121200175\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"currency\\\":\\\"USD\\\",\\\"country\\\":\\\"BY\\\",\\\"language\\\":\\\"en\\\",\\\"agentId\\\":null,\\\"isTest\\\":true,\\\"lastLogin\\\":\\\"2017-10-11T12:30:57.842Z\\\",\\\"customData\\\":null,\\\"createdAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"brandId\\\":\\\"GJqNWRkX\\\",\\\"brandTitle\\\":null},\\\"limits\\\":{\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200},\\\"settings\\\":{\\\"transferEnabled\\\":true}},\\\"gameVersion\\\":\\\"0.3.0\\\",\\\"gameContext\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"scenesState\\\":{\\\"main\\\":{\\\"multiplier\\\":1,\\\"behaviorsState\\\":{\\\"regularSlotSceneBehavior\\\":null}},\\\"freeSpins\\\":{\\\"multiplier\\\":3,\\\"behaviorsState\\\":{\\\"freeGamesSlotSceneBehavior\\\":{\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2}}}},\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"history\\\":[{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"scene\\\":\\\"main\\\",\\\"multiplier\\\":1,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"main\\\",\\\"positions\\\":[36,0,0,3,0],\\\"view\\\":[[13,6,4,4,7],[2,1,6,13,10],[5,2,13,2,2],[3,7,3,6,3]]},\\\"rewards\\\":[{\\\"reward\\\":\\\"scatter\\\",\\\"positions\\\":[[0,0],[1,3],[2,2]],\\\"payout\\\":2,\\\"paytable\\\":[6,2]}],\\\"events\\\":[{\\\"id\\\":\\\"freeSpinsStart\\\",\\\"amount\\\":12,\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[46,0,20,21,23],\\\"view\\\":[[1,4,2,1,1],[5,1,4,4,4],[2,9,6,8,2],[7,6,12,3,8]]},\\\"triggeredSceneId\\\":\\\"freeSpins\\\",\\\"triggerSymbols\\\":[[0,0],[1,3],[2,2]]}],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}],\\\"currentRoundWin\\\":2,\\\"nextScene\\\":\\\"\\\",\\\"previousProcessSceneResult\\\":{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0,\\\"totalWin\\\":0,\\\"scene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsWin\\\":0,\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[36,20,11,21,36],\\\"view\\\":[[2,8,9,1,4],[7,4,4,4,5],[8,1,5,8,9],[5,11,6,3,3]]},\\\"rewards\\\":[],\\\"events\\\":[],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}},\\\"settings\\\":{\\\"transferEnabled\\\":true,\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200,\\\"defaultCoin\\\":1,\\\"coins\\\":[1],\\\"currencyMultiplier\\\":100},\\\"roundId\\\":11980748,\\\"roundEnded\\\":false,\\\"round\\\":{\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"totalEvents\\\":3,\\\"balanceBefore\\\":50000,\\\"startedAt\\\":\\\"2017-10-11T12:30:58.244Z\\\",\\\"broken\\\":true},\\\"requestContext\\\":{\\\"ip\\\":\\\"::ffff:************\\\",\\\"language\\\":\\\"en\\\",\\\"country\\\":\\\"BY\\\"},\\\"createdAt\\\":\\\"2017-10-11T12:30:58.008Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:58.609Z\\\"}\"}",
            createdAt: new Date("2017-11-09 10:40:17.5"),
            updatedAt: new Date("2017-11-09 10:40:17.5"),
            broken: true,
            brokenPayment: true,
            requireLogout: true,
            requireTransferOut: false,
            requireCompletion: false,
            specialState: SpecialState.BROKEN_INTEGRATION,
            merchantSessionId: "merchant_session",
            retryAttempts: 1
        });

        const contextId = GameContextID.createFromString(id);
        const result = await getGameFlowContextManager().findOfflineGameContext(contextId);

        const ctx = new GameFlowContextImpl(contextId);
        await getGameContextDecoder().decode(ctx, result.data);
        expect(ctx).deep.equal({
            persistencePolicy: 0,
            id: {
                gameCode: "sw_mrmnky",
                brandId: 25,
                playerCode: "AUTO_PLAYER_2121200175",
                deviceId: "web",
                idValue: "games:context:25:AUTO_PLAYER_2121200175:sw_mrmnky:web"
            },
            lockExclusively: false,
            reactive: true,
            _prev_sessionId: undefined,
            _sessionId:
                {
                    // tslint:disable-next-line:max-line-length
                    id: "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOjMzOTMyOSwiaWQiOiJnYW1lczpjb250ZXh0OjI1OkFVVE9fUExBWUVSXzIxMjEyMDAxNzU6c3dfbXJtbmt5OndlYiIsImlhdCI6MTUwNzcyNTA1NywiaXNzIjoic2t5d2luZGdyb3VwIn0.syF_8A29OEHw4zMEW7ydXZl6xk-M1BRt30NtlBISplduFkaUm0dwBKBCyRSA_O04gExWWzb4VRNax05a89rnog",
                    _sessionId: undefined,
                    _gameMode: undefined
                },
            lastRequestId: 3,
            version: 8,
            settings:
                {
                    transferEnabled: true,
                    winMax: 500000,
                    stakeAll:
                        [0.01, 0.02, 0.03, 0.04, 0.05, 0.06, 0.08, 0.1, 0.2, 0.25, 0.3, 0.4, 0.5, 0.75, 1, 2, 3, 4],
                    stakeDef: 0.04,
                    stakeMax: 4,
                    stakeMin: 0.01,
                    maxTotalStake: 200,
                    defaultCoin: 1,
                    coins: [1],
                    currencyMultiplier: 100
                },
            gameData:
                {
                    gameId: "sw_mrmnky",
                    gameTokenData:
                        {
                            gameCode: "sw_mrmnky",
                            brandId: 25,
                            playerCode: "AUTO_PLAYER_2121200175",
                            currency: "USD",
                            test: true,
                            transferEnabled: true,
                            iat: 1507725057,
                            iss: "skywindgroup",
                            // tslint:disable-next-line:max-line-length
                            token: "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJnYW1lQ29kZSI6InN3X21ybW5reSIsImJyYW5kSWQiOjI1LCJwbGF5ZXJDb2RlIjoiQVVUT19QTEFZRVJfMjEyMTIwMDE3NSIsImN1cnJlbmN5IjoiVVNEIiwidGVzdCI6dHJ1ZSwidHJhbnNmZXJFbmFibGVkIjp0cnVlLCJpYXQiOjE1MDc3MjUwNTcsImlzcyI6InNreXdpbmRncm91cCJ9.p_stFZuaxYFF3u6sNbWEiXr9ygeujPYN-dTHJR4gBxC_hfduscpP1LJ9h34r0Mx2awnAVHspn6fk3lvCGaOQlg"
                        },
                    balance: { main: 50000 },
                    player:
                        {
                            code: "AUTO_PLAYER_2121200175",
                            id: "LB0n1dql",
                            status: "normal",
                            firstName: "AUTO_PLAYER_FIRSTNAME_2121200175",
                            lastName: "AUTO_PLAYER_LASTNAME_2121200175",
                            email: "<EMAIL>",
                            currency: "USD",
                            country: "BY",
                            language: "en",
                            agentId: null,
                            isTest: true,
                            lastLogin: "2017-10-11T12:30:57.842Z",
                            customData: null,
                            createdAt: "2017-10-11T12:30:57.131Z",
                            updatedAt: "2017-10-11T12:30:57.131Z",
                            brandId: "GJqNWRkX",
                            brandTitle: null
                        },
                    limits:
                        {
                            winMax: 500000,
                            stakeAll: [
                                0.01,
                                0.02,
                                0.03,
                                0.04,
                                0.05,
                                0.06,
                                0.08,
                                0.1,
                                0.2,
                                0.25,
                                0.3,
                                0.4,
                                0.5,
                                0.75,
                                1,
                                2,
                                3,
                                4
                            ],
                            stakeDef: 0.04,
                            stakeMax: 4,
                            stakeMin: 0.01,
                            maxTotalStake: 200
                        },
                    settings: { transferEnabled: true }
                },
            gameVersion: "0.3.0",
            pendingModification: undefined,
            jackpotPending: undefined,
            gameContext:
                {
                    currentScene: "freeSpins",
                    scenesState:
                        {
                            main:
                                {
                                    multiplier: 1,
                                    behaviorsState: { regularSlotSceneBehavior: null }
                                },
                            freeSpins:
                                {
                                    multiplier: 3,
                                    behaviorsState:
                                        {
                                            freeGamesSlotSceneBehavior:
                                                {
                                                    freeSpinsCount: 10,
                                                    initialFreeSpinsCount: 12,
                                                    totalFreeSpinsCount: 12,
                                                    freeSpinsWin: 0,
                                                    initialFreeSpinWin: 2
                                                }
                                        }
                                }
                        },
                    stake: { lines: 50, bet: 0.01, coin: 1 },
                    history:
                        [
                            {
                                request: "spin",
                                stake: { lines: 50, bet: 0.01, coin: 1 },
                                totalBet: 0.5,
                                totalWin: 2,
                                scene: "main",
                                multiplier: 1,
                                state:
                                    {
                                        currentScene: "freeSpins",
                                        multiplier: 3,
                                        freeSpinsCount: 12,
                                        freeSpinsWin: 0,
                                        initialFreeSpinWin: 2,
                                        initialFreeSpinsCount: 12,
                                        totalFreeSpinsCount: 12
                                    },
                                reels:
                                    {
                                        set: "main",
                                        positions: [36, 0, 0, 3, 0],
                                        view:
                                            [
                                                [13, 6, 4, 4, 7],
                                                [2, 1, 6, 13, 10],
                                                [5, 2, 13, 2, 2],
                                                [3, 7, 3, 6, 3]
                                            ]
                                    },
                                rewards:
                                    [
                                        {
                                            reward: "scatter",
                                            positions: [[0, 0], [1, 3], [2, 2]],
                                            payout: 2,
                                            paytable: [6, 2]
                                        }
                                    ],
                                events:
                                    [
                                        {
                                            id: "freeSpinsStart",
                                            amount: 12,
                                            reels:
                                                {
                                                    set: "freeSpins",
                                                    positions: [46, 0, 20, 21, 23],
                                                    view:
                                                        [
                                                            [1, 4, 2, 1, 1],
                                                            [5, 1, 4, 4, 4],
                                                            [2, 9, 6, 8, 2],
                                                            [7, 6, 12, 3, 8]
                                                        ]
                                                },
                                            triggeredSceneId: "freeSpins",
                                            triggerSymbols: [[0, 0], [1, 3], [2, 2]]
                                        }
                                    ],
                                roundEnded: false,
                                version: "0.3.0"
                            }
                        ],
                    currentRoundWin: 2,
                    nextScene: "",
                    previousProcessSceneResult:
                        {
                            request: "spin",
                            stake: { lines: 50, bet: 0.01, coin: 1 },
                            totalBet: 0,
                            totalWin: 0,
                            scene: "freeSpins",
                            multiplier: 3,
                            state:
                                {
                                    currentScene: "freeSpins",
                                    multiplier: 3,
                                    freeSpinsWin: 0,
                                    freeSpinsCount: 10,
                                    initialFreeSpinWin: 2,
                                    initialFreeSpinsCount: 12,
                                    totalFreeSpinsCount: 12
                                },
                            reels:
                                {
                                    set: "freeSpins",
                                    positions: [36, 20, 11, 21, 36],
                                    view:
                                        [
                                            [2, 8, 9, 1, 4],
                                            [7, 4, 4, 4, 5],
                                            [8, 1, 5, 8, 9],
                                            [5, 11, 6, 3, 3]
                                        ]
                                },
                            rewards: [],
                            events: [],
                            roundEnded: false,
                            version: "0.3.0"
                        }
                },
            roundId: 11980748,
            roundEnded: false,
            jpContext: undefined,
            round:
                {
                    totalBet: 0.5,
                    totalWin: 2,
                    totalEvents: 3,
                    balanceBefore: 50000,
                    startedAt: "2017-10-11T12:30:58.244Z",
                    broken: true
                },
            requestContext:
                {
                    ip: "::ffff:************",
                    language: "en",
                    country: "BY"
                },
            createdAt: "2017-10-11T12:30:58.008Z",
            updatedAt: "2017-10-11T12:30:58.609Z",
            gameSerialNumber: 3,
            totalEventId: 3,
            retryAttempts: undefined,
            logoutResult: undefined,
            logoutId: undefined,
            specialState: undefined
        });
    });

    it("archives context", async () => {
        await getGameFlowContextManager().archiveGameContext(context);
        const result = await getArchiveContextModel().findAll({ where: { contextId: context.id.asString() } });
        expect(result.length).equal(1);
        expect(result[0].contextId).equal(context.id.asString());
        expect(result[0].roundId).equal(context.roundId);
        expect(result[0].data).deep.equal(await getEngineContext(context));

        const newContext = _.cloneDeep(context);
        newContext.gameContext["newVersion"] = true;
        await getGameFlowContextManager().archiveGameContext(newContext);

        const lastResult = await getArchiveContextModel()
            .findAll({ where: { contextId: context.id.asString() }, order: ["id"] });
        expect(lastResult.length).equal(2);
        expect(lastResult[0].contextId).equal(context.id.asString());
        expect(lastResult[0].roundId).equal(context.roundId);
        expect(lastResult[0].data).deep.equal(await getEngineContext(context));
        expect(lastResult[1].contextId).equal(newContext.id.asString());
        expect(lastResult[1].roundId).equal(context.roundId);
        expect(lastResult[1].data).deep.equal(await getEngineContext(newContext));
    });

    it("saves player context", async () => {
        const ctx1 = new PlayerContextImpl(PlayerContextID.createFromString("games:player:2:player001"));

        ctx1.brokenGamesWithMode = [
            "games:context:1:player001:games004:mobile",
            "real",
            "games:context:1:player001:game005:web",
            "bns"
        ];

        ctx1.version = 10;

        const ctx2 = new PlayerContextImpl(PlayerContextID.createFromString("games:player:2:player002"));

        ctx2.brokenGamesWithMode = [
            "games:context:1:player002:games004:mobile",
            "real",
        ];

        ctx2.version = 5;

        await getGameFlowContextManager().savePlayerContextOffline([ctx1, ctx2]);
        const items = await getPlayerContextModel().findAll();

        expect(items.map(i => _.omit(i.get(), "createdAt", "updatedAt"))).deep.equal([
            {
                data: null,
                games: [
                    {
                        id: "games:context:1:player001:games004:mobile",
                        mode: "real"
                    },
                    {
                        id: "games:context:1:player001:game005:web",
                        mode: "bns"
                    }
                ],
                id: "games:player:2:player001",
                version: "10",
                dataVersion: 1
            },
            {
                data: null,
                games: [
                    {
                        id: "games:context:1:player002:games004:mobile",
                        mode: "real"
                    }
                ],
                id: "games:player:2:player002",
                version: "5",
                dataVersion: 1
            }
        ]);
    });

    it("update player context", async () => {
        let ctx = new PlayerContextImpl(PlayerContextID.createFromString("games:player:2:player001"));

        ctx.brokenGamesWithMode = [
            "games:context:1:player001:games004:mobile",
            "real",
            "games:context:1:player001:game005:web",
            "bns",
        ];

        ctx.version = 10;

        await getGameFlowContextManager().savePlayerContextOffline([ctx]);
        let items = await getPlayerContextModel().findAll();

        expect(items.map(i => _.omit(i.get(), "createdAt", "updatedAt"))).deep.equal([
            {
                data: null,
                games: [
                    {
                        id: "games:context:1:player001:games004:mobile",
                        mode: "real"
                    },
                    {
                        id: "games:context:1:player001:game005:web",
                        mode: "bns"
                    }
                ],
                id: "games:player:2:player001",
                version: "10",
                dataVersion: 1
            }
        ]);

        ctx = new PlayerContextImpl(PlayerContextID.createFromString("games:player:2:player001"));
        ctx.brokenGamesWithMode = [
            "games:context:1:player001:games004:mobile",
            "real",
            "games:context:1:player001:game005:web",
            "bns",
            "games:context:1:player001:game003:web",
            "bns",
        ];

        ctx.version = 10;

        await getGameFlowContextManager().savePlayerContextOffline([ctx]);
        items = await getPlayerContextModel().findAll();

        expect(items.map(i => _.omit(i.get(), "createdAt", "updatedAt"))).deep.equal([
            {
                data: null,
                games: [
                    {
                        id: "games:context:1:player001:games004:mobile",
                        mode: "real"
                    },
                    {
                        id: "games:context:1:player001:game005:web",
                        mode: "bns"

                    },
                    {
                        id: "games:context:1:player001:game003:web",
                        mode: "bns"
                    }
                ],
                id: "games:player:2:player001",
                version: "10",
                dataVersion: 1
            }
        ]);
    });

    it("remove player contexts", async () => {
        const ctx1 = new PlayerContextImpl(PlayerContextID.createFromString("games:player:2:player001"));

        ctx1.brokenGamesWithMode = [
            "games:context:1:player001:games004:mobile",
            "real",
            "games:context:1:player001:game005:web",
            "bns"
        ];

        ctx1.version = 10;

        const ctx2 = new PlayerContextImpl(PlayerContextID.createFromString("games:player:2:player002"));

        ctx2.brokenGamesWithMode = [
            "games:context:1:player002:games004:mobile",
            "real",
        ];

        ctx2.version = 5;

        await getGameFlowContextManager().savePlayerContextOffline([ctx1, ctx2]);
        let items = await getPlayerContextModel().findAll();

        expect(items.length).equal(2);

        await getGameFlowContextManager().removePlayerContextOffline([ctx1, ctx2]);

        items = await getPlayerContextModel().findAll();

        expect(items.length).equal(0);
    });

    it("updates expiredAt", async () => {
        const ts = new Date();
        expect(await await getGameFlowContextManager().findExpiredGameContext(ts, 1)).deep.equals([]);
        const id = "games:context:25:AUTO_PLAYER_2121200175:sw_mrmnky:web";
        const gameContextId = GameContextID.createFromString(id);

        const item = {
            id,
            brandId: gameContextId.brandId,
            playerCode: gameContextId.playerCode,
            gameCode: gameContextId.gameCode,
            gameId: gameContextId.gameCode,
            deviceId: gameContextId.deviceId,
            roundId: "1",
            // tslint:disable-next-line:max-line-length
            data: "{\"dataVersion\":\"2\",\"currentSessionId\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOjMzOTMyOSwiaWQiOiJnYW1lczpjb250ZXh0OjI1OkFVVE9fUExBWUVSXzIxMjEyMDAxNzU6c3dfbXJtbmt5OndlYiIsImlhdCI6MTUwNzcyNTA1NywiaXNzIjoic2t5d2luZGdyb3VwIn0.syF_8A29OEHw4zMEW7ydXZl6xk-M1BRt30NtlBISplduFkaUm0dwBKBCyRSA_O04gExWWzb4VRNax05a89rnog\",\"requestId\":\"3\",\"historyCounter\":\"3\",\"version\":\"8\",\"state\":\"{\\\"gameData\\\":{\\\"gameTokenData\\\":{\\\"gameCode\\\":\\\"sw_mrmnky\\\",\\\"brandId\\\":25,\\\"playerCode\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"currency\\\":\\\"USD\\\",\\\"test\\\":true,\\\"transferEnabled\\\":true,\\\"iat\\\":1507725057,\\\"iss\\\":\\\"skywindgroup\\\",\\\"token\\\":\\\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJnYW1lQ29kZSI6InN3X21ybW5reSIsImJyYW5kSWQiOjI1LCJwbGF5ZXJDb2RlIjoiQVVUT19QTEFZRVJfMjEyMTIwMDE3NSIsImN1cnJlbmN5IjoiVVNEIiwidGVzdCI6dHJ1ZSwidHJhbnNmZXJFbmFibGVkIjp0cnVlLCJpYXQiOjE1MDc3MjUwNTcsImlzcyI6InNreXdpbmRncm91cCJ9.p_stFZuaxYFF3u6sNbWEiXr9ygeujPYN-dTHJR4gBxC_hfduscpP1LJ9h34r0Mx2awnAVHspn6fk3lvCGaOQlg\\\"},\\\"balance\\\":{\\\"main\\\":50000},\\\"player\\\":{\\\"code\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"id\\\":\\\"LB0n1dql\\\",\\\"status\\\":\\\"normal\\\",\\\"firstName\\\":\\\"AUTO_PLAYER_FIRSTNAME_2121200175\\\",\\\"lastName\\\":\\\"AUTO_PLAYER_LASTNAME_2121200175\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"currency\\\":\\\"USD\\\",\\\"country\\\":\\\"BY\\\",\\\"language\\\":\\\"en\\\",\\\"agentId\\\":null,\\\"isTest\\\":true,\\\"lastLogin\\\":\\\"2017-10-11T12:30:57.842Z\\\",\\\"customData\\\":null,\\\"createdAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"brandId\\\":\\\"GJqNWRkX\\\",\\\"brandTitle\\\":null},\\\"limits\\\":{\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200},\\\"settings\\\":{\\\"transferEnabled\\\":true}},\\\"gameVersion\\\":\\\"0.3.0\\\",\\\"gameContext\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"scenesState\\\":{\\\"main\\\":{\\\"multiplier\\\":1,\\\"behaviorsState\\\":{\\\"regularSlotSceneBehavior\\\":null}},\\\"freeSpins\\\":{\\\"multiplier\\\":3,\\\"behaviorsState\\\":{\\\"freeGamesSlotSceneBehavior\\\":{\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2}}}},\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"history\\\":[{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"scene\\\":\\\"main\\\",\\\"multiplier\\\":1,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"main\\\",\\\"positions\\\":[36,0,0,3,0],\\\"view\\\":[[13,6,4,4,7],[2,1,6,13,10],[5,2,13,2,2],[3,7,3,6,3]]},\\\"rewards\\\":[{\\\"reward\\\":\\\"scatter\\\",\\\"positions\\\":[[0,0],[1,3],[2,2]],\\\"payout\\\":2,\\\"paytable\\\":[6,2]}],\\\"events\\\":[{\\\"id\\\":\\\"freeSpinsStart\\\",\\\"amount\\\":12,\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[46,0,20,21,23],\\\"view\\\":[[1,4,2,1,1],[5,1,4,4,4],[2,9,6,8,2],[7,6,12,3,8]]},\\\"triggeredSceneId\\\":\\\"freeSpins\\\",\\\"triggerSymbols\\\":[[0,0],[1,3],[2,2]]}],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}],\\\"currentRoundWin\\\":2,\\\"nextScene\\\":\\\"\\\",\\\"previousProcessSceneResult\\\":{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0,\\\"totalWin\\\":0,\\\"scene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsWin\\\":0,\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[36,20,11,21,36],\\\"view\\\":[[2,8,9,1,4],[7,4,4,4,5],[8,1,5,8,9],[5,11,6,3,3]]},\\\"rewards\\\":[],\\\"events\\\":[],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}},\\\"settings\\\":{\\\"transferEnabled\\\":true,\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200,\\\"defaultCoin\\\":1,\\\"coins\\\":[1],\\\"currencyMultiplier\\\":100},\\\"roundId\\\":11980748,\\\"roundEnded\\\":false,\\\"round\\\":{\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"totalEvents\\\":3,\\\"balanceBefore\\\":50000,\\\"startedAt\\\":\\\"2017-10-11T12:30:58.244Z\\\",\\\"broken\\\":true},\\\"requestContext\\\":{\\\"ip\\\":\\\"::ffff:************\\\",\\\"language\\\":\\\"en\\\",\\\"country\\\":\\\"BY\\\"},\\\"createdAt\\\":\\\"2017-10-11T12:30:58.008Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:58.609Z\\\"}\"}",
            createdAt: new Date("2017-11-09 10:40:17.5"),
            updatedAt: new Date("2017-11-09 10:40:17.5"),
            expireAt: ts,
            broken: true,
            brokenPayment: true,
            requireLogout: true,
            requireTransferOut: false,
            requireCompletion: false,
            specialState: null,
            merchantSessionId: "merchant_session",
            retryAttempts: 1
        };
        await getGameContextModel().create(item);

        let dbContexts = await getGameFlowContextManager().findExpiredGameContext(ts, 1);
        expect(dbContexts.length).equals(1);

        const newExpiredAt = new Date(ts.getTime() + 10 * 60000);
        await getGameFlowContextManager()
            .updateExpirationDate(dbContexts[0], newExpiredAt);

        expect(await getGameFlowContextManager().findExpiredGameContext(ts, 1)).deep.equal([]);
        dbContexts = await getGameFlowContextManager().findExpiredGameContext(newExpiredAt, 1);
        expect(dbContexts.length).equals(1);
    });

    it("clear expiredAt", async () => {
        const ts = new Date();
        expect(await await getGameFlowContextManager().findExpiredGameContext(ts, 1)).deep.equals([]);
        const id = "games:context:25:AUTO_PLAYER_2121200175:sw_mrmnky:web";
        const gameContextId = GameContextID.createFromString(id);

        const item = {
            id,
            brandId: gameContextId.brandId,
            playerCode: gameContextId.playerCode,
            gameCode: gameContextId.gameCode,
            gameId: gameContextId.gameCode,
            deviceId: gameContextId.deviceId,
            roundId: "1",
            // tslint:disable-next-line:max-line-length
            data: "{\"dataVersion\":\"2\",\"currentSessionId\":\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzZXNzaW9uSWQiOjMzOTMyOSwiaWQiOiJnYW1lczpjb250ZXh0OjI1OkFVVE9fUExBWUVSXzIxMjEyMDAxNzU6c3dfbXJtbmt5OndlYiIsImlhdCI6MTUwNzcyNTA1NywiaXNzIjoic2t5d2luZGdyb3VwIn0.syF_8A29OEHw4zMEW7ydXZl6xk-M1BRt30NtlBISplduFkaUm0dwBKBCyRSA_O04gExWWzb4VRNax05a89rnog\",\"requestId\":\"3\",\"historyCounter\":\"3\",\"version\":\"8\",\"state\":\"{\\\"gameData\\\":{\\\"gameTokenData\\\":{\\\"gameCode\\\":\\\"sw_mrmnky\\\",\\\"brandId\\\":25,\\\"playerCode\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"currency\\\":\\\"USD\\\",\\\"test\\\":true,\\\"transferEnabled\\\":true,\\\"iat\\\":1507725057,\\\"iss\\\":\\\"skywindgroup\\\",\\\"token\\\":\\\"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJnYW1lQ29kZSI6InN3X21ybW5reSIsImJyYW5kSWQiOjI1LCJwbGF5ZXJDb2RlIjoiQVVUT19QTEFZRVJfMjEyMTIwMDE3NSIsImN1cnJlbmN5IjoiVVNEIiwidGVzdCI6dHJ1ZSwidHJhbnNmZXJFbmFibGVkIjp0cnVlLCJpYXQiOjE1MDc3MjUwNTcsImlzcyI6InNreXdpbmRncm91cCJ9.p_stFZuaxYFF3u6sNbWEiXr9ygeujPYN-dTHJR4gBxC_hfduscpP1LJ9h34r0Mx2awnAVHspn6fk3lvCGaOQlg\\\"},\\\"balance\\\":{\\\"main\\\":50000},\\\"player\\\":{\\\"code\\\":\\\"AUTO_PLAYER_2121200175\\\",\\\"id\\\":\\\"LB0n1dql\\\",\\\"status\\\":\\\"normal\\\",\\\"firstName\\\":\\\"AUTO_PLAYER_FIRSTNAME_2121200175\\\",\\\"lastName\\\":\\\"AUTO_PLAYER_LASTNAME_2121200175\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"currency\\\":\\\"USD\\\",\\\"country\\\":\\\"BY\\\",\\\"language\\\":\\\"en\\\",\\\"agentId\\\":null,\\\"isTest\\\":true,\\\"lastLogin\\\":\\\"2017-10-11T12:30:57.842Z\\\",\\\"customData\\\":null,\\\"createdAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:57.131Z\\\",\\\"brandId\\\":\\\"GJqNWRkX\\\",\\\"brandTitle\\\":null},\\\"limits\\\":{\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200},\\\"settings\\\":{\\\"transferEnabled\\\":true}},\\\"gameVersion\\\":\\\"0.3.0\\\",\\\"gameContext\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"scenesState\\\":{\\\"main\\\":{\\\"multiplier\\\":1,\\\"behaviorsState\\\":{\\\"regularSlotSceneBehavior\\\":null}},\\\"freeSpins\\\":{\\\"multiplier\\\":3,\\\"behaviorsState\\\":{\\\"freeGamesSlotSceneBehavior\\\":{\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2}}}},\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"history\\\":[{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"scene\\\":\\\"main\\\",\\\"multiplier\\\":1,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsCount\\\":12,\\\"freeSpinsWin\\\":0,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"main\\\",\\\"positions\\\":[36,0,0,3,0],\\\"view\\\":[[13,6,4,4,7],[2,1,6,13,10],[5,2,13,2,2],[3,7,3,6,3]]},\\\"rewards\\\":[{\\\"reward\\\":\\\"scatter\\\",\\\"positions\\\":[[0,0],[1,3],[2,2]],\\\"payout\\\":2,\\\"paytable\\\":[6,2]}],\\\"events\\\":[{\\\"id\\\":\\\"freeSpinsStart\\\",\\\"amount\\\":12,\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[46,0,20,21,23],\\\"view\\\":[[1,4,2,1,1],[5,1,4,4,4],[2,9,6,8,2],[7,6,12,3,8]]},\\\"triggeredSceneId\\\":\\\"freeSpins\\\",\\\"triggerSymbols\\\":[[0,0],[1,3],[2,2]]}],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}],\\\"currentRoundWin\\\":2,\\\"nextScene\\\":\\\"\\\",\\\"previousProcessSceneResult\\\":{\\\"request\\\":\\\"spin\\\",\\\"stake\\\":{\\\"lines\\\":50,\\\"bet\\\":0.01,\\\"coin\\\":1},\\\"totalBet\\\":0,\\\"totalWin\\\":0,\\\"scene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"state\\\":{\\\"currentScene\\\":\\\"freeSpins\\\",\\\"multiplier\\\":3,\\\"freeSpinsWin\\\":0,\\\"freeSpinsCount\\\":10,\\\"initialFreeSpinWin\\\":2,\\\"initialFreeSpinsCount\\\":12,\\\"totalFreeSpinsCount\\\":12},\\\"reels\\\":{\\\"set\\\":\\\"freeSpins\\\",\\\"positions\\\":[36,20,11,21,36],\\\"view\\\":[[2,8,9,1,4],[7,4,4,4,5],[8,1,5,8,9],[5,11,6,3,3]]},\\\"rewards\\\":[],\\\"events\\\":[],\\\"roundEnded\\\":false,\\\"version\\\":\\\"0.3.0\\\"}},\\\"settings\\\":{\\\"transferEnabled\\\":true,\\\"winMax\\\":500000,\\\"stakeAll\\\":[0.01,0.02,0.03,0.04,0.05,0.06,0.08,0.1,0.2,0.25,0.3,0.4,0.5,0.75,1,2,3,4],\\\"stakeDef\\\":0.04,\\\"stakeMax\\\":4,\\\"stakeMin\\\":0.01,\\\"maxTotalStake\\\":200,\\\"defaultCoin\\\":1,\\\"coins\\\":[1],\\\"currencyMultiplier\\\":100},\\\"roundId\\\":11980748,\\\"roundEnded\\\":false,\\\"round\\\":{\\\"totalBet\\\":0.5,\\\"totalWin\\\":2,\\\"totalEvents\\\":3,\\\"balanceBefore\\\":50000,\\\"startedAt\\\":\\\"2017-10-11T12:30:58.244Z\\\",\\\"broken\\\":true},\\\"requestContext\\\":{\\\"ip\\\":\\\"::ffff:************\\\",\\\"language\\\":\\\"en\\\",\\\"country\\\":\\\"BY\\\"},\\\"createdAt\\\":\\\"2017-10-11T12:30:58.008Z\\\",\\\"updatedAt\\\":\\\"2017-10-11T12:30:58.609Z\\\"}\"}",
            createdAt: new Date("2017-11-09 10:40:17.5"),
            updatedAt: new Date("2017-11-09 10:40:17.5"),
            expireAt: ts,
            broken: true,
            brokenPayment: true,
            requireLogout: true,
            requireTransferOut: false,
            requireCompletion: false,
            specialState: null,
            merchantSessionId: "merchant_session",
            retryAttempts: 1
        };
        await getGameContextModel().create(item);

        const dbContexts = await getGameFlowContextManager().findExpiredGameContext(ts, 1);
        expect(dbContexts.length).equals(1);

        await getGameFlowContextManager()
            .updateExpirationDate(dbContexts[0], null);

        expect(await getGameFlowContextManager().findExpiredGameContext(new Date(), 1)).deep.equal([]);

    });
});
