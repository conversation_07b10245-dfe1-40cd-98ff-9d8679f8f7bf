import { suite, test, timeout } from "mocha-typescript";
import { BrokenPaymentsStat } from "../../skywind/services/cleanup/brokenPaymentsStat";
import { Redis as RedisClient } from "ioredis";
import { getFactory } from "../../skywind/storage/redis";
import { expect, use } from "chai";
import { flushAll } from "../helper";
import "deep-equal-in-any-order";
import config from "../../skywind/config";
import { sleep } from "@skywind-group/sw-utils";

use(require("deep-equal-in-any-order"));

@suite()
@timeout(10000)
class BrokenPaymentStatsSpec {
    private stat1: BrokenPaymentsStat;
    private stat2: BrokenPaymentsStat;
    private client1: RedisClient;
    private client2: RedisClient;
    private prevValue: number;

    public async before() {
        await flushAll();
        this.prevValue = config.cleanUp.brokenPaymentMonitoringRequestTimeout;
        config.cleanUp.brokenPaymentMonitoringRequestTimeout = 3000;
        this.client1 = await getFactory().createClient();
        this.client2 = await getFactory().createClient();
        this.stat1 = new BrokenPaymentsStat(this.client1);
        this.stat2 = new BrokenPaymentsStat(this.client2);
        await this.stat1.clear();
        await sleep(1000);
    }

    public async after() {
        config.cleanUp.brokenPaymentMonitoringRequestTimeout = this.prevValue;
        await this.client1.unsubscribe();
        await this.client2.unsubscribe();
        await this.client1.quit();
        await this.client2.quit();
        this.stat1 = undefined;
        this.stat2 = undefined;
    }

    @test()
    public async testVisit() {
        this.stat1.visitBeforeCommit({ id: { brandId: "1" }, retryAttempts: 1 } as any);
        this.stat1.visitBeforeCommit({ id: { brandId: "1" }, retryAttempts: 1 } as any);
        this.stat1.visitBeforeCommit({ id: { brandId: "1" }, retryAttempts: 1 } as any);
        this.stat1.visitBeforeCommit({ id: { brandId: "1" }, retryAttempts: 2 } as any);

        this.stat1.visitBeforeCommit({ id: { brandId: "5" }, retryAttempts: 1 } as any);
        this.stat1.visitBeforeCommit({ id: { brandId: "5" }, retryAttempts: 1 } as any);
        this.stat1.visitBeforeCommit({ id: { brandId: "5" }, retryAttempts: 1 } as any);
        this.stat1.visitBeforeCommit({ id: { brandId: "5" }, retryAttempts: 2 } as any);

        this.stat1.visitBeforeCommit({ id: { brandId: "3" }, retryAttempts: 1 } as any);
        this.stat1.visitBeforeCommit({ id: { brandId: "3" }, retryAttempts: 2 } as any);

        this.stat2.visitBeforeCommit({ id: { brandId: "2" }, retryAttempts: 1 } as any);
        this.stat2.visitBeforeCommit({ id: { brandId: "2" }, retryAttempts: 1 } as any);
        this.stat2.visitBeforeCommit({ id: { brandId: "2" }, retryAttempts: 1 } as any);
        this.stat2.visitBeforeCommit({ id: { brandId: "2" }, retryAttempts: 1 } as any);
        this.stat2.visitBeforeCommit({ id: { brandId: "2" }, retryAttempts: 2 } as any);

        this.stat2.visitAfterCommit({ id: { brandId: "1" }, retryAttempts: 3 } as any);
        this.stat2.visitAfterCommit({ id: { brandId: "4" }, retryAttempts: 3 } as any);

        this.stat1.visitAfterCommit({ id: { brandId: "2" }, retryAttempts: 3 } as any);
        this.stat1.visitAfterCommit({ id: { brandId: "5" }, retryAttempts: 3 } as any);

        this.stat1.visitAfterFailCommit({
            id: { brandId: "5" },
            retryAttempts: 2,
            gameData: { settings: { maxPaymentRetryAttempts: 3 } }, brokenPayment: {}
        } as any);

        this.stat1.visitAfterFailCommit({
            id: { brandId: "5" },
            retryAttempts: 3,
            gameData: { settings: { maxPaymentRetryAttempts: 3 } }, brokenPayment: {}
        } as any);

        await this.stat2.getData();

        expect(await this.stat2.getData()).deep.equalInAnyOrder([
            {
                brandId: "2",
                pendingPayments: 3
            },
            {
                brandId: "1",
                pendingPayments: 2
            },
            {
                brandId: "5",
                pendingPayments: 1
            },
            {
                brandId: "3",
                pendingPayments: 1
            }
        ]);
    }

}
