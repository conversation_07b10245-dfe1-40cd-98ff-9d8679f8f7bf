import { suite, test } from "mocha-typescript";
import { CSRandomGenerator } from "@skywind-group/sw-random-cs";
import { mock, SinonStub, stub } from "sinon";
import { JackpotFlow } from "../../../skywind/services/jpn/jackpotFlow";
import { expect } from "chai";
import { getJPNServer } from "../../../skywind/services/jpn/jpnserver";

@suite("JackpotFlow")
class JackpotFlowSpec {
    private authStub: SinonStub;

    public jackpotService: any = {
        commitOperation: stub()
    };

    public jackpotsInfo = [
        { id: "jpId1", jpGameId: "test", currency: "EUR" },
        { id: "jpId2", jpGameId: "test", currency: "EUR" }];

    public flow = (info: any = {
        isJackpotEnabled: true,
        jackpotContext: {
            token: "some_token",
            jackpotId: ["jpId1", "jpId2"],
            jackpotsInfo: this.jackpotsInfo,
            contributionEnabled: true
        }
    }) => {
        return {
            ...info,
            updateJackpotPendingModification: mock(),
            jackpotPendingFactory: {
                createConfirmWinModification: mock()
            },
            walletManager: {
                commitOperation: mock()
            },
            rng: () => {
                return new CSRandomGenerator();
            }
        };
    }

    public before() {
        this.jackpotService.commitOperation.reset();
        const jpnServer = getJPNServer();
        this.authStub = stub(jpnServer, "auth");
    }

    public after() {
        this.authStub.restore();
    }

    @test("processes contribution")
    public async processContribution() {
        const flow = this.flow();
        const jpFlow = new JackpotFlow(flow, this.jackpotService);

        const jackpotOperation = {
            type: "contribution",
            transactionId: "contribution_trx_id"
        };
        const tickers = {
            jackpotId: "test-jp-it"
        };

        flow.jackpotPending = { jackpotOperation };

        this.jackpotService.commitOperation.withArgs(jackpotOperation).returns({ tickers });
        const result = await jpFlow.processJackpotOperation();

        expect(result).deep.equals({
            jpnResult: { tickers },
            jackpotContext:
                {
                    token: "some_token",
                    jackpotsInfo: this.jackpotsInfo,
                    contributionEnabled: true,
                }
        });
        expect(await jpFlow.jackpotTickers()).deep.equal(tickers);
        expect(this.jackpotService.commitOperation.callCount).equal(1);
    }

    @test("processes contribution and start mini game")
    public async processContributionAndStartMiniGame() {
        const flow = this.flow();
        const jpFlow = new JackpotFlow(flow, this.jackpotService);

        const jackpotOperation = {
            type: "contribution",
            transactionId: "contribution_trx_id"
        };
        const tickers = {
            jackpotId: "test-jp-it"
        };
        const jackpot = {
            transactionId: "trx_id",
            result: [
                {
                    event: "start-mini-game",
                    jackpotId: "jp-id"
                }
            ]
        };

        flow.jackpotPending = { jackpotOperation };

        this.jackpotService.commitOperation.withArgs(jackpotOperation).returns({ tickers, jackpot });
        const result = await jpFlow.processJackpotOperation();

        expect(result).deep.equals({
            jpnResult: { jackpot, tickers },
            jackpotContext:
                {
                    token: "some_token",
                    jackpotsInfo: this.jackpotsInfo,
                    contributionEnabled: true,
                    jackpot
                }
        });
        expect(await jpFlow.jackpotTickers()).deep.equal(tickers);
        expect(this.jackpotService.commitOperation.callCount).equal(1);
    }

    @test("processes contribution and win")
    public async processContributionAndWin() {
        const flow = this.flow();
        const jpFlow = new JackpotFlow(flow, this.jackpotService);

        const jackpotOperation = {
            type: "contribution",
            transactionId: "contribution_trx_id"
        };
        const tickers = {
            jackpotId: "test-jp-it"
        };
        const jackpot = {
            transactionId: "trx_id",
            result: [
                {
                    event: "win",
                    jackpotId: "jpId1"
                }
            ]
        };

        flow.jackpotPending = { jackpotOperation };

        const confirmOperation = {
            type: "win-confirm",
            payload: { transactionId: "trx_id", jackpotId: ["jpId1"] }
        };

        const walletOperation = {
            type: "payment"
        };

        const newPending = {
            jackpotOperation: confirmOperation,
            walletOperation: walletOperation,
            history:
                {
                    type: "jackpot-win",
                    roundEnded: false,
                    data:
                        {
                            transactionId: "trx_id",
                            result: [{ event: "win", jackpotId: "jpId1" }]
                        }
                }
        };

        flow.jackpotPendingFactory.createConfirmWinModification.once().returns(newPending);

        this.jackpotService.commitOperation.onCall(0)
            .returns({ tickers, jackpot }).onCall(1).returns({ tickers });

        flow.updateJackpotPendingModification.once().callsFake(function() {
                flow.jackpotPending = newPending;
                return newPending;
            }
        );
        flow.walletManager.commitOperation.never();

        let result = await jpFlow.processJackpotOperation();
        result = await jpFlow.resolveJackpotWin(result.jpnResult);

        expect(result).deep.equals({
            jpnResult: {
                jackpot: {
                    result: [
                        {
                            event: "win",
                            jackpotId: "jpId1",
                        }
                    ],
                    transactionId: "trx_id",
                },
                tickers: {
                    jackpotId: "test-jp-it"
                }
            },
            jackpotContext:
                {
                    token: "some_token",
                    jackpotsInfo: this.jackpotsInfo,
                    contributionEnabled: true,
                }
        });
        expect(await jpFlow.jackpotTickers()).deep.equal(tickers);
        expect(this.jackpotService.commitOperation.callCount).equal(2);
        flow.jackpotPendingFactory.createConfirmWinModification.verify();
        flow.walletManager.commitOperation.verify();
    }

    @test("processes mini game")
    public async processMiniGame() {
        const flow = this.flow();
        const jpFlow = new JackpotFlow(flow, this.jackpotService);

        const jackpotOperation = {
            payload: {
                exchangeRate: 2,
                jackpotId: "jpId1",
                transactionId: "trx_id",
                type: "mini-game"
            },
            type: "mini-game"
        };

        const tickers = {
            jackpotId: "test-jp-it"
        };

        flow.jackpotPending = {
            history: {
                data: {
                    exchangeRate: 2,
                    jackpotId: "jpId1",
                    transactionId: "trx_id",
                    type: "mini-game"
                },
                roundEnded: false,
                type: "mini-game",
            },
            jackpotOperation: jackpotOperation
        };

        this.jackpotService.commitOperation.withArgs(jackpotOperation).returns({
            tickers: tickers
        });
        const result = await jpFlow.processJackpotOperation();

        expect(result).deep.equals({
            jpnResult: { tickers },
            jackpotContext:
                {
                    token: "some_token",
                    jackpotsInfo: this.jackpotsInfo,
                    contributionEnabled: true,
                }
        });
        expect(await jpFlow.jackpotTickers()).deep.equal(tickers);
        expect(this.jackpotService.commitOperation.callCount).equal(1);
    }

    @test("handle jackpot configuration changes on restoration")
    public async correctRestoration() {
        const flow = this.flow({
            isJackpotEnabled: true,
            jackpotContext: {
                token: "some_token",
                jackpotId: ["jpId1", "jpId2"],
                jackpotsInfo: this.jackpotsInfo,
                contributionEnabled: true
            },
            gameData: { gameTokenData: {} },
            jackpotPending: {
                jackpotOperation: {
                    type: "contribution",
                    payload: {
                        type: "contribution",
                        amount: 0.4,
                        jackpotIds: [
                            "gov1"
                        ],
                        externalId: "NnbgDHQX0gkAAALyNnbgDXKJrCg=",
                        exchangeRates: { "RON": 1 },
                        roundId: "2001865664443",
                        transactionId: "NnbgJ16LfXkAAALyNnbgJ3R+Uys="
                    }
                }
            }
        });

        this.authStub.returns({
            "token": "token",
            "jackpots": [
                {
                    "id": "jpId1",
                    "currency": "RON",
                    "type": "ph-must-win-multi-type",
                },
                {
                    "id": "jpId2",
                    "currency": "RON",
                    "type": "ph-must-win-multi-type",
                },
                {
                    "id": "gov1",
                    "currency": "RON",
                    "type": "ph-must-win-multi-type",
                }
            ]
        });
        this.jackpotService.commitOperation.returns({});

        const jpFlow = new JackpotFlow(flow, this.jackpotService);
        await jpFlow.processJackpotOperation();
        expect(flow.jackpotContext.jackpotsInfo.length).to.equal(3);
        expect(flow.jackpotContext.jackpotsInfo[0]).deep.equal({
            "id": "jpId1",
            "currency": "RON",
            "type": "ph-must-win-multi-type",
        });
    }

    @test("fix broken pending jp operation(jp duplication)")
    public async fixBrokenPending() {
        const flow = this.flow({
            isJackpotEnabled: true,
            jackpotContext: {
                token: "some_token",
                jackpotId: ["jpId1", "jpId2", "gov1"],
                jackpotsInfo: this.jackpotsInfo,
                contributionEnabled: true
            },
            gameData: { gameTokenData: {} },
            jackpotPending: {
                jackpotOperation: {
                    type: "contribution",
                    payload: {
                        type: "contribution",
                        amount: 0.4,
                        jackpotIds: [
                            "gov1", "jpId1", "jpId2", "gov1", "jpId2", "jpId1", "gov1"
                        ],
                        externalId: "NnbgDHQX0gkAAALyNnbgDXKJrCg=",
                        exchangeRates: { "RON": 1 },
                        roundId: "2001865664443",
                        transactionId: "NnbgJ16LfXkAAALyNnbgJ3R+Uys="
                    }
                }
            }
        });

        this.authStub.returns({
            "token": "token",
            "jackpots": [
                {
                    "id": "jpId1",
                    "currency": "RON",
                    "type": "ph-must-win-multi-type",
                }
            ]
        });
        this.jackpotService.commitOperation.returns({});

        const jpFlow = new JackpotFlow(flow, this.jackpotService);
        await jpFlow.processJackpotOperation();
        expect(this.jackpotService.commitOperation.args[0][0].payload.jackpotIds).deep
            .equal(["gov1", "jpId1", "jpId2"]);
    }
}
