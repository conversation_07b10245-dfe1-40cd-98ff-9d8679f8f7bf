import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { JackpotUtil } from "../../../skywind/services/jpn/jackpotUtil";
import { mock } from "sinon";
import { JackpotInfo } from "../../../skywind/services/jpn/jackpot";

@suite("JackpotUtil")
class JackpotUtilSpec {

    @test()
    public isStartMiniGameResult() {
        expect(JackpotUtil.isStartMiniGameResult({
            jackpot: {
                transactionId: "trx_id",
                result: [
                    {
                        event: "start-mini-game",
                        jackpotId: "jp-id"
                    }
                ]
            },
        })).is.true;

        expect(JackpotUtil.isStartMiniGameResult({
            jackpot: {
                transactionId: "trx_id",
                result: [
                    {
                        event: "start-mini-game",
                        jackpotId: "jp-id"
                    },
                    {
                        event: "win",
                        jackpotId: "jp-id"
                    }
                ]
            },
        })).is.true;

        expect(JackpotUtil.isStartMiniGameResult({
            jackpot: {
                transactionId: "trx_id",
                result: [
                    {
                        event: "win",
                        jackpotId: "jp-id"
                    },
                    {
                        event: "start-mini-game",
                        jackpotId: "jp-id"
                    }
                ]
            },
        })).is.true;

        expect(JackpotUtil.isStartMiniGameResult({
            jackpot: {
                transactionId: "trx_id",
                result: [
                    {
                        event: "win",
                        jackpotId: "jp-id"
                    }
                ]
            },
        })).is.false;

        expect(JackpotUtil.isStartMiniGameResult({})).is.undefined;
    }

    @test()
    public isWinResult() {
        expect(JackpotUtil.isWinResult({
            jackpot: {
                transactionId: "trx_id",
                result: [
                    {
                        event: "start-mini-game",
                        jackpotId: "jp-id"
                    }
                ]
            },
        })).is.false;

        expect(JackpotUtil.isWinResult({
            jackpot: {
                transactionId: "trx_id",
                result: [
                    {
                        event: "win",
                        jackpotId: "jp-id"
                    }
                ]
            },
        })).is.true;

        expect(JackpotUtil.isWinResult({
            jackpot: {
                transactionId: "trx_id",
                result: [
                    {
                        event: "win",
                        jackpotId: "jp-id"
                    },
                    {
                        event: "start-mini-game",
                        jackpotId: "jp-id"
                    }

                ]
            },
        })).is.true;

        expect(JackpotUtil.isWinResult({
            jackpot: {
                transactionId: "trx_id",
                result: [
                    {
                        event: "start-mini-game",
                        jackpotId: "jp-id"
                    },
                    {
                        event: "win",
                        jackpotId: "jp-id"
                    }
                ]
            },
        })).is.true;

        expect(JackpotUtil.isWinResult({})).is.undefined;
    }

    @test()
    public isContributionOperation() {
        expect(JackpotUtil.isContributionOperation({
            type: "contribution", payload: {
                transactionId: "trxId",
                amount: 1000,
                roundId: "1"
            }
        })).is.true;

        expect(JackpotUtil.isContributionOperation({
            type: "win-confirm", payload: {
                transactionId: "trxId",
                jackpotId: "jp-id",
                roundId: "1"
            }
        })).is.false;
    }

    @test()
    public isMiniGameOperation() {
        expect(JackpotUtil.isMiniGameOperation({
            type: "contribution", payload: {
                transactionId: "trxId",
                amount: 1000,
                roundId: "1"
            }
        })).is.false;

        expect(JackpotUtil.isMiniGameOperation({
            type: "mini-game", payload: {
                transactionId: "trxId",
                jackpotId: "jp-id",
                roundId: "1"
            }
        })).is.true;
    }

    @test()
    public isWinOperation() {
        expect(JackpotUtil.isWinOperation({
            type: "contribution", payload: {
                transactionId: "trxId",
                amount: 1000,
                roundId: "1"
            }
        })).is.false;

        expect(JackpotUtil.isWinOperation({
            type: "win-jackpot", payload: {
                transactionId: "trxId",
                jackpotId: "jp-id",
                roundId: "1"
            }
        })).is.true;
    }

    @test()
    public isConfirmOperation() {
        expect(JackpotUtil.isConfirmOperation({
            type: "contribution", payload: {
                transactionId: "trxId",
                amount: 1000,
                roundId: "1"
            }
        })).is.false;

        expect(JackpotUtil.isConfirmOperation({
            type: "win-confirm", payload: {
                transactionId: "trxId",
                jackpotId: "jp-id",
                roundId: "1"
            }
        })).is.true;
    }

    @test()
    public validateJackpotStateOnPreviousStateWin() {
        const flow: any = {
            request: mock(),
            jackpotContext: {
                token: "some_token",
                currency: "usd",
                contributionEnabled: true,
                jackpot: {
                    transactionId: "trx_id",
                    result: [
                        {
                            event: "win",
                            jackpotId: "jp-id",
                            amout: 10000
                        }
                    ]
                }
            }
        } as any;

        flow.request.once().returns({
            request: "start-mini-game"
        });
        expect(JackpotUtil.validateJackpotState(flow)).is.false;
        flow.request.verify();
        flow.request.reset();

        flow.request.once().returns({
            request: "play-mini-game"
        });
        expect(JackpotUtil.validateJackpotState(flow)).is.false;
        flow.request.verify();
        flow.request.reset();

        flow.request.once().returns({
            request: "spin"
        });
        expect(JackpotUtil.validateJackpotState(flow)).is.true;
        flow.request.verify();
        flow.request.reset();
    }

    @test()
    public validateJackpotStateOnStartMiniGame() {
        const flow: any = {
            request: mock(),
            jackpotContext: {
                token: "some_token",
                currency: "usd",
                contributionEnabled: "true",
                jackpot: {
                    transactionId: "trx_id",
                    result: [
                        {
                            event: "start-mini-game",
                            jackpotId: "jp-id"
                        }
                    ]
                }
            }
        } as any;

        flow.request.once().returns({
            request: "start-mini-game"
        });
        expect(JackpotUtil.validateJackpotState(flow)).is.true;
        flow.request.verify();
        flow.request.reset();

        flow.request.once().returns({
            request: "play-mini-game"
        });
        expect(JackpotUtil.validateJackpotState(flow)).is.true;
        flow.request.verify();
    }

    @test()
    public validateJackpotStateNegativeBadRequest() {
        const flow: any = {
            request: mock(),
            jackpotContext: {
                token: "some_token",
                currency: "usd",
                contributionEnabled: "true",
                jackpot: {
                    transactionId: "trx_id",
                    result: [
                        {
                            event: "start-mini-game",
                            jackpotId: "jp-id"
                        }
                    ]
                }
            }
        } as any;

        flow.request.once().returns({
            request: "spin"
        });
        expect(JackpotUtil.validateJackpotState(flow)).is.false;
        flow.request.verify();
    }

    @test()
    public validateJackpotStateNoJackpotContext() {
        const flow: any = {
            request: mock(),
        } as any;

        flow.request.once().returns({
            request: "spin"
        });
        expect(JackpotUtil.validateJackpotState(flow)).is.true;
        flow.request.verify();
    }

    @test()
    public getJackpotResult() {
        const result1 = JackpotUtil.getJackpotResult({
            transactionId: "trx_id",
            result: [
                {
                    event: "win",
                    transactionId: "trx_id1",
                    jackpotId: "jp-id1",
                    amout: 10000,
                    gameData: "data1"
                },
                {
                    event: "win",
                    transactionId: "trx_id2",
                    jackpotId: "jp-id2",
                    amout: 20000,
                    gameData: "data2"
                }
            ]
        });

        expect(result1).deep.equal([
            {
                event: "win",
                transactionId: undefined,
                jackpotId: "jp-id1",
                amout: 10000,
                gameData: "data1"
            },
            {
                event: "win",
                transactionId: undefined,
                jackpotId: "jp-id2",
                amout: 20000,
                gameData: "data2"
            }
        ]);

        const result2 = JackpotUtil.getJackpotResult({
            transactionId: "trx_id",
            result: [
                {
                    event: "win",
                    transactionId: "trx_id1",
                    jackpotId: "jp-id1",
                    amout: 10000,
                    gameData: "data1"
                },
            ]
        });

        expect(result2).deep.equal({
            event: "win",
            transactionId: undefined,
            jackpotId: "jp-id1",
            amout: 10000,
            gameData: "data1"
        });

        const result3 = JackpotUtil.getJackpotResult({
            transactionId: "trx_id",
            result: []
        });

        expect(result3).deep.equal(undefined);

        const result4 = JackpotUtil.getJackpotResult(undefined);

        expect(result4).is.undefined;
    }

    @test()
    public getJackpotResultFiltered() {
        const jpTrxResult = {
            transactionId: "trx_id",
            result: [
                {
                    event: "win",
                    transactionId: "trx_id1",
                    jackpotId: "jp-id1",
                    amout: 10000,
                    gameData: "data1"
                },
                {
                    event: "win",
                    transactionId: "trx_id2",
                    jackpotId: "jp-id2",
                    amout: 20000,
                    gameData: "data2"
                }
            ]
        };
        const result1 = JackpotUtil.getJackpotResult(jpTrxResult,  {
            token: "aaa",
            jackpotsInfo: [{id: "jp-id1", isGameOwned: false}, {id: "jp-id2", isGameOwned: true }] as JackpotInfo[]
        } );

        expect(result1).deep.equal(
            {
                event: "win",
                transactionId: undefined,
                jackpotId: "jp-id2",
                amout: 20000,
                gameData: "data2"
            }
        );

        const result2 = JackpotUtil.getJackpotResult(jpTrxResult,  {
            token: "aaa",
            jackpotsInfo: [{id: "jp-id1", isGameOwned: false}, {id: "jp-id2", isGameOwned: false}] as JackpotInfo[]
        } );
        expect(result2).to.equal(undefined);
    }
}
