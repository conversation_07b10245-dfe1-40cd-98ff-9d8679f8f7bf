import { expect, should, use } from "chai";
import {
    GameInitSettingsDBInstance, GameInitSettingsService, getGameInitSettingsModel
} from "../../skywind/services/gameInitSettings";
import { CacheableGameModule } from "../../skywind/services/game/gamemodule";
import config from "../../skywind/config";
import { testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;
import status200 = testing.status200;

use(require("chai-as-promised"));
should();

describe("Game init settings", () => {

    describe("Storage", () => {

        const gameInitSettings = new GameInitSettingsService();

        before(async () => {
            await getGameInitSettingsModel().sync();
        });

        beforeEach(async () => {
            await getGameInitSettingsModel().truncate();
        });

        it("checks if saved", async () => {
            await gameInitSettings.checkIfSaved({
                version: "1.1.1",
                id: "module1",
                name: "@skywind-group/module1",
                nameWithVersion: "@skywind-group/module1@1.1.1"
            }, { test: "SOMEDATA" } as any);

            const result1 = await getGameInitSettingsModel()
                .findAll()
                .then((result: GameInitSettingsDBInstance[]) => result.map(x => x.toJSON()));
            expect(result1).deep.equal([
                {
                    "data": {
                        "test": "SOMEDATA"
                    },
                    "gameId": "module1",
                    "version": "1.1.1",
                }
            ]);

            await gameInitSettings.checkIfSaved({
                version: "1.1.1",
                id: "module1",
                name: "@skywind-group/module1",
                nameWithVersion: "@skywind-group/module1@1.1.1"
            }, { test: "SOMEDATA" } as any);

            const result2 = await getGameInitSettingsModel()
                .findAll()
                .then((result: GameInitSettingsDBInstance[]) => result.map(x => x.toJSON()));
            expect(result2).deep.equal(result1);
        });

        it("checks all if saved", async () => {
            await getGameInitSettingsModel().create({
                gameId: "module3",
                version: "3.1.1",
                data: { data: "NEWDATA" }
            });

            await gameInitSettings.checkAll([
                {
                    moduleName: {
                        version: "3.1.1",
                        name: "@skywind-group/newModule",
                        id: "newModule",
                        nameWithVersion: "@skywind-group/module3@3.1.1"
                    }
                }
            ] as CacheableGameModule[]);

            await gameInitSettings.checkIfSaved({
                version: "3.1.1",
                id: "module3",
                name: "@skywind-group/module3",
                nameWithVersion: "@skywind-group/module3@3.1.1"
            }, {} as any);

            const result = await getGameInitSettingsModel()
                .findAll()
                .then((item: GameInitSettingsDBInstance[]) => item.map(x => x.toJSON()));
            expect(result).deep.equal([
                {
                    "data": {
                        "data": "NEWDATA"
                    },
                    "gameId": "module3",
                    "version": "3.1.1",
                }
            ]);
        });

        it("checks if saved and processed duplicate", async () => {
            await getGameInitSettingsModel().create({
                gameId: "module4",
                version: "4.1.1",
                data: { data: "NEWDATA" }
            });

            await gameInitSettings.checkIfSaved({
                version: "4.1.1",
                id: "module4",
                name: "@skywind-group/module4",
                nameWithVersion: "@skywind-group/module4@4.1.1"
            }, {} as any);

            const result = await getGameInitSettingsModel()
                .findAll()
                .then((item: GameInitSettingsDBInstance[]) => item.map(x => x.toJSON()));
            expect(result).deep.equal([
                {
                    "data": {
                        "data": "NEWDATA"
                    },
                    "gameId": "module4",
                    "version": "4.1.1",
                }
            ]);
        });
    });

    describe("Remote storage", () => {
        let useRemote;
        let gameInitSettings: GameInitSettingsService;
        let request: RequestMock;

        before(() => {
            request = requestMock(superagent);
            useRemote = config.offlineStorage.useRemote;
            config.offlineStorage.useRemote = true;
            gameInitSettings = new GameInitSettingsService();
        });

        beforeEach(() => {
            request.clearRoutes();
        });

        after(() => {
            request.unmock(superagent);
            config.offlineStorage.useRemote = useRemote;
        });

        it("checks if saved", async () => {
            request.post("http://context-storage:4006/v1/initSettings", status200({
                version: "1.1.1",
                gameId: "module1",
                data: {
                    test: "SOMEDATA"
                }
            }));

            await gameInitSettings.checkIfSaved({
                version: "1.1.1",
                id: "module1",
                name: "@skywind-group/module1",
                nameWithVersion: "@skywind-group/module1@1.1.1"
            }, { test: "SOMEDATA" } as any);

            expect(request.args[0]).deep.equal({
                "url": "http://context-storage:4006/v1/initSettings",
                "body": {
                    "data": {
                        "test": "SOMEDATA"
                    },
                    "gameId": "module1",
                    "version": "1.1.1"
                },
                "query": {},
                "headers": {
                    "content-type": "application/json"
                },
                "method": "POST"
            });
        });

        it("checks all if saved", async () => {
            request.post("http://context-storage:4006/v1/initSettings/load", status200([
                {
                    gameId: "module3",
                    version: "3.1.1",
                    data: { data: "NEWDATA" }
                }
            ]));
            await gameInitSettings.checkAll([
                {
                    moduleName: {
                        version: "3.1.1",
                        name: "@skywind-group/newModule",
                        id: "newModule",
                        nameWithVersion: "@skywind-group/module3@3.1.1"
                    }
                }
            ] as CacheableGameModule[]);

            await gameInitSettings.checkIfSaved({
                version: "3.1.1",
                id: "module3",
                name: "@skywind-group/module3",
                nameWithVersion: "@skywind-group/module3@3.1.1"
            }, {} as any);
            expect(request.args[0]).deep.equal({
                "url": "http://context-storage:4006/v1/initSettings/load",
                "method": "POST",
                "query": {},
                "body": {
                    "0": {
                        "gameId": "newModule",
                        "version": "3.1.1"
                    }
                },
                "headers": {
                    "content-type": "application/json"
                }
            });
            expect(request.args.length).equal(1);
        });
    });
});
