import { expect, should, use } from "chai";
import { MerchantLogoutResult, SpecialState } from "../../../skywind/services/context/gamecontext";
import { GameContextID } from "../../../skywind/services/contextIds";
import { PaymentOperation } from "../../../skywind/services/wallet";
import GameRecoveryService from "../../../skywind/services/gamerecovery";
import { GameFinalizedError, ManagementAPITransientError, RoundNotFoundError } from "../../../skywind/errors";
import { getGameHistory, getRoundHistory } from "../../helper";
import { getArchiveContextModel } from "../../../skywind/services/offlinestorage/models";
import { testing } from "@skywind-group/sw-utils";
import { BaseGameRecoverySpec } from "./gamerecovery.spec";
import { suite, test } from "mocha-typescript";
import status200 = testing.status200;
import status500 = testing.status500;

should();
use(require("chai-as-promised"));

@suite()
class GameRecoveryRetrySpec extends BaseGameRecoverySpec {
    @test("fails to retry if game context not found")
    public async testFailedToRetry() {
        await expect(GameRecoveryService.retryPending({
            gameContextId: GameContextID.create("unknown", 404, "unknown", "unknown").asString(),
            round: "1"
        })).rejectedWith(RoundNotFoundError);
    }

    @test("round is closed already")
    public async testRoundIsClosed() {
        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "1"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: true, data: { positions: [6, 7, 8] } });
        await this.context.commitPendingModification();

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(roundHistory.length).equals(1);

        await expect(GameRecoveryService.retryPending({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "1"
        })).rejectedWith(RoundNotFoundError);

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(roundHistory.length).equals(1);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(0);
        await expect(this.contextManager.findGameContextById(this.gameID)).is.not.undefined;
    }

    @test("successful retry of pending modification")
    public async testSuccessfullRetry() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));

        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "TRX_ID",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "0"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: true, data: { positions: [6, 7, 8] } });

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);

        const result = await GameRecoveryService.retryPending({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "0"
        });

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.transactionId).equals("TRX_ID");

        expect(result).deep.equals({ result: "finished" });
        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(roundHistory.length).equals(1);
        expect(roundHistory[0].recoveryType).is.undefined;
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(0);
    }

    @test("successful retry of pending modification - clear special state: BROKEN_INTEGRATION")
    public async testClearBrokenIntegration() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));

        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "TRX_ID",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "0"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: false, data: { positions: [6, 7, 8] } });

        await this.context.setSpecialState(SpecialState.BROKEN_INTEGRATION);

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);

        const result = await GameRecoveryService.retryPending({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "0"
        });

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.transactionId).equals("TRX_ID");

        expect(result).deep.equals({ result: "finished" });
        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(roundHistory.length).equals(0);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(0);
        const context = await this.contextManager.findGameContextById(this.context.id);
        expect(context.specialState).is.undefined;
    }

    @test("forbid to retry/force-finish/revert in case of FINALISING state")
    public async testForbidIfFinalizing() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));

        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "TRX_ID",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "0"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: false, data: { positions: [6, 7, 8] } });

        await this.context.setSpecialState(SpecialState.FINALIZING);
        await expect(GameRecoveryService.retryPending({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "0"
        })).to.be.rejectedWith(GameFinalizedError);
        await expect(GameRecoveryService.forceFinish({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "0"
        })).to.be.rejectedWith(GameFinalizedError);
        await this.context.commitPendingModification();
        await expect(GameRecoveryService.revert({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "0"
        })).to.be.rejectedWith(GameFinalizedError);
    }

    @test("failure of retry of pending modification")
    public async testFailToRetry() {
        this.request.put("http://api:3006//v2/play/payment", status500());

        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "TRX_ID",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "0"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: true, data: { positions: [6, 7, 8] } });

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);

        await expect(GameRecoveryService.retryPending({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "0"
        })).rejectedWith(ManagementAPITransientError);

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.transactionId).equals("TRX_ID");

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(0);
        expect(await this.contextManager.findGameContextById(this.gameID)).to.exist;
    }

    @test("successful retry of logout")
    public async testRetryOfLogout() {
        this.request.post("http://api:3006//v2/play/game/logout", status200({}));

        await this.context.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);

        const result = await GameRecoveryService.retryPending({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "0"
        });

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/game/logout");

        expect(result).deep.equals({ result: "finished" });
        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(0);
    }

    @test("failure to retry of logout")
    public async testFailToRetryLogout() {
        this.request.post("http://api:3006//v2/play/game/logout", status500({}));

        await this.context.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);

        await expect(GameRecoveryService.retryPending({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "0"
        })).rejectedWith(ManagementAPITransientError);

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/game/logout");

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(0);
    }

    @test("successful retry of pending modification and then logout")
    public async testSuccessfullRetyrOfPaymentAndLogout() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/logout", status200({}));

        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "TRX_ID",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "0"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: true, data: { positions: [6, 7, 8] } });
        await this.context.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);

        const result = await GameRecoveryService.retryPending({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "0"
        });

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.transactionId).equals("TRX_ID");
        expect(this.request.args[1].url).equals("http://api:3006//v2/play/game/logout");

        expect(result).deep.equals({ result: "finished" });
        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(roundHistory.length).equals(1);
        expect(roundHistory[0].recoveryType).is.undefined;
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(0);
    }
}
