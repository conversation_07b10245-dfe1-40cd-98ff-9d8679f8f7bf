import { expect, should, use } from "chai";
import GameRecoveryService, { FinalizeBrokenPaymentType, RoundToRecover } from "../../../skywind/services/gamerecovery";
import { CannotFinalizePaymentIsIncompleteError, GameContextNotExists } from "../../../skywind/errors";
import { BaseGameRecoverySpec } from "./gamerecovery.spec";
import { suite, test } from "mocha-typescript";
import { testing } from "@skywind-group/sw-utils";
import { getGameHistory, getRoundHistory } from "../../helper";
import { getArchiveContextModel } from "../../../skywind/services/offlinestorage/models";
import { CleanupGameContextService } from "../../../skywind/services/cleanup/cleanupGameContextService";
import * as _ from "lodash";
import { SpecialState } from "../../../skywind/services/context/gamecontext";
import "chai-exclude";
import status200 = testing.status200;

should();
use(require("chai-as-promised"));
use(require("chai-exclude"));

@suite()
class GameRecoveryItgFinalizationSpec extends BaseGameRecoverySpec {
    constructor() {
        super();
        this.settings.splitPayment = true;
        this.gameData.gameTokenData.merchantSessionId = "test_merchant_session_id";
    }

    @test("fails to retry if game context not found")
    public async testFailedToStartFinalize() {
        await expect(GameRecoveryService.finalizeItgGame({
            brandId: 1,
            merchantSessionId: "uknown"
        })).rejectedWith(GameContextNotExists);
    }

    @test("finalize pending payment, mark finalized")
    public async finalizePendingPayment_MarkFinalized() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "finalize_game_transaction_id",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/finalize", testing.status(201));
        await this.context.updatePendingModification({
            actions: undefined,
            bet: 10000,
            creditActions: [
                {
                    action: "credit",
                    amount: 3000,
                    attribute: "balance"
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "xp",
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "vip",
                }
            ],
            currency: "USD",
            debitActions: [
                {
                    action: "debit",
                    amount: 10000,
                    attribute: "balance"
                }
            ],
            deviceId: "web",
            operation: "split-payment",
            roundEnded: true,
            roundId: 777,
            roundPID: "round_pid",
            transactionId: "debit_trx",
            win: 3000
        } as any, {} as any, {} as any, { type: "spin1", roundEnded: true } as any);

        const result = await GameRecoveryService.finalizeItgGame({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            brandFinalizationType: "roundStatistics",
            finalizeBrokenPayment: FinalizeBrokenPaymentType.MARK_FINALIZED
        });

        const roundStatistics = {
            balanceAfter: 100,
            balanceBefore: 100,
            totalBet: 10000,
            totalEvents: 1,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalWin: 3000,
        };

        expect(result).to.contain(roundStatistics);

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.actions).deep.equals([
            {
                action: "debit",
                amount: 10000,
                attribute: "balance",
            }
        ]);
        expect(this.request.args[0].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[0].body.offlineRetry).is.true;

        expect(this.request.args[1].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[1].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[1].body.offlineRetry).is.true;
        expect(this.request.args[1].body.actions).deep.equals([
            {
                action: "credit",
                amount: 3000,
                attribute: "balance"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "xp"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "vip"
            }
        ]);

        expect(this.request.args[2].url).equals("http://api:3006//v2/play/payment/transactionId");

        expect(this.request.args[3].url).equals("http://api:3006//v2/play/game/finalize");
        expect(this.request.args[3].body.transactionId).equal("finalize_game_transaction_id");
        expect(this.request.args[3].body.roundStatistics).contains(roundStatistics);

        const playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep.equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        const gameEvent = await getGameHistory(0, 100);
        const roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("finalize");
        expect(gameEvent[0].walletTransactionId).is.not.undefined;
        expect(roundHistory.length).equals(1);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        const ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx.round).is.undefined;
        expect(ctx.gameContext).is.undefined;
        expect(ctx.roundEnded).is.true;
    }

    @test("finalize pending payment, retry")
    public async finalizePendingPayment_Retry() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "finalize_game_transaction_id",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/finalize", testing.status(201));
        await this.context.updatePendingModification({
            actions: undefined,
            bet: 10000,
            creditActions: [
                {
                    action: "credit",
                    amount: 3000,
                    attribute: "balance"
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "xp",
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "vip",
                }
            ],
            currency: "USD",
            debitActions: [
                {
                    action: "debit",
                    amount: 10000,
                    attribute: "balance"
                }
            ],
            deviceId: "web",
            operation: "split-payment",
            roundEnded: true,
            roundId: 777,
            roundPID: "round_pid",
            transactionId: "debit_trx",
            win: 3000
        } as any, {} as any, {} as any, { type: "spin1", roundEnded: false } as any);
        const result = await GameRecoveryService.finalizeItgGame({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            finalizeBrokenPayment: FinalizeBrokenPaymentType.RETRY
        });

        const roundStatistics = {
            balanceAfter: 100,
            balanceBefore: 100,
            totalBet: 10000,
            totalEvents: 1,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalWin: 3000,
        };

        expect(result).to.contain(roundStatistics);

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.actions).deep.equals([
            {
                action: "debit",
                amount: 10000,
                attribute: "balance",
            }
        ]);
        expect(this.request.args[0].body.finalize).is.undefined;
        expect(this.request.args[0].body.offlineRetry).is.true;

        expect(this.request.args[1].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[1].body.finalize).is.undefined;
        expect(this.request.args[1].body.offlineRetry).is.true;
        expect(this.request.args[1].body.actions).deep.equals([
            {
                action: "credit",
                amount: 3000,
                attribute: "balance"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "xp"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "vip"
            }
        ]);
        expect(this.request.args[1].body.finalize).is.undefined;

        const playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep.equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        const gameEvent = await getGameHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(gameEvent[0].roundEnded).is.false;
        expect(gameEvent[0].type).eq("spin1");
        expect(gameEvent[0].walletTransactionId).is.not.undefined;
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        const ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx.round).to.deep.equal({
            balanceAfter: 100,
            balanceBefore: 100,
            betsCount: 1,
            currentBet: 10000,
            startedAt: ctx.round.startedAt,
            totalBet: 10000,
            totalEvents: 1,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalWin: 3000
        });
        expect(ctx.roundEnded).is.false;
    }

    @test("finalize pending payment, disable")
    public async finalizePendingPayment_disable() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/finalize", testing.status(201));
        await this.context.updatePendingModification({
            actions: undefined,
            bet: 10000,
            creditActions: [
                {
                    action: "credit",
                    amount: 3000,
                    attribute: "balance"
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "xp",
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "vip",
                }
            ],
            currency: "USD",
            debitActions: [
                {
                    action: "debit",
                    amount: 10000,
                    attribute: "balance"
                }
            ],
            deviceId: "web",
            operation: "split-payment",
            roundEnded: true,
            roundId: 777,
            roundPID: "round_pid",
            transactionId: "debit_trx",
            win: 3000
        } as any, {} as any, {} as any, { type: "spin1", roundEnded: true } as any);
        await expect(GameRecoveryService.finalizeItgGame({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            finalizeBrokenPayment: FinalizeBrokenPaymentType.DISABLE
        })).to.be.rejectedWith(CannotFinalizePaymentIsIncompleteError);

        expect(this.request.args).is.undefined;

        const playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep
            .equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        const ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx.pendingModification).is.not.undefined;

        const gameEvent = await getGameHistory(0, 100);
        const roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(0);
    }

    @test("finalize pending without payment")
    public async finalizePendingWithoutPayment() {
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "finalize_game_transaction_id",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/finalize", testing.status(201));
        await this.context.updatePendingModification(undefined,
            {} as any, {} as any, { type: "spin1", roundEnded: true } as any);
        await GameRecoveryService.finalizeItgGame({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });

        const playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep.equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        const gameEvent = await getGameHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(gameEvent[0].roundEnded).is.false;
        expect(gameEvent[0].type).eq("spin1");
        expect(gameEvent[0].walletTransactionId).is.undefined;
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        const ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx.round).to.deep.equal({
            betsCount: 0,
            currentBet: 0,
            startedAt: ctx.round.startedAt,
            totalBet: 0,
            totalEvents: 1,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalWin: 0
        });
        expect(ctx.roundEnded).is.false;
    }

    @test("finalize pending payment, gameContext unloaded")
    public async finalizePendingPaymentGameContextUnloaded() {
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "finalize_game_transaction_id",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/finalize", testing.status(201));
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        await this.context.updatePendingModification({
            actions: undefined,
            bet: 10000,
            creditActions: [
                {
                    action: "credit",
                    amount: 3000,
                    attribute: "balance"
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "xp",
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "vip",
                }
            ],
            currency: "USD",
            debitActions: [
                {
                    action: "debit",
                    amount: 10000,
                    attribute: "balance"
                }
            ],
            deviceId: "web",
            operation: "split-payment",
            roundEnded: true,
            roundId: 777,
            roundPID: "round_pid",
            transactionId: "debit_trx",
            win: 3000
        } as any, {} as any, {} as any, { type: "spin1", roundEnded: false } as any);
        await new CleanupGameContextService().cleanUp([this.context.id.asString()], true);
        let playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames).deep.equal([]);
        expect(playerContext.brokenGames.map(game => game.id.asString()))
            .deep
            .equal(["games:context:1:PLAYER1:GM0001:test"]);
        const result = await GameRecoveryService.finalizeItgGame({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            finalizeBrokenPayment: FinalizeBrokenPaymentType.RETRY
        });

        const roundStatistics = {
            balanceAfter: 100,
            balanceBefore: 100,
            totalBet: 10000,
            totalEvents: 1,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalWin: 3000,
        };

        expect(result).to.contain(roundStatistics);

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.actions).deep.equals([
            {
                action: "debit",
                amount: 10000,
                attribute: "balance",
            }
        ]);
        expect(this.request.args[0].body.finalize).is.undefined;
        expect(this.request.args[0].body.offlineRetry).is.true;

        expect(this.request.args[1].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[1].body.finalize).is.undefined;
        expect(this.request.args[1].body.offlineRetry).is.true;
        expect(this.request.args[1].body.actions).deep.equals([
            {
                action: "credit",
                amount: 3000,
                attribute: "balance"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "xp"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "vip"
            }
        ]);

        const gameEvent = await getGameHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(gameEvent[0].roundEnded).is.false;
        expect(gameEvent[0].type).eq("spin1");
        expect(gameEvent[0].walletTransactionId).is.not.undefined;
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        const ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx.round).to.deep.equal({
            ...roundStatistics,
            betsCount: 1,
            currentBet: 10000,
            startedAt: ctx.round.startedAt
        });
        expect(ctx.roundEnded).is.false;
        playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep
            .equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);
    }

    @test("finalize pending payment and jp contribution")
    public async finalizePaymentAndJPContribution() {
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "finalize_game_transaction_id",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/finalize", testing.status(201));
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        this.request.post("http://jackpot:5000/api/v2/jpn/contribute", status200({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: {
                            amount: 10.15
                        },
                        medium: {
                            amount: 100.2
                        },
                        large: {
                            amount: 1000.35
                        }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ],
                    totalContribution: 19
                }
            ]
        }));

        const jpnContext = {
            token: "jpn-auth-token",
            jackpotsInfo: [
                {
                    id: "jp-id", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, gameHistoryEnabled: true, paymentStatisticEnabled: true
                }
            ],
            contributionEnabled: true
        };
        await this.context.updateJackpotPendingModification(undefined, undefined, jpnContext);
        await this.context.updatePendingModification({
                actions: undefined,
                bet: 10000,
                creditActions: [
                    {
                        action: "credit",
                        amount: 3000,
                        attribute: "balance"
                    },
                    {
                        action: "credit",
                        amount: 1,
                        attribute: "xp",
                    },
                    {
                        action: "credit",
                        amount: 1,
                        attribute: "vip",
                    }
                ],
                currency: "USD",
                debitActions: [
                    {
                        action: "debit",
                        amount: 10000,
                        attribute: "balance"
                    }
                ],
                deviceId: "web",
                operation: "split-payment",
                roundEnded: true,
                roundId: 777,
                roundPID: "round_pid",
                transactionId: "debit_trx",
                win: 3000
            } as any, {} as any, {} as any, { type: "spin1", roundEnded: false } as any,
            {
                jackpotOperation: {
                    type: "contribution",
                    payload: {
                        transactionId: "jpn_trx_id",
                        bet: 10000
                    }
                }
            } as any
        );
        const result = await GameRecoveryService.finalizeItgGame({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            finalizeBrokenPayment: FinalizeBrokenPaymentType.RETRY
        });

        const roundStatistics = {
            balanceAfter: 100,
            balanceBefore: 100,
            totalBet: 10000,
            totalEvents: 1,
            betsCount: 1,
            totalJpContribution: 19,
            totalJpWin: 0,
            totalWin: 3000,
            currentBet: 10000,
            jpStatistic: {
                "jp-id": {
                    large: {
                        contribution: {
                            progressive: 5,
                            seed: 4
                        },
                        win: 0
                    },
                    medium: {
                        contribution: {
                            progressive: 4,
                            seed: 3,
                        },
                        win: 0
                    },
                    small: {
                        contribution: {
                            progressive: 2,
                            seed: 1,
                        },
                        win: 0
                    }
                }
            }
        };

        delete result.finishedAt;
        delete result.startedAt;
        expect(result).to.deep.equal(roundStatistics);

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.actions).deep.equals([
            {
                action: "debit",
                amount: 10000,
                attribute: "balance",
            }
        ]);
        expect(this.request.args[0].body.finalize).is.undefined;
        expect(this.request.args[0].body.offlineRetry).is.true;

        expect(this.request.args[1].url).equals("http://jackpot:5000/api/v2/jpn/contribute");

        expect(this.request.args[2].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[2].body.actions).deep.equals([
            {
                action: "credit",
                amount: 3000,
                attribute: "balance"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "xp"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "vip"
            }
        ]);
        expect(this.request.args[2].body.finalize).is.undefined;
        expect(this.request.args[2].body.offlineRetry).is.true;

        const gameEvent = await getGameHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(gameEvent[0].roundEnded).is.false;
        expect(gameEvent[0].type).eq("spin1");
        expect(gameEvent[0].walletTransactionId).is.not.undefined;
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        const ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx.round).to.deep.equal({
            ...roundStatistics,
            startedAt: ctx.round.startedAt
        });
        expect(ctx.roundEnded).is.false;
        const playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep.equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);
    }

    @test("finalize pending payment and jp contribution and jp win")
    public async finalizePaymentAndJPContributionAndWin() {
        this.request.post("http://api:3006//v2/play/game/finalize", testing.status(201));
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "new_trx_id"
        }));
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        this.request.post("http://jackpot:5000/api/v2/jpn/contribute", status200({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: {
                            amount: 10.15
                        },
                        medium: {
                            amount: 100.2
                        },
                        large: {
                            amount: 1000.35
                        }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ],
                    totalContribution: 19
                },
                {
                    type: "win",
                    jackpotId: "jp-id",
                    pool: "small",
                    amount: 10
                },
                {
                    type: "win",
                    jackpotId: "jp-id",
                    pool: "medium",
                    amount: 100
                }

            ]
        }));

        this.request.post("http://jackpot:5000/api/v2/jpn/win/confirm", status200({ events: [{
                type: "win-confirm",
                jackpotId: "jp-id",
                jackpotType: "test",
                transactionId: "jpn_trx_id",
            }]
        }));

        const jpnContext = {
            token: "jpn-auth-token",
            jackpotsInfo: [
                {
                    id: "jp-id", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, gameHistoryEnabled: true, paymentStatisticEnabled: true
                }
            ],
            contributionEnabled: true
        };
        await this.context.updateJackpotPendingModification(undefined, undefined, jpnContext);
        await this.context.updatePendingModification({
                actions: undefined,
                bet: 10000,
                creditActions: [
                    {
                        action: "credit",
                        amount: 3000,
                        attribute: "balance"
                    },
                    {
                        action: "credit",
                        amount: 1,
                        attribute: "xp",
                    },
                    {
                        action: "credit",
                        amount: 1,
                        attribute: "vip",
                    }
                ],
                currency: "USD",
                debitActions: [
                    {
                        action: "debit",
                        amount: 10000,
                        attribute: "balance"
                    }
                ],
                deviceId: "web",
                operation: "split-payment",
                roundEnded: true,
                roundId: 777,
                roundPID: "round_pid",
                transactionId: "debit_trx",
                win: 3000
            } as any, {} as any, {} as any, { type: "spin1", roundEnded: false } as any,
            {
                jackpotOperation: {
                    type: "contribution",
                    payload: {
                        transactionId: "jpn_trx_id",
                        bet: 10000
                    }
                }
            } as any
        );
        const result = await GameRecoveryService.finalizeItgGame({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            finalizeBrokenPayment: FinalizeBrokenPaymentType.MARK_FINALIZED
        });

        const roundStatistics = {
            balanceAfter: 100,
            balanceBefore: 100,
            totalBet: 10000,
            totalEvents: 2,
            totalJpContribution: 19,
            totalJpWin: 110,
            totalWin: 3110,
            betsCount: 1,
            currentBet: 10000,
            jpStatistic: {
                "jp-id": {
                    large: {
                        contribution: {
                            progressive: 5,
                            seed: 4
                        },
                        win: 0
                    },
                    medium: {
                        contribution: {
                            progressive: 4,
                            seed: 3,
                        },
                        win: 100
                    },
                    small: {
                        contribution: {
                            progressive: 2,
                            seed: 1,
                        },
                        win: 10
                    }
                }
            }
        };

        delete result.finishedAt;
        delete result.startedAt;
        expect(result).to.deep.equal(roundStatistics);

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.actions).deep.equals([
            {
                action: "debit",
                amount: 10000,
                attribute: "balance",
            }
        ]);
        expect(this.request.args[0].body.finalize).is.undefined;

        expect(this.request.args[1].url).equals("http://jackpot:5000/api/v2/jpn/contribute");

        expect(this.request.args[2].url).equals("http://api:3006//v2/play/payment/transactionId");

        expect(this.request.args[3].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[3].body.actions).deep.equals([
            {
                action: "credit",
                amount: 3000,
                attribute: "balance"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "xp"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "vip"
            }
        ]);
        expect(this.request.args[3].body.finalize).is.undefined;

        expect(this.request.args[4].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[4].body.actions).deep.equals([
            {
                action: "credit",
                amount: 10,
                attribute: "balance",
                changeType: "jackpot_win",
                jackpotId: "jp-id",
                pool: "small"
            },
            {
                action: "credit",
                amount: 100,
                attribute: "balance",
                changeType: "jackpot_win",
                jackpotId: "jp-id",
                pool: "medium"
            }
        ]);
        expect(this.request.args[4].body.finalize).is.undefined;

        expect(this.request.args[6].url).equals("http://api:3006//v2/play/payment/transactionId");

        expect(this.request.args[7].url).equals("http://api:3006//v2/play/game/finalize");
        expect(_.omit(this.request.args[7].body.roundStatistics, "startedAt")).deep.equal(roundStatistics);

        const gameEvent = await getGameHistory(0, 100);
        const roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(3);
        expect(gameEvent[2].roundEnded).is.false;
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("finalize");
        expect(gameEvent[0].walletTransactionId).is.not.undefined;
        expect(roundHistory.length).equals(1);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        const ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx.round).is.undefined;
        expect(ctx.gameContext).is.undefined;
        expect(ctx.roundEnded).is.true;
        const playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep.equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);
    }

    @test("finalize and invoke Game.finalizeRound()")
    public async finalizePendingWithoutPayment_GameFinalizeRound() {
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "finalize_game_transaction_id",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/finalize", testing.status(201));
        await this.context.updatePendingModification(undefined,
            {} as any, {} as any, { type: "spin1", roundEnded: true } as any);
        const result = await GameRecoveryService.finalizeItgGame({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            finalizeBrokenPayment: FinalizeBrokenPaymentType.MARK_FINALIZED
        });

        const roundStatistics = {
            totalBet: 0,
            totalEvents: 1,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalWin: 0
        };

        expect(result).to.contain(roundStatistics);

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment/transactionId");

        expect(this.request.args[1].url).equals("http://api:3006//v2/play/game/finalize");
        expect(this.request.args[1].body.roundStatistics).contains(roundStatistics);

        const playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep.equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        const gameEvent = await getGameHistory(0, 100);
        const roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("finalize");
        expect(gameEvent[0].walletTransactionId).is.not.undefined;
        expect(roundHistory.length).equals(1);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        const ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx.round).is.undefined;
        expect(ctx.roundEnded).is.true;
    }

    @test("finalize by round in request")
    public async finalizeByRoundInRequest() {
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "finalize_game_transaction_id",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/finalize", testing.status(201));
        await this.context.updatePendingModification(undefined,
            {} as any, {} as any, { type: "spin1", roundEnded: true } as any);
        await GameRecoveryService.finalizeItgGame({
            round: {
                brandId: this.context.id.brandId,
                gameCode: this.context.id.gameCode,
                playerCode: this.context.id.playerCode,
                device: this.context.id.deviceId
            } as RoundToRecover,
            finalizeBrokenPayment: FinalizeBrokenPaymentType.MARK_FINALIZED
        });

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment/transactionId");

        expect(this.request.args[1].url).equals("http://api:3006//v2/play/game/finalize");

        const playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep.equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        const gameEvent = await getGameHistory(0, 100);
        const roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("finalize");
        expect(gameEvent[0].walletTransactionId).is.not.undefined;
        expect(roundHistory.length).equals(1);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        const ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx.round).is.undefined;
        expect(ctx.roundEnded).is.true;
    }

    @test("finalize pending payment, flush CANNOT_COMPLETE_PAYMENT")
    public async finalizePendingPayment_Retry_Flush_Cannot_Complete_Payment() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "finalize_game_transaction_id",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/finalize", testing.status(201));
        await this.context.updatePendingModification({
            actions: undefined,
            bet: 10000,
            creditActions: [
                {
                    action: "credit",
                    amount: 3000,
                    attribute: "balance"
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "xp",
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "vip",
                }
            ],
            currency: "USD",
            debitActions: [
                {
                    action: "debit",
                    amount: 10000,
                    attribute: "balance"
                }
            ],
            deviceId: "web",
            operation: "split-payment",
            roundEnded: true,
            roundId: 777,
            roundPID: "round_pid",
            transactionId: "debit_trx",
            win: 3000
        } as any, {} as any, {} as any, { type: "spin1", roundEnded: false } as any);
        await this.context.setSpecialState(SpecialState.CANNOT_COMPLETE_PAYMENT);
        const result = await GameRecoveryService.finalizeItgGame({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            finalizeBrokenPayment: FinalizeBrokenPaymentType.MARK_FINALIZED
        });

        const roundStatistics = {
            balanceAfter: 100,
            balanceBefore: 100,
            totalBet: 10000,
            totalEvents: 1,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalWin: 3000,
        };

        expect(result).to.contain(roundStatistics);

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.actions).deep.equals([
            {
                action: "debit",
                amount: 10000,
                attribute: "balance",
            }
        ]);
        expect(this.request.args[0].body.finalize).is.undefined;
        expect(this.request.args[0].body.offlineRetry).is.true;

        expect(this.request.args[1].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[1].body.finalize).is.undefined;
        expect(this.request.args[1].body.offlineRetry).is.true;
        expect(this.request.args[1].body.actions).deep.equals([
            {
                action: "credit",
                amount: 3000,
                attribute: "balance"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "xp"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "vip"
            }
        ]);
        expect(this.request.args[1].body.finalize).is.undefined;

        expect(this.request.args[2].url).equals("http://api:3006//v2/play/payment/transactionId");

        expect(this.request.args[3].url).equals("http://api:3006//v2/play/game/finalize");
        expect(this.request.args[3].body.roundStatistics).contains(roundStatistics);
        expect(this.request.args[3].body.transactionId).contains("finalize_game_transaction_id");

        const playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(this.request.args[3].body.transactionId).equal("finalize_game_transaction_id");
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep.equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        const gameEvent = await getGameHistory(0, 100);
        const roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("finalize");
        expect(gameEvent[0].walletTransactionId).is.not.undefined;
        expect(roundHistory.length).equals(1);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        const ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx.round).is.undefined;
        expect(ctx.gameContext).is.undefined;
        expect(ctx.roundEnded).is.true;
        expect(ctx.specialState).is.undefined;
    }

    @test("finalize refund-payment (startItgFinalize and completeFinalize")
    public async finalizePendingRefundPayment() {
        this.request.delete("http://api:3006//v2/play/payment/refund-bet", status200({
            currency: "USD",
            main: 100,
        }));
        await this.context.updatePendingModification({
            actions: undefined,
            markForRefund: true,
            bet: 10000,
            creditActions: [
                {
                    action: "credit",
                    amount: 3000,
                    attribute: "balance"
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "xp",
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "vip",
                }
            ],
            currency: "USD",
            debitActions: [
                {
                    action: "debit",
                    amount: 10000,
                    attribute: "balance"
                }
            ],
            deviceId: "web",
            operation: "split-payment",
            roundEnded: true,
            roundId: 777,
            roundPID: "round_pid",
            transactionId: "debit_trx",
            win: 3000
        } as any, {} as any, {} as any, { type: "spin1", roundEnded: true } as any);

        const result = await GameRecoveryService.startItgFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });

        await GameRecoveryService.completeFinalize({brandId: 1,
            merchantSessionId: "test_merchant_session_id"});

        const roundStatistics = {
            balanceAfter: undefined,
            balanceBefore: undefined,
            totalBet: 10000,
            totalEvents: 1,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalWin: 0,
            refund: 10000
        };

        expect(result.roundStatistics).deep.eq(roundStatistics);
        expect(!!result.gameTokenData).is.true;
        expect(!!result.roundId).is.true;
        expect(!!result.roundPID).is.true;

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment/refund-bet");
        expect(this.request.args[0].body.actions).deep.equals([
            {
                action: "debit",
                amount: 10000,
                attribute: "balance",
            }
        ]);

        const roundHistory = await getRoundHistory(0, 100);
        expect(roundHistory.length).equals(0);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        const ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx).is.undefined;
    }

    @test("finalize pending payment - retry, on roundEnded - startItgFinalize")
    public async finalizePendingPayment_Retry_RoundEnded_startItgFinalize() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        await this.context.updatePendingModification({
            actions: undefined,
            bet: 10000,
            creditActions: [
                {
                    action: "credit",
                    amount: 3000,
                    attribute: "balance"
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "xp",
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "vip",
                }
            ],
            currency: "USD",
            debitActions: [
                {
                    action: "debit",
                    amount: 10000,
                    attribute: "balance"
                }
            ],
            deviceId: "web",
            operation: "split-payment",
            roundEnded: true,
            roundId: 777,
            roundPID: "round_pid",
            transactionId: "debit_trx",
            win: 3000
        } as any, undefined, {} as any, { type: "spin1", roundEnded: true } as any);
        await GameRecoveryService.startItgFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            finalizeBrokenPayment: FinalizeBrokenPaymentType.RETRY
        });

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.actions).deep.equals([
            {
                action: "debit",
                amount: 10000,
                attribute: "balance",
            }
        ]);
        expect(this.request.args[0].body.brandFinalizationType).is.undefined;
        expect(this.request.args[0].body.offlineRetry).is.true;

        expect(this.request.args[1].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[1].body.brandFinalizationType).is.undefined;
        expect(this.request.args[1].body.offlineRetry).is.true;
        expect(this.request.args[1].body.actions).deep.equals([
            {
                action: "credit",
                amount: 3000,
                attribute: "balance"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "xp"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "vip"
            }
        ]);

        const playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep.equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        const gameEvent = await getGameHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("spin1");
        expect(gameEvent[0].walletTransactionId).is.not.undefined;
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        const ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx.round).to.deep.equal( {
            balanceAfter: 100,
            balanceBefore: 100,
            totalBet: 10000,
            betsCount: 1,
            currentBet: 10000,
            totalEvents: 1,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalWin: 3000,
            startedAt: ctx.round.startedAt,
            finishedAt: ctx.round.finishedAt
        });
        expect(ctx.gameContext).is.undefined;
        expect(ctx.roundEnded).is.true;
    }

    @test("finalize refund-payment  of multibet(startFinalize and completeFinalize)")
    public async finalizePendingRefundPaymentWithMultiBet() {
        this.request.delete("http://api:3006//v2/play/payment/refund-bet", status200({
            currency: "USD",
            main: 100,
        }));
        await this.context.updatePendingModification({
            actions: undefined,
            bet: 10000,
            creditActions: [
                {
                    action: "credit",
                    amount: 3000,
                    attribute: "balance"
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "xp",
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "vip",
                }
            ],
            currency: "USD",
            debitActions: [
                {
                    action: "debit",
                    amount: 10000,
                    attribute: "balance"
                }
            ],
            deviceId: "web",
            operation: "split-payment",
            roundEnded: true,
            roundId: 777,
            roundPID: "round_pid",
            transactionId: "debit_trx",
            win: 3000
        } as any, {} as any, {} as any, { type: "spin1", roundEnded: false } as any);
        await this.context.commitPendingModification();

        await this.context.updatePendingModification({
            actions: undefined,
            markForRefund: true,
            bet: 100,
            creditActions: [
                {
                    action: "credit",
                    amount: 3000,
                    attribute: "balance"
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "xp",
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "vip",
                }
            ],
            currency: "USD",
            debitActions: [
                {
                    action: "debit",
                    amount: 100,
                    attribute: "balance"
                }
            ],
            deviceId: "web",
            operation: "split-payment",
            roundEnded: true,
            roundId: 777,
            roundPID: "round_pid",
            transactionId: "debit_trx",
            win: 3000
        } as any, {} as any, {} as any, { type: "spin1", roundEnded: true } as any);

        const result = await GameRecoveryService.startItgFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });

        await GameRecoveryService.completeFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });

        const roundStatistics = {
            refund: 100,
            totalBet: 10100,
            totalEvents: 1,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalWin: 3000
        };

        expect(result.roundStatistics).contains(roundStatistics);
        expect(!!result.gameTokenData).is.true;
        expect(!!result.roundId).is.true;
        expect(!!result.roundPID).is.true;

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment/refund-bet");
        expect(this.request.args[0].body.actions).deep.equals([
            {
                action: "debit",
                amount: 100,
                attribute: "balance",
            }
        ]);

        const roundHistory = await getRoundHistory(0, 100);
        expect(roundHistory.length).equals(1);
        expect(roundHistory).excludingEvery(["sessionId", "startedAt", "finishedAt", "id", "ctrl"]).deep.equal([
            {
                gameCode: "GM0001",
                brandId: 1,
                playerCode: "PLAYER1",
                deviceId: "test",
                currency: "USD",
                totalWin: 3000,
                totalBet: 10000,
                totalEvents: 1,
                broken: false,
                totalJpWin: 0,
                totalJpContribution: 0,
                ctrl: 1411276175,
                recoveryType: "finalize"
            }
        ]);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
    }

    @test("finalize pending payment - retry, on roundEnded - finalize-game request is not required")
    public async finalizePendingPayment_Retry_RoundEnded() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        await this.context.updatePendingModification({
            actions: undefined,
            bet: 10000,
            creditActions: [
                {
                    action: "credit",
                    amount: 3000,
                    attribute: "balance"
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "xp",
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "vip",
                }
            ],
            currency: "USD",
            debitActions: [
                {
                    action: "debit",
                    amount: 10000,
                    attribute: "balance"
                }
            ],
            deviceId: "web",
            operation: "split-payment",
            roundEnded: true,
            roundId: 777,
            roundPID: "round_pid",
            transactionId: "debit_trx",
            win: 3000
        } as any, undefined, {} as any, { type: "spin1", roundEnded: true } as any);
        await GameRecoveryService.finalizeItgGame({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            finalizeBrokenPayment: FinalizeBrokenPaymentType.MARK_FINALIZED
        });

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.actions).deep.equals([
            {
                action: "debit",
                amount: 10000,
                attribute: "balance",
            }
        ]);
        expect(this.request.args[0].body.brandFinalizationType).is.undefined;
        expect(this.request.args[0].body.offlineRetry).is.true;

        expect(this.request.args[1].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[1].body.brandFinalizationType).is.undefined;
        expect(this.request.args[1].body.offlineRetry).is.true;
        expect(this.request.args[1].body.actions).deep.equals([
            {
                action: "credit",
                amount: 3000,
                attribute: "balance"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "xp"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "vip"
            }
        ]);

        const playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep.equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        const gameEvent = await getGameHistory(0, 100);
        const roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("finalize");
        expect(gameEvent[0].walletTransactionId).is.not.undefined;
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[1].type).eq("spin1");
        expect(gameEvent[1].walletTransactionId).is.not.undefined;
        expect(roundHistory.length).equals(1);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        const ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx.round).is.undefined;
        expect(ctx.gameContext).is.undefined;
        expect(ctx.roundEnded).is.true;
    }

    @test("finalize pending payment with lockContext and MARK_FINALIZED - finalize-game request is required")
    public async finalizePendingPaymentWithLockContext() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/finalize", testing.status(201));
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "new_trx_id"
        }));
        await this.context.updatePendingModification({
            actions: undefined,
            bet: 10000,
            creditActions: [
                {
                    action: "credit",
                    amount: 3000,
                    attribute: "balance"
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "xp",
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "vip",
                }
            ],
            currency: "USD",
            debitActions: [
                {
                    action: "debit",
                    amount: 10000,
                    attribute: "balance"
                }
            ],
            deviceId: "web",
            operation: "split-payment",
            roundEnded: true,
            roundId: 777,
            roundPID: "round_pid",
            transactionId: "debit_trx",
            win: 3000
        } as any, undefined, {} as any, { type: "spin1", roundEnded: true } as any);
        await GameRecoveryService.finalizeItgGame({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            finalizeBrokenPayment: FinalizeBrokenPaymentType.MARK_FINALIZED,
            lockContext: true
        });

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[1].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[2].url).equals("http://api:3006//v2/play/payment/transactionId");
        expect(this.request.args[3].url).equals("http://api:3006//v2/play/game/finalize");

        const playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep.equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        const gameEvent = await getGameHistory(0, 100);
        const roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("finalize");
        expect(gameEvent[1].type).eq("spin1");
        expect(gameEvent[1].roundEnded).is.false;

        expect(roundHistory.length).equals(1);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        const ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx.round).is.undefined;
        expect(ctx.gameContext).is.undefined;
        expect(ctx.roundEnded).is.true;
    }

    @test("start finalize then complete finalize - merchant session should be deleted")
    public async finalizeMerchantSessionShouldBeDeleted() {
        await this.context.updatePendingModification(undefined,
            {} as any, {} as any, { type: "spin1", roundEnded: true } as any);
        await GameRecoveryService.startItgFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });
        await GameRecoveryService.completeFinalize({brandId: 1,
            merchantSessionId: "test_merchant_session_id"});
    }
}
