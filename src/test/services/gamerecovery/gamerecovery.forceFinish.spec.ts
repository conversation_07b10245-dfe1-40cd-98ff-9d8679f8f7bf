import { expect, should, use } from "chai";
import { JackpotPendingModification } from "../../../skywind/services/context/gamecontext";
import { GameContextID } from "../../../skywind/services/contextIds";
import { PaymentOperation, SplitPaymentOperation } from "../../../skywind/services/wallet";
import GameRecoveryService from "../../../skywind/services/gamerecovery";
import {
    ForbiddenToForceFinishContextError,
    ForbidToRecoverGameWithJPError,
    ForceFlagIsRequiredForOperationError,
    RoundNotFoundError
} from "../../../skywind/errors";
import { getGameHistory, getRoundHistory } from "../../helper";
import { getArchiveContextModel } from "../../../skywind/services/offlinestorage/models";
import { BaseGameRecoverySpec } from "./gamerecovery.spec";
import { suite, test } from "mocha-typescript";
import { testing } from "@skywind-group/sw-utils";
import status500 = testing.status500;
import status200 = testing.status200;
import { GameContextPersistencePolicy } from "@skywind-group/sw-game-core";

should();
use(require("chai-as-promised"));

@suite()
export class GameRecoveryForceFinishSpec extends BaseGameRecoverySpec {

    @test("fails to force finish game if game context not found")
    public async testForceFinishIfGameContextNotFounnd() {
        await expect(GameRecoveryService.forceFinish({
            gameContextId: GameContextID.create("unknown", 404, "unknown", "unknown").asString(),
            round: "1"
        })).rejectedWith(RoundNotFoundError);
    }

    @test("round is closed already")
    public async testFoundClosedAlready() {
        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "1"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: true, data: { positions: [6, 7, 8] } });
        await this.context.commitPendingModification();

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(roundHistory.length).equals(1);

        await expect(GameRecoveryService.forceFinish({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "1"
        })).rejectedWith(RoundNotFoundError);

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(roundHistory.length).equals(1);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(0);
        await expect(this.contextManager.findGameContextById(this.gameID)).is.not.undefined;
    }

    @test("failing to force-finish game context with long persistence policy")
    public async testForceFinishingRoundWithLongPersistencePolicy() {
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "finalize_game_transaction_id",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/finalize", testing.status(201));

        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "0"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: false, data: { positions: [6, 7, 8] } });
        await this.context.commitPendingModification();

        const gameEvent = await getGameHistory(0, 100);
        const roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(roundHistory.length).equals(0);

        this.context.persistencePolicy = GameContextPersistencePolicy.LONG_TERM;
        expect(GameRecoveryService.forceFinish({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "0"
        })).to.eventually.be.rejectedWith(ForbiddenToForceFinishContextError);
    }

    @test("closes round")
    public async testCloseRound() {
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "finalize_game_transaction_id",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/finalize", testing.status(201));

        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "0"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: false, data: { positions: [6, 7, 8] } });
        await this.context.commitPendingModification();

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(roundHistory.length).equals(0);

        const result = await GameRecoveryService.forceFinish({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "0"
        });

        delete result.roundStatistics.finishedAt;
        delete result.roundStatistics.startedAt;
        expect(result).deep.equals({
            result: "force-finished",
            roundStatistics: {
                "balanceBefore": undefined,
                "betsCount": 1,
                "currentBet": 100,
                "totalBet": 100,
                "totalEvents": 2,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                "totalWin": 20
            }
        });
        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[0].type).equals("force-finish");
        expect(roundHistory.length).equals(1);
        expect(roundHistory[0].recoveryType).equals("force-finish");
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("force-finish");
        expect(await this.contextManager.findGameContextById(this.gameID)).is.undefined;
    }

    @test("closes round - use round history")
    public async testCloseRoundUseRoundHistory() {
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "finalize_game_transaction_id",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/finalize", testing.status(201));

        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "0"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: false, data: { positions: [6, 7, 8] } });
        await this.context.commitPendingModification();

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(roundHistory.length).equals(0);

        const result = await GameRecoveryService.forceFinish({
            round: {
                roundId: "0",
                gameCode: this.gameID.gameCode,
                brandId: this.gameID.brandId,
                playerCode: this.gameID.playerCode,
                device: this.gameID.deviceId,
            } as any
        });

        delete result.roundStatistics.finishedAt;
        delete result.roundStatistics.startedAt;
        expect(result).deep.equals({
            result: "force-finished",
            roundStatistics: {
                "balanceBefore": undefined,
                "betsCount": 1,
                "currentBet": 100,
                "totalBet": 100,
                "totalEvents": 2,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                "totalWin": 20
            }
        });
        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[0].type).equals("force-finish");
        expect(roundHistory.length).equals(1);
        expect(roundHistory[0].recoveryType).equals("force-finish");
        expect(roundHistory[0].totalWin).equals(20);
        expect(roundHistory[0].totalBet).equals(100);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("force-finish");
        expect(await this.contextManager.findGameContextById(this.gameID)).is.undefined;
    }

    @test("closes round - mark reverted")
    public async testCloseRoundMarkReverted() {
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "finalize_game_transaction_id",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/finalize", testing.status(201));

        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "0"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: false, data: { positions: [6, 7, 8] } });
        await this.context.commitPendingModification();

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(roundHistory.length).equals(0);

        const result = await GameRecoveryService.forceFinish({
            round: {
                roundId: "0",
                gameCode: this.gameID.gameCode,
                brandId: this.gameID.brandId,
                playerCode: this.gameID.playerCode,
                device: this.gameID.deviceId,
            } as any,
            reverted: true
        });

        expect(result).deep.equals({ result: "force-finished" });
        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[0].type).equals("revert");
        expect(roundHistory.length).equals(1);
        expect(roundHistory[0].recoveryType).equals("revert");
        expect(roundHistory[0].totalWin).equals(0);
        expect(roundHistory[0].totalBet).equals(0);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("revert");
        expect(await this.contextManager.findGameContextById(this.gameID)).is.undefined;
    }

    @test("rejects force-finish without force flag")
    public async testRejectForceFinishWithoutForce() {
        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "1"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: true, data: { positions: [6, 7, 8] } },
            {
                jackpotOperation: {
                    type: "win-jackpot"
                }
            } as JackpotPendingModification);

        const gameEvent = await getGameHistory(0, 100);
        const roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);

        await (expect(GameRecoveryService.forceFinish({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "0"
        }))).rejectedWith(ForceFlagIsRequiredForOperationError);
    }

    @test("failed, there is JPN context")
    public async testForceFinishFailedWithJPNContext() {
        this.request.put("http://api:3006//v2/play/payment", status500());

        await this.context.updateJackpotPendingModification(undefined, undefined, {} as any);
        await this.context.updatePendingModification(
            {
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "1"
            } as PaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: true, data: { positions: [6, 7, 8] } },
            {
                jackpotOperation: {
                    type: "win-jackpot"
                }
            } as JackpotPendingModification);

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);

        await (expect(GameRecoveryService.forceFinish({
            gameContextId: GameContextID.create(this.gameID.gameCode,
                this.gameID.brandId,
                this.gameID.playerCode,
                this.gameID.deviceId)
                .asString(),
            round: "0", force: true
        }))).rejectedWith(ForbidToRecoverGameWithJPError);

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(0); // we don't archive if we don't attempt to revert
        expect(await this.contextManager.findGameContextById(this.gameID)).is.not.undefined;
    }

    @test("success when split payment and pending bet")
    public async testForceFinishSplitPaymentWithPendingBet() {
        this.request.put("http://api:3006//v2/play/payment", status500());
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "finalize_game_transaction_id",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/finalize", testing.status(201));

        await this.context.updateJackpotPendingModification(undefined, undefined, {} as any);
        await this.context.updatePendingModification(
            {
                operation: "split-payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "1"
            } as SplitPaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: true, data: { positions: [6, 7, 8] } },
            {
                jackpotOperation: {
                    type: "win-jackpot"
                }
            } as JackpotPendingModification);

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);

        const result = await GameRecoveryService.forceFinish({
            round: {
                roundId: "0",
                gameCode: this.gameID.gameCode,
                brandId: this.gameID.brandId,
                playerCode: this.gameID.playerCode,
                device: this.gameID.deviceId,
            } as any,
            force: true
        });

        expect(result).deep.equals({ result: "force-finished", roundStatistics: undefined });
        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(gameEvent[0].type).equals("force-finish");
        expect(roundHistory.length).equals(1);
        expect(roundHistory[0].recoveryType).equals("force-finish");
        expect(roundHistory[0].totalWin).equals(0);
        expect(roundHistory[0].totalBet).equals(0);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("force-finish");
        expect(await this.contextManager.findGameContextById(this.gameID)).is.undefined;
    }

    @test("success when split payment and pending bet and revert")
    public async testForceFinishSplitPaymentWithPendingBetRever() {
        this.request.put("http://api:3006//v2/play/payment", status500());
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "finalize_game_transaction_id",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/finalize", testing.status(201));

        await this.context.updateJackpotPendingModification(undefined, undefined, {} as any);
        await this.context.updatePendingModification(
            {
                operation: "split-payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD",
                roundId: "1"
            } as SplitPaymentOperation,
            {
                scene: "current",
                nextScene: "SOMESCENE"
            },
            { request: "req", requestId: 1 },
            { type: "slot", roundEnded: true, data: { positions: [6, 7, 8] } },
            {
                jackpotOperation: {
                    type: "win-jackpot"
                }
            } as JackpotPendingModification);

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);

        const result = await GameRecoveryService.forceFinish({
            round: {
                roundId: "0",
                gameCode: this.gameID.gameCode,
                brandId: this.gameID.brandId,
                playerCode: this.gameID.playerCode,
                device: this.gameID.deviceId,
            } as any,
            force: true,
            reverted: true
        });

        expect(result).deep.equals({ result: "force-finished" });
        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("revert");
        expect(await this.contextManager.findGameContextById(this.gameID)).is.undefined;
    }
}
