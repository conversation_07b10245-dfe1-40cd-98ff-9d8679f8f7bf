import { suite, test } from "mocha-typescript";
import { GameFlowContext } from "../../../skywind/services/context/gamecontext";
import { getArchiveContextModel } from "../../../skywind/services/offlinestorage/models";
import { expect } from "chai";
import { GameTokenData, Settings } from "../../../skywind/services/tokens";
import { GameContextID } from "../../../skywind/services/contextIds";
import { createGameToken, flushAll, getGameHistory, getRoundHistory, syncModels, TEST_MODULE_NAME } from "../../helper";
import { GameSession } from "../../../skywind/services/gameSession";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import GameRecoveryService from "../../../skywind/services/gamerecovery";
import { testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import requestMock = testing.requestMock;
import RequestMock = testing.RequestMock;
import status200 = testing.status200;
import { ContextManager } from "../../../skywind/services/contextmanager/contextManager";
import { GameData } from "../../../skywind/services/auth";
import { SinonStub, stub } from "sinon";
import config from "../../../skywind/config";
import * as GameService from "../../../skywind/services/game/game";
import { DumyGame } from "../../testGames";
import { init as initCurrencyExchange } from "../../../skywind/services/currencyexchange";
import { setRandomGeneratorFactory } from "../../..";
import { RandomGeneratorFactoryImpl } from "../../testRandomFactory";

@suite()
class GameRecoveryFinalizePlayMoneySpec {

    private contextManager: ContextManager = getGameFlowContextManager();
    private settings: Settings = {
        coins: [1],
        defaultCoin: 1,
        maxTotalStake: 500,
        stakeAll: [0.1, 0.5, 1, 2, 3, 5],
        stakeDef: 1,
        stakeMax: 10,
        stakeMin: 0.1,
        winMax: 3000000,
        currencyMultiplier: 100,
        splitPayment: true,
    };

    private gameID = GameContextID.create("GM0001", 1, "PLAYER1", "test", "play_money");

    private gameTokenData: GameTokenData = {
        playerCode: this.gameID.playerCode,
        gameCode: this.gameID.playerCode,
        brandId: this.gameID.brandId,
        currency: "XXX",
        playmode: "play_money",
        merchantSessionId: "test_merchant_session_id",
    };

    private gameData: GameData = {
        gameTokenData: this.gameTokenData,
        limits: this.settings
    };

    protected request: RequestMock;
    protected loadGame: SinonStub;
    protected context: GameFlowContext;
    protected session: GameSession;
    protected prevValue: boolean;

    public async before() {
        await syncModels();
        await flushAll();
        this.request = requestMock(superagent);
        this.prevValue = config.walletThroughAPI;
        config.walletThroughAPI = true;
        this.session = await GameSession.generate(this.gameID, "play_money");
        this.loadGame = stub(GameService, "load");
        const token = await createGameToken(this.gameTokenData);
        this.gameTokenData.token = token;
        this.context = await this.contextManager.findOrCreateGameContext(this.gameID,
            this.session,
            this.gameData,
            TEST_MODULE_NAME);
        await getArchiveContextModel().truncate();
        this.loadGame.returns(Promise.resolve({ name: this.gameID.gameCode, game: new DumyGame() }));
        await initCurrencyExchange();
        setRandomGeneratorFactory(new RandomGeneratorFactoryImpl());
    }

    public async after() {
        config.walletThroughAPI = this.prevValue;
        this.request.unmock(superagent);
        this.loadGame.restore();
    }

    @test("finalize pending payment, mark finalized")
    public async finalizePendingPayment_MarkFinalized() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "finalize_game_transaction_id",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/finalize", testing.status(201));
        await this.context.updatePendingModification({
            actions: undefined,
            bet: 10000,
            creditActions: [
                {
                    action: "credit",
                    amount: 3000,
                    attribute: "balance"
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "xp",
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "vip",
                }
            ],
            currency: "XXX",
            debitActions: [
                {
                    action: "debit",
                    amount: 10000,
                    attribute: "balance"
                }
            ],
            deviceId: "web",
            operation: "split-payment",
            roundEnded: true,
            roundId: 777,
            roundPID: "round_pid",
            transactionId: "debit_trx",
            win: 3000
        } as any, {} as any, {} as any, { type: "spin1", roundEnded: true } as any);
        const result = await GameRecoveryService.finalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            brandFinalizationType: "roundStatistics"
        });

        const roundStatistics = {
            balanceAfter: 100,
            balanceBefore: 100,
            totalBet: 10000,
            totalEvents: 1,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalWin: 3000,
        };

        expect(result).to.contain({
            ...roundStatistics,
            totalEvents: 2,
        });

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.actions).deep.equals([
            {
                action: "debit",
                amount: 10000,
                attribute: "balance",
            }
        ]);
        expect(this.request.args[0].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[0].body.offlineRetry).is.true;

        expect(this.request.args[1].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[1].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[1].body.offlineRetry).is.true;
        expect(this.request.args[1].body.actions).deep.equals([
            {
                action: "credit",
                amount: 3000,
                attribute: "balance"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "xp"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "vip"
            }
        ]);

        expect(this.request.args[2].url).equals("http://api:3006//v2/play/payment/transactionId");

        expect(this.request.args[3].url).equals("http://api:3006//v2/play/game/finalize");
        expect(this.request.args[3].body.transactionId).equal("finalize_game_transaction_id");
        expect(this.request.args[3].body.roundStatistics).contains(roundStatistics);

        const playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep.equal(["games:play_money:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        const gameEvent = await getGameHistory(0, 100);
        const roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        const ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx.round).is.undefined;
        expect(ctx.gameContext).is.undefined;
        expect(ctx.roundEnded).is.true;
    }

}
