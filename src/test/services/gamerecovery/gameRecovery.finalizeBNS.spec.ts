import { suite, test } from "mocha-typescript";
import { RoundStatistics } from "../../../skywind/services/context/gamecontext";
import { getGameContextModel } from "../../../skywind/services/offlinestorage/models";
import { expect } from "chai";
import { GameTokenData } from "../../../skywind/services/tokens";
import { GameContextID } from "../../../skywind/services/contextIds";
import { createGameToken, getRoundHistory, TEST_MODULE_NAME } from "../../helper";
import { Currency } from "@skywind-group/sw-game-core";
import { GameSession } from "../../../skywind/services/gameSession";
import { CleanupGameContextService } from "../../../skywind/services/cleanup/cleanupGameContextService";
import { GameFlowContextImpl } from "../../../skywind/services/context/gameContextImpl";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { Redis } from "../../../skywind/storage/redis";
import GameRecoveryService from "../../../skywind/services/gamerecovery";
import { testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import { BaseGameRecoverySpec } from "./gamerecovery.spec";
import requestMock = testing.requestMock;
import RequestMock = testing.RequestMock;
import status200 = testing.status200;

@suite()
class GameRecoveryFinalizeBNSSpec extends BaseGameRecoverySpec {
    private requestMock: RequestMock;

    public async before() {
        await super.before();
        this.requestMock = requestMock(superagent);
    }

    public async after() {
        await super.after();
        this.requestMock.unmock(superagent);
    }

    @test()
    public async finalizeBNSRound() {
        this.requestMock.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "finalize_game_transaction_id",
            main: 100,
        }));
        this.requestMock.post("http://api:3006//v2/play/game/finalize", testing.status(201));

        let ctx = await this.createContext("PLAYER1", "1", {
            startedAt: new Date(),
            roundEnded: false
        } as any, new Date().getTime());

        await new CleanupGameContextService().forceCleanUp(1, 10);

        await GameRecoveryService.finalize({
            gameContextId: ctx.id.asString(),
        });

        expect((await getGameContextModel().findAll()).length).equal(0);
        const rounds = await getRoundHistory(0, 1000);
        expect(rounds).deep.equals([
            {
                brandId: 1,
                broken: true,
                currency: "BNS",
                deviceId: "web",
                finishedAt: rounds[0].finishedAt,
                gameCode: "game_id",
                gameId: "gameId",
                ctrl: rounds[0].ctrl,
                id: "1",
                playerCode: "PLAYER1",
                sessionId: "2",
                recoveryType: "finalize",
                startedAt: rounds[0].startedAt,
                totalEvents: null
            },
            {
                brandId: 1,
                broken: true,
                currency: "BNS",
                deviceId: "web",
                gameCode: "game_id",
                gameId: "gameId",
                ctrl: rounds[1].ctrl,
                id: "1",
                playerCode: "PLAYER1",
                sessionId: "2",
                startedAt: rounds[1].startedAt
            }
        ]);

        ctx = await getGameFlowContextManager().findGameContextById(ctx.id);
        expect(ctx.round).is.undefined;
        expect(ctx.gameContext).is.undefined;
    }

    private async createContext(playerCode: string, roundId: string, round: RoundStatistics, expireAt: number) {
        const currency: Currency = 0;
        const settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: currency,
            stakeAll: [1, 2, 3, 4],
            stakeDef: currency,
            stakeMax: currency,
            stakeMin: currency,
            winMax: currency,
            currencyMultiplier: 100,
        };

        const gameID: GameContextID = GameContextID.create("gameId", 1, "playerId", "deviceId");

        const gameData = {
            gameTokenData: undefined,
            gameId: "gameId",
            limits: settings,
            bnsPromotion: {
                promoId: "1",
                expireAt: expireAt
            }
        };

        const gameTokenData: GameTokenData = {
            playerCode: gameID.playerCode,
            gameCode: "GM001",
            brandId: gameID.brandId,
            currency: "USD",
            playmode: "bns"
        };

        gameTokenData.token = await createGameToken(gameTokenData);
        gameData.gameTokenData = gameTokenData;
        const sessionId = await GameSession.generate(gameID, "bns");
        const newSessionId = await GameSession.generate(gameID, "bns");

        const id = GameContextID.create("game_id", 1, playerCode, "web", "bns");
        const context: GameFlowContextImpl = new GameFlowContextImpl(id);
        context.gameVersion = "1";
        context.gameContext = {
            scenesState: {},
            currentScene: "scene",
            nextScene: "next",
            history: [],
            stake: null,
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };
        context.session = sessionId;
        context.lastRequestId = 1;
        context.gameSerialNumber = 1;
        context.totalEventId = 1;
        context.settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: 0,
            stakeAll: [1, 2, 3, 4],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 100,
            transferEnabled: false,
        };
        context.version = 3;
        context.gameData = gameData;
        context.round = round;
        context.roundId = roundId;
        context.createdAt = new Date();
        context.updatedAt = new Date();

        const newSettings = {
            coins: [5, 6],
            defaultCoin: 5,
            maxTotalStake: 0,
            stakeAll: [5, 6, 7, 8],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 1000,
            transferEnabled: false,
        };

        return getGameFlowContextManager().createContextFrom(id,
            context,
            newSessionId,
            gameData,
            TEST_MODULE_NAME,
            newSettings);
    }

    private async clearRoundEvent() {
        const client = await Redis.get().get();
        try {
            await client.del("games:rounds");
        } finally {
            await Redis.get().release(client);
        }
    }

}
