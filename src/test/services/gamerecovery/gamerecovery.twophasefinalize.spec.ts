import { expect, should, use } from "chai";
import GameRecoveryService from "../../../skywind/services/gamerecovery";
import { GameContextNotExists } from "../../../skywind/errors";
import { BaseGameRecoverySpec } from "./gamerecovery.spec";
import { suite, test } from "mocha-typescript";
import { testing } from "@skywind-group/sw-utils";
import { getGameHistory, getRoundHistory, TEST_MODULE_NAME } from "../../helper";
import { getArchiveContextModel } from "../../../skywind/services/offlinestorage/models";
import * as _ from "lodash";
import { CleanupGameContextService } from "../../../skywind/services/cleanup/cleanupGameContextService";
import { SpecialState } from "../../../skywind/services/context/gamecontext";
import { DummyGameWithFinalizeGame } from "../../testGames";
import { GameContext } from "@skywind-group/sw-game-core";
import status200 = testing.status200;

should();
use(require("chai-as-promised"));

@suite()
class GameTwoPhaseFinalizationSpec extends BaseGameRecoverySpec {
    constructor() {
        super();
        this.settings.splitPayment = true;
        this.gameData.gameTokenData.merchantSessionId = "test_merchant_session_id";
    }

    @test("fails to retry if game context not found")
    public async testFailedToStartFinalize() {
        await expect(GameRecoveryService.startFinalize({
            brandId: 1,
            merchantSessionId: "uknown"
        })).rejectedWith(GameContextNotExists);
    }

    @test("finalize pending payment")
    public async finalizePendingPayment() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        await this.context.updatePendingModification({
            actions: undefined,
            bet: 10000,
            creditActions: [
                {
                    action: "credit",
                    amount: 3000,
                    attribute: "balance"
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "xp",
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "vip",
                }
            ],
            currency: "USD",
            debitActions: [
                {
                    action: "debit",
                    amount: 10000,
                    attribute: "balance"
                }
            ],
            deviceId: "web",
            operation: "split-payment",
            roundEnded: true,
            roundId: 777,
            roundPID: "round_pid",
            transactionId: "debit_trx",
            win: 3000
        } as any, {} as any, {} as any, { type: "spin1", roundEnded: true } as any);
        const result = await GameRecoveryService.startFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            brandFinalizationType: "roundStatistics"
        });

        const roundStatistics = {
            balanceAfter: 100,
            balanceBefore: 100,
            totalBet: 10000,
            betsCount: 1,
            totalEvents: 1,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalWin: 3000,
            currentBet: 10000,
        };
        expect(_.omit(result, "roundStatistics.startedAt", "roundStatistics.finishedAt", "gameTokenData"))
            .deep
            .equals({
                gameContextId: "games:context:1:PLAYER1:GM0001:test",
                currency: "USD",
                roundStatistics,
                roundPID: "doRlLRrM",
                roundId: "0"
            });
        expect(result.gameTokenData).is.not.undefined;

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.actions).deep.equals([
            {
                action: "debit",
                amount: 10000,
                attribute: "balance",
            }
        ]);
        expect(this.request.args[0].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[0].body.offlineRetry).is.true;

        expect(this.request.args[1].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[1].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[1].body.offlineRetry).is.true;
        expect(this.request.args[1].body.actions).deep.equals([
            {
                action: "credit",
                amount: 3000,
                attribute: "balance"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "xp"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "vip"
            }
        ]);

        let playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep
            .equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(gameEvent[0].roundEnded).is.false;
        expect(roundHistory.length).equals(0);
        let archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        let ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(_.omit(ctx.round, "startedAt", "finishedAt")).deep.equals(roundStatistics);
        expect(ctx.roundId).equal("0");

        await GameRecoveryService.completeFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("finalize");
        expect(roundHistory.length).equals(1);
        archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx).is.undefined;
        playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames).deep.equal([]);
        expect(playerContext.brokenGames).deep.equal([]);
    }

    @test("finalize pending payment, double start")
    public async finalizePendingPayment_DuplicateStart() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        await this.context.updatePendingModification({
            actions: undefined,
            bet: 10000,
            creditActions: [
                {
                    action: "credit",
                    amount: 3000,
                    attribute: "balance"
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "xp",
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "vip",
                }
            ],
            currency: "USD",
            debitActions: [
                {
                    action: "debit",
                    amount: 10000,
                    attribute: "balance"
                }
            ],
            deviceId: "web",
            operation: "split-payment",
            roundEnded: true,
            roundId: 777,
            roundPID: "round_pid",
            transactionId: "debit_trx",
            win: 3000
        } as any, {} as any, {} as any, { type: "spin1", roundEnded: true } as any);
        const result = await GameRecoveryService.startFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            brandFinalizationType: "roundStatistics"
        });

        const roundStatistics = {
            balanceAfter: 100,
            balanceBefore: 100,
            totalBet: 10000,
            totalEvents: 1,
            totalJpContribution: 0,
            betsCount: 1,
            totalJpWin: 0,
            totalWin: 3000,
            currentBet: 10000,
        };
        expect(_.omit(result, "roundStatistics.startedAt", "roundStatistics.finishedAt", "gameTokenData"))
            .deep
            .equals({
                gameContextId: "games:context:1:PLAYER1:GM0001:test",
                currency: "USD",
                roundStatistics,
                roundPID: "doRlLRrM",
                roundId: "0"
            });
        expect(result.gameTokenData).is.not.undefined;

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.actions).deep.equals([
            {
                action: "debit",
                amount: 10000,
                attribute: "balance",
            }
        ]);
        expect(this.request.args[0].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[1].body.offlineRetry).is.true;

        expect(this.request.args[1].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[1].body.offlineRetry).is.true;
        expect(this.request.args[1].body.actions).deep.equals([
                {
                    action: "credit",
                    amount: 3000,
                    attribute: "balance"
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "xp"
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "vip"
                }
            ]
        );
        expect(this.request.args[1].body.finalizationType).to.equal("roundStatistics");
        this.request.clearRoutes();
        const result1 = await GameRecoveryService.startFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });
        expect(_.omit(result1, "roundStatistics.startedAt", "roundStatistics.finishedAt", "gameTokenData"))
            .deep
            .equals({
                gameContextId: "games:context:1:PLAYER1:GM0001:test",
                currency: "USD",
                roundStatistics,
                roundPID: "doRlLRrM",
                roundId: "0"
            });

        let playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep
            .equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(gameEvent[0].roundEnded).is.false;
        expect(roundHistory.length).equals(0);
        let archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        let ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(_.omit(ctx.round, "startedAt", "finishedAt")).deep.equals(roundStatistics);
        expect(ctx.roundId).equal("0");

        await GameRecoveryService.completeFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("finalize");
        expect(roundHistory.length).equals(1);
        archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx).is.undefined;
        playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames).deep.equal([]);
        expect(playerContext.brokenGames).deep.equal([]);
    }

    @test("finalize pending payment, skip if BROKEN_INTEGRATION on first bet")
    public async finalizePendingPayment_SkipBrokenIntegration() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        await this.context.updatePendingModification({
            actions: undefined,
            bet: 10000,
            creditActions: [
                {
                    action: "credit",
                    amount: 3000,
                    attribute: "balance"
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "xp",
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "vip",
                }
            ],
            currency: "USD",
            debitActions: [
                {
                    action: "debit",
                    amount: 10000,
                    attribute: "balance"
                }
            ],
            deviceId: "web",
            operation: "split-payment",
            roundEnded: true,
            roundId: 777,
            roundPID: "round_pid",
            transactionId: "debit_trx",
            win: 3000
        } as any, {} as any, {} as any, { type: "spin1", roundEnded: true } as any);

        await this.context.setSpecialState(SpecialState.BROKEN_INTEGRATION);

        const result = await GameRecoveryService.startFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });

        expect(_.omit(result)).deep.equals({
            gameContextId: "games:context:1:PLAYER1:GM0001:test",
            currency: "USD",
            roundStatistics: undefined,
            roundId: undefined,
            roundPID: undefined,
            gameTokenData: undefined
        });

        expect(this.request.args).is.undefined;

        let playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep
            .equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);
        let archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        let ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx.round).is.undefined;
        expect(ctx.gameContext).is.undefined;

        await GameRecoveryService.completeFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);
        archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx).is.undefined;
        playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames).deep.equal([]);
        expect(playerContext.brokenGames).deep.equal([]);
    }

    @test("finalize pending payment, process BROKEN_INTEGRATION")
    public async finalizePendingPayment_ProcessBrokenIntegration() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        await this.context.updatePendingModification({
            actions: undefined,
            bet: 10000,
            creditActions: [
                {
                    action: "credit",
                    amount: 3000,
                    attribute: "balance"
                },
            ],
            currency: "USD",
            debitActions: [
                {
                    action: "debit",
                    amount: 10000,
                    attribute: "balance"
                }
            ],
            deviceId: "web",
            operation: "split-payment",
            roundEnded: true,
            balanceAfterDebit: 1000,
            roundId: 777,
            roundPID: "round_pid",
            transactionId: "debit_trx",
            win: 3000
        } as any, {} as any, {} as any, { type: "spin1", roundEnded: true } as any);
        await this.context.setSpecialState(SpecialState.BROKEN_INTEGRATION);
        const result = await GameRecoveryService.startFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            brandFinalizationType: "roundStatistics"
        });

        const roundStatistics = {
            balanceAfter: 100,
            balanceBefore: 100,
            totalBet: 10000,
            totalEvents: 1,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalWin: 3000,
            currentBet: 10000,
            betsCount: 1,
        };
        expect(_.omit(result, "roundStatistics.startedAt", "roundStatistics.finishedAt", "gameTokenData")).deep.equals({
            gameContextId: "games:context:1:PLAYER1:GM0001:test",
            currency: "USD",
            roundStatistics,
            roundPID: "doRlLRrM",
            roundId: "0"
        });
        expect(result.gameTokenData).is.not.undefined;

        expect(this.request.args[0].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[0].body.offlineRetry).is.true;
        expect(this.request.args[0].body.actions).deep.equals([
            {
                action: "credit",
                amount: 3000,
                attribute: "balance"
            }
        ]);

        let playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep
            .equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(gameEvent[0].roundEnded).is.false;
        expect(roundHistory.length).equals(0);
        let archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        let ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(_.omit(ctx.round, "startedAt", "finishedAt")).deep.equals(roundStatistics);
        expect(ctx.roundId).equal("0");

        await GameRecoveryService.completeFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("finalize");
        expect(roundHistory.length).equals(1);
        archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx).is.undefined;
        playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames).deep.equal([]);
        expect(playerContext.brokenGames).deep.equal([]);
    }

    @test("finalize pending without payment")
    public async finalizePendingWithoutPayment() {
        await this.context.updatePendingModification(undefined,
            {} as any, {} as any, { type: "spin1", roundEnded: true } as any);
        const result = await GameRecoveryService.startFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });

        expect(_.omit(result, "roundStatistics.startedAt", "roundStatistics.finishedAt", "gameTokenData"))
            .deep.equals({
            gameContextId: "games:context:1:PLAYER1:GM0001:test",
            currency: "USD",
            roundStatistics: {
                balanceAfter: undefined,
                balanceBefore: undefined,
                totalBet: 0,
                totalEvents: 1,
                totalJpContribution: 0,
                totalJpWin: 0,
                totalWin: 0,
                currentBet: 0,
                betsCount: 0
            },
            roundPID: "doRlLRrM",
            roundId: "0"
        });
        expect(result.gameTokenData).is.not.undefined;

        let playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep
            .equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(gameEvent[0].roundEnded).is.false;
        expect(roundHistory.length).equals(0);
        let archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        let ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(_.omit(ctx.round, "startedAt", "finishedAt")).deep.equals({
            totalBet: 0,
            totalEvents: 1,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalWin: 0,
            currentBet: 0,
            betsCount: 0
        });
        expect(ctx.roundId).equal("0");

        await GameRecoveryService.completeFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("finalize");
        expect(roundHistory.length).equals(1);
        archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx).is.undefined;
        playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames).deep.equal([]);
        expect(playerContext.brokenGames).deep.equal([]);
    }

    @test("finalize and call Game.finalizeGame without payments")
    public async finalizePendingWithoutPayment_FinalizeGame() {
        const newState: GameContext = { type: "context_after_finalize" };
        this.loadGame.returns(Promise.resolve({
            name: this.gameID.gameCode,
            game: new DummyGameWithFinalizeGame({ context: newState })
        }));
        await this.context.updatePendingModification(undefined,
            {} as any, {} as any, { type: "spin1", roundEnded: true } as any);
        const result = await GameRecoveryService.startFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });

        expect(_.omit(result, "roundStatistics.startedAt", "roundStatistics.finishedAt", "gameTokenData"))
            .deep.equals({
            gameContextId: "games:context:1:PLAYER1:GM0001:test",
            currency: "USD",
            roundStatistics: {
                balanceAfter: undefined,
                balanceBefore: undefined,
                totalBet: 0,
                totalEvents: 1,
                totalJpContribution: 0,
                totalJpWin: 0,
                totalWin: 0,
                currentBet: 0,
                betsCount: 0
            },
            roundPID: "doRlLRrM",
            roundId: "0"
        });
        expect(result.gameTokenData).is.not.undefined;

        let playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep
            .equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(gameEvent[0].roundEnded).is.false;
        expect(roundHistory.length).equals(0);
        let archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        let ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(_.omit(ctx.round, "startedAt", "finishedAt")).deep.equals({
            totalBet: 0,
            totalEvents: 1,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalWin: 0,
            currentBet: 0,
            betsCount: 0
        });
        expect(ctx.roundId).equal("0");

        await GameRecoveryService.completeFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("finalize");
        expect(roundHistory.length).equals(1);
        archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx).is.not.undefined;
        expect(ctx.round).is.undefined;
        expect(ctx.specialState).is.undefined;
        expect(ctx.gameContext).deep.equal(newState);

        playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(g => g.id.asString())).deep.equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);
    }

    @test("finalize and call Game.finalizeGame skip payments if no flag")
    public async finalizeGame_SkipPayment() {
        const newState: GameContext = { type: "context_after_finalize" };
        this.loadGame.returns(Promise.resolve({
            name: this.gameID.gameCode,
            game: new DummyGameWithFinalizeGame({
                context: newState,
                payment: {
                    actions: [
                        { action: "debit", attribute: "balance", amount: 0 },
                        { action: "credit", attribute: "balance", amount: 1000 }
                    ]
                },
                history: { type: "last_change", data: { type: "guaranteed_winnings" }, roundEnded: true }
            })
        }));
        await this.context.updatePendingModification(undefined,
            {} as any, {} as any, { type: "spin1", roundEnded: true } as any);
        const result = await GameRecoveryService.startFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });

        expect(_.omit(result, "roundStatistics.startedAt", "roundStatistics.finishedAt", "gameTokenData"))
            .deep.equals({
            gameContextId: "games:context:1:PLAYER1:GM0001:test",
            currency: "USD",
            roundStatistics: {
                balanceAfter: undefined,
                balanceBefore: undefined,
                totalBet: 0,
                totalEvents: 1,
                totalJpContribution: 0,
                totalJpWin: 0,
                totalWin: 0,
                currentBet: 0,
                betsCount: 0
            },
            roundPID: "doRlLRrM",
            roundId: "0"
        });
        expect(result.gameTokenData).is.not.undefined;

        let playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep
            .equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(gameEvent[0].roundEnded).is.false;
        expect(roundHistory.length).equals(0);
        let archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        let ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(_.omit(ctx.round, "startedAt", "finishedAt")).deep.equals({
            totalBet: 0,
            totalEvents: 1,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalWin: 0,
            currentBet: 0,
            betsCount: 0
        });
        expect(ctx.roundId).equal("0");

        await GameRecoveryService.completeFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });
        expect(this.request.args).is.undefined;

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("finalize");
        expect(roundHistory.length).equals(1);
        archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx).is.not.undefined;
        expect(ctx.round).is.undefined;
        expect(ctx.specialState).is.undefined;
        expect(ctx.gameContext).deep.equal(newState);

        playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(g => g.id.asString())).deep.equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);
    }

    @test("finalize and call Game.finalizeGame with payments")
    public async finalizeGame_With_payments() {
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "new_trx_id"
        }));
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        const newState: GameContext = { type: "context_after_finalize" };
        this.loadGame.returns(Promise.resolve({
            name: this.gameID.gameCode,
            game: new DummyGameWithFinalizeGame({
                context: newState,
                payment: {
                    actions: [
                        { action: "debit", attribute: "balance", amount: 0 },
                        { action: "credit", attribute: "balance", amount: 1000 }
                    ]
                },
                history: { type: "last_change", data: { type: "guaranteed_winnings" }, roundEnded: true }
            })
        }));
        await this.context.updatePendingModification(undefined,
            {} as any, {} as any, { type: "spin1", roundEnded: true } as any);
        const result = await GameRecoveryService.startFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            operatorSupportsFinalization: true,
            brandFinalizationType: "roundStatistics"
        });

        expect(_.omit(result, "roundStatistics.startedAt", "roundStatistics.finishedAt", "gameTokenData"))
            .deep.equals({
            gameContextId: "games:context:1:PLAYER1:GM0001:test",
            currency: "USD",
            roundStatistics: {
                balanceAfter: 100,
                balanceBefore: 100,
                totalBet: 0,
                totalEvents: 2,
                totalJpContribution: 0,
                totalJpWin: 0,
                totalWin: 1000,
                currentBet: 0,
                betsCount: 0
            },
            roundPID: "doRlLRrM",
            roundId: "0"
        });
        expect(result.gameTokenData).is.not.undefined;

        let playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep
            .equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[0].roundEnded).is.false;
        expect(gameEvent[0].result).deep.equals({ type: "guaranteed_winnings" });
        expect(gameEvent[0].bet).eq(0);
        expect(gameEvent[0].win).eq(1000);
        expect(gameEvent[0].type).eq("last_change");
        let archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        let ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(_.omit(ctx.round, "startedAt", "finishedAt")).deep.equals({
            balanceAfter: 100,
            balanceBefore: 100,
            totalBet: 0,
            totalEvents: 2,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalWin: 1000,
            currentBet: 0,
            betsCount: 0
        });
        expect(ctx.roundId).equal("0");

        await GameRecoveryService.completeFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            operatorSupportsFinalization: true
        });

        expect(this.request.args.length).equal(3);
        expect(this.request.args[1].url).eq("http://api:3006//v2/play/payment");
        expect(this.request.args[1].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[1].body.transactionId).eq("new_trx_id");
        expect(this.request.args[1].body.offlineRetry).is.true;
        expect(this.request.args[1].body.actions).deep.eq([{ action: "debit", attribute: "balance", amount: 0 }]);
        expect(this.request.args[2].url).eq("http://api:3006//v2/play/payment");
        expect(this.request.args[2].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[2].body.transactionId).eq("new_trx_id");
        expect(this.request.args[2].body.offlineRetry).is.true;
        expect(this.request.args[2].body.actions).deep.eq([{ action: "credit", attribute: "balance", amount: 1000 }]);

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(3);
        expect(gameEvent[2].roundEnded).is.false;
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[1].result).deep.equals({ type: "guaranteed_winnings" });
        expect(gameEvent[1].bet).eq(0);
        expect(gameEvent[1].win).eq(1000);
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("finalize");
        expect(roundHistory.length).equals(1);
        archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx).is.not.undefined;
        expect(ctx.round).is.undefined;
        expect(ctx.specialState).is.undefined;
        expect(ctx.gameContext).deep.equal(newState);

        playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(g => g.id.asString())).deep.equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);
    }

    @test("finalize and call Game.finalizeGame with payments double start")
    public async finalizeGame_With_payments_double_start() {
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "new_trx_id"
        }));
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        const newState: GameContext = null;
        this.loadGame.returns(Promise.resolve({
            name: this.gameID.gameCode,
            game: new DummyGameWithFinalizeGame({
                context: newState,
                payment: {
                    actions: [
                        { action: "debit", attribute: "balance", amount: 0 },
                        { action: "credit", attribute: "balance", amount: 1000 }
                    ]
                },
                history: { type: "last_change", data: { type: "guaranteed_winnings" }, roundEnded: true }
            })
        }));
        await this.context.updatePendingModification(undefined,
            {} as any, {} as any, { type: "spin1", roundEnded: true } as any);

        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "new_trx_id"
        }));
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));

        const result = await GameRecoveryService.startFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            operatorSupportsFinalization: true,
            brandFinalizationType: "roundStatistics"
        });

        expect(_.omit(result, "roundStatistics.startedAt", "roundStatistics.finishedAt", "gameTokenData"))
            .deep.equals({
            gameContextId: "games:context:1:PLAYER1:GM0001:test",
            currency: "USD",
            roundStatistics: {
                balanceAfter: 100,
                balanceBefore: 100,
                totalBet: 0,
                totalEvents: 2,
                totalJpContribution: 0,
                totalJpWin: 0,
                totalWin: 1000,
                currentBet: 0,
                betsCount: 0
            },
            roundPID: "doRlLRrM",
            roundId: "0"
        });
        expect(result.gameTokenData).is.not.undefined;

        const playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep
            .equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[0].roundEnded).is.false;
        expect(gameEvent[0].result).deep.equals({ type: "guaranteed_winnings" });
        expect(gameEvent[0].bet).eq(0);
        expect(gameEvent[0].win).eq(1000);
        expect(gameEvent[0].type).eq("last_change");
        let archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        let ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(_.omit(ctx.round, "startedAt", "finishedAt")).deep.equals({
            balanceAfter: 100,
            balanceBefore: 100,
            totalBet: 0,
            totalEvents: 2,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalWin: 1000,
            currentBet: 0,
            betsCount: 0
        });
        expect(ctx.roundId).equal("0");

        const doubleStart = await GameRecoveryService.startFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            operatorSupportsFinalization: true
        });
        expect(_.omit(doubleStart, "roundStatistics.totalEvents"))
            .deep
            .eq(_.omit(result, "roundStatistics.totalEvents"));
        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(3);
        expect(gameEvent[0].roundEnded).is.false;
        expect(gameEvent[0].result).deep.equals({});
        expect(gameEvent[0].bet).eq(0);
        expect(gameEvent[0].win).eq(0);
        expect(gameEvent[0].type).eq("finalize");
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[1].result).deep.equals({ type: "guaranteed_winnings" });
        expect(gameEvent[1].bet).eq(0);
        expect(gameEvent[1].win).eq(1000);
        expect(gameEvent[1].type).eq("last_change");

        await GameRecoveryService.completeFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            operatorSupportsFinalization: true
        });

        expect(this.request.args.length).equal(6);
        expect(this.request.args[1].url).eq("http://api:3006//v2/play/payment");
        expect(this.request.args[1].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[1].body.transactionId).eq("new_trx_id");
        expect(this.request.args[1].body.offlineRetry).is.true;
        expect(this.request.args[1].body.actions).deep.eq([{ action: "debit", attribute: "balance", amount: 0 }]);
        expect(this.request.args[2].url).eq("http://api:3006//v2/play/payment");
        expect(this.request.args[2].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[2].body.transactionId).eq("new_trx_id");
        expect(this.request.args[2].body.offlineRetry).is.true;
        expect(this.request.args[2].body.actions).deep.eq([{ action: "credit", attribute: "balance", amount: 1000 }]);

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(4);
        expect(gameEvent[3].roundEnded).is.false;
        expect(gameEvent[3].roundEnded).is.false;
        expect(gameEvent[2].result).deep.equals({ type: "guaranteed_winnings" });
        expect(gameEvent[2].bet).eq(0);
        expect(gameEvent[2].win).eq(1000);
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[1].type).eq("finalize");
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("finalize");
        expect(roundHistory.length).equals(1);
        archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx).is.undefined;
    }

    @test("finalize and call Game.finalizeGame with payments and jackpotWin")
    public async finalizeGameRound_With_payments_And_JPWin() {
        this.context = await this.contextManager.findOrCreateGameContext(this.gameID,
            this.session,
            this.gameData,
            TEST_MODULE_NAME, undefined, undefined, {
                token: "jptoken",
                contributionEnabled: true,
                jackpotsInfo: [
                    {
                        id: "super-jackpot",
                        currency: "EUR",
                        gameHistoryEnabled: true,
                        paymentStatisticEnabled: true
                    }
                ]
            } as any);
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "new_trx_id"
        }));
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));

        this.request.get("http://jackpot:5000/api/v2/jpn/contribute/transactionId",
            status200({ transactionId: "jpn_tr_id" }));
        this.request.post("http://jackpot:5000/api/v2/jpn/win", status200({
            events: [
                {
                    type: "win",
                    transactionId: "jpn_tr_id",
                    pool: "small",
                    amount: 1000,
                    jackpotId: "super-jackpot",
                }
            ]
        }));
        this.request.post("http://jackpot:5000/api/v2/jpn/win/confirm", status200({ events: [] }));
        const newState: GameContext = { type: "context_after_finalize" };
        this.loadGame.returns(Promise.resolve({
            name: this.gameID.gameCode,
            game: new DummyGameWithFinalizeGame({
                context: newState,
                payment: {
                    actions: [
                        { action: "debit", attribute: "balance", amount: 0 },
                        { action: "credit", attribute: "balance", amount: 1000 }
                    ]
                },
                jackpotAction: {
                    type: "win-jackpot",
                    jackpotId: "super-jackpot",
                    amount: 1000
                } as any,

                history: { type: "last_change", data: { type: "guaranteed_winnings" }, roundEnded: true }
            })
        }));

        await this.context.updatePendingModification(undefined,
            {} as any, {} as any, { type: "spin1", roundEnded: true } as any);
        const result = await GameRecoveryService.startFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            operatorSupportsFinalization: true,
            brandFinalizationType: "roundStatistics"
        });

        expect(_.omit(result, "roundStatistics.startedAt", "roundStatistics.finishedAt", "gameTokenData"))
            .deep.equals({
            gameContextId: "games:context:1:PLAYER1:GM0001:test",
            currency: "USD",
            roundStatistics: {
                balanceAfter: 100,
                balanceBefore: 100,
                totalBet: 0,
                betsCount: 0,
                totalEvents: 3,
                currentBet: 0,
                jpStatistic: {
                    "super-jackpot": {
                        small: {
                            contribution: {
                                progressive: 0,
                                seed: 0
                            },
                            win: 1000
                        }
                    }
                },
                totalJpContribution: 0,
                totalJpWin: 1000,
                totalWin: 2000,
            },
            roundPID: "doRlLRrM",
            roundId: "0"
        });
        expect(result.gameTokenData).is.not.undefined;

        let playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep
            .equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(3);
        expect(gameEvent[0].roundEnded).is.false;
        expect(gameEvent[0].type).eq("jackpot-win");
        expect(gameEvent[0].totalJpWin).eq(1000);
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[1].result).deep.equals({ type: "guaranteed_winnings" });
        expect(gameEvent[1].bet).eq(0);
        expect(gameEvent[1].win).eq(1000);
        expect(gameEvent[1].type).eq("last_change");
        expect(roundHistory.length).equals(0);
        let archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        let ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(_.omit(ctx.round, "startedAt", "finishedAt")).deep.equals({
            balanceAfter: 100,
            balanceBefore: 100,
            totalBet: 0,
            betsCount: 0,
            totalEvents: 3,
            jpStatistic: {
                "super-jackpot": {
                    small: {
                        contribution: {
                            progressive: 0,
                            seed: 0
                        },
                        win: 1000
                    }
                }
            },
            totalJpContribution: 0,
            totalJpWin: 1000,
            totalWin: 2000,
            currentBet: 0
        });
        expect(ctx.roundId).equal("0");

        await GameRecoveryService.completeFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            operatorSupportsFinalization: true
        });

        expect(this.request.args.length).equal(8);

        expect(this.request.args[2].url).eq("http://api:3006//v2/play/payment");
        expect(this.request.args[2].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[2].body.transactionId).eq("new_trx_id");
        expect(this.request.args[2].body.offlineRetry).is.true;
        expect(this.request.args[2].body.actions).deep.eq([{ action: "debit", attribute: "balance", amount: 0 }]);
        expect(this.request.args[2].url).eq("http://api:3006//v2/play/payment");

        expect(this.request.args[3].url).eq("http://jackpot:5000/api/v2/jpn/win");
        expect(this.request.args[3].body).contains({
            "type": "win-jackpot",
            "jackpotId": "super-jackpot",
            "amount": 1000,
            "transactionId": "jpn_tr_id",
            "externalId": "new_trx_id",
        });

        expect(this.request.args[5].url).eq("http://api:3006//v2/play/payment");
        expect(this.request.args[5].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[5].body.transactionId).eq("new_trx_id");
        expect(this.request.args[5].body.offlineRetry).is.true;
        expect(this.request.args[5].body.actions).deep.eq([{ action: "credit", attribute: "balance", amount: 1000 }]);

        expect(this.request.args[6].url).eq("http://api:3006//v2/play/payment");
        expect(this.request.args[6].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[6].body.transactionId).eq("new_trx_id");
        expect(this.request.args[6].body.offlineRetry).is.true;
        expect(this.request.args[6].body.actions).deep.eq([
            {
                action: "credit", attribute: "balance", amount: 1000,
                changeType: "jackpot_win",
                jackpotId: "super-jackpot",
                pool: "small"
            }
        ]);

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(4);
        expect(gameEvent[2].roundEnded).is.false;
        expect(gameEvent[2].result).deep.equals({ type: "guaranteed_winnings" });
        expect(gameEvent[2].bet).eq(0);
        expect(gameEvent[2].win).eq(1000);
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[1].win).eq(1000);
        expect(gameEvent[1].type).eq("jackpot-win");
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("finalize");
        expect(roundHistory.length).equals(1);
        archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx).is.not.undefined;
        expect(ctx.round).is.undefined;
        expect(ctx.specialState).is.undefined;
        expect(ctx.gameContext).deep.equal(newState);

        playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames.map(g => g.id.asString())).deep.equal(["games:context:1:PLAYER1:GM0001:test"]);
        expect(playerContext.brokenGames).deep.equal([]);
    }

    @test("finalize pending payment, gameContext unloaded")
    public async finalizePendingPaymentGameContextUnloaded() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        await this.context.updatePendingModification({
            actions: undefined,
            bet: 10000,
            creditActions: [
                {
                    action: "credit",
                    amount: 3000,
                    attribute: "balance"
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "xp",
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "vip",
                }
            ],
            currency: "USD",
            debitActions: [
                {
                    action: "debit",
                    amount: 10000,
                    attribute: "balance"
                }
            ],
            deviceId: "web",
            operation: "split-payment",
            roundEnded: true,
            roundId: 777,
            roundPID: "round_pid",
            transactionId: "debit_trx",
            win: 3000
        } as any, {} as any, {} as any, { type: "spin1", roundEnded: true } as any);
        await new CleanupGameContextService().cleanUp([this.context.id.asString()], true);
        let playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames).deep.equal([]);
        expect(playerContext.brokenGames.map(game => game.id.asString()))
            .deep
            .equal(["games:context:1:PLAYER1:GM0001:test"]);
        const result = await GameRecoveryService.startFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            brandFinalizationType: "roundStatistics"
        });

        const roundStatistics = {
            balanceAfter: 100,
            balanceBefore: 100,
            totalBet: 10000,
            totalEvents: 1,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalWin: 3000,
            currentBet: 10000,
            betsCount: 1
        };
        expect(_.omit(result, "roundStatistics.startedAt", "roundStatistics.finishedAt", "gameTokenData")).deep.equals({
            gameContextId: "games:context:1:PLAYER1:GM0001:test",
            currency: "USD",
            roundStatistics,
            roundPID: "doRlLRrM",
            roundId: "0"
        });
        expect(result.gameTokenData).is.not.undefined;

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.actions).deep.equals([
            {
                action: "debit",
                amount: 10000,
                attribute: "balance",
            }
        ]);
        expect(this.request.args[0].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[0].body.offlineRetry).is.true;

        expect(this.request.args[1].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[1].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[1].body.offlineRetry).is.true;
        expect(this.request.args[1].body.actions).deep.equals([
            {
                action: "credit",
                amount: 3000,
                attribute: "balance"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "xp"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "vip"
            }
        ]);

        playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.brokenGames).deep.equal([]);
        expect(playerContext.activeGames.map(game => game.id.asString()))
            .deep
            .equal(["games:context:1:PLAYER1:GM0001:test"]);

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(gameEvent[0].roundEnded).is.false;
        expect(roundHistory.length).equals(0);
        let archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        let ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(_.omit(ctx.round, "startedAt", "finishedAt")).deep.equals(roundStatistics);
        expect(ctx.roundId).equal("0");

        await GameRecoveryService.completeFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("finalize");
        expect(roundHistory.length).equals(1);
        archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx).is.undefined;
        playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames).deep.equal([]);
        expect(playerContext.brokenGames).deep.equal([]);
    }

    @test("finalize if not start-finalize before")
    public async finalizeNotStarted() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        await this.context.updatePendingModification({
            actions: undefined,
            bet: 10000,
            creditActions: [
                {
                    action: "credit",
                    amount: 3000,
                    attribute: "balance"
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "xp",
                },
                {
                    action: "credit",
                    amount: 1,
                    attribute: "vip",
                }
            ],
            currency: "USD",
            debitActions: [
                {
                    action: "debit",
                    amount: 10000,
                    attribute: "balance"
                }
            ],
            deviceId: "web",
            operation: "split-payment",
            roundEnded: true,
            roundId: 777,
            roundPID: "round_pid",
            transactionId: "debit_trx",
            win: 3000
        } as any, {} as any, {} as any, { type: "spin1", roundEnded: true } as any);

        expect(this.request.args).is.undefined;
        await GameRecoveryService.completeFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });

        const gameEvent = await getGameHistory(0, 100);
        const roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(0);
        expect(roundHistory.length).equals(0);
        const archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(0);
        const ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx.roundId).equal("0");
    }

    @test("finalize pending payment and jp contribution")
    public async finalizePaymentAndJPContribution() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        this.request.post("http://jackpot:5000/api/v2/jpn/contribute", status200({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: {
                            amount: 10.15
                        },
                        medium: {
                            amount: 100.2
                        },
                        large: {
                            amount: 1000.35
                        }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ],
                    totalContribution: 19
                }
            ]
        }));

        const jpnContext = {
            token: "jpn-auth-token",
            jackpotsInfo: [
                {
                    id: "jp-id", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, paymentStatisticEnabled: true, gameHistoryEnabled: true
                }
            ],
            contributionEnabled: true
        };
        await this.context.updateJackpotPendingModification(undefined, undefined, jpnContext);
        await this.context.updatePendingModification({
                actions: undefined,
                bet: 10000,
                creditActions: [
                    {
                        action: "credit",
                        amount: 3000,
                        attribute: "balance"
                    },
                    {
                        action: "credit",
                        amount: 1,
                        attribute: "xp",
                    },
                    {
                        action: "credit",
                        amount: 1,
                        attribute: "vip",
                    }
                ],
                currency: "USD",
                debitActions: [
                    {
                        action: "debit",
                        amount: 10000,
                        attribute: "balance"
                    }
                ],
                deviceId: "web",
                operation: "split-payment",
                roundEnded: true,
                roundId: 777,
                roundPID: "round_pid",
                transactionId: "debit_trx",
                win: 3000
            } as any, {} as any, {} as any, { type: "spin1", roundEnded: true } as any,
            {
                jackpotOperation: {
                    type: "contribution",
                    payload: {
                        transactionId: "jpn_trx_id",
                        bet: 10000
                    }
                }
            } as any
        );
        const result = await GameRecoveryService.startFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            brandFinalizationType: "roundStatistics"
        });

        const roundStatistics = {
            balanceAfter: 100,
            balanceBefore: 100,
            totalBet: 10000,
            totalEvents: 1,
            totalJpContribution: 19,
            totalJpWin: 0,
            totalWin: 3000,
            currentBet: 10000,
            betsCount: 1,
            jpStatistic: {
                "jp-id": {
                    large: {
                        contribution: {
                            progressive: 5,
                            seed: 4
                        },
                        win: 0
                    },
                    medium: {
                        contribution: {
                            progressive: 4,
                            seed: 3,
                        },
                        win: 0
                    },
                    small: {
                        contribution: {
                            progressive: 2,
                            seed: 1,
                        },
                        win: 0
                    }
                }
            }
        };
        expect(_.omit(result, "roundStatistics.startedAt", "roundStatistics.finishedAt", "gameTokenData"))
            .deep.equals({
            gameContextId: "games:context:1:PLAYER1:GM0001:test",
            currency: "USD",
            roundStatistics,
            roundPID: "doRlLRrM",
            roundId: "0"
        });
        expect(result.gameTokenData).is.not.undefined;

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.actions).deep.equals([
            {
                action: "debit",
                amount: 10000,
                attribute: "balance",
            }
        ]);
        expect(this.request.args[0].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[0].body.offlineRetry).is.true;

        expect(this.request.args[1].url).equals("http://jackpot:5000/api/v2/jpn/contribute");

        expect(this.request.args[2].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[2].body.actions).deep.equals([
            {
                action: "credit",
                amount: 3000,
                attribute: "balance"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "xp"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "vip"
            }
        ]);
        expect(this.request.args[2].body.finalizationType).to.equal("roundStatistics");
        expect(this.request.args[2].body.offlineRetry).is.true;

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(1);
        expect(gameEvent[0].roundEnded).is.false;
        expect(roundHistory.length).equals(0);
        let archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        let ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(_.omit(ctx.round, "startedAt", "finishedAt")).deep.equals(roundStatistics);
        expect(ctx.roundId).equal("0");

        await GameRecoveryService.completeFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("finalize");
        expect(roundHistory.length).equals(1);
        archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx).is.undefined;
        const playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames).deep.equal([]);
        expect(playerContext.brokenGames).deep.equal([]);
    }

    @test("finalize pending payment and jp contribution and jp win")
    public async finalizePaymentAndJPContributionAndWin() {
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            transactionId: "new_trx_id"
        }));
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        this.request.post("http://jackpot:5000/api/v2/jpn/contribute", status200({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: {
                            amount: 10.15
                        },
                        medium: {
                            amount: 100.2
                        },
                        large: {
                            amount: 1000.35
                        }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ],
                    totalContribution: 19
                },
                {
                    type: "win",
                    jackpotId: "jp-id",
                    pool: "small",
                    amount: 10
                },
                {
                    type: "win",
                    jackpotId: "jp-id",
                    pool: "medium",
                    amount: 100
                }

            ]
        }));

        this.request.post("http://jackpot:5000/api/v2/jpn/win/confirm", status200({
            events: [
                {
                    type: "win-confirm",
                    jackpotId: "jp-id",
                    jackpotType: "test",
                    transactionId: "jpn_trx_id",
                }
            ]
        }));

        const jpnContext = {
            token: "jpn-auth-token",
            jackpotsInfo: [
                {
                    id: "jp-id", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, paymentStatisticEnabled: true, gameHistoryEnabled: true
                }
            ],
            contributionEnabled: true
        };
        await this.context.updateJackpotPendingModification(undefined, undefined, jpnContext);
        await this.context.updatePendingModification({
                actions: undefined,
                bet: 10000,
                creditActions: [
                    {
                        action: "credit",
                        amount: 3000,
                        attribute: "balance"
                    },
                    {
                        action: "credit",
                        amount: 1,
                        attribute: "xp",
                    },
                    {
                        action: "credit",
                        amount: 1,
                        attribute: "vip",
                    }
                ],
                currency: "USD",
                debitActions: [
                    {
                        action: "debit",
                        amount: 10000,
                        attribute: "balance"
                    }
                ],
                deviceId: "web",
                operation: "split-payment",
                roundEnded: true,
                roundId: 777,
                roundPID: "round_pid",
                transactionId: "debit_trx",
                win: 3000
            } as any, {} as any, {} as any, { type: "spin1", roundEnded: true } as any,
            {
                jackpotOperation: {
                    type: "contribution",
                    payload: {
                        transactionId: "jpn_trx_id",
                        bet: 10000
                    }
                }
            } as any
        );
        const result = await GameRecoveryService.startFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id",
            brandFinalizationType: "roundStatistics"
        });

        const roundStatistics = {
            balanceAfter: 100,
            balanceBefore: 100,
            totalBet: 10000,
            totalEvents: 2,
            totalJpContribution: 19,
            totalJpWin: 110,
            totalWin: 3110,
            currentBet: 10000,
            betsCount: 1,
            jpStatistic: {
                "jp-id": {
                    large: {
                        contribution: {
                            progressive: 5,
                            seed: 4
                        },
                        win: 0
                    },
                    medium: {
                        contribution: {
                            progressive: 4,
                            seed: 3,
                        },
                        win: 100
                    },
                    small: {
                        contribution: {
                            progressive: 2,
                            seed: 1,
                        },
                        win: 10
                    }
                }
            }
        };
        expect(_.omit(result, "roundStatistics.startedAt", "roundStatistics.finishedAt", "gameTokenData")).deep.equals({
            gameContextId: "games:context:1:PLAYER1:GM0001:test",
            currency: "USD",
            roundStatistics,
            roundPID: "doRlLRrM",
            roundId: "0"
        });
        expect(result.gameTokenData).is.not.undefined;

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.actions).deep.equals([
            {
                action: "debit",
                amount: 10000,
                attribute: "balance",
            }
        ]);
        expect(this.request.args[0].body.finalizationType).to.equal("roundStatistics");

        expect(this.request.args[1].url).equals("http://jackpot:5000/api/v2/jpn/contribute");

        expect(this.request.args[2].url).equals("http://api:3006//v2/play/payment/transactionId");

        expect(this.request.args[3].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[3].body.actions).deep.equals([
            {
                action: "credit",
                amount: 3000,
                attribute: "balance"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "xp"
            },
            {
                action: "credit",
                amount: 1,
                attribute: "vip"
            }
        ]);
        expect(this.request.args[3].body.finalizationType).to.equal("roundStatistics");

        expect(this.request.args[4].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[4].body.actions).deep.equals([
            {
                action: "credit",
                amount: 10,
                attribute: "balance",
                changeType: "jackpot_win",
                jackpotId: "jp-id",
                pool: "small"
            },
            {
                action: "credit",
                amount: 100,
                attribute: "balance",
                changeType: "jackpot_win",
                jackpotId: "jp-id",
                pool: "medium"
            }
        ]);
        expect(this.request.args[4].body.finalizationType).to.equal("roundStatistics");

        let gameEvent = await getGameHistory(0, 100);
        let roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(2);
        expect(gameEvent[0].roundEnded).is.false;
        expect(gameEvent[1].roundEnded).is.false;
        expect(roundHistory.length).equals(0);
        let archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        let ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(_.omit(ctx.round, "startedAt", "finishedAt")).deep.equals(roundStatistics);
        expect(ctx.roundId).equal("0");

        await GameRecoveryService.completeFinalize({
            brandId: 1,
            merchantSessionId: "test_merchant_session_id"
        });

        gameEvent = await getGameHistory(0, 100);
        roundHistory = await getRoundHistory(0, 100);
        expect(gameEvent.length).equals(3);
        expect(gameEvent[2].roundEnded).is.false;
        expect(gameEvent[1].roundEnded).is.false;
        expect(gameEvent[0].roundEnded).is.true;
        expect(gameEvent[0].type).eq("finalize");
        expect(roundHistory.length).equals(1);
        archived = await getArchiveContextModel().findAll();
        expect(archived.length).equals(1);
        expect(archived[0].recoveryType).equals("finalize");
        ctx = await this.contextManager.findGameContextById(this.context.id);
        expect(ctx).is.undefined;
        const playerContext = await this.contextManager.findPlayerContextById(this.context.id.playerContextID);
        expect(playerContext.activeGames).deep.equal([]);
        expect(playerContext.brokenGames).deep.equal([]);
    }
}
