import { suite, test } from "mocha-typescript";
import { GameData } from "../../skywind/services/auth";
import { createGameToken, flushAll, hGetAll, syncModels, TEST_MODULE_NAME } from "../helper";
import { GameTokenData } from "../../skywind/services/tokens";
import { GameSession } from "../../skywind/services/gameSession";
import { GameContextID } from "../../skywind/services/contextIds";
import { Currency } from "@skywind-group/sw-game-core";
import { expect, use } from "chai";
import { getGameFlowContextManager } from "../../skywind/services/contextmanager/contextManagerImpl";
import { getGameContextModel, getPlayerContextModel } from "../../skywind/services/offlinestorage/models";
import { PaymentOperation } from "../../skywind/services/wallet";
import { CleanupGameContextService } from "../../skywind/services/cleanup/cleanupGameContextService";
import { GameFlowContextImpl } from "../../skywind/services/context/gameContextImpl";
import * as _ from "lodash";
import MigrationState from "../../skywind/services/cleanup/forceCleanupJobStateTracker";
import { EnvIdChangedError } from "../../skywind/errors";

use(require("chai-as-promised"));

@suite()
class ContextManagerFindOrCreateSpec {
    private contextManager = getGameFlowContextManager();
    private currency: Currency = 0;
    private settings = {
        coins: [3, 4],
        defaultCoin: 4,
        maxTotalStake: this.currency,
        stakeAll: [1, 2, 3, 4],
        stakeDef: this.currency,
        stakeMax: this.currency,
        stakeMin: this.currency,
        winMax: this.currency,
        currencyMultiplier: 100,
    };

    private gameID: GameContextID = GameContextID.create("gameId", 1, "playerId", "deviceId");

    private gameData: GameData = {
        gameTokenData: undefined,
        gameId: "gameId",
        limits: this.settings
    };

    private gameTokenData: GameTokenData = {
        playerCode: this.gameID.playerCode,
        gameCode: this.gameID.gameCode,
        brandId: this.gameID.brandId,
        currency: "USD",
        playmode: "real"
    };

    private sessionId: GameSession;

    public async before() {
        this.gameTokenData.token = await createGameToken(this.gameTokenData);
        this.gameData.gameTokenData = this.gameTokenData;
        this.sessionId = await GameSession.generate(this.gameID, "real");

        await flushAll();
        await syncModels();
        await getPlayerContextModel().truncate();
        await getGameContextModel().truncate();
    }

    @test()
    public async testCreateNewGameContextAndPlayerContext() {
        const ctx: GameFlowContextImpl = await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME);

        expect(ctx.id).deep.equal(this.gameID);
        expect(ctx.session).deep.equal(this.sessionId);
        expect(ctx.gameData).deep.equal(this.gameData);
        expect((await hGetAll(ctx.id.asString())).dataVersion).equal("5");
        // todo add active game here
        expect(ctx.playerContext).is.not.undefined;
        expect(ctx.playerContext.brokenGames).deep.equals([]);
        expect(ctx.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);
        expect((await hGetAll(ctx.id.playerContextID.asString() + ":metaInf")).dataVersion).equal("1");
    }

    @test()
    public async testGetExistingPlayerContextAndGameContext() {
        let ctx: GameFlowContextImpl = await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME);

        expect(ctx.id).deep.equal(this.gameID);
        expect(ctx.session).deep.equal(this.sessionId);
        expect(ctx.gameData).deep.equal(this.gameData);
        expect((await hGetAll(ctx.id.asString())).dataVersion).equal("5");
        expect(ctx.playerContext).is.not.undefined;
        expect(ctx.playerContext.brokenGames).deep.equals([]);
        expect(ctx.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);
        expect((await hGetAll(ctx.id.playerContextID.asString() + ":metaInf")).dataVersion).equal("1");

        ctx = await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME);

        expect(ctx.id).deep.equal(this.gameID);
        expect(ctx.session).deep.equal(this.sessionId);
        expect(ctx.gameData).deep.equal(this.gameData);
        expect((await hGetAll(ctx.id.asString())).dataVersion).equal("5");
        expect(ctx.playerContext).is.not.undefined;
        expect((await hGetAll(ctx.id.playerContextID.asString() + ":metaInf")).dataVersion).equal("1");

        expect(ctx.playerContext.brokenGames).deep.equals([]);
        expect(ctx.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);

    }

    @test()
    public async testCreateNewGameContextLoadPlayerContextFromOffline() {
        await getPlayerContextModel().create({
            id: this.gameID.playerContextID.asString(),
            data: undefined,
            games:
                [
                    { id: "games:context:1:player001:games004:mobile", mode: "real" },
                    { id: "games:context:1:player001:game005:web", mode: "bns" }
                ],
            version: 1,
            dataVersion: 1
        });

        const ctx: GameFlowContextImpl = await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME);

        expect(ctx.id).deep.equal(this.gameID);
        expect(ctx.session).deep.equal(this.sessionId);
        expect(ctx.gameData).deep.equal(this.gameData);
        expect((await hGetAll(ctx.id.asString())).dataVersion).equal("5");
        expect((await hGetAll(ctx.id.playerContextID.asString() + ":metaInf")).dataVersion).equal("1");
        expect(ctx.playerContext).is.not.undefined;
        expect(ctx.playerContext.brokenGames).deep.equals(
            [
                {
                    id: {
                        brandId: 1,
                        deviceId: "mobile",
                        gameCode: "games004",
                        idValue: "games:context:1:player001:games004:mobile",
                        playerCode: "player001",
                    },
                    mode: "real"
                },
                {
                    id: {
                        brandId: 1,
                        deviceId: "web",
                        gameCode: "game005",
                        idValue: "games:context:1:player001:game005:web",
                        playerCode: "player001"
                    },
                    mode: "bns"
                }
            ]
        );
        expect(ctx.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);
        expect(await getPlayerContextModel().findAll()).deep.equal([]);
    }

    @test()
    public async testLoadGameContextAndPlayerContextFromOffline() {
        let ctx = await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME);
        await ctx.updatePendingModification({
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD"
            } as PaymentOperation,
            {
                state: { currentScene: "sceneTest" },
            }, { request: "req", requestId: 1 });

        const cleanupService = new CleanupGameContextService();
        await cleanupService.forceCleanUp(this.gameID.brandId, 100);
        await flushAll();
        expect((await hGetAll(this.gameID.asString())).dataVersion).is.undefined;
        await getPlayerContextModel().create({
            id: this.gameID.playerContextID.asString(),
            data: undefined,
            games:
                [
                    { id: "games:context:1:player001:games004:mobile", mode: "real" },
                    { id: "games:context:1:player001:game005:web", mode: "bns" }
                ],
            version: 1,
            dataVersion: 1
        });

        expect((await getGameContextModel().findAll()).length).equal(1);

        ctx = await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME);

        expect(ctx.id).deep.equal(this.gameID);
        expect(ctx.session).deep.equal(this.sessionId);
        expect(ctx.gameData).deep.equal(this.gameData);
        expect((await hGetAll(ctx.id.asString())).dataVersion).equal("5");
        expect((await hGetAll(ctx.id.playerContextID.asString() + ":metaInf")).dataVersion).equal("1");
        expect(ctx.playerContext).is.not.undefined;
        expect(ctx.playerContext.brokenGames).deep.equals(
            [
                {
                    id: {
                        brandId: 1,
                        deviceId: "mobile",
                        gameCode: "games004",
                        idValue: "games:context:1:player001:games004:mobile",
                        playerCode: "player001",
                    },
                    mode: "real"
                },
                {
                    id: {
                        brandId: 1,
                        deviceId: "web",
                        gameCode: "game005",
                        idValue: "games:context:1:player001:game005:web",
                        playerCode: "player001"
                    },
                    mode: "bns"
                }
            ]
        );
        expect(ctx.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);
        expect(await getPlayerContextModel().findAll()).deep.equal([]);
        expect(await getGameContextModel().findAll()).deep.equal([]);
    }

    @test()
    public async testGetExistingPlayerContextAndCreateGameContext() {
        await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME);

        const gameID2: GameContextID = GameContextID.create("gameId_2", 1, "playerId", "deviceId");

        const gameData2: GameData = {
            gameTokenData: undefined,
            gameId: "gameId",
            limits: this.settings
        };

        const gameTokenData: GameTokenData = {
            playerCode: gameID2.playerCode,
            gameCode: gameID2.gameCode,
            brandId: gameID2.brandId,
            currency: "USD",
            playmode: "real"
        };

        gameTokenData.token = await createGameToken(this.gameTokenData);
        gameData2.gameTokenData = this.gameTokenData;
        const sessionId2 = await GameSession.generate(gameID2, "real");

        let ctx = await this.contextManager.findOrCreateGameContext(gameID2,
            sessionId2,
            gameData2,
            TEST_MODULE_NAME);

        await ctx.updatePendingModification({
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD"
            } as PaymentOperation,
            {
                state: { currentScene: "sceneTest" },
            }, { request: "req", requestId: 1 });

        const cleanupService = new CleanupGameContextService();
        await cleanupService.forceCleanUp(this.gameID.brandId, 100);

        ctx = await this.contextManager.findOrCreateGameContext(gameID2,
            sessionId2,
            gameData2,
            TEST_MODULE_NAME);

        expect(ctx.id).deep.equal(gameID2);
        expect(ctx.session).deep.equal(sessionId2);
        expect(ctx.gameData).deep.equal(gameData2);
        expect((await hGetAll(ctx.id.asString())).dataVersion).equal("5");
        expect((await hGetAll(ctx.id.playerContextID.asString() + ":metaInf")).dataVersion).equal("1");
        expect(ctx.playerContext).is.not.undefined;
        expect(ctx.playerContext.activeGames).deep.equals(
            [
                {
                    id: {
                        brandId: 1,
                        deviceId: "deviceId",
                        gameCode: "gameId_2",
                        idValue: "games:context:1:playerId:gameId_2:deviceId",
                        playerCode: "playerId",
                    },
                    mode: "real"
                }
            ]
        );
        expect(ctx.playerContext.brokenGames).deep.equal([]);
        expect(await getPlayerContextModel().findAll()).deep.equal([]);
        expect(await getGameContextModel().findAll()).deep.equal([]);
    }

    @test()
    public async testFindPlayerContextOffline() {
        const ctx = await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME);
        await ctx.updatePendingModification({
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD"
            } as PaymentOperation,
            {
                state: { currentScene: "sceneTest" },
            }, { request: "req", requestId: 1 });

        const cleanupService = new CleanupGameContextService();
        await cleanupService.forceCleanUp(this.gameID.brandId, 100);
        await flushAll();
        expect((await hGetAll(this.gameID.asString())).dataVersion).is.undefined;
        const dbPlayerContext: any = {
            id: this.gameID.playerContextID.asString(),
            data: null,
            games:
                [
                    { id: "games:context:1:player001:games004:mobile", mode: "real" },
                    { id: "games:context:1:player001:game005:web", mode: "bns" }
                ],
            version: "1",
            dataVersion: 1
        };
        await getPlayerContextModel().create(dbPlayerContext);

        const playerContext = await this.contextManager.findOfflinePlayerContext(ctx.id.playerContextID);
        expect(playerContext.version).equals("1");
        expect(playerContext.brokenGames).deep.equals(
            [
                {
                    id: {
                        brandId: 1,
                        deviceId: "mobile",
                        gameCode: "games004",
                        idValue: "games:context:1:player001:games004:mobile",
                        playerCode: "player001",
                    },
                    mode: "real"
                },
                {
                    id: {
                        brandId: 1,
                        deviceId: "web",
                        gameCode: "game005",
                        idValue: "games:context:1:player001:game005:web",
                        playerCode: "player001"
                    },
                    mode: "bns"
                }
            ]
        );
        expect(playerContext.activeGames).deep.equals([]);

        const items = (await getPlayerContextModel().findAll());
        expect(items.map(i => _.omit(i.get(), "createdAt", "updatedAt")))
            .deep
            .equal([dbPlayerContext]);
    }

    @test()
    public async testCreateNewGameContextAndPlayerContext_EnvIdChanged() {
        expect(await MigrationState.init(this.gameID.brandId)).is.true;

        await expect(this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME)).to.be.rejectedWith(EnvIdChangedError);

        expect((await hGetAll(this.gameID.playerContextID.asString() + ":metaInf"))).deep.equal({});
        expect((await hGetAll(this.gameID.asString()))).deep.equal({});
    }

    @test()
    public async testLoadGameContextAndPlayerContextFromOffline_EnvIdChanged() {
        const ctx = await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME);
        await ctx.updatePendingModification({
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD"
            } as PaymentOperation,
            {
                state: { currentScene: "sceneTest" },
            }, { request: "req", requestId: 1 });

        const cleanupService = new CleanupGameContextService();
        await cleanupService.forceCleanUp(this.gameID.brandId, 100);
        await flushAll();
        expect((await hGetAll(this.gameID.asString())).dataVersion).is.undefined;
        await getPlayerContextModel().create({
            id: this.gameID.playerContextID.asString(),
            data: undefined,
            games:
                [
                    { id: "games:context:1:player001:games004:mobile", mode: "real" },
                    { id: "games:context:1:player001:game005:web", mode: "bns" }
                ],
            version: 1,
            dataVersion: 1
        });

        expect((await getGameContextModel().findAll()).length).equal(1);

        expect(await MigrationState.init(this.gameID.brandId)).is.true;

        await expect(this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME)).to.be.rejectedWith(EnvIdChangedError);

        expect((await hGetAll(this.gameID.playerContextID.asString() + ":metaInf"))).deep.equal({});
        expect((await hGetAll(this.gameID.asString()))).deep.equal({});

        expect((await getGameContextModel().findAll()).length).equal(1);
        expect((await getPlayerContextModel().findAll()).length).equal(1);
    }

    @test()
    public async testGetExistingPlayerContextAndGameContext_EnvIdChanged() {
        const ctx: GameFlowContextImpl = await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME);

        expect(ctx.id).deep.equal(this.gameID);
        expect(ctx.session).deep.equal(this.sessionId);
        expect(ctx.gameData).deep.equal(this.gameData);
        expect((await hGetAll(ctx.id.asString())).dataVersion).equal("5");
        expect(ctx.playerContext).is.not.undefined;
        expect(ctx.playerContext.brokenGames).deep.equals([]);
        expect(ctx.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);
        expect((await hGetAll(ctx.id.playerContextID.asString() + ":metaInf")).dataVersion).equal("1");

        expect(await MigrationState.init(this.gameID.brandId)).is.true;

        await expect(this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME)).to.be.rejectedWith(EnvIdChangedError);

        const context = await this.contextManager.findGameContextById(this.gameID);

        expect(context.id).deep.equal(this.gameID);
        expect(context.gameData).deep.equal(this.gameData);
        expect((await hGetAll(context.id.asString())).dataVersion).equal("5");
        expect(context.playerContext).is.not.undefined;
        expect((await hGetAll(context.id.playerContextID.asString() + ":metaInf")).dataVersion).equal("1");

        expect(context.playerContext.brokenGames).deep.equals([]);
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);

        // check that there were no updates
        expect(context.version).eq(ctx.version);
    }
}
