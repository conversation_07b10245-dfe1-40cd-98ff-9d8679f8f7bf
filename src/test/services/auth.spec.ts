import { ManagementAPIConnectionError, ManagementAPIError, ManagementAPITransientError } from "../../skywind/errors";
import { expect, should, use } from "chai";
import * as jwt from "jsonwebtoken";
import { getService as getAuthservice, PlayerInfo } from "../../skywind/services/auth";
import { Limits } from "../../skywind/services/tokens";
import { funDefaultLimit } from "../funDefaultLimits";
import { Balance } from "../../skywind/services/wallet";
import { testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;
import status200 = testing.status200;
import status400 = testing.status400;
import status500 = testing.status500;

require("source-map-support").install();

const defaultLimitsByCurrency = funDefaultLimit.byCurrencies;
const defaultLimits: Limits = funDefaultLimit.default;

should();
use(require("chai-as-promised"));

describe("StartGame service", () => {
    let request: RequestMock;

    after(() => {
        request.unmock(superagent);
    });

    before(() => {
        request = requestMock(superagent);
    });

    beforeEach(() => {
        request.clearRoutes();
    });

    it("Start game successful", async () => {
        const tokenData = {
            playerCode: "PL001",
            gameCode: "G001",
            brandId: 1,
            currency: "USD"
        };
        const gameToken = await jwt.sign(tokenData, "SOMESECRETKEY");
        const balance: Balance = {
            currency: "USD",
            main: 100
        };
        const player: PlayerInfo = {
            code: "playerId",
            status: "normal",
            firstName: "Name",
            lastName: "Surname",
            nickname: "Nickname",
            email: "email",
            currency: "USD",
            country: "US",
            language: "EN",
            isTest: false,
            isVip: true
        };

        request.post("http://api:3006//v2/play/startgame", status200({
            gameToken: gameToken,
            balance: balance,
            player: player,
            limits: defaultLimitsByCurrency["USD"],
            settings: {},
            jrsdSettings: {},
            jurisdictionCode: undefined,
            playedFromCountry: "UK",
            region: "default",
            gameSettings: {},
            brandSettings: {},
            jackpots: [
                {
                    isGameOwned: true,
                    gameHistoryEnabled: true,
                    paymentStatisticEnabled: true,
                    winPaymentType: "jackpot_win",
                    id: "jackpotId"
                }
            ],
            renderType: 0,
            brandInfo: {
                name: "Brand",
                title: ""
            },
            operatorSiteId: 1
        }));
        const data = await getAuthservice("real")
            .startGame("G001", "STARTGAMETOKEN", null, null, "en", "mobile");

        delete ((data.gameData.gameTokenData as any).iat);
        expect(data.gameData).deep.equal({
            gameId: "G001",
            gameTokenData: {
                token: gameToken,
                playerCode: "PL001",
                gameCode: "G001",
                brandId: 1,
                currency: "USD"
            },
            balance: balance,
            player: player,
            logoutOptions: undefined,
            limits: defaultLimitsByCurrency["USD"],
            settings: {},
            jrsdSettings: {},
            jurisdictionCode: undefined,
            playedFromCountry: "UK",
            operatorPlayerCountry: undefined,
            region: "default",
            currencyReplacement: undefined,
            gameSettings: {},
            brandSettings: {},
            jackpots: [
                {
                    isGameOwned: true,
                    gameHistoryEnabled: true,
                    paymentStatisticEnabled: true,
                    winPaymentType: "jackpot_win",
                    id: "jackpotId"
                },
            ],
            renderType: 0,
            brandInfo: {
                name: "Brand",
                title: ""
            },
            operatorSiteId: 1
        });

        expect(request.args[0]).deep.equal({
            "url": "http://api:3006//v2/play/startgame",
            "method": "POST",
            "body": {
                "deviceId": "mobile",
                "language": "en",
                "startGameToken": "STARTGAMETOKEN",
                "deviceData": undefined,
                "gameVersion": undefined
            },
            "headers": {
                "content-type": "application/json",
                "x-gs": ""
            },
            "query": {}
        });
    });

    it("Start game successful - with additional options", async () => {
        const tokenData = {
            playerCode: "PL001",
            gameCode: "G001",
            brandId: 1,
            currency: "USD"
        };
        const gameToken = await jwt.sign(tokenData, "SOMESECRETKEY");
        const balance: Balance = {
            currency: "USD",
            main: 100
        };
        const player: PlayerInfo = {
            code: "playerId",
            status: "normal",
            firstName: "Name",
            lastName: "Surname",
            nickname: "Nickname",
            email: "email",
            currency: "USD",
            country: "US",
            language: "EN",
            isTest: false
        };

        request.post("http://api:3006//v2/play/startgame", status200({
            gameToken: gameToken,
            balance: balance,
            player: player,
            limits: defaultLimitsByCurrency["USD"],
            logoutOptions: { maxRetryAttemptsTimeout: 24, type: "all", maxSessionTimeout: 5 },
            settings: { maxPaymentAttemptTimeout: 24 },
            jrsdSettings: {},
            gameSettings: {},
            brandSettings: {},
            jurisdictionCode: undefined,
            playedFromCountry: "UK",
            region: "default",
            brandInfo: {
                name: "Brand",
                title: ""
            },
            operatorSiteId: 1
        }));
        const data = await getAuthservice("real").startGame("G001", "STARTGAMETOKEN");

        delete ((data.gameData.gameTokenData as any).iat);
        expect(data.gameData).deep.equal({
            gameId: "G001",
            gameTokenData: {
                token: gameToken,
                playerCode: "PL001",
                gameCode: "G001",
                brandId: 1,
                currency: "USD"
            },
            balance: balance,
            player: player,
            limits: defaultLimitsByCurrency["USD"],
            logoutOptions: { maxRetryAttemptsTimeout: 24, type: "all", maxSessionTimeout: 5 },
            settings: { maxPaymentAttemptTimeout: 24 },
            jrsdSettings: {},
            gameSettings: {},
            brandSettings: {},
            jurisdictionCode: undefined,
            playedFromCountry: "UK",
            operatorPlayerCountry: undefined,
            region: "default",
            currencyReplacement: undefined,
            jackpots: undefined,
            renderType: undefined,
            brandInfo: {
                name: "Brand",
                title: ""
            },
            operatorSiteId: 1
        });

        expect(request.args[0]).deep.equal({
            "url": "http://api:3006//v2/play/startgame",
            "method": "POST",
            "body": {
                "deviceId": undefined,
                "language": undefined,
                "startGameToken": "STARTGAMETOKEN",
                "deviceData": undefined,
                "gameVersion": undefined
            },
            "headers": {
                "content-type": "application/json",
                "x-gs": ""
            },
            "query": {}
        });
    });

    it("Start game successful - fun mode", async () => {

        const funStartGameToken = {
            playerCode: "PL001",
            gameCode: "G001",
            brandId: 1,
            currency: "USD",
            playmode: "fun"
        };

        const gameToken = await jwt.sign(funStartGameToken, "SOMESECRETKEY");

        request.post("http://api:3006//v2/play/fun/startgame", status200({
            gameToken,
            limits: defaultLimitsByCurrency["USD"],
            region: "default",
            brandInfo: {
                name: "Brand",
                title: ""
            },
            operatorSiteId: 1
        }));
        const data = await getAuthservice("fun").startGame("G001", funStartGameToken);

        delete ((data.gameData.gameTokenData as any).iat);
        expect(data.gameData).deep.equal({
            gameId: "G001",
            gameTokenData: {
                token: gameToken,
                playerCode: "PL001",
                gameCode: "G001",
                brandId: 1,
                currency: "USD",
                playmode: "fun"
            },
            limits: defaultLimitsByCurrency["USD"],
            jrsdSettings: undefined,
            jurisdictionCode: undefined,
            playedFromCountry: undefined,
            gameSettings: undefined,
            brandSettings: undefined,
            player: undefined,
            balance: undefined,
            settings: undefined,
            region: "default",
            jackpots: undefined,
            brandInfo: {
                name: "Brand",
                title: ""
            },
            operatorSiteId: 1
        });
    });

    it("Start game successful - fun mode, by other Currencies", async () => {
        const currencies: string[] = ["JPY", "GBP", "WRONGNAME", "BYN"];

        for (const currency of currencies) {
            let limits: Limits = defaultLimitsByCurrency[currency];
            if (!limits) {
                limits = defaultLimits;
            }
            const funStartGameToken = {
                playerCode: "PL001",
                gameCode: "G001",
                brandId: 1,
                currency: currency,
                playmode: "fun"
            };

            const funGameTokenData = {
                playerCode: funStartGameToken.playerCode,
                gameCode: funStartGameToken.gameCode,
                brandId: funStartGameToken.brandId,
                currency: funStartGameToken.currency,
                playmode: "fun"
            };

            const gameToken = await jwt.sign(funGameTokenData, "SOMESECRETKEY");

            request.post("http://api:3006//v2/play/fun/startgame", status200({
                gameToken,
                playedFromCountry: "UK",
                limits: limits,
                region: "default",
                brandInfo: {
                    name: "Brand",
                    title: ""
                },
                operatorSiteId: 1
            }));
            const data = await getAuthservice("fun").startGame("G001", funStartGameToken);

            delete ((data.gameData.gameTokenData as any).iat);
            expect(data.gameData).deep.equal({
                gameId: "G001",
                gameTokenData: {
                    token: gameToken,
                    playerCode: "PL001",
                    gameCode: "G001",
                    brandId: 1,
                    currency: currency,
                    playmode: "fun"
                },
                limits: limits,
                jrsdSettings: undefined,
                jurisdictionCode: undefined,
                playedFromCountry: "UK",
                gameSettings: undefined,
                brandSettings: undefined,
                player: undefined,
                balance: undefined,
                settings: undefined,
                region: "default",
                jackpots: undefined,
                brandInfo: {
                    name: "Brand",
                    title: ""
                },
                operatorSiteId: 1
            });
        }
    });

    it("startGameRequest - reject error", async () => {
        request.post("http://api:3006//v2/play/startgame", () => {
            throw new Error("Can't connect!");
        });

        return getAuthservice("real")
            .startGame("G001", "STARTGAMETOKEN")
            .should
            .eventually
            .rejectedWith(ManagementAPIConnectionError);

    });

    it("startGameRequest - error response", async () => {
        request.post("http://api:3006//v2/play/startgame", status400());

        return getAuthservice("real")
            .startGame("G001", "STARTGAMETOKEN")
            .should
            .eventually
            .rejectedWith(ManagementAPIError);
    });

    it("startGameRequest - server error", async () => {
        request.post("http://api:3006//v2/play/startgame", status500());

        return getAuthservice("real")
            .startGame("G001", "STARTGAMETOKEN")
            .should
            .eventually
            .rejectedWith(ManagementAPITransientError);
    });

    it("Start game successful - BNS mode", async () => {
        const tokenData = {
            playerCode: "PL001",
            gameCode: "G001",
            brandId: 1,
            currency: "USD",
            playmode: "bns"
        };
        const gameToken = await jwt.sign(tokenData, "SOMESECRETKEY");
        const dateNow = new Date().toISOString();
        const balance = {
            main: 1000000,
            bonusCoins: {
                amount: 100,
                rewardedAmount: 100,
                redeemMinAmount: 50,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: dateNow,
                exchangeRate: 1,
                promoId: "1",
            }
        };
        const player: PlayerInfo = {
            code: "playerId",
            status: "normal",
            firstName: "Name",
            lastName: "Surname",
            nickname: "Nickname",
            email: "email",
            currency: "USD",
            country: "US",
            language: "EN",
            isTest: false
        };

        request.post("http://api:3006//v2/play/startgame", status200({
            gameToken: gameToken,
            balance: balance,
            player: player,
            limits: defaultLimitsByCurrency["USD"],
            settings: {},
            jrsdSettings: {},
            gameSettings: {},
            brandSettings: {},
            jurisdictionCode: undefined,
            playedFromCountry: "UK",
            region: "default",
            brandInfo: {
                name: "Brand",
                title: ""
            },
            operatorSiteId: 1
        }));
        const data = await getAuthservice("bns")
            .startGame("G001", "STARTGAMETOKEN", null, null, "ru", "web");
        expect(request.args[0]).deep.equal({
            "url": "http://api:3006//v2/play/startgame",
            "method": "POST",
            "body": {
                "language": "ru",
                "deviceId": "web",
                "startGameToken": "STARTGAMETOKEN",
                "deviceData": undefined,
                "gameVersion": undefined
            },
            "headers": {
                "content-type": "application/json",
                "x-gs": ""
            },
            "query": {}
        });
        delete ((data.gameData.gameTokenData as any).iat);
        expect(data.gameData).deep.equal({
            gameId: "G001",
            gameTokenData: {
                token: gameToken,
                playerCode: "PL001",
                gameCode: "G001",
                brandId: 1,
                currency: "USD",
                playmode: "bns"
            },
            balance: balance,
            player: player,
            limits: defaultLimitsByCurrency["USD"],
            settings: {},
            jrsdSettings: {},
            gameSettings: {},
            brandSettings: {},
            jurisdictionCode: undefined,
            logoutOptions: undefined,
            playedFromCountry: "UK",
            operatorPlayerCountry: undefined,
            bnsPromotion: {
                promoId: balance.bonusCoins.promoId,
                exchangeRate: 1,
                expireAt: Date.parse(dateNow).valueOf()
            },
            region: "default",
            currencyReplacement: undefined,
            jackpots: undefined,
            renderType: undefined,
            brandInfo: {
                name: "Brand",
                title: ""
            },
            operatorSiteId: 1
        });
    });

    it("Start game successful- 'play-money' mode", async () => {
        const tokenData = {
            playerCode: "PL001",
            gameCode: "G001",
            brandId: 1,
            currency: "XXX"
        };
        const gameToken = await jwt.sign(tokenData, "SOMESECRETKEY");
        const balance: Balance = {
            currency: "USD",
            main: 100
        };
        const player: PlayerInfo = {
            code: "playerId",
            status: "normal",
            firstName: "Name",
            lastName: "Surname",
            nickname: "Nickname",
            email: "email",
            currency: "USD",
            country: "US",
            language: "EN",
            isTest: false
        };

        request.post("http://api:3006//v2/play/startgame", status200({
            gameToken: gameToken,
            balance: balance,
            player: player,
            limits: defaultLimitsByCurrency["USD"],
            settings: {},
            jrsdSettings: {},
            gameSettings: {},
            brandSettings: {},
            jurisdictionCode: undefined,
            playedFromCountry: "UK",
            region: "default",
            renderType: 0,
            brandInfo: {
                name: "Brand",
                title: ""
            },
            operatorSiteId: 1
        }));
        const data = await getAuthservice("play_money")
            .startGame("G001", "STARTGAMETOKEN", undefined, undefined, undefined, "mobile");

        delete ((data.gameData.gameTokenData as any).iat);
        expect(data.gameData).deep.equal({
            gameId: "G001",
            gameTokenData: {
                token: gameToken,
                playerCode: "PL001",
                gameCode: "G001",
                brandId: 1,
                currency: "XXX"
            },
            balance: balance,
            player: player,
            logoutOptions: undefined,
            limits: defaultLimitsByCurrency["USD"],
            settings: {},
            jrsdSettings: {},
            gameSettings: {},
            brandSettings: {},
            jurisdictionCode: undefined,
            playedFromCountry: "UK",
            operatorPlayerCountry: undefined,
            region: "default",
            currencyReplacement: undefined,
            jackpots: undefined,
            renderType: 0,
            brandInfo: {
                name: "Brand",
                title: ""
            },
            operatorSiteId: 1
        });

        expect(request.args[0]).deep.equal({
            "url": "http://api:3006//v2/play/startgame",
            "method": "POST",
            "body": {
                "deviceId": "mobile",
                "language": undefined,
                "startGameToken": "STARTGAMETOKEN",
                "deviceData": undefined,
                "gameVersion": undefined
            },
            "headers": {
                "content-type": "application/json",
                "x-gs": ""
            },
            "query": {}
        });
    });
});
