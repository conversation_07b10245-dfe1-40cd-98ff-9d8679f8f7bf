import { expect, use } from "chai";
import { Redis } from "../../skywind/storage/redis";
import { flushAll, getRange, getRoundHistory } from "../helper";
import { sleep } from "@skywind-group/sw-utils";
import { GameRoundQueue } from "../../skywind/services/queue/redisQueue";

use(require("chai-as-promised"));

describe("GameRoundQueue", () => {

    const data: any = {
        id: 1,
        gameId: "sw_game",
        brandId: 1,
        playerCode: "player001",
        deviceId: "web",
        gameCode: "sw_game_001",
        currency: "USD",
        test: false,
        totalBet: 100,
        totalWin: 20,
        totalEvents: 10,
        startedAt: new Date().toISOString(),
        finishedAt: new Date().toISOString(),
    };

    const saveData = async (queue: GameRoundQueue) => {
        const redisClient = await Redis.get().get();
        const trx = redisClient.multi();
        try {

            queue.save(trx, data);

            await new Promise<void>((resolve, reject) => {
                trx.exec((err, result) => {
                    if (err) {
                        return reject(err);
                    }
                    return resolve(undefined);
                });
            });
        } finally {
            await Redis.get().release(redisClient);
        }
    };

    beforeEach(() => {
        return flushAll();
    });

    it("pops, saves, removes", async () => {
        const queue = new GameRoundQueue();
        await saveData(queue);

        const history = await getRoundHistory(0, 1000);
        expect(history).deep.equal([data]);

        const value = await queue.pop(1);
        expect(value.map(i => i.data)).deep.equal([data]);

        const history1 = await getRoundHistory(0, 1000);
        expect(history1).deep.equal([]);

        const workers = await getRange(queue.workerList, 0, 1000);
        expect(workers).deep.equal([data]);

        await queue.commit();

        const workers1 = await getRange(queue.workerList, 0, 1000);
        expect(workers1).deep.equal([]);

    });
    it("illegal state", async () => {
        const queue = new GameRoundQueue();
        await saveData(queue);

        expect(await getRoundHistory(0, 1000)).deep.equal([data]);

        const value = await queue.pop(1);
        expect(value.map(i => i.data)).deep.equal([data]);
        await expect(queue.pop()).to.be.rejectedWith(Error);
        await expect(queue.pop()).to.be.rejectedWith(Error);

        expect(await getRoundHistory(0, 1000)).deep.equal([]);

        expect(await getRange(queue.workerList, 0, 1000)).deep.equal([data]);

        await queue.repair();

        expect(await getRange(queue.workerList, 0, 1000)).deep.equal([]);
        expect(await getRoundHistory(0, 1000)).deep.equal([data]);

        await queue.pop();
        await queue.commit();
        expect(await getRange(queue.workerList, 0, 1000)).deep.equal([]);
        expect(await getRoundHistory(0, 1000)).deep.equal([]);
    });

    it("empty commit", async () => {
        const queue = new GameRoundQueue();
        await saveData(queue);
        await queue.commit();

        expect(await getRoundHistory(0, 1000)).deep.equal([data]);
        expect(await getRange(queue.workerList, 0, 1000)).deep.equal([]);

        const value = await queue.pop(1);
        expect(value.map(i => i.data)).deep.equal([data]);
        await queue.commit();

        expect(await getRange(queue.workerList, 0, 1000)).deep.equal([]);
        expect(await getRoundHistory(0, 1000)).deep.equal([]);
    });

    it("pop with waiting for", async function() {
        this.timeout(10000);
        const queue = new GameRoundQueue();

        const history = await getRoundHistory(0, 1000);
        expect(history).deep.equal([]);

        const promise = queue.pop(1);
        await sleep(600);
        await saveData(queue);

        const value = await promise;

        expect(value.map(i => i.data)).deep.equal([data]);

        const history1 = await getRoundHistory(0, 1000);
        expect(history1).deep.equal([]);

        const workers = await getRange(queue.workerList, 0, 1000);
        expect(workers).deep.equal([data]);
    });

    it("spin pop", async function() {
        this.timeout(10000);
        const queue = new GameRoundQueue();

        const history = await getRoundHistory(0, 1000);
        expect(history).deep.equal([]);

        const promise = queue.pop(1000);
        await sleep(100);
        await saveData(queue);

        const value = await promise;

        expect(value.map(i => i.data)).deep.equal([data]);

        const history1 = await getRoundHistory(0, 1000);
        expect(history1).deep.equal([]);

        const workers = await getRange(queue.workerList, 0, 1000);
        expect(workers).deep.equal([data]);
    });

    it("repairs old orphan workers", async () => {
        const queue = new GameRoundQueue();
        await saveData(queue);
        const history = await getRoundHistory(0, 1000);
        expect(history).deep.equal([data]);

        const value = await queue.pop(1);
        expect(value.map(i => i.data)).deep.equal([data]);

        const history1 = await getRoundHistory(0, 1000);
        expect(history1).deep.equal([]);

        const newQueue = new GameRoundQueue();
        await newQueue.tryToRepairOrphanWorkers(Date.now());

        const history2 = await getRoundHistory(0, 1000);
        expect(history2).deep.equal([data]);
        const workers1 = await getRange(queue.workerList, 0, 1000);
        expect(workers1).deep.equal([]);

    });
});
