import { suite, test } from "mocha-typescript";
import { expect, use } from "chai";
import { createGameToken, flushAll, TEST_MODULE_NAME, TestLoadResult } from "../helper";
import { Currency, GameEvent, GameInitResponse, GamePlayResponse, PaymentInfo } from "@skywind-group/sw-game-core";
import { GameData, LogoutType } from "../../skywind/services/auth";
import { GameContextID } from "../../skywind/services/contextIds";
import { GameTokenData } from "../../skywind/services/tokens";
import { GameSession } from "../../skywind/services/gameSession";
import { getGameFlowContextManager } from "../../skywind/services/contextmanager/contextManagerImpl";
import { testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import MerchantGameSessionService from "../../skywind/services/merchantGameSessionService";
import { MerchantLogoutResult } from "../../skywind/services/context/gamecontext";
import { ConcurrentAccessToGameSession, LogoutForbiddenError, ManagementAPITransientError } from "../../skywind/errors";
import { PaymentOperation } from "../../skywind/services/wallet";
import { GameRecoveryService } from "../../skywind/services/gamerecovery";
import * as GameService from "../../skywind/services/game/game";
import { stub } from "sinon";
import { AsyncTestGame } from "../testGames";
import { setRandomGeneratorFactory } from "../..";
import { RandomGeneratorFactoryImpl } from "../testRandomFactory";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;
import status200 = testing.status200;
import status500 = testing.status500;

use(require("chai-as-promised"));

@suite()
class MerchantGameSessionServiceSpec {
    private request: RequestMock;

    public static before() {
        setRandomGeneratorFactory(new RandomGeneratorFactoryImpl());
    }

    public async before() {
        this.request = requestMock(superagent);
        await flushAll();
    }

    public after() {
        this.request.clearRoutes();
        this.request.unmock(superagent);
    }

    @test()
    public async testRequireLogout() {
        expect((await this.createContext("player001")).requireLogout).is.false;
        expect((await this.createContext("player002",
            { logoutOptions: { type: LogoutType.ALL } })).requireLogout).is.true;
        expect((await this.createContext("player003",
            { logoutOptions: { type: LogoutType.UNFINISHED } })).requireLogout).is.false;

        const context1 = await this.createContext("player004",
            { logoutOptions: { type: LogoutType.UNFINISHED } });
        await context1.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context1.session.sessionId,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        expect(context1.requireLogout).is.false;

        const context2 = await this.createContext("player005",
            { logoutOptions: { type: LogoutType.ALL } });
        await context2.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context2.session.sessionId,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        expect(context2.requireLogout).is.true;

        const context3 = await this.createContext("player006",
            { logoutOptions: { type: LogoutType.UNFINISHED } });
        await context3.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context3.session.sessionId,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });

        await context3.commitPendingModification();
        expect(context3.requireLogout).is.true;

        const context4 = await this.createContext("player007",
            { logoutOptions: { type: LogoutType.UNFINISHED } });
        await context4.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context4.session.sessionId,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: true, data: {} });

        await context4.commitPendingModification();
        expect(context4.requireLogout).is.false;

        const context5 = await this.createContext("player008",
            { logoutOptions: { type: LogoutType.UNFINISHED } });
        await context5.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);
        expect(context5.requireLogout).is.true;

        const context6 = await this.createContext("player008",
            { logoutOptions: { type: LogoutType.ALL } });
        await context6.setLogoutResult(null);
        expect(context6.requireLogout).is.true;
    }

    @test()
    public async testLogout_ALL() {
        this.request.post("http://api:3006//v2/play/game/logout", status200({}));
        let context = await this.createContext("player002",
            { logoutOptions: { type: LogoutType.ALL } });
        await MerchantGameSessionService.logout(context);
        expect(this.getLastRequest()).deep.equal({ roundId: "0", state: "finished" });
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(context.logoutResult).equal(MerchantLogoutResult.SKIP_LOGIN);
    }

    @test()
    public async testLogout_RaceCondition_on_Payment_1() {
        this.request.post("http://api:3006//v2/play/game/logout", status200({}));
        let context = await this.createContext("player002",
            { logoutOptions: { type: LogoutType.ALL } });
        const concurrentContext = await getGameFlowContextManager().findGameContextById(context.id);
        await concurrentContext.updatePendingModification({ type: "payment", bet: 10, win: 100 } as any,
            {} as any, undefined);
        await expect(MerchantGameSessionService.logout(context)).to.be.rejectedWith(ConcurrentAccessToGameSession);
        context = await getGameFlowContextManager().findGameContextById(concurrentContext.id);
        expect(context.logoutResult).is.undefined;
    }

    @test()
    public async testLogout_RaceCondition_on_Payment_2() {
        this.request.post("http://api:3006//v2/play/game/logout", status200({}));
        let context = await this.createContext("player002",
            { logoutOptions: { type: LogoutType.ALL } });
        const concurrentContext = await getGameFlowContextManager().findGameContextById(context.id);
        await MerchantGameSessionService.logout(context);
        await expect(concurrentContext.updatePendingModification({ type: "payment", bet: 10, win: 100 } as any,
            {} as any, undefined)).to.be.rejectedWith(ConcurrentAccessToGameSession);
        expect(this.getLastRequest()).deep.equal({ roundId: "0", state: "finished" });
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(context.logoutResult).equal(MerchantLogoutResult.SKIP_LOGIN);
    }

    @test()
    public async testLogout_PreventLogout_if_broken_payment() {
        this.request.post("http://api:3006//v2/play/game/logout", status200({}));
        let context = await this.createContext("player002",
            { logoutOptions: { type: LogoutType.ALL } });
        const concurrentContext = await getGameFlowContextManager().findGameContextById(context.id);
        await concurrentContext.updatePendingModification({ type: "payment", bet: 10, win: 100 } as any,
            {} as any, undefined);
        context = await getGameFlowContextManager().findGameContextById(concurrentContext.id);
        await expect(MerchantGameSessionService.logout(context)).to.be.rejectedWith(LogoutForbiddenError);
        context = await getGameFlowContextManager().findGameContextById(concurrentContext.id);
        expect(context.logoutResult).is.undefined;
    }

    @test()
    public async testLogoutControl_MakeLogout_if_broken_payment() {
        this.request.post("http://api:3006//v2/play/game/logout", status200({}));
        let context = await this.createContext("player002test",
            {
                logoutOptions: { type: LogoutType.ALL }, settings: {
                    logoutControl: {
                        ignorePayments: {
                            offlineRetry: true,
                            gameClosure: true,
                            gameRelaunch: true
                        }
                    }
                }
            });
        const concurrentContext = await getGameFlowContextManager().findGameContextById(context.id);
        await concurrentContext.updatePendingModification({ type: "payment", bet: 10, win: 100 } as any,
            {} as any, undefined);
        context = await getGameFlowContextManager().findGameContextById(concurrentContext.id);
        await MerchantGameSessionService.logout(context, true);
        context = await getGameFlowContextManager().findGameContextById(concurrentContext.id);
        expect(context.logoutResult).is.not.empty;
    }

    @test()
    public async testLogout_ALL_requreLogin() {
        this.request.post("http://api:3006//v2/play/game/logout", status200({ requireLogin: true }));
        let context = await this.createContext("player002",
            { logoutOptions: { type: LogoutType.ALL } });
        await MerchantGameSessionService.logout(context);
        expect(this.getLastRequest()).deep.equal({ roundId: "0", state: "finished" });
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(context.logoutResult).equal(MerchantLogoutResult.REQUIRE_LOGIN);
    }

    @test()
    public async testLogout_ALL_failed() {
        this.request.post("http://api:3006//v2/play/game/logout", status500());
        let context = await this.createContext("player002",
            { logoutOptions: { type: LogoutType.ALL } });
        await expect(MerchantGameSessionService.logout(context)).to.be.rejectedWith(ManagementAPITransientError);
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(context.logoutResult).equal(MerchantLogoutResult.PENDING_LOGOUT
        );
    }

    @test()
    public async testLogoutStates() {
        this.request.post("http://api:3006//v2/play/game/logout", status200({}));
        await MerchantGameSessionService.logout(await this.createContext("player002",
            { logoutOptions: { type: LogoutType.ALL } }));
        expect(this.getLastRequest()).deep.equal({ roundId: "0", state: "finished" });

        const context1 = await this.createContext("player003",
            { logoutOptions: { type: LogoutType.ALL } });
        await context1.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context1.session.sessionId,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        await context1.commitPendingModification();
        await MerchantGameSessionService.logout(context1);
        expect(this.getLastRequest()).deep.equal({ roundId: "1", state: "unfinished" });

        const context2 = await this.createContext("player004",
            { logoutOptions: { type: LogoutType.UNFINISHED } });
        await context2.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context2.session.sessionId,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });

        await context2.commitPendingModification();
        await MerchantGameSessionService.logout(context2);
        expect(this.getLastRequest()).deep.equal({ roundId: "2", state: "unfinished" });
    }

    @test()
    public async testLogoutState_requireLogin() {
        let context = await this.createContext("player002",
            { logoutOptions: { type: LogoutType.ALL } });
        await context.setLogoutResult(MerchantLogoutResult.REQUIRE_LOGIN);
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(context.logoutResult).equal(MerchantLogoutResult.REQUIRE_LOGIN);
    }

    @test()
    public async testLogoutState_afterFinalize__logoutAll() {
        const loadGame = stub(GameService, "load");
        try {
            this.request.put("http://api:3006//v2/play/payment", status200());
            this.request.post("http://api:3006//v2/play/payment/transactionId",
                status200({ transactionId: 123 }));
            this.request.put("http://api:3006//v2/play/balance", status200());
            this.request.post("http://api:3006//v2/play/game/login", status200());
            this.request.get("http://api:3006//v2/play/rounds",
                status200([{ roundId: 1, data: "history1" }, { roundId: 2, data: "history2" }]));
            this.request.post("http://api:3006//v2/play/game/finalize", status200());
            this.request.post("http://api:3006//v2/play/game/logout", status200({}));

            const events: GameEvent[] = [];
            const payments: PaymentInfo[] = [];
            const playResponses: GamePlayResponse[] = [];

            for (let i = 1; i <= 2; i += 1) {
                events.push({ type: "async", request: { request: "test", requestId: i } } as GameEvent);
                payments.push({ bet: i * 100, win: (i - 1) * 100 });
                playResponses.push({ request: "test", requestId: i } as GamePlayResponse);
            }

            const game = new AsyncTestGame([{ request: "init" } as GameInitResponse],
                [], events, payments, [], playResponses, 10);

            loadGame.returns(Promise.resolve(new TestLoadResult(game)));
            const contextManager = getGameFlowContextManager();
            let context = await this.createContext("player002",
                { logoutOptions: { type: LogoutType.ALL } });

            await context.updatePendingModification(
                {
                    operation: "payment",
                    transactionId: "1",
                    bet: 100,
                    win: 20,
                    currency: "USD",
                    roundId: "0"
                } as PaymentOperation,
                {
                    scene: "current",
                    nextScene: "SOMESCENE"
                },
                { request: "req", requestId: 1 },
                { type: "slot", roundEnded: false, data: { positions: [6, 7, 8] } });
            await context.commitPendingModification();

            const contextId = GameContextID.create("gameId",
                1,
                "player002",
                "deviceId");

            await context.setLogoutResult(MerchantLogoutResult.REQUIRE_LOGIN);
            context = await contextManager.findGameContextById(context.id);
            expect(context.logoutResult).equal(MerchantLogoutResult.REQUIRE_LOGIN);

            await new GameRecoveryService(contextManager).finalize({
                gameContextId: contextId.asString()
            });

            const gameContext = await contextManager.findOrRestoreGameContext(contextId);
            expect(gameContext.logoutResult).eq("skip_login");
            expect(gameContext.logoutId).eq(undefined);
        } finally {
            loadGame.restore();
        }
    }

    @test()
    public async testLogoutState_afterFinalize_logoutBrokenGame() {
        const loadGame = stub(GameService, "load");
        try {
            this.request.put("http://api:3006//v2/play/payment", status200());
            this.request.post("http://api:3006//v2/play/payment/transactionId",
                status200({ transactionId: 123 }));
            this.request.put("http://api:3006//v2/play/balance", status200());
            this.request.post("http://api:3006//v2/play/game/login", status200());
            this.request.get("http://api:3006//v2/play/rounds",
                status200([{ roundId: 1, data: "history1" }, { roundId: 2, data: "history2" }]));
            this.request.post("http://api:3006//v2/play/game/finalize", status200());

            const events: GameEvent[] = [];
            const payments: PaymentInfo[] = [];
            const playResponses: GamePlayResponse[] = [];

            for (let i = 1; i <= 2; i += 1) {
                events.push({ type: "async", request: { request: "test", requestId: i } } as GameEvent);
                payments.push({ bet: i * 100, win: (i - 1) * 100 });
                playResponses.push({ request: "test", requestId: i } as GamePlayResponse);
            }

            const game = new AsyncTestGame([{ request: "init" } as GameInitResponse],
                [], events, payments, [], playResponses, 10);

            loadGame.returns(Promise.resolve(new TestLoadResult(game)));
            const contextManager = getGameFlowContextManager();
            let context = await this.createContext("player002",
                { logoutOptions: { type: LogoutType.UNFINISHED } });

            await context.updatePendingModification(
                {
                    operation: "payment",
                    transactionId: "1",
                    bet: 100,
                    win: 20,
                    currency: "USD",
                    roundId: "0"
                } as PaymentOperation,
                {
                    scene: "current",
                    nextScene: "SOMESCENE"
                },
                { request: "req", requestId: 1 },
                { type: "slot", roundEnded: false, data: { positions: [6, 7, 8] } });
            await context.commitPendingModification();

            const contextId = GameContextID.create("gameId",
                1,
                "player002",
                "deviceId");

            await context.setLogoutResult(MerchantLogoutResult.REQUIRE_LOGIN);
            context = await contextManager.findGameContextById(context.id);
            expect(context.logoutResult).equal(MerchantLogoutResult.REQUIRE_LOGIN);

            await new GameRecoveryService(contextManager).finalize({
                gameContextId: contextId.asString()
            });

            const gameContext = await contextManager.findOrRestoreGameContext(contextId);
            expect(gameContext.logoutResult).eq(undefined);
            expect(gameContext.logoutId).eq(undefined);
        } finally {
            loadGame.restore();
        }
    }

    @test()
    public async testLogoutState_undefined() {
        const context = await this.createContext("player002",
            { logoutOptions: { type: LogoutType.ALL } });
        expect(context.logoutResult).is.undefined;
    }

    @test()
    public async testLogin_succcess() {
        this.request.post("http://api:3006//v2/play/game/login", status200({}));
        let context = await this.createContext("player002",
            { logoutOptions: { type: LogoutType.ALL } });
        await context.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);
        await context.setLogoutResult(MerchantLogoutResult.REQUIRE_LOGIN);
        const logoutId = context.logoutId;
        await MerchantGameSessionService.login(context);
        expect(this.request.args[0].url).equal("http://api:3006//v2/play/game/login");
        expect(this.request.args[0].body).deep.equal({
            roundId: context.roundId,
            roundPID: "doRlLRrM",
            logoutId,
            gameContextId: context.id.asString(),
            gameToken: context.gameData.gameTokenData.token
        });
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(context.logoutResult).is.undefined;
    }

    @test()
    public async testLoginWithNewLogoutId() {
        this.request.post("http://api:3006//v2/play/game/login", status200({}));
        let context = await this.createContext("player002",
            { logoutOptions: { type: LogoutType.ALL } });
        await context.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);
        await context.setLogoutResult(MerchantLogoutResult.REQUIRE_LOGIN);
        const logoutId = context.logoutId;
        await MerchantGameSessionService.login(context, true);
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(context.logoutId).to.equal(`${logoutId}_0`);
    }

    @test()
    public async testLogin_failed() {
        this.request.post("http://api:3006//v2/play/game/login", status500({}));
        let context = await this.createContext("player002",
            { logoutOptions: { type: LogoutType.ALL } });
        await context.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);
        await context.setLogoutResult(MerchantLogoutResult.REQUIRE_LOGIN);
        await expect(MerchantGameSessionService.login(context)).to.be.rejectedWith(ManagementAPITransientError);
        expect(this.request.args[0].url).equal("http://api:3006//v2/play/game/login");
        expect(this.request.args[0].body).deep.equal({
            roundId: context.roundId,
            roundPID: "doRlLRrM",
            logoutId: context.logoutId,
            gameContextId: context.id.asString(),
            gameToken: context.gameData.gameTokenData.token
        });
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(context.logoutResult).equal(MerchantLogoutResult.REQUIRE_LOGIN);
    }

    private getLastRequest() {
        const data = this.request.args[this.request.args.length - 1];
        return { roundId: data.body.roundId, state: data.body.state };
    }

    private async createContext(playerCode: string, customGameData: Partial<GameData> = {}) {
        const currency: Currency = 0;
        const settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: currency,
            stakeAll: [1, 2, 3, 4],
            stakeDef: currency,
            stakeMax: currency,
            stakeMin: currency,
            winMax: currency,
            currencyMultiplier: 100,
        };

        const gameID: GameContextID = GameContextID.create("gameId", 1, playerCode, "deviceId", "real");

        const gameData: GameData = {
            ...{
                gameTokenData: undefined,
                limits: settings,
            },
            ...customGameData
        };

        const gameTokenData: GameTokenData = {
            playerCode: gameID.playerCode,
            gameCode: "GM001",
            brandId: gameID.brandId,
            currency: "USD",
            playmode: "real"
        };

        gameTokenData.token = await createGameToken(gameTokenData);
        gameData.gameTokenData = gameTokenData;
        const sessionId = await GameSession.generate(gameID, "real");

        return await getGameFlowContextManager().findOrCreateGameContext(gameID, sessionId, gameData, TEST_MODULE_NAME);
    }
}
