import { suite, test } from "mocha-typescript";
import { TEST_MODULE_NAME } from "../helper";
import { expect } from "chai";
import { GameFlowInfoImpl } from "../../skywind/services/context/gameFlowInfo";
import { BaseGameContextSpec } from "./gameContext.spec";

@suite()
class GameContextPrepareFinalizeSpec extends BaseGameContextSpec {

    @test()
    public async testPrepareCommitFinalize() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        let context = await this.contextManager.findGameContextById(this.gameID);
        const state = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };

        const history: any = {
            type: "slot",
            roundEnded: false,
            data: {
                positions: [1, 2, 3],
            }
        };

        const payment: any = {
            operation: "payment",
            transactionId: "1",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history.roundEnded,
            bet: 1900,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(payment, state, {} as any, history);
        await context.commitPendingModification();

        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state);
        expect(context.gameSerialNumber).equal(1);
        expect(context.totalEventId).equal(1);

        const walletOperation: any = {
            operation: "finalize-game",
            transactionId: "2",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: true,
            bet: 0,
            win: 0,
            currency: "USD",
            ts: new Date()
        };
        await context.prepareFinalizeGame(walletOperation);
        context = await this.contextManager.findGameContextById(this.gameID);

        walletOperation.ts = walletOperation.ts.toISOString();
        expect(context.pendingModification).deep.equal({
            "newState": null,
            "walletOperation": walletOperation,
        });

        await context.commitPendingModification();

        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        expect(context.gameSerialNumber).equal(0);
        expect(context.totalEventId).equal(1);
        expect(context.pendingModification).is.undefined;
        expect(context.gameContext).is.undefined;
    }

    @test()
    public async testPrepareCommitFinalize_WithNewState() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        let context = await this.contextManager.findGameContextById(this.gameID);
        const state = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };

        const history: any = {
            type: "slot",
            roundEnded: false,
            data: {
                positions: [1, 2, 3],
            }
        };

        const payment: any = {
            operation: "payment",
            transactionId: "1",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history.roundEnded,
            bet: 1900,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(payment, state, {} as any, history);
        await context.commitPendingModification();

        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state);
        expect(context.gameSerialNumber).equal(1);
        expect(context.totalEventId).equal(1);

        const walletOperation: any = {
            operation: "finalize-game",
            transactionId: "2",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: true,
            bet: 0,
            win: 0,
            currency: "USD",
            ts: new Date()
        };
        const newState = { type: "state_after_finalization" };
        await context.prepareFinalizeGame(walletOperation, newState);
        context = await this.contextManager.findGameContextById(this.gameID);

        walletOperation.ts = walletOperation.ts.toISOString();
        expect(context.pendingModification).deep.equal({
            "newState": newState,
            "walletOperation": walletOperation,
        });

        await context.commitPendingModification();

        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameSerialNumber).equal(0);
        expect(context.totalEventId).equal(1);
        expect(context.pendingModification).is.undefined;
        expect(context.gameContext).deep.equal(newState);
    }

    @test()
    public async testPrepareCommitFinalize_WithJPContext() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        let context = await this.contextManager.findGameContextById(this.gameID);
        const state = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };

        const history: any = {
            type: "slot",
            roundEnded: false,
            data: {
                positions: [1, 2, 3],
            }
        };

        const payment: any = {
            operation: "payment",
            transactionId: "1",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history.roundEnded,
            bet: 1900,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(payment, state, {} as any, history);
        const jpnContext = {
            token: "jpn-auth-token",
            jackpotsInfo: [
                {
                    id: "jp-id",
                    jpGameId: "test",
                    type: "test",
                    baseType: "test",
                    currency: "EUR",
                    isGameOwned: true,
                    gameHistoryEnabled: true,
                    paymentStatisticEnabled: true
                }
            ],
            contributionEnabled: true
        };
        await context.updateJackpotPendingModification(undefined, undefined, jpnContext);
        await context.commitPendingModification();

        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state);
        expect(context.gameSerialNumber).equal(1);
        expect(context.totalEventId).equal(1);
        expect(context.jpContext).deep.equal(jpnContext);

        const walletOperation: any = {
            operation: "finalize-game",
            transactionId: "2",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: true,
            bet: 0,
            win: 0,
            currency: "USD",
            ts: new Date()
        };
        await context.prepareFinalizeGame(walletOperation);
        context = await this.contextManager.findGameContextById(this.gameID);

        walletOperation.ts = walletOperation.ts.toISOString();
        expect(context.pendingModification).deep.equal({
            "newState": null,
            "walletOperation": walletOperation,
        });

        await context.commitPendingModification();

        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        expect(context.gameSerialNumber).equal(0);
        expect(context.totalEventId).equal(1);
        expect(context.pendingModification).is.undefined;
        expect(context.gameContext).is.undefined;
        expect(context.jpContext).is.undefined;
    }
}
