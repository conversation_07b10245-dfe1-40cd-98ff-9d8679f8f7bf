import { expect, should, use } from "chai";
import { TEST_MODULE_NAME } from "../helper";
import { BaseGameContextSpec } from "./gameContext.spec";
import { suite, test } from "mocha-typescript";
import { SpecialState } from "../../skywind/services/context/gamecontext";
import * as _ from "lodash";

const chaiAsPromise = require("chai-as-promised");

should();
use(chaiAsPromise);

@suite("GameContext.specialState")
class GameContextSpecialStateSpec extends BaseGameContextSpec {
    public async before() {
        await super.before();
        await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME);
    }

    @test("sets special state: BROKEN_INTEGRATION")
    public async testSetSpecialState() {
        let gameContext = await this.contextManager.findGameContextById(this.gameID);

        await gameContext.setSpecialState(SpecialState.BROKEN_INTEGRATION);
        expect(gameContext.specialState).equal(SpecialState.BROKEN_INTEGRATION);

        gameContext = await this.contextManager.findGameContextById(gameContext.id);
        const expected = {
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "gameData": this.gameData,
            "id": gameContext.id,
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "gameVersion": "0.1.0",
            "roundEnded": true,
            "roundId": "0",
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "round": undefined,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "persistencePolicy": 0,
            "jpContext": undefined,
            "gameContext": undefined,
            "settings": this.settings,
            "specialState": SpecialState.BROKEN_INTEGRATION,
            "version": 2,
        };
        expect(_.omit(gameContext, "createdAt", "updatedAt", "requestContext", "_sessionId")).deep.equal(expected);
    }

    @test("clear special state")
    public async testClearSpecialState() {
        let gameContext = await this.contextManager.findGameContextById(this.gameID);
        await gameContext.setSpecialState(SpecialState.BROKEN_INTEGRATION);
        gameContext = await this.contextManager.findGameContextById(gameContext.id);
        expect(gameContext.specialState).equal(SpecialState.BROKEN_INTEGRATION);
        await gameContext.clearSpecialState();
        gameContext = await this.contextManager.findGameContextById(gameContext.id);
        const expected = {
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "gameData": this.gameData,
            "id": gameContext.id,
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "gameVersion": "0.1.0",
            "roundEnded": true,
            "roundId": "0",
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "round": undefined,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "persistencePolicy": 0,
            "jpContext": undefined,
            "gameContext": undefined,
            "settings": this.settings,
            "specialState": undefined,
            "version": 3,
        };
        expect(_.omit(gameContext, "createdAt", "updatedAt", "requestContext", "_sessionId")).deep.equal(expected);
    }

}
