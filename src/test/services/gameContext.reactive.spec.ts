import { expect, should, use } from "chai";
import { activityList, activityListCount, TEST_MODULE_NAME } from "../helper";
import { BaseGameContextSpec } from "./gameContext.spec";
import { suite, test } from "mocha-typescript";
import { CleanupGameContextService } from "../../skywind/services/cleanup/cleanupGameContextService";

const chaiAsPromise = require("chai-as-promised");

should();
use(chaiAsPromise);

@suite("GameContext.NotReactive")
class GameContextNotReactiveLockSpec extends BaseGameContextSpec {
    public async before() {
        await super.before();
        await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME);
    }

    @test()
    public async testNotReactive() {
        const gameContext = await this.contextManager.findGameContextById(this.gameID, false, false);
        await gameContext.updatePendingModification({ type: "paymnet", win: 10, bet: 1, roundEnded: true } as any, {},
            { type: "spin", data: { type: "someData" } } as any);
        expect(await activityListCount()).equals(1);
        await new CleanupGameContextService().enqueueContexts(Number.MAX_SAFE_INTEGER, 1000);
        expect(await activityListCount()).equals(0);
        await gameContext.commitPendingModification();
        expect(await activityListCount()).equals(0);
    }

    @test()
    public async testReactive() {
        const gameContext = await this.contextManager.findGameContextById(this.gameID, false, true);
        await gameContext.updatePendingModification({ type: "paymnet", win: 10, bet: 1, roundEnded: true } as any, {},
            { type: "spin", data: { type: "someData" } } as any);
        expect(await activityListCount()).equals(1);
        await new CleanupGameContextService().enqueueContexts(Number.MAX_SAFE_INTEGER, 1000);
        expect(await activityListCount()).equals(0);
        await gameContext.commitPendingModification();
        expect(await activityListCount()).equals(1);
    }
}
