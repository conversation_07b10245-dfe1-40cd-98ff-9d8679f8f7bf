import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { ServerProbs } from "../../skywind/services/probs/serverProbs";
import * as request from "supertest";
import { getApplication } from "../../skywind/internalserver";
import * as http from "http";

@suite()
class ServerProbsSpec {
    private server: http.Server;

    public async before() {
        this.server = getApplication().server;
        ServerProbs.install(this.server);
    }

    public async after() {
        if (this.server) {
            this.server.close();
        }
    }

    @test()
    public async testStates() {
        expect(ServerProbs.alive).is.true;
        expect(ServerProbs.ready).is.false;

        expect(await this.getProbes()).deep.equals({
            alive: "true",
            ready: "false"
        });

        ServerProbs.markReady();

        expect(ServerProbs.alive).is.true;
        expect(ServerProbs.ready).is.true;

        expect(await this.getProbes()).deep.equals({
            alive: "true",
            ready: "true"
        });

        ServerProbs.markDecomissioned();

        expect(ServerProbs.alive).is.true;
        expect(ServerProbs.ready).is.false;

        expect(await this.getProbes()).deep.equals({
            alive: "true",
            ready: "false"
        });

        ServerProbs.markDead();

        expect(await this.getProbes()).deep.equals({
            alive: "false",
            ready: "false"
        });
    }

    private async getProbes() {
        const res = await request(this.server)
            .get("/probs")
            .send();

        return {
            alive: res.headers["x-alive"],
            ready: res.headers["x-ready"]
        };
    }
}
