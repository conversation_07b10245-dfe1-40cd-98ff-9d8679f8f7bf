import { expect, should, use } from "chai";
import { getGameHistoryItem, TEST_MODULE_NAME } from "../helper";
import { PaymentOperation } from "../../skywind/services/wallet";
import { GameFlowContextImpl } from "../../skywind/services/context/gameContextImpl";
import { Redis } from "../../skywind/storage/redis";
import { BaseGameContextSpec } from "./gameContext.spec";
import { suite, test } from "mocha-typescript";
import { GameFlowInfoImpl } from "../../skywind/services/context/gameFlowInfo";

const chaiAsPromise = require("chai-as-promised");

should();
use(chaiAsPromise);

@suite("GameContext.forceUpdateFields")
class GameContextForceUpdateFieldsLockSpec extends BaseGameContextSpec {

    @test("markedForFullUpdate and update affects all fields")
    public async markForFullUpdate() {
        let context = await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME);

        // force change version to check markForFullUpdate
        const client = await Redis.get().get();
        try {
            await client.hset(context.id.asString(), "dataVersion", "4");
        } finally {
            await Redis.get().release(client);
        }

        context = await this.contextManager.findGameContextById(this.gameID);

        const state1 = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };
        context.lastRequestId = 2;
        (context.settings as any).marker = "fullUpdate_settings";
        (context.gameData as any).marker = "fullUpdate_gameData";

        await context.update(state1);
        context = await this.contextManager.findGameContextById(this.gameID);

        expect(context.settings).deep.equal({ ...this.settings, marker: "fullUpdate_settings" });
        expect(context.gameData).deep.equal({ ...this.gameData, marker: "fullUpdate_gameData" });
        expect(context.gameContext).deep.equal(state1);
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);

        const state2 = {
            currentScene: "sceneTest2",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };
        context.lastRequestId = 2;
        (context.settings as any).marker = "fullUpdate_settings2";
        (context.gameData as any).marker = "fullUpdate_gameData2";

        await context.update(state2);
        context = await this.contextManager.findGameContextById(this.gameID);

        expect(context.settings).deep.equal({ ...this.settings, marker: "fullUpdate_settings" });
        expect(context.gameData).deep.equal({ ...this.gameData, marker: "fullUpdate_gameData" });
        expect(context.gameContext).deep.equal(state2);
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);
    }

    @test("if no markForFullUpdate - update will not affect all fields")
    public async testNoMarkForFullUpdate() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        let context = await this.contextManager.findGameContextById(this.gameID);

        const state1 = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };
        context.lastRequestId = 2;
        (context.settings as any).marker = "fullUpdate_settings";
        (context.gameData as any).marker = "fullUpdate_gameData";

        await context.update(state1);
        context = await this.contextManager.findGameContextById(this.gameID);

        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state1);
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);

        const state2 = {
            currentScene: "sceneTest2",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };
        context.lastRequestId = 2;
        (context.settings as any).marker = "fullUpdate_settings2";
        (context.gameData as any).marker = "fullUpdate_gameData2";

        await context.update(state2);
        context = await this.contextManager.findGameContextById(this.gameID);

        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state2);
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);
    }

    @test("update affects all fields")
    public async testUpdateAllFields() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        let context: GameFlowContextImpl = await this.contextManager.findGameContextById(this.gameID);
        context.markForFullUpdate();
        const state1 = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };
        context.lastRequestId = 2;
        (context.settings as any).marker = "fullUpdate_settings";
        (context.gameData as any).marker = "fullUpdate_gameData";

        await context.update(state1);
        context = await this.contextManager.findGameContextById(this.gameID);

        expect(context.settings).deep.equal({ ...this.settings, marker: "fullUpdate_settings" });
        expect(context.gameData).deep.equal({ ...this.gameData, marker: "fullUpdate_gameData" });
        expect(context.gameContext).deep.equal(state1);
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);

        const state2 = {
            currentScene: "sceneTest2",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };
        context.lastRequestId = 2;
        (context.settings as any).marker = "fullUpdate_settings2";
        (context.gameData as any).marker = "fullUpdate_gameData2";

        await context.update(state2);
        context = await this.contextManager.findGameContextById(this.gameID);

        expect(context.settings).deep.equal({ ...this.settings, marker: "fullUpdate_settings" });
        expect(context.gameData).deep.equal({ ...this.gameData, marker: "fullUpdate_gameData" });
        expect(context.gameContext).deep.equal(state2);
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);
    }

    @test("updatePending affects all fields")
    public async testUpdatePending() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        let context = await this.contextManager.findGameContextById(this.gameID);
        const request = { request: "req", requestId: 1 };
        const state1 = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };

        const history1: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2, 3],
            }
        };

        const walletOperation1: any = {
            operation: "payment",
            transactionId: "1",
            bet: 1900,
            win: 200,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history1.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        (context.settings as any).marker = "fullUpdate_settings";
        (context.gameData as any).marker = "fullUpdate_gameData";
        context.markForFullUpdate();
        await context.updatePendingModification(walletOperation1, state1, request, history1);
        walletOperation1.ts = walletOperation1.ts.toISOString();
        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };

        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context).contain(expected);
        expect(context.settings).deep.equal({ ...this.settings, marker: "fullUpdate_settings" });
        expect(context.gameData).deep.equal({ ...this.gameData, marker: "fullUpdate_gameData" });
        expect(context.gameContext).is.undefined;
        expect(context.pendingModification).deep.equal({
            "history": history1,
            "newState": state1,
            "walletOperation": walletOperation1,
        });

        (context.settings as any).marker = "fullUpdate_settings2";
        (context.gameData as any).marker = "fullUpdate_gameData2";
        await context.commitPendingModification();
        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context).contain(expected);
        expect(context.settings).deep.equal({ ...this.settings, marker: "fullUpdate_settings" });
        expect(context.gameData).deep.equal({ ...this.gameData, marker: "fullUpdate_gameData" });
        expect(context.gameContext).deep.equal(state1);
        expect(context.pendingModification).is.undefined;

        const result: any = await getGameHistoryItem();
        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 0,
            ctrl: result.ctrl,
            roundId: "0",
            sessionId: this.sessionId.sessionId,
            result: history1.data,
            type: history1.type,
            gameVersion: TEST_MODULE_NAME.version,
            walletTransactionId: "1",
            currency: "USD",
            win: walletOperation1.win,
            bet: walletOperation1.bet,
            roundEnded: true,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 1900,
            totalRoundWin: 200
        });
    }

    @test("commitPending affects all fields")
    public async testCommitPending() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        let context = await this.contextManager.findGameContextById(this.gameID);
        const request = { request: "req", requestId: 1 };
        const state1 = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };

        const history1: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2, 3],
            }
        };

        const walletOperation1: any = {
            operation: "payment",
            transactionId: "1",
            bet: 1900,
            win: 200,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history1.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        (context.settings as any).marker = "fullUpdate_settings";
        (context.gameData as any).marker = "fullUpdate_gameData";
        await context.updatePendingModification(walletOperation1, state1, request, history1);
        walletOperation1.ts = walletOperation1.ts.toISOString();
        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };

        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        expect(context.pendingModification).deep.equal({
            "history": history1,
            "newState": state1,
            "walletOperation": walletOperation1,
        });

        (context.settings as any).marker = "fullUpdate_settings2";
        (context.gameData as any).marker = "fullUpdate_gameData2";
        context.markForFullUpdate();
        await context.commitPendingModification();
        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context).contain(expected);
        expect(context.settings).deep.equal({ ...this.settings, marker: "fullUpdate_settings2" });
        expect(context.gameData).deep.equal({ ...this.gameData, marker: "fullUpdate_gameData2" });
        expect(context.gameContext).deep.equal(state1);
        expect(context.pendingModification).is.undefined;

        const result: any = await getGameHistoryItem();
        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 0,
            ctrl: result.ctrl,
            roundId: "0",
            sessionId: this.sessionId.sessionId,
            result: history1.data,
            type: history1.type,
            gameVersion: TEST_MODULE_NAME.version,
            walletTransactionId: "1",
            currency: "USD",
            win: walletOperation1.win,
            bet: walletOperation1.bet,
            roundEnded: true,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 1900,
            totalRoundWin: 200
        });
    }

    @test("rollback affects all fields")
    public async testRollback() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        const context = await this.contextManager.findGameContextById(this.gameID);

        const state1 = {
            currentScene: "sceneTest",
            state: {
                multiplier: 1,
                behaviorsState: {}
            }
        };
        context.lastRequestId = 2;

        const request = { request: "req", requestId: 1 };

        const history1: any = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: {
                positions: [1, 2, 3, 4],
            }
        };

        const walletOperation1: PaymentOperation = {
            operation: "payment",
            transactionId: "1",
            bet: 1900,
            win: 200,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.id,
            roundEnded: history1.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, state1, request, history1, {
            jackpotOperation: {
                type: "contribution",
                payload: {
                    transactionId: "jpn_trx_id",
                    amount: 1000,
                    roundId: "1"
                }
            }
        });

        expect(context).contain({
            id: this.gameID,
            lastRequestId: 2,
        });
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history1,
            "newState": state1,
            "walletOperation": walletOperation1,
        });

        expect(context.jackpotPending).deep.equal({
            jackpotOperation: {
                type: "contribution",
                payload: {
                    transactionId: "jpn_trx_id",
                    amount: 1000,
                    roundId: "1"
                }
            }
        });

        expect(context.roundId).equals("0");
        (context.settings as any).marker = "fullUpdate_settings";
        (context.gameData as any).marker = "fullUpdate_gameData";
        context.markForFullUpdate();
        await context.rollbackPendingModification();
        expect(context).contain({
            id: this.gameID,
            lastRequestId: 2,
        });
        expect(context.roundId).equals("1");
        expect(context.session).to.not.be.empty;
        expect(context.settings).deep.equal({ ...this.settings, marker: "fullUpdate_settings" });
        expect(context.gameData).deep.equal({ ...this.gameData, marker: "fullUpdate_gameData" });
        expect(context.gameContext).is.undefined;
        expect(context.pendingModification).is.undefined;
        expect(context.jackpotPending).is.undefined;
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);

        const result: any = await getGameHistoryItem();
        expect(result).is.undefined;
    }
}
