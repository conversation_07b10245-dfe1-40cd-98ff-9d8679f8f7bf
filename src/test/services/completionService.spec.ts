import { suite, test } from "mocha-typescript";
import { expect, use } from "chai";
import { createGameToken, flushAll, TEST_MODULE_NAME } from "../helper";
import { Currency } from "@skywind-group/sw-game-core";
import { GameData, LogoutType } from "../../skywind/services/auth";
import { GameContextID } from "../../skywind/services/contextIds";
import { GameTokenData } from "../../skywind/services/tokens";
import { GameSession } from "../../skywind/services/gameSession";
import { getGameFlowContextManager } from "../../skywind/services/contextmanager/contextManagerImpl";
import { testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import { MerchantLogoutResult, SpecialState } from "../../skywind/services/context/gamecontext";
import CompletionService from "../../skywind/services/pendingCompletionService";
import * as GameService from "../../skywind/services/game/game";
import { SinonStub, stub } from "sinon";
import { DumyGame } from "../testGames";
import { ManagementAPITransientError } from "../../skywind/errors";
import config from "../../skywind/config";
import * as _ from "lodash";
import { setRandomGeneratorFactory } from "../../skywind/services/random";
import { RandomGeneratorFactoryImpl } from "../testRandomFactory";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;
import status200 = testing.status200;
import status500 = testing.status500;

use(require("chai-as-promised"));

@suite()
class CompletionServiceSpec {
    private request: RequestMock;
    private loadGame: SinonStub;
    private prevValue: boolean;

    public async before() {
        setRandomGeneratorFactory(new RandomGeneratorFactoryImpl());
        this.prevValue = config.walletThroughAPI;
        config.walletThroughAPI = true;
        this.request = requestMock(superagent);
        this.loadGame = stub(GameService, "load");
        this.loadGame.returns(Promise.resolve({ name: "gameId", game: new DumyGame() }));
        await flushAll();
    }

    public after() {
        config.walletThroughAPI = this.prevValue;
        this.loadGame.restore();
        this.request.unmock(superagent);
    }

    @test()
    public async testRequireRetry() {
        expect(CompletionService.requireRetry(await this.createContext("player001"))).is.false;
        expect(CompletionService.requireRetry(await this.createContext("player002",
            { logoutOptions: { type: LogoutType.ALL } }))).is.true;
        expect(CompletionService.requireRetry(await this.createContext("player003",
            { logoutOptions: { type: LogoutType.UNFINISHED } }))).is.false;

        const context1 = await this.createContext("player004",
            { logoutOptions: { type: LogoutType.UNFINISHED } });
        await context1.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context1.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        expect(CompletionService.requireRetry(context1)).is.true;

        const context2 = await this.createContext("player005",
            { logoutOptions: { type: LogoutType.ALL } });
        await context2.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context2.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        expect(CompletionService.requireRetry(context2)).is.true;

        const context3 = await this.createContext("player006",
            { logoutOptions: { type: LogoutType.UNFINISHED } });
        await context3.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context3.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });

        await context3.commitPendingModification();
        expect(CompletionService.requireRetry(context3)).is.true;

        const context4 = await this.createContext("player007",
            { logoutOptions: { type: LogoutType.UNFINISHED } });
        await context4.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context4.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: true, data: {} });

        await context4.commitPendingModification();
        expect(CompletionService.requireRetry(context4)).is.false;

        const context5 = await this.createContext("player008",
            { logoutOptions: { type: LogoutType.UNFINISHED } });
        await context5.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);
        expect(CompletionService.requireRetry(context5)).is.true;
    }

    @test()
    public async testRequireCompletion() {
        expect(CompletionService.requireCompletion(await this.createContext("player001"))).is.false;
        expect(CompletionService.requireCompletion(await this.createContext("player002",
            { logoutOptions: { type: LogoutType.ALL } }))).is.true;
        expect(CompletionService.requireCompletion(await this.createContext("player003",
            { logoutOptions: { type: LogoutType.UNFINISHED } }))).is.false;

        const context1 = await this.createContext("player004",
            { logoutOptions: { type: LogoutType.UNFINISHED } });
        await context1.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context1.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        expect(CompletionService.requireCompletion(context1)).is.false;

        const context11 = await this.createContext("player004_1",
            { settings: { maxPaymentRetryAttempts: 10 } });
        await context11.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context11.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        expect(CompletionService.requireCompletion(context11)).is.true;

        await context11.setSpecialState(SpecialState.BROKEN_INTEGRATION);
        expect(CompletionService.requireCompletion(context11)).is.false;
        await context11.setSpecialState(SpecialState.FINALIZING);
        expect(CompletionService.requireCompletion(context11)).is.false;
        await context11.setSpecialState(SpecialState.CANNOT_COMPLETE_PAYMENT);
        expect(CompletionService.requireCompletion(context11)).is.false;

        const context2 = await this.createContext("player005",
            { settings: {maxPaymentRetryAttempts: 3}, logoutOptions: { type: LogoutType.ALL } });
        await context2.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context2.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        expect(CompletionService.requireCompletion(context2)).is.true;

        const context3 = await this.createContext("player006",
            { logoutOptions: { type: LogoutType.UNFINISHED } });
        await context3.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context3.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });

        await context3.commitPendingModification();
        expect(CompletionService.requireCompletion(context3)).is.true;

        const context4 = await this.createContext("player007",
            { logoutOptions: { type: LogoutType.UNFINISHED } });
        await context4.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context4.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: true, data: {} });

        await context4.commitPendingModification();
        expect(CompletionService.requireRetry(context4)).is.false;

        const context5 = await this.createContext("player008",
            { logoutOptions: { type: LogoutType.UNFINISHED } });
        await context5.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);
        expect(CompletionService.requireCompletion(context5)).is.true;
    }

    @test()
    public async testRequireCompletionPendingPaymentLogoutSettingsIgnored() {
        const context = await this.createContext("player005",
            {
                settings: { maxPaymentRetryAttempts: 4 },
                logoutOptions: { type: LogoutType.ALL, maxRetryAttempts: 10 }
            });
        await context.incrementRetryAttempts();
        await context.incrementRetryAttempts();
        await context.incrementRetryAttempts();
        await context.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        expect(CompletionService.requireCompletion(context)).is.true;

        const context2 = await this.createContext("player005",
            {
                settings: { maxPaymentRetryAttempts: 3 },
                logoutOptions: { type: LogoutType.ALL, maxRetryAttempts: 10 }
            });
        await context2.incrementRetryAttempts();
        await context2.incrementRetryAttempts();
        await context2.incrementRetryAttempts();
        await context2.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context2.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        expect(CompletionService.requireCompletion(context2)).is.false;
    }

    @test()
    public async testRequireCompletionWhenLogoutRequired() {
        const context = await this.createContext("player005",
            {
                settings: { maxPaymentRetryAttempts: 10 },
                logoutOptions: { type: LogoutType.ALL, maxRetryAttempts: 4 }
            });
        await context.incrementRetryAttempts();
        await context.incrementRetryAttempts();
        await context.incrementRetryAttempts();
        expect(CompletionService.requireCompletion(context)).is.true;

        const context2 = await this.createContext("player005",
            {
                settings: { maxPaymentRetryAttempts: 10 },
                logoutOptions: { type: LogoutType.ALL, maxRetryAttempts: 3 }
            });
        await context2.incrementRetryAttempts();
        await context2.incrementRetryAttempts();
        await context2.incrementRetryAttempts();
        expect(CompletionService.requireCompletion(context2)).is.false;
    }

    @test()
    public async testRetryPayment() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        let context = await this.createContext("player001",
            { settings: { maxRetryAttemptsTimeout: 10 } });
        await context.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        await CompletionService.retry(context);
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.transactionId).equals("trxId");
        expect(this.request.args.length).equal(1);
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(CompletionService.requireRetry(context)).is.false;
    }

    @test()
    public async testRetryPayment_Logout() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/logout", status200({}));
        let context = await this.createContext("player001",
            {
                settings: { maxRetryAttemptsTimeout: 10 },
                logoutOptions: { type: LogoutType.UNFINISHED },
            });
        await context.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        await CompletionService.retry(context);
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.transactionId).equals("trxId");
        expect(this.request.args[1].url).equals("http://api:3006//v2/play/game/logout");
        expect(this.request.args.length).equal(2);
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(CompletionService.requireRetry(context)).is.false;
    }

    @test()
    public async testRetryPayment_LogoutFailed_Logout() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/logout", status500({}));
        let context = await this.createContext("player001",
            {
                settings: { maxRetryAttemptsTimeout: 10 },
                logoutOptions: { type: LogoutType.UNFINISHED },
            });
        await context.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        await expect(CompletionService.retry(context)).to.be.rejectedWith(ManagementAPITransientError);
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.transactionId).equals("trxId");
        expect(this.request.args[1].url).equals("http://api:3006//v2/play/game/logout");
        expect(this.request.args.length).equal(2);
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(CompletionService.requireRetry(context)).is.true;

        this.request.clearRoutes();

        this.request.post("http://api:3006//v2/play/game/logout", status200({}));
        await CompletionService.retry(context);
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/game/logout");
        expect(this.request.args.length).equal(1);
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(CompletionService.requireRetry(context)).is.false;
    }

    @test()
    public async testCompletePayment() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        let context = await this.createContext("player001",
            { settings: { maxPaymentRetryAttempts: 2 } });
        await context.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        await CompletionService.complete(context);
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.transactionId).equals("trxId");
        expect(this.request.args.length).equal(1);
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(CompletionService.requireCompletion(context)).is.false;
        expect(context.retryAttempts).equal(0);
    }

    @test()
    public async testMaxAttemptsForPayment() {
        this.request.put("http://api:3006//v2/play/payment", status500());
        let context = await this.createContext("player001",
            { settings: { maxPaymentRetryAttempts: 2 } });
        await context.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            operation: "payment",
            gameSessionId: context.session.id,
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        await expect(CompletionService.complete(context)).to.be.rejectedWith(ManagementAPITransientError);
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.transactionId).equals("trxId");
        expect(this.request.args.length).equal(1);
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(CompletionService.requireCompletion(context)).is.true;
        expect(context.retryAttempts).equal(1);

        await expect(CompletionService.complete(context)).to.be.rejectedWith(ManagementAPITransientError);
        expect(this.request.args[1].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[1].body.transactionId).equals("trxId");
        expect(this.request.args.length).equal(2);
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(CompletionService.requireCompletion(context)).is.false;
        expect(context.retryAttempts).equal(2);
    }

    @test()
    public async testCompletePayment_Failed() {
        this.request.put("http://api:3006//v2/play/payment", status500({}));
        let context = await this.createContext("player001",
            { settings: { maxPaymentRetryAttempts: 10 } });
        await context.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        await expect(CompletionService.complete(context)).to.be.rejectedWith(ManagementAPITransientError);
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.transactionId).equals("trxId");
        expect(this.request.args.length).equal(1);
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(CompletionService.requireCompletion(context)).is.true;
        expect(context.retryAttempts).equal(1);
    }

    @test()
    public async testCompletePayment_Failed_CannotCompletePayment_state() {
        this.request.put("http://api:3006//v2/play/payment", status500({ code: 806 }));
        let context = await this.createContext("player001",
            { settings: { maxPaymentRetryAttempts: 10 } });
        await context.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        await expect(CompletionService.complete(context)).to.be.rejectedWith(ManagementAPITransientError);
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.transactionId).equals("trxId");
        expect(this.request.args.length).equal(1);
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(CompletionService.requireCompletion(context)).is.false;
        expect(context.retryAttempts).equal(1);
        expect(context.specialState).equal(SpecialState.CANNOT_COMPLETE_PAYMENT);
    }

    @test()
    public async testCompletePayment_Logout() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/logout", status200({}));
        let context = await this.createContext("player001",
            {
                settings: { maxRetryAttemptsTimeout: 10 },
                logoutOptions: { type: LogoutType.UNFINISHED },
            });
        await context.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        await CompletionService.complete(context);
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.transactionId).equals("trxId");
        expect(this.request.args[1].url).equals("http://api:3006//v2/play/game/logout");
        expect(this.request.args.length).equal(2);
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(CompletionService.requireCompletion(context)).is.false;
        expect(context.retryAttempts).equal(0);
    }

    @test()
    public async testCompletePayment_LogoutFailed_Logout() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/logout", status500({}));
        let context = await this.createContext("player001",
            {
                settings: { maxPaymentRetryAttempts: 10 },
                logoutOptions: { type: LogoutType.UNFINISHED, maxRetryAttempts: 10 },
            });
        await context.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        await expect(CompletionService.complete(context)).to.be.rejectedWith(ManagementAPITransientError);
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.transactionId).equals("trxId");
        expect(this.request.args[1].url).equals("http://api:3006//v2/play/game/logout");
        expect(this.request.args.length).equal(2);
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(CompletionService.requireCompletion(context)).is.true;
        expect(context.retryAttempts).equal(1);

        this.request.clearRoutes();

        this.request.post("http://api:3006//v2/play/game/logout", status200({}));
        await CompletionService.complete(context);
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/game/logout");
        expect(this.request.args.length).equal(1);
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(CompletionService.requireRetry(context)).is.false;
        expect(context.retryAttempts).equal(0);
    }

    @test()
    public async testLogoutControl_brokenPayment_and_success_logout() {
        this.request.put("http://api:3006//v2/play/payment", status500({ code: 806 }));
        this.request.post("http://api:3006//v2/play/game/logout", status200({}));
        const context = await this.createContext("player001",
            {
                settings: {
                    maxPaymentRetryAttempts: 10,
                    maxRetryAttemptsTimeout: 10,
                    logoutControl: { ignorePayments: { offlineRetry: true, gameClosure: true, gameRelaunch: true } }
                },
                logoutOptions: { type: LogoutType.ALL },
            });
        await context.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        await expect(CompletionService.complete(context)).to.be.rejectedWith(ManagementAPITransientError);
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[0].body.transactionId).equals("trxId");
        expect(this.request.args[1].url).equals("http://api:3006//v2/play/game/logout");
        expect(this.request.args.length).equal(2);
    }

    @test()
    public async testLogoutControl_for_relaunch() {
        this.request.put("http://api:3006//v2/play/payment", status500({ code: 806 }));
        this.request.post("http://api:3006//v2/play/game/logout", status200({}));
        let context = await this.createContext("player001",
            {
                settings: {
                    maxPaymentRetryAttempts: 10,
                    maxRetryAttemptsTimeout: 10,
                    logoutControl: { ignorePayments: { offlineRetry: true, gameClosure: true, gameRelaunch: true } }
                },
                logoutOptions: { type: LogoutType.ALL },
            });
        await context.updatePendingModification({
            transactionId: "trxId",
            roundId: "1",
            roundPID: "pid",
            gameSessionId: context.session.id,
            operation: "payment",
            currency: "USD",
            ts: new Date()
        }, {}, {} as any, { type: "spin", roundEnded: false, data: {} });
        await CompletionService.completeBeforeRelaunch(context);
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/game/logout");
        expect(this.request.args.length).equal(1);
        context = await getGameFlowContextManager().findGameContextById(context.id);
        expect(CompletionService.requireCompletion(context)).is.true;
    }

    private async createContext(playerCode: string, customGameData: Partial<GameData> = {}) {
        const currency: Currency = 0;
        const settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: currency,
            stakeAll: [1, 2, 3, 4],
            stakeDef: currency,
            stakeMax: currency,
            stakeMin: currency,
            winMax: currency,
            currencyMultiplier: 100,
        };

        const gameID: GameContextID = GameContextID.create("gameId", 1, playerCode, "deviceId", "real");

        const gameData: GameData = _.merge({
            gameTokenData: undefined,
            limits: settings,
        }, customGameData);

        const gameTokenData: GameTokenData = {
            playerCode: gameID.playerCode,
            gameCode: "GM001",
            brandId: gameID.brandId,
            currency: "USD",
            playmode: "real"
        };

        gameTokenData.token = await createGameToken(gameTokenData);
        gameData.gameTokenData = gameTokenData;
        const sessionId = await GameSession.generate(gameID, "real");

        return await getGameFlowContextManager().findOrCreateGameContext(gameID, sessionId, gameData, TEST_MODULE_NAME);
    }
}
