import { expect, should, use } from "chai";
import * as Errors from "../../skywind/errors";
import { ConcurrentAccessToGameSession } from "../../skywind/errors";
import { Currency } from "@skywind-group/sw-game-core";
import { GameTokenData } from "../../skywind/services/tokens";
import { GameContextID } from "../../skywind/services/contextIds";
import { GameFlowContext } from "../../skywind/services/context/gamecontext";
import { createGameToken, flushAll, getGameHistoryItem, TEST_MODULE_NAME } from "../helper";
import { PaymentOperation } from "../../skywind/services/wallet";
import config from "../../skywind/config";
import { getGameContextEncoder } from "../../skywind/services/encoder/contextEncoding";
import { GameData } from "../../skywind/services/auth";
import { GameSession } from "../../skywind/services/gameSession";
import { sleep } from "@skywind-group/sw-utils";
import { GameFlowContextImpl } from "../../skywind/services/context/gameContextImpl";
import { getGameFlowContextManager } from "../../skywind/services/contextmanager/contextManagerImpl";
import { GameFlowInfoImpl } from "../../skywind/services/context/gameFlowInfo";
import _ = require("lodash");

const chaiAsPromise = require("chai-as-promised");

should();
use(chaiAsPromise);

describe("Fun Game context", () => {
    const contextManager = getGameFlowContextManager("fun");
    const currency: Currency = 0;
    const settings = {
        coins: [3, 4],
        defaultCoin: 4,
        maxTotalStake: currency,
        stakeAll: [1, 2, 3, 4],
        stakeDef: currency,
        stakeMax: currency,
        stakeMin: currency,
        winMax: currency,
        currencyMultiplier: 100,
    };

    const gameID: GameContextID = GameContextID.create("gameId", 1, "playerId", "deviceId");

    const gameTokenData: GameTokenData = {
        playerCode: gameID.playerCode,
        gameCode: "GM001",
        brandId: gameID.brandId,
        currency: "USD",
        playmode: "fun"
    };

    const gameData: GameData = {
        gameTokenData: gameTokenData,
        limits: settings
    };

    let sessionId: GameSession;

    before(async () => {
        gameTokenData.token = await createGameToken(gameTokenData);
    });

    beforeEach(async () => {
        await flushAll();
        sessionId = await GameSession.generate(gameID, "fun");
    });

    it("is created", async () => {
        let context: GameFlowContext = await contextManager
            .findOrCreateGameContext(gameID, sessionId, gameData, TEST_MODULE_NAME);
        const expected = {
            currentScene: "scene",
        };

        expect(context.settings).deep.equal(settings);
        expect(context.gameData).deep.equal(gameData);
        expect(context.gameContext).is.undefined;
        expect(context.playerContext).is.undefined;

        await context.update(expected);
        context = await contextManager.findGameContextById(context.id);
        expect(context.settings).deep.equal(settings);
        expect(context.gameData).deep.equal(gameData);
        expect(context.gameContext).deep.equal(expected);
        expect(context.playerContext).is.undefined;
    });

    it("is created from copy", async () => {
        const id = GameContextID.createFromString(
            config.namespaces.contextPrefix + ":1:playerId:gameId:deviceId");
        const context: GameFlowContextImpl = new GameFlowContextImpl(id);
        context.gameContext = {
            scenesState: {},
            currentScene: "scene",
            nextScene: "next",
            history: [],
            stake: null,
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };
        context.session = sessionId;
        context.lastRequestId = 1;
        context.gameSerialNumber = 1;
        context.totalEventId = 1;
        context.settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: 0,
            stakeAll: [1, 2, 3, 4],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 100,
            transferEnabled: false,
        };
        context.version = 3;
        context.gameData = gameData;
        context.pendingModification = {
            history: {
                type: "slot",
                roundEnded: true,
                data: { positions: [1, 2, 3] }
            },
            walletOperation: {
                operation: "payment",
                transactionId: "trxId",
                currency: "USD",
                bet: 100,
                win: 1000,
            } as PaymentOperation,
            newState: {},
        };

        context.roundEnded = false;
        context.roundId = "1";

        const newSettings = {
            coins: [5, 6],
            defaultCoin: 5,
            maxTotalStake: 0,
            stakeAll: [5, 6, 7, 8],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 1000,
            transferEnabled: false,
        };

        const newSessionId = await GameSession.generate(gameID, "fun");

        const copy = await contextManager.createContextFrom(id,
            await getGameContextEncoder().encode(context as GameFlowContextImpl),
            newSessionId,
            gameData,
            TEST_MODULE_NAME,
            newSettings);

        const expected = {
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": newSessionId,
            "gameData": gameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": context.id,
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "roundEnded": false,
            "roundId": "1",
            "pendingModification": context.pendingModification,
            "gameContext": context.gameContext,
            "settings": newSettings,
            "jpContext": undefined,
            "persistencePolicy": 0,
            "round": undefined,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "jackpotPending": undefined,
            "specialState": undefined,
            "version": 4,
        };
        expect(_.omit(copy, "createdAt", "updatedAt", "requestContext")).deep.equal(expected);
    });
    //
    it("is updated successfully", async () => {
        let context = await contextManager
            .findOrCreateGameContext(gameID, sessionId, gameData, TEST_MODULE_NAME);
        context = await contextManager.findGameContextById(gameID);
        const state = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };
        context.lastRequestId = 2;

        await context.update(state);

        expect(context.settings).deep.equal(settings);
        expect(context.gameData).deep.equal(gameData);
        expect(context.gameContext).deep.equal(state);
        expect(context.playerContext).is.undefined;
    });
    it("is updated & committed pending modification", async () => {
        await contextManager.findOrCreateGameContext(gameID,
            sessionId,
            gameData,
            TEST_MODULE_NAME);
        let context = await contextManager.findGameContextById(gameID);
        const request = { request: "req", requestId: 1 };
        const state1 = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };

        const history1: any = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: {
                positions: [1, 2, 3],
            }
        };

        const walletOperation1: any = {
            operation: "payment",
            transactionId: "1",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history1.roundEnded,
            bet: 1900,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, state1, request, history1);

        walletOperation1.ts = walletOperation1.ts.toISOString();
        const expected = {
            id: gameID,
            lastRequestId: 0,
        };

        context = await contextManager.findGameContextById(gameID);
        expect(context).contain(expected);
        expect(context.settings).deep.equal(settings);
        expect(context.gameData).deep.equal(gameData);
        expect(context.gameContext).is.undefined;
        expect(context.pendingModification).deep.equal({
            "history": history1,
            "newState": state1,
            "walletOperation": walletOperation1,
        });
        expect(context.playerContext).is.undefined;

        await context.commitPendingModification(undefined);
        expect(context).contain(expected);
        expect(context.settings).deep.equal(settings);
        expect(context.gameData).deep.equal(gameData);
        expect(context.gameContext).deep.equal(state1);
        expect(context.pendingModification).is.undefined;
        expect(context.playerContext).is.undefined;

        let result: any = await getGameHistoryItem();
        expect(result).is.undefined;

        const history2: any = {
            type: "slot",
            gameVersion: TEST_MODULE_NAME.version,
            roundEnded: true,
            data: {
                positions: [1, 2],
            }
        };

        const state2 = _.cloneDeep(state1);
        state2["nextScene"] = "SOMESCENE2";

        const walletOperation2: any = {
            operation: "payment",
            transactionId: "2",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history2.roundEnded,
            bet: 2000,
            win: 100,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation2, state2, request, history2);
        walletOperation2.ts = walletOperation2.ts.toISOString();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(settings);
        expect(context.gameContext).deep.equal(state1);
        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history2,
            "newState": state2,
            "walletOperation": walletOperation2,
        });

        await context.commitPendingModification(undefined);
        expect(context).contain(expected);
        expect(context.settings).deep.equal(settings);
        expect(context.gameData).deep.equal(gameData);
        expect(context.gameContext).deep.equal(state2);
        expect(context.pendingModification).is.undefined;

        result = await getGameHistoryItem();
        expect(result).is.undefined;

        const history3: any = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: {
                positions: [1, 2, 3, 4],
            }
        };

        const state3 = _.cloneDeep(state2);
        state3["nextScene"] = "any";
        const walletOperation3: any = {
            operation: "payment",
            transactionId: "3",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history3.roundEnded,
            bet: 100,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation3, state3, request, history3);
        walletOperation3.ts = walletOperation3.ts.toISOString();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(settings);
        expect(context.gameContext).deep.equal(state2);

        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history3,
            "newState": state3,
            "walletOperation": walletOperation3,
        });

        await context.commitPendingModification(undefined);
        expect(context).contain(expected);
        expect(context.settings).deep.equal(settings);
        expect(context.gameData).deep.equal(gameData);
        expect(context.gameContext).deep.equal(state3);
        expect(context.pendingModification).undefined;
        expect(context.playerContext).is.undefined;
        result = await getGameHistoryItem();
        expect(result).is.undefined;
    });

    it("is updated & rollbacked pending modification", async () => {
        await contextManager.findOrCreateGameContext(gameID, sessionId, gameData, TEST_MODULE_NAME);
        const context = await contextManager.findGameContextById(gameID);
        const request = { request: "req", requestId: 1 };

        const state1 = {
            currentScene: "sceneTest",
            state: {
                multiplier: 1,
                behaviorsState: {}
            }
        };
        context.lastRequestId = 2;

        const history1: any = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: {
                positions: [1, 2, 3, 4],
            }
        };

        const walletOperation1: any = {
            operation: "payment",
            transactionId: "1",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history1.roundEnded,
            bet: 1900,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, state1, request, history1);
        walletOperation1.ts = walletOperation1.ts.toISOString();
        expect(context).contain({
            id: gameID,
            lastRequestId: 2,
        });
        expect(context.settings).deep.equal(settings);
        expect(context.gameData).deep.equal(gameData);
        expect(context.gameContext).is.undefined;
        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history1,
            "newState": state1,
            "walletOperation": walletOperation1,
        });
        expect(context.playerContext).is.undefined;

        await context.rollbackPendingModification();
        expect(context).contain({
            id: gameID,
            lastRequestId: 2,
        });
        expect(context.session).to.not.be.empty;
        expect(context.settings).deep.equal(settings);
        expect(context.gameData).deep.equal(gameData);
        expect(context.gameContext).is.undefined;
        expect(context.pendingModification).is.undefined;
        expect(context.playerContext).is.undefined;

        const result: any = await getGameHistoryItem();
        expect(result).is.undefined;
    });

    it("is found by playerCode,gameID, deviceId and brandID", async () => {
        const context1: GameFlowContext = await contextManager
            .findOrCreateGameContext(gameID,
                sessionId,
                gameData,
                TEST_MODULE_NAME);
        context1.lastRequestId = 2;
        const state = {
            multiplier: 1,
            behaviorsState: {},
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true,
            }
        };

        await context1.update(state);
        expect(context1.playerContext).is.undefined;
        const context2: GameFlowContext = await contextManager.findGameContextById(gameID);

        const expected = {
            id: gameID,
            lastRequestId: 2,
        };

        expect(context2.session.id).contain(sessionId.id);
        expect(context2.gameData).deep.equal(gameData);
        expect(context2.settings).deep.equal(settings);
        expect(context2.gameContext).deep.equal(state);
        expect(context2.playerContext).is.undefined;
    });

    it("is deleted", async () => {
        const context: GameFlowContext = await contextManager
            .findOrCreateGameContext(gameID, sessionId, gameData, TEST_MODULE_NAME);
        const expected = {
            id: gameID,
            lastRequestId: 0,
        };
        expect(context).contain(expected);
        expect(context.session.id).contain(sessionId.id);
        expect(context.gameData).deep.equal(gameData);
        expect(context.settings).deep.equal(settings);
        expect(context.playerContext).is.undefined;
        await context.remove();

        expect(await contextManager.findGameContextById(gameID)).is.undefined;
    });

    it("isn't found because not exists", async () => {
        expect(await contextManager.findGameContextById(GameContextID.create("gameId",
            1,
            "playerId",
            "deviceId"))).is.undefined;
    });

    describe("doesn't support method", () => {

        it("getLruGameContextIDs", () => {
            return contextManager.getLruGameContextIDs(Date.now(), 1000)
                .should
                .eventually
                .rejectedWith("Unsupported method!");
        });

        it("removeGameContextActivity", () => {
            return contextManager.removeGameContextActivity("id", 1000)
                .should
                .eventually
                .rejectedWith("Unsupported method!");
        });

        it("saveGameContextOffline", async () => {
            const gameContext: GameFlowContextImpl = await contextManager.findOrCreateGameContext(gameID,
                sessionId,
                gameData,
                TEST_MODULE_NAME);
            return contextManager.saveGameContextOffline([gameContext])
                .should
                .eventually
                .rejectedWith("Unsupported method!");
        });

        it("removeGameContextOffline", async () => {
            const gameContext: GameFlowContext = await contextManager.findOrCreateGameContext(gameID,
                sessionId,
                gameData,
                TEST_MODULE_NAME);
            const result = await contextManager.removeGameContextOffline([gameContext.id]);
            expect(result).to.be.undefined;
        });

        it("findTopActiveGameContextIDsByBrand", async () => {
            const gameContext: GameFlowContext = await contextManager.findOrCreateGameContext(gameID,
                sessionId,
                gameData,
                TEST_MODULE_NAME);
            expect(await contextManager.findTopActiveGameContextIDsByBrand(gameContext.id.brandId, 100)).is.undefined;
        });

        it("findTopActivePlayerContextIDsByBrand", async () => {
            const gameContext: GameFlowContext = await contextManager.findOrCreateGameContext(gameID,
                sessionId,
                gameData,
                TEST_MODULE_NAME);
            expect(await contextManager.findTopActivePlayerContextIDsByBrand(gameContext.id.brandId, 100)).is.undefined;
        });
    });

    describe("support differently method", () => {
        let oldValue: number;
        before(() => {
            oldValue = config.funGame.TTL;
            config.funGame.TTL = 1;
        });

        after(() => {
            config.funGame.TTL = oldValue;
        });

        it("is removed after expiry time", async () => {
            const gameContext: GameFlowContext = await contextManager.findOrCreateGameContext(gameID,
                sessionId,
                gameData,
                TEST_MODULE_NAME);

            await sleep(1500);

            expect(await contextManager.findGameContextById(gameContext.id)).is.undefined;
        });

        it("findOffline", async () => {
            const gameContext: GameFlowContext = await contextManager.findOrCreateGameContext(gameID,
                sessionId,
                gameData,
                TEST_MODULE_NAME);
            const result = await contextManager.findOfflineGameContext(gameContext.id);
            expect(result).is.undefined;
        });
    });

    describe("isn't updated because of ConcurrentAccessToGameSession error", () => {
        it("when requestId is stale", async () => {
            const originGameContext: GameFlowContext = await contextManager
                .findOrCreateGameContext(gameID,
                    sessionId,
                    gameData,
                    TEST_MODULE_NAME);

            const expected = {
                id: gameID,
                lastRequestId: 0,
            };

            expect(originGameContext).contain(expected);
            expect(originGameContext.gameData).deep.equal(gameData);
            const gameContext = await contextManager.findGameContextById(gameID);
            gameContext.lastRequestId = gameContext.lastRequestId + 1;
            await gameContext.update();

            return originGameContext.update()
                .should
                .eventually
                .rejectedWith(Errors.ConcurrentAccessToGameSession);
        });

        it("wrong sessionId is stale", async () => {
            const originGameContext: GameFlowContext = await contextManager
                .findOrCreateGameContext(gameID,
                    sessionId,
                    gameData,
                    TEST_MODULE_NAME);

            const expected = {
                id: gameID,
                lastRequestId: 0,
            };
            expect(originGameContext).contain(expected);
            expect(originGameContext.gameData).deep.equal(gameData);

            const gameContext = await contextManager.findGameContextById(gameID);
            gameContext.session = GameSession.create("NEWSESSIONID");
            await gameContext.update();

            return originGameContext.update()
                .should
                .eventually
                .rejectedWith(Errors.ConcurrentAccessToGameSession);
        });
        it("when walletTransactionID is stale", async () => {
            const originGameContext = await contextManager
                .findOrCreateGameContext(gameID,
                    sessionId,
                    gameData,
                    TEST_MODULE_NAME);

            const expected = {
                id: gameID,
                lastRequestId: 0,
            };
            expect(originGameContext).contain(expected);
            expect(originGameContext.gameData).deep.equal(gameData);

            const gameContext = await contextManager.findGameContextById(gameID);

            const newState = {
                nextScene: "SOMESCENE3"
            };

            await gameContext.updatePendingModification({
                    operation: "payment",
                    transactionId: "1",
                    bet: 100,
                    win: 20,
                    currency: "USD"
                } as PaymentOperation,
                newState, { request: "req", requestId: 1 });
            return originGameContext.update()
                .should
                .eventually
                .rejectedWith(Errors.ConcurrentAccessToGameSession);
        });

        it("when we made rollback modifications", async () => {
            const originGameContext: GameFlowContext = await contextManager
                .findOrCreateGameContext(gameID,
                    sessionId,
                    gameData,
                    TEST_MODULE_NAME);

            const expected = {
                id: gameID,
                lastRequestId: 0,

            };
            expect(originGameContext).contain(expected);
            expect(originGameContext.gameData).deep.equal(gameData);

            const gameContext = await contextManager.findGameContextById(gameID);

            const newState = {
                nextScene: "SOMESCENE3"
            };

            await gameContext.updatePendingModification(
                { operation: "payment", transactionId: "1", bet: 100, win: 20, currency: "USD" } as PaymentOperation,
                newState, { request: "req", requestId: 1 });

            await gameContext.rollbackPendingModification();

            return originGameContext.update()
                .should
                .eventually
                .rejectedWith(Errors.ConcurrentAccessToGameSession);
        });

        it("when it was deleted", async () => {
            const originGameContext: GameFlowContext = await contextManager
                .findOrCreateGameContext(gameID,
                    sessionId,
                    gameData,
                    TEST_MODULE_NAME);

            await originGameContext.remove();

            return originGameContext.update()
                .should
                .eventually
                .rejectedWith(Errors.ConcurrentAccessToGameSession);
        });

        it("and history item isn't saved", async () => {
            const originGameContext: GameFlowContext = await contextManager
                .findOrCreateGameContext(gameID,
                    sessionId,
                    gameData,
                    TEST_MODULE_NAME);

            const request = { request: "req", requestId: 1 };
            const expected = {
                id: gameID,
                lastRequestId: 0,
            };
            expect(originGameContext).contain(expected);
            expect(originGameContext.gameData).deep.equal(gameData);

            const history1: any = {
                type: "slot",
                version: 1,
                roundEnded: true,
                data: {
                    positions: [1, 2, 3, 4],
                }
            };
            const newState = {
                nextScene: "SOMESCENE3"
            };

            const walletOperation: any = {
                operation: "payment",
                transactionId: "1",
                roundId: originGameContext.roundId,
                roundPID: new GameFlowInfoImpl(originGameContext).roundPID,
                roundEnded: history1.roundEnded,
                bet: 100,
                win: 20,
                currency: "USD",
                ts: new Date()
            };
            await originGameContext.updatePendingModification(walletOperation, newState, request, history1);
            const gameContext = await contextManager.findGameContextById(gameID);
            expect(gameContext).contain(expected);
            expect(gameContext.gameData).deep.equal(gameData);
            walletOperation.ts = walletOperation.ts.toISOString();
            expect(gameContext.pendingModification).deep.equal({
                "history": history1,
                "newState": newState,
                "walletOperation": walletOperation
            });

            gameContext.lastRequestId = gameContext.lastRequestId + 1;
            await gameContext.update();
            try {
                await originGameContext.commitPendingModification();

                expect.fail(undefined, ConcurrentAccessToGameSession);
            } catch (err) {
                if (!(err instanceof ConcurrentAccessToGameSession)) {
                    expect.fail(err, ConcurrentAccessToGameSession);
                }
            }

            const savedHistoryItem = await getGameHistoryItem();
            expect(savedHistoryItem).is.undefined;
        });

        it("isn't removed because was concurrently updated", async () => {
            const originGameContext: GameFlowContext = await contextManager
                .findOrCreateGameContext(gameID,
                    sessionId,
                    gameData,
                    TEST_MODULE_NAME);

            const gameContext = await contextManager.findGameContextById(gameID);
            const newState = {
                nextScene: "SOMESCENE3"
            };

            await gameContext.updatePendingModification(
                { operation: "payment", transactionId: "1", bet: 100, win: 20, currency: "USD" } as PaymentOperation,
                newState, { request: "req", requestId: 1 });

            return originGameContext.remove()
                .should
                .eventually
                .rejectedWith(Errors.ConcurrentAccessToGameSession);
        });

    });
});
