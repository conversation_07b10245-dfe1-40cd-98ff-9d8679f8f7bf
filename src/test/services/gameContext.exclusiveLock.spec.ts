import { expect, should, use } from "chai";
import * as Errors from "../../skywind/errors";
import { TEST_MODULE_NAME } from "../helper";
import { BaseGameContextSpec } from "./gameContext.spec";
import { suite, test } from "mocha-typescript";

const chaiAsPromise = require("chai-as-promised");

should();
use(chaiAsPromise);

@suite("GameContext.exclusiveLock")
class GameContextExclusiveLockSpec extends BaseGameContextSpec {
    public async before() {
        await super.before();
        await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME);
    }

    @test("isn't updated because of ConcurrentAccessToGameSession error(exclusiveLock)")
    public async testNotUpdated() {
        const exclusive = await this.contextManager.findGameContextById(this.gameID, true);

        const gameContext = await this.contextManager.findGameContextById(this.gameID);

        await expect(gameContext.update())
            .to.be.rejectedWith(Errors.ConcurrentAccessToGameSession);

        await expect(gameContext.update())
            .to.be.rejectedWith(Errors.GameContextStateIsBroken);
        await exclusive.update();
    }

    @test("isn't remove because of ConcurrentAccessToGameSession error(exclusiveLock)")
    public async testNotRemoved() {
        const exclusive = await this.contextManager.findGameContextById(this.gameID, true);

        const gameContext = await this.contextManager.findGameContextById(this.gameID);

        await expect(gameContext.remove())
            .to.be.rejectedWith(Errors.ConcurrentAccessToGameSession);

        await exclusive.remove();
    }
}
