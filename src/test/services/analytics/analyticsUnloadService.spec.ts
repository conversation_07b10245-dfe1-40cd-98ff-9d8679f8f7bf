import { suite, test } from "mocha-typescript";
import { SinonStub, stub } from "sinon";
import { AnalyticsService } from "../../../skywind/services/analytics/analyticsService";
import { getAnalytics, getGameHistoryItem, getRoundHistory, TEST_MODULE_NAME } from "../../helper";
import { PaymentOperation } from "../../../skywind/services/wallet";
import { expect } from "chai";
import { BaseGameContextSpec } from "../gameContext.spec";
import { deepClone } from "../../../skywind/utils/cloner";
import * as _ from "lodash";
import { GameFlowInfoImpl } from "../../../skywind/services/context/gameFlowInfo";

@suite()
class AnalyticsUnloadServiceSpec extends BaseGameContextSpec {

    public static stub: SinonStub;

    public static async before() {
        AnalyticsUnloadServiceSpec.stub = stub(AnalyticsService.prototype, "send");
        BaseGameContextSpec.before();
    }

    public static after() {
        AnalyticsUnloadServiceSpec.stub.restore();
        BaseGameContextSpec.after();
    }

    public async before() {
        AnalyticsUnloadServiceSpec.stub.reset();
        return super.before();
    }

    @test()
    public async testUnloadItem() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        let context = await this.contextManager.findGameContextById(this.gameID);
        const request = { request: "req", requestId: 1 };
        const state1 = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };

        const history1: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2, 3],
            }
        };

        const walletOperation1: PaymentOperation = {
            operation: "payment",
            transactionId: "1",
            bet: 1900,
            win: 200,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.id,
            roundEnded: history1.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, state1, request, history1);

        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };

        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        const expectedWalletOperation1 = { ...walletOperation1, ts: walletOperation1.ts.toISOString() };

        expect(context.pendingModification).deep.equal({
            "history": history1,
            "newState": state1,
            "walletOperation": expectedWalletOperation1,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state1);
        expect(context.pendingModification).is.undefined;

        let result: any = await getGameHistoryItem();
        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 0,
            roundId: "0",
            sessionId: this.sessionId.sessionId,
            result: history1.data,
            type: history1.type,
            gameVersion: TEST_MODULE_NAME.version,
            walletTransactionId: "1",
            currency: "USD",
            win: walletOperation1.win,
            bet: walletOperation1.bet,
            roundEnded: true,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            ctrl: result.ctrl,
            totalRoundBet: 1900,
            totalRoundWin: 200
        });

        const history2: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2],
            }
        };

        const state2 = deepClone(state1);
        state2["nextScene"] = "SOMESCENE2";

        const walletOperation2: PaymentOperation = {
            operation: "payment",
            transactionId: "2",
            bet: 2000,
            win: 100,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.id,
            roundEnded: history2.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        const analytics2 = {
            type: "bi-analytics",
            data: {
                type: "some_type"
            }
        };
        await context.updatePendingModification(walletOperation2, state2, request, history2, undefined, analytics2);

        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state1);
        const uuid1 = context.pendingModification.analytics.id;

        expect(context.pendingModification).deep.equal({
            "analytics": {
                "brandId": 1,
                "deviceId": "deviceId",
                "eventId": 0,
                "gameCode": "gameId",
                "gameId": "gameId",
                "playerCode": "playerId",
                "roundId": "1",
                "data": {
                    "type": "some_type"
                },
                "id": uuid1,
                "type": "bi-analytics",
                "ts": BaseGameContextSpec.clock.now
            },
            "history": history2,
            "newState": state2,
            "walletOperation": walletOperation2,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state2);
        expect(context.pendingModification).is.undefined;

        result = await getGameHistoryItem();

        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 0,
            gameVersion: TEST_MODULE_NAME.version,
            result: history2.data,
            type: history2.type,
            roundEnded: true,
            roundId: "1",
            sessionId: this.sessionId.sessionId,
            walletTransactionId: "2",
            currency: "USD",
            win: walletOperation2.win,
            bet: walletOperation2.bet,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            ctrl: result.ctrl,
            totalRoundBet: 2000,
            totalRoundWin: 100
        });
        const history3: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2, 3, 4],
            }
        };

        const state3 = deepClone(state2);
        state3["nextScene"] = "SOMESCENE3";
        const walletOperation3: any = {
            operation: "payment",
            transactionId: "3",
            bet: 100,
            win: 200,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history3.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        const analytics3 = {
            type: "bi-analytics-3",
            data: {
                type: "some_type-3"
            }
        };
        await context.updatePendingModification(walletOperation3, state3, request, history3, undefined, analytics3);

        walletOperation3.ts = walletOperation3.ts.toISOString();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state2);

        const uuid2 = context.pendingModification.analytics.id;
        expect(context.pendingModification).deep.equal({
            "analytics": {
                "data": {
                    "type": "some_type-3"
                },
                "id": uuid2,
                "type": "bi-analytics-3",
                "deviceId": "deviceId",
                "eventId": 0,
                "gameCode": "gameId",
                "gameId": "gameId",
                "playerCode": "playerId",
                "roundId": "2",
                "brandId": 1,
                "ts": BaseGameContextSpec.clock.now
            },
            "history": history3,
            "newState": state3,
            "walletOperation": walletOperation3,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state3);
        expect(context.pendingModification).undefined;

        result = await getGameHistoryItem();
        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameTokenData.gameCode,
            eventId: 0,
            result: history3.data,
            type: history3.type,
            gameVersion: TEST_MODULE_NAME.version,
            roundEnded: true,
            roundId: "2",
            sessionId: this.sessionId.sessionId,
            walletTransactionId: "3",
            currency: "USD",
            win: walletOperation3.win,
            bet: walletOperation3.bet,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            ctrl: result.ctrl,
            totalRoundBet: 100,
            totalRoundWin: 200
        });

        const rounds = await getRoundHistory(0, 1000);
        expect(rounds.map((item) => _.omit(item, "finishedAt", "startedAt", "ctrl"))).deep.equals([
            {
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "gameId",
                gameId: "gameId",
                id: "2",
                playerCode: "playerId",
                sessionId: this.sessionId.sessionId,
                totalBet: 100,
                totalEvents: 1,
                totalWin: 200,
                totalJpContribution: 0,
                totalJpWin: 0,
            },
            {
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "gameId",
                gameId: "gameId",
                id: "1",
                playerCode: "playerId",
                sessionId: this.sessionId.sessionId,
                totalBet: 2000,
                totalEvents: 1,
                totalWin: 100,
                totalJpContribution: 0,
                totalJpWin: 0,
            },
            {
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "gameId",
                gameId: "gameId",
                id: "0",
                playerCode: "playerId",
                sessionId: this.sessionId.sessionId,
                totalBet: 1900,
                totalEvents: 1,
                totalWin: 200,
                totalJpContribution: 0,
                totalJpWin: 0,
            }
        ]);
        expect(AnalyticsUnloadServiceSpec.stub.args).deep.equals([]);
        const analyticsHistory = await getAnalytics(0, 1000);
        expect(analyticsHistory).deep.equals([
            {
                "brandId": 1,
                "data": {
                    "type": "some_type-3"
                },
                "deviceId": "deviceId",
                "eventId": 0,
                "gameCode": "gameId",
                "gameId": "gameId",
                "id": uuid2,
                "playerCode": "playerId",
                "roundId": "2",
                "type": "bi-analytics-3",
                "ts": BaseGameContextSpec.clock.now
            },
            {
                "brandId": 1,
                "data": {
                    "type": "some_type"
                },
                "deviceId": "deviceId",
                "eventId": 0,
                "gameCode": "gameId",
                "gameId": "gameId",
                "id": uuid1,
                "playerCode": "playerId",
                "roundId": "1",
                "type": "bi-analytics",
                "ts": BaseGameContextSpec.clock.now
            }
        ]);
    }
}
