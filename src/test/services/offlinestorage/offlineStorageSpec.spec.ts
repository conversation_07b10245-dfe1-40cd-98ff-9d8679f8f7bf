import { suite, test } from "mocha-typescript";
import { GameContextID, PlayerContextID } from "../../../skywind/services/contextIds";

import { PendingModification } from "../../../skywind/services/context/gamecontext";
import { PaymentOperation } from "../../../skywind/services/wallet";
import { GameSession } from "../../../skywind/services/gameSession";
import { expect, use } from "chai";
import { PlayerContextImpl } from "../../../skywind/services/playercontext/playerContext";
import { GameFlowContextImpl } from "../../../skywind/services/context/gameContextImpl";
import {
    FunGameContextOfflineCommands,
    FunPlayerContextOfflineCommands,
    GameContextOfflineCommandsImpl,
    PlayerContextOfflineCommandsImpl
} from "../../../skywind/services/offlinestorage/offlineCommandsImpl";

use(require("chai-as-promised"));

@suite()
export class OfflineStorageSpec {

    private readonly commands = new GameContextOfflineCommandsImpl();

    @test()
    public testsRemoveContexts() {
        const cmd = this.commands.remove([
            GameContextID.create("game001", 1, "test_player1", "web", "bns"),
            GameContextID.create("game002", 2, "test_player2", "mobile", "real")
        ]);
        expect(cmd).deep.equal({
            type: "removeGameContext",
            ids:
                [
                    "games:bns:1:test_player1:game001:web",
                    "games:context:2:test_player2:game002:mobile"
                ]
        });
    }

    @test()
    public testFindContexts() {
        expect(this.commands.findContext(GameContextID.create("game001", 1, "test_player1", "web", "bns"))).deep.equal({
            type: "findGameContext",
            brandId: 1,
            envId: undefined,
            id: "games:bns:1:test_player1:game001:web"
        });

        expect(this.commands.findContextByMerchantSessionId(1, "merchant_session_id")).deep.equal({
            type: "findContextByMerchantSessionId",
            brandId: 1,
            envId: undefined,
            merchantSessionId: "merchant_session_id"
        });
    }

    @test()
    public testFindExpired() {
        const ts = new Date();
        const cmd = this.commands.findExpired(ts, 333);

        expect(cmd).deep.equal({
            type: "findExpiredGameContexts",
            expiredAt: ts.toISOString(),
            limit: 333
        });
    }

    @test()
    public async testArchive() {
        const id = GameContextID.create("game001", 1, "test_player1", "web", "bns");
        const context = new GameFlowContextImpl(id);

        context.gameContext = {
            scenesState: {},
            currentScene: "scene",
            nextScene: "next",
            history: ["event1", "event2"],
            stake: {
                bet: 10,
                win: 1000
            }
        };

        context.session = GameSession.create("sessionID");
        context.lastRequestId = 1;
        context.gameSerialNumber = 1;
        context.totalEventId = 1;

        context.settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: 0,
            stakeAll: [1, 2, 3, 4],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 100,
            transferEnabled: false,
        };
        context.gameData = {
            limits: {},
            gameTokenData: {
                playerCode: "playerId",
                gameCode: "gameCode",
                brandId: 1,
                currency: "USD",
                token: "SOMETOKEN",
                playmode: "real"
            }
        };
        context.pendingModification = {
            request: { request: "req", requestId: 1 },
            history: {
                type: "slot",
                version: 1,
                roundEnded: true,
                data: { positions: [4, 3, 2, 1] }
            },
            walletOperation: {
                operation: "payment",
                transactionId: "trxId",
                currency: "USD",
                bet: 100,
                win: 1000,
            } as PaymentOperation,
            newState: {
                nextScene: "SOMESCENE"
            }
        } as PendingModification;
        context.roundEnded = false;
        context.roundId = "1";

        const cmd = await this.commands.save(context);

        expect(cmd).deep.equal({
            type: "saveGameContext",
            gameContext:
                {
                    id: "games:bns:1:test_player1:game001:web",
                    merchantSessionId: undefined,
                    brandId: context.id.brandId,
                    playerCode: context.id.playerCode,
                    deviceId: context.id.deviceId,
                    gameCode: context.id.gameCode,
                    gameId: context.gameData.gameId,
                    roundId: "1",
                    policy: 0,
                    broken: true,
                    data:
                        {
                            dataVersion: "5",
                            version: "0",
                            policy: "0",
                            requestId: "1",
                            round: cmd.gameContext.data.round,
                            historyCounter: "1",
                            totalEventId: "1",
                            gameState: cmd.gameContext.data.gameState,
                            pending: cmd.gameContext.data.pending,
                            jpPending: "",
                            sessionId: "sessionID",
                            metaInf: cmd.gameContext.data.metaInf,
                            jpContext: ""
                        },
                    expireAt: undefined,
                    brokenPayment: context.brokenPayment,
                    requireLogout: context.requireLogout,
                    requireTransferOut: context.requireTransferOut,
                    requireCompletion: false,
                    retryAttempts: context.retryAttempts,
                    specialState: context.specialState
                }
        });
    }

    @test()
    public async testSave() {
        const id = GameContextID.create("game001", 1, "test_player1", "web", "bns");
        const context = new GameFlowContextImpl(id);

        context.gameContext = {
            scenesState: {},
            currentScene: "scene",
            nextScene: "next",
            history: ["event1", "event2"],
            stake: {
                bet: 10,
                win: 1000
            }
        };

        context.session = GameSession.create("sessionID");
        context.lastRequestId = 1;
        context.gameSerialNumber = 1;
        context.totalEventId = 1;

        context.settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: 0,
            stakeAll: [1, 2, 3, 4],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 100,
            transferEnabled: false,
        };
        context.gameData = {
            limits: {},
            gameTokenData: {
                playerCode: "playerId",
                gameCode: "gameCode",
                brandId: 1,
                currency: "USD",
                token: "SOMETOKEN",
                playmode: "real"
            }
        };
        context.pendingModification = {
            request: { request: "req", requestId: 1 },
            history: {
                type: "slot",
                version: 1,
                roundEnded: true,
                data: { positions: [4, 3, 2, 1] }
            },
            walletOperation: {
                operation: "payment",
                transactionId: "trxId",
                currency: "USD",
                bet: 100,
                win: 1000,
            } as PaymentOperation,
            newState: {
                nextScene: "SOMESCENE"
            }
        } as PendingModification;
        context.roundEnded = false;
        context.roundId = "1";

        const cmd = await this.commands.archive(context);
        expect(cmd).deep.equal({
            type: "archiveGameContext",
            archive:
                {
                    contextId: "games:bns:1:test_player1:game001:web",
                    data:
                        {
                            dataVersion: "5",
                            version: "0",
                            policy: "0",
                            requestId: "1",
                            round: cmd.archive.data.round,
                            historyCounter: "1",
                            totalEventId: "1",
                            gameState: cmd.archive.data.gameState,
                            pending: cmd.archive.data.pending,
                            jpPending: "",
                            sessionId: "sessionID",
                            metaInf: cmd.archive.data.metaInf,
                            jpContext: ""
                        },
                    brandId: 1,
                    broken: true,
                    brokenPayment: true,
                    recoveryType: undefined,
                    roundId: "1",
                    deviceId: "web",
                    expireAt: undefined,
                    gameCode: "game001",
                    gameId: undefined,
                    merchantSessionId: undefined,
                    playerCode: "test_player1",
                    policy: 0,
                    requireLogout: false,
                    requireTransferOut: false,
                    retryAttempts: undefined,
                    specialState: undefined
                },
            recoveryType: undefined
        });
    }
}

@suite()
export class FunOfflineStorageSpec {
    private readonly commands = new FunGameContextOfflineCommands();

    @test()
    public async testsRemoveContexts() {
        const result = await this.commands.remove([
            GameContextID.create("game001", 1, "test_player1", "web", "bns"),
            GameContextID.create("game002", 2, "test_player2", "mobile", "real")
        ]);
        expect(result).to.be.undefined;
    }

    @test()
    public testFindContexts() {
        const cmd = this.commands.findContext(GameContextID.create("game001", 1, "test_player1", "web", "bns"));
        expect(cmd).is.undefined;
    }

    @test()
    public async testFindExpired() {
        const ts = new Date();
        await expect(this.commands.findExpired(ts, 333)).rejectedWith(Error);
    }

    @test()
    public async testUpdateExpiration() {
        await expect(this.commands.updateGameContextExpiration({ id: "someId", data: {} } as any, new Date()))
            .rejectedWith(Error);
    }

    @test()
    public async testSave() {
        const id = GameContextID.create("game001", 1, "test_player1", "web", "bns");
        const context = new GameFlowContextImpl(id);

        context.gameContext = {
            scenesState: {},
            currentScene: "scene",
            nextScene: "next",
            history: ["event1", "event2"],
            stake: {
                bet: 10,
                win: 1000
            }
        };

        context.session = GameSession.create("sessionID");
        context.lastRequestId = 1;
        context.gameSerialNumber = 1;
        context.totalEventId = 1;

        context.settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: 0,
            stakeAll: [1, 2, 3, 4],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 100,
            transferEnabled: false,
        };
        context.gameData = {
            limits: {},
            gameTokenData: {
                playerCode: "playerId",
                gameCode: "gameCode",
                brandId: 1,
                currency: "USD",
                token: "SOMETOKEN",
                playmode: "real"
            }
        };
        context.pendingModification = {
            request: { request: "req", requestId: 1 },
            history: {
                type: "slot",
                version: 1,
                roundEnded: true,
                data: { positions: [4, 3, 2, 1] }
            },
            walletOperation: {
                operation: "payment",
                transactionId: "trxId",
                currency: "USD",
                bet: 100,
                win: 1000,
            } as PaymentOperation,
            newState: {
                nextScene: "SOMESCENE"
            }
        } as PendingModification;
        context.roundEnded = false;
        context.roundId = "1";

        await expect(this.commands.save(context)).rejectedWith(Error);
    }

    @test()
    public async testArchive() {
        const id = GameContextID.create("game001", 1, "test_player1", "web", "bns");
        const context = new GameFlowContextImpl(id);

        context.gameContext = {
            scenesState: {},
            currentScene: "scene",
            nextScene: "next",
            history: ["event1", "event2"],
            stake: {
                bet: 10,
                win: 1000
            }
        };

        context.session = GameSession.create("sessionID");
        context.lastRequestId = 1;
        context.gameSerialNumber = 1;
        context.totalEventId = 1;

        context.settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: 0,
            stakeAll: [1, 2, 3, 4],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 100,
            transferEnabled: false,
        };
        context.gameData = {
            limits: {},
            gameTokenData: {
                playerCode: "playerId",
                gameCode: "gameCode",
                brandId: 1,
                currency: "USD",
                token: "SOMETOKEN",
                playmode: "real"
            }
        };
        context.pendingModification = {
            request: { request: "req", requestId: 1 },
            history: {
                type: "slot",
                version: 1,
                roundEnded: true,
                data: { positions: [4, 3, 2, 1] }
            },
            walletOperation: {
                operation: "payment",
                transactionId: "trxId",
                currency: "USD",
                bet: 100,
                win: 1000,
            } as PaymentOperation,
            newState: {
                nextScene: "SOMESCENE"
            }
        } as PendingModification;
        context.roundEnded = false;
        context.roundId = "1";

        await expect(this.commands.archive(context)).rejectedWith(Error);
    }
}

@suite()
export class PlayerContextOfflineStorageSpec {

    private readonly commands = new PlayerContextOfflineCommandsImpl();

    @test()
    public testRemoveContexts() {
        const cmd = this.commands.removePlayerContext([
            PlayerContextID.create(1, "test_player1"),
            PlayerContextID.create(2, "test_player2"),
        ]);
        expect(cmd).deep.equal({
            type: "removePlayerContext",
            ids:
                [
                    "games:player:1:test_player1",
                    "games:player:2:test_player2"
                ]
        });
    }

    @test()
    public testFindContext() {
        const cmd = this.commands.findPlayerContext(PlayerContextID.create(1, "test_player1"));
        expect(cmd).deep.equal({
            type: "findPlayerContext",
            id: "games:player:1:test_player1",
            brandId: 1,
            envId: undefined
        });
    }

    @test()
    public testSaveContext() {
        const id = PlayerContextID.create(1, "test_player1");
        const ctx = new PlayerContextImpl(id);
        ctx.version = 1;

        ctx.brokenGamesWithMode = [
            "games:context:1:player001:games004:mobile",
            "real",
            "games:context:1:player001:game005:web",
            "bns"
        ];

        const cmd = this.commands.savePlayerContext(ctx);
        expect(cmd).deep.equal({
            type: "savePlayerContext",
            playerContext:
                {
                    id: "games:player:1:test_player1",
                    data: undefined,
                    games:
                        [
                            { id: "games:context:1:player001:games004:mobile", mode: "real" },
                            { id: "games:context:1:player001:game005:web", mode: "bns" }
                        ],
                    version: 1,
                    dataVersion: 1
                }
        });
    }
}

@suite()
export class FunPlayerContextOfflineStorageSpec {

    private readonly commands = new FunPlayerContextOfflineCommands();

    @test()
    public async testRemoveContexts() {
        await expect(this.commands.removePlayerContext([
            PlayerContextID.create(1, "test_player1"),
            PlayerContextID.create(2, "test_player2"),
        ])).to.be.undefined;
    }

    @test()
    public testFindContext() {
        const cmd = this.commands.findPlayerContext(PlayerContextID.create(1, "test_player1"));
        expect(cmd).is.undefined;
    }

    @test()
    public async testSaveContext() {
        const id = PlayerContextID.create(1, "test_player1");
        const ctx = new PlayerContextImpl(id);
        ctx.version = 1;

        ctx.brokenGamesWithMode = [
            "games:context:1:player001:games004:mobile",
            "real",
            "games:context:1:player001:game005:web",
            "bns"
        ];

        await expect(this.commands.savePlayerContext(ctx)).rejectedWith(Error);
    }
}
