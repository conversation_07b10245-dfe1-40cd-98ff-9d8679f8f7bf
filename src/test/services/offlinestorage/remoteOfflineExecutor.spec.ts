import { suite, test } from "mocha-typescript";
import { GameContextID, PlayerContextID } from "../../../skywind/services/contextIds";
import { expect, use } from "chai";
import { PendingModification } from "../../../skywind/services/context/gamecontext";
import { PaymentOperation } from "../../../skywind/services/wallet";
import { GameSession } from "../../../skywind/services/gameSession";
import { PlayerContextImpl } from "../../../skywind/services/playercontext/playerContext";
import { GameFlowContextImpl } from "../../../skywind/services/context/gameContextImpl";
import {
    GameContextOfflineCommandsImpl,
    PlayerContextOfflineCommandsImpl
} from "../../../skywind/services/offlinestorage/offlineCommandsImpl";
import { testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;
import status200 = testing.status200;
import { RemoteOfflineCommandExecutor } from "../../../skywind/services/offlinestorage/remoteOfflineCommandExecutor";

use(require("chai-as-promised"));

@suite()
export class RemoveOfflineExecutorSpec {
    private static request: RequestMock;
    private readonly commands = new GameContextOfflineCommandsImpl();
    private readonly playerStorage = new PlayerContextOfflineCommandsImpl();
    private readonly executor = new RemoteOfflineCommandExecutor("http://localhost");

    public static after() {
        RemoveOfflineExecutorSpec.request.unmock(superagent);
    }

    public static before() {
        RemoveOfflineExecutorSpec.request = requestMock(superagent);
    }

    public after() {
        RemoveOfflineExecutorSpec.request.clearRoutes();
    }

    @test()
    public async testsRemoveContexts() {
        RemoveOfflineExecutorSpec.request.post("http://localhost/commands", status200([1]));

        await this.executor.execute(this.commands.remove([
            GameContextID.create("game001", 1, "test_player1", "web", "bns"),
            GameContextID.create("game002", 2, "test_player2", "mobile", "real")
        ]));

        expect(RemoveOfflineExecutorSpec.request.args[0].url).equal("http://localhost/commands");
        expect(RemoveOfflineExecutorSpec.request.args[0].body).deep.equal({
            "0": {
                type: "removeGameContext",
                ids:
                    [
                        "games:bns:1:test_player1:game001:web",
                        "games:context:2:test_player2:game002:mobile"
                    ]
            }
        });
    }

    @test()
    public async testFindContexts() {
        RemoveOfflineExecutorSpec.request.post("http://localhost/commands", status200([1]));
        await this.executor.execute([
            this.commands.findContext(GameContextID.create("game001", 1, "test_player1", "web", "bns")),
            this.playerStorage.findPlayerContext(PlayerContextID.create(1, "test_player1"))
        ]);
        expect(RemoveOfflineExecutorSpec.request.args[0].url).equal("http://localhost/commands");
        expect(RemoveOfflineExecutorSpec.request.args[0].body).deep.equal({
            0: {
                type: "findGameContext",
                id: "games:bns:1:test_player1:game001:web",
                brandId: 1,
                envId: undefined
            },
            1: {
                id: "games:player:1:test_player1",
                type: "findPlayerContext",
                brandId: 1,
                envId: undefined
            }
        });
    }

    @test()
    public async findContextByMerchantSessionId() {
        RemoveOfflineExecutorSpec.request.post("http://localhost/commands", status200([1]));
        await this.executor.execute([
            this.commands.findContextByMerchantSessionId(1, "merchant_session_id"),
            this.playerStorage.findPlayerContext(PlayerContextID.create(1, "test_player1"))
        ]);
        expect(RemoveOfflineExecutorSpec.request.args[0].url).equal("http://localhost/commands");
        expect(RemoveOfflineExecutorSpec.request.args[0].body).deep.equal({
            0: {
                type: "findContextByMerchantSessionId",
                merchantSessionId: "merchant_session_id",
                brandId: 1,
                envId: undefined
            },
            1: {
                id: "games:player:1:test_player1",
                type: "findPlayerContext",
                brandId: 1,
                envId: undefined
            }
        });
    }

    @test()
    public async testFindExpired() {
        RemoveOfflineExecutorSpec.request.post("http://localhost/commands", status200([1]));

        const ts = new Date();
        await this.executor.execute(this.commands.findExpired(ts, 333));

        expect(RemoveOfflineExecutorSpec.request.args[0].url).equal("http://localhost/commands");
        expect(RemoveOfflineExecutorSpec.request.args[0].body).deep.equal({
            0: {
                type: "findExpiredGameContexts",
                expiredAt: ts.toISOString(),
                limit: 333
            }
        });
    }

    @test()
    public async testUpdateGameContextExpiredAt() {
        RemoveOfflineExecutorSpec.request.post("http://localhost/commands", status200([1]));

        const id = GameContextID.create("game001", 1, "test_player1", "web", "bns");
        const context = new GameFlowContextImpl(id);

        context.gameContext = {
            scenesState: {},
            currentScene: "scene",
            nextScene: "next",
            history: ["event1", "event2"],
            stake: {
                bet: 10,
                win: 1000
            }
        };

        context.session = GameSession.create("sessionID");
        context.lastRequestId = 1;
        context.gameSerialNumber = 1;
        context.totalEventId = 1;

        context.settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: 0,
            stakeAll: [1, 2, 3, 4],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 100,
            transferEnabled: false,
        };
        context.gameData = {
            limits: {},
            gameTokenData: {
                playerCode: "playerId",
                merchantSessionId: "test_game_session",
                gameCode: "gameCode",
                brandId: 1,
                currency: "USD",
                token: "SOMETOKEN",
                playmode: "real"
            }
        };
        context.pendingModification = {
            request: { request: "req", requestId: 1 },
            history: {
                type: "slot",
                version: 1,
                roundEnded: true,
                data: { positions: [4, 3, 2, 1] }
            },
            walletOperation: {
                operation: "payment",
                transactionId: "trxId",
                currency: "USD",
                bet: 100,
                win: 1000,
            } as PaymentOperation,
            newState: {
                nextScene: "SOMESCENE"
            }
        } as PendingModification;
        context.roundEnded = false;
        context.roundId = "1";
        const ts = new Date();
        await this.executor.execute(this.commands.updateGameContextExpiration(context, ts));

        expect(RemoveOfflineExecutorSpec.request.args[0].url).equal("http://localhost/commands");
        expect(RemoveOfflineExecutorSpec.request.args[0].body).deep.equal({
            0: {
                type: "updateGameContextExpiration",
                newExpiredAt: ts.toISOString(),
                lastExpiredAt: undefined,
                id: context.id.asString()
            }
        });
    }

    @test()
    public async testArchive() {
        RemoveOfflineExecutorSpec.request.post("http://localhost/commands", status200([1]));

        const id = GameContextID.create("game001", 1, "test_player1", "web", "bns");
        const context = new GameFlowContextImpl(id);

        context.gameContext = {
            scenesState: {},
            currentScene: "scene",
            nextScene: "next",
            history: ["event1", "event2"],
            stake: {
                bet: 10,
                win: 1000
            }
        };

        context.session = GameSession.create("sessionID");
        context.lastRequestId = 1;
        context.gameSerialNumber = 1;
        context.totalEventId = 1;

        context.settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: 0,
            stakeAll: [1, 2, 3, 4],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 100,
            transferEnabled: false,
        };
        context.gameData = {
            limits: {},
            gameTokenData: {
                playerCode: "playerId",
                merchantSessionId: "test_game_session",
                gameCode: "gameCode",
                brandId: 1,
                currency: "USD",
                token: "SOMETOKEN",
                playmode: "real"
            }
        };
        context.pendingModification = {
            request: { request: "req", requestId: 1 },
            history: {
                type: "slot",
                version: 1,
                roundEnded: true,
                data: { positions: [4, 3, 2, 1] }
            },
            walletOperation: {
                operation: "payment",
                transactionId: "trxId",
                currency: "USD",
                bet: 100,
                win: 1000,
            } as PaymentOperation,
            newState: {
                nextScene: "SOMESCENE"
            }
        } as PendingModification;
        context.roundEnded = false;
        context.roundId = "1";

        await this.executor.execute(this.commands.save(context));

        expect(RemoveOfflineExecutorSpec.request.args[0].url).equal("http://localhost/commands");
        const cmd = RemoveOfflineExecutorSpec.request.args[0].body[0];
        expect(RemoveOfflineExecutorSpec.request.args[0].body).deep.equal({
            0: {
                type: "saveGameContext",
                gameContext:
                    {
                        id: "games:bns:1:test_player1:game001:web",
                        brandId: context.id.brandId,
                        playerCode: context.id.playerCode,
                        deviceId: context.id.deviceId,
                        gameCode: context.id.gameCode,
                        gameId: context.gameData.gameId,
                        roundId: "1",
                        policy: 0,
                        broken: true,
                        merchantSessionId: "test_game_session",
                        data:
                            {
                                dataVersion: "5",
                                version: "0",
                                policy: "0",
                                requestId: "1",
                                round: cmd.gameContext.data.round,
                                historyCounter: "1",
                                totalEventId: "1",
                                gameState: cmd.gameContext.data.gameState,
                                pending: cmd.gameContext.data.pending,
                                jpPending: "",
                                sessionId: "sessionID",
                                metaInf: cmd.gameContext.data.metaInf,
                                jpContext: ""
                            },
                        expireAt: undefined,
                        brokenPayment: context.brokenPayment,
                        requireLogout: context.requireLogout,
                        requireTransferOut: context.requireTransferOut,
                        requireCompletion: false,
                        retryAttempts: context.retryAttempts,
                        specialState: context.specialState
                    }
            }
        });
    }

    @test()
    public async testSave() {
        RemoveOfflineExecutorSpec.request.post("http://localhost/commands", status200([1]));

        const id = GameContextID.create("game001", 1, "test_player1", "web", "bns");
        const context = new GameFlowContextImpl(id);

        context.gameContext = {
            scenesState: {},
            currentScene: "scene",
            nextScene: "next",
            history: ["event1", "event2"],
            stake: {
                bet: 10,
                win: 1000
            }
        };

        context.session = GameSession.create("sessionID");
        context.lastRequestId = 1;
        context.gameSerialNumber = 1;
        context.totalEventId = 1;

        context.settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: 0,
            stakeAll: [1, 2, 3, 4],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 100,
            transferEnabled: false,
        };
        context.gameData = {
            limits: {},
            gameTokenData: {
                playerCode: "playerId",
                gameCode: "gameCode",
                brandId: 1,
                currency: "USD",
                token: "SOMETOKEN",
                playmode: "real"
            }
        };
        context.pendingModification = {
            request: { request: "req", requestId: 1 },
            history: {
                type: "slot",
                version: 1,
                roundEnded: true,
                data: { positions: [4, 3, 2, 1] }
            },
            walletOperation: {
                operation: "payment",
                transactionId: "trxId",
                currency: "USD",
                bet: 100,
                win: 1000,
            } as PaymentOperation,
            newState: {
                nextScene: "SOMESCENE"
            }
        } as PendingModification;
        context.roundEnded = false;
        context.roundId = "1";

        await this.executor.execute(this.commands.archive(context));
        expect(RemoveOfflineExecutorSpec.request.args[0].url).equal("http://localhost/commands");
        const cmd = RemoveOfflineExecutorSpec.request.args[0].body[0];
        expect(RemoveOfflineExecutorSpec.request.args[0].body).deep.equal({
            0: {
                type: "archiveGameContext",
                archive:
                    {
                        contextId: "games:bns:1:test_player1:game001:web",
                        data:
                            {
                                dataVersion: "5",
                                version: "0",
                                policy: "0",
                                requestId: "1",
                                round: cmd.archive.data.round,
                                historyCounter: "1",
                                totalEventId: "1",
                                gameState: cmd.archive.data.gameState,
                                pending: cmd.archive.data.pending,
                                jpPending: "",
                                sessionId: "sessionID",
                                metaInf: cmd.archive.data.metaInf,
                                jpContext: ""
                            },
                        brandId: 1,
                        broken: true,
                        brokenPayment: true,
                        recoveryType: undefined,
                        roundId: "1",
                        deviceId: "web",
                        expireAt: undefined,
                        gameCode: "game001",
                        gameId: undefined,
                        merchantSessionId: undefined,
                        playerCode: "test_player1",
                        policy: 0,
                        requireLogout: false,
                        requireTransferOut: false,
                        retryAttempts: undefined,
                        specialState: undefined
                    },
                recoveryType: undefined
            }
        });
    }

    @test()
    public async testRemoveContexts() {
        RemoveOfflineExecutorSpec.request.post("http://localhost/commands", status200([1]));

        await this.executor.execute(this.playerStorage.removePlayerContext([
            PlayerContextID.create(1, "test_player1"),
            PlayerContextID.create(2, "test_player2"),
        ]));

        expect(RemoveOfflineExecutorSpec.request.args[0].url).equal("http://localhost/commands");
        expect(RemoveOfflineExecutorSpec.request.args[0].body).deep.equal({
            0: {
                type: "removePlayerContext",
                ids:
                    [
                        "games:player:1:test_player1",
                        "games:player:2:test_player2"
                    ]
            }
        });
    }

    @test()
    public async testSaveContext() {
        RemoveOfflineExecutorSpec.request.post("http://localhost/commands", status200([1]));

        const id = PlayerContextID.create(1, "test_player1");
        const ctx = new PlayerContextImpl(id);
        ctx.version = 1;

        ctx.brokenGamesWithMode = [
            "games:context:1:player001:games004:mobile",
            "real",
            "games:context:1:player001:game005:web",
            "bns"
        ];

        await this.executor.execute(this.playerStorage.savePlayerContext(ctx));
        expect(RemoveOfflineExecutorSpec.request.args[0].body).deep.equal({
            0: {
                type: "savePlayerContext",
                playerContext:
                    {
                        id: "games:player:1:test_player1",
                        data:
                        undefined,
                        games:
                            [
                                { id: "games:context:1:player001:games004:mobile", mode: "real" },
                                { id: "games:context:1:player001:game005:web", mode: "bns" }
                            ],
                        version:
                            1,
                        dataVersion:
                            1
                    }
            }
        });
    }
}
