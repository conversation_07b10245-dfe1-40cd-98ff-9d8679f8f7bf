import { expect, should, use } from "chai";
import { injectRandomGenerator } from "../../../skywind/services/random";
import { suite, test } from "mocha-typescript";
import { BaseGameFlowSpec } from "./gameflow.spec";
import { getAnalytics, getGameHistoryItem } from "../../helper";
import { createWalletManager } from "../../../skywind/services/wallet";
import config from "../../../skywind/config";
import { AnalyticsService } from "../../../skywind/services/analytics/analyticsService";
import { SinonStub, stub } from "sinon";

const chaiAsPromise = require("chai-as-promised");

should();
use(chaiAsPromise);

@suite("GameFlow.Send analytics")
class GameFlowSendAnalyticsSpec extends BaseGameFlowSpec {

    @test("deferred updates with analytics")
    public async deferredUpdate() {
        const flow = await this.createForInit();
        injectRandomGenerator(flow);

        let balance = await flow.getBalance();
        expect(balance).deep.equal({
            "amount": 1000,
            "bonus": {
                "amount": 0,
            },
            "currency": "USD",
            "real": {
                "amount": 1000,
            }
        });

        flow.deferredUpdate({
            payment: { win: 10, bet: 100 },
            history: { type: "slot", roundEnded: true, data: { field: "some_history" } }
        });

        flow.deferredUpdate({
            analytics: {
                type: "bi-analytics",
                data: { type: "some_data" }
            }
        });
        flow.deferredUpdate({ context: { data: "FOR_TEST_REASON" } });
        await flow.commitDeferredUpdate();

        const newBalance = await flow.getBalance();
        balance = await flow.getBalance();
        expect(balance).deep.equal({
            "amount": 910,
            "bonus": {
                "amount": 0,
            },
            "currency": "USD",
            "real": {
                "amount": 910,
            }
        });
        expect(balance).deep.equal(newBalance);

        const context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.gameContext).deep.equals({ data: "FOR_TEST_REASON" });

        const historyItem: any = await getGameHistoryItem();
        expect(historyItem.result).deep.equal({ field: "some_history" });
        expect(historyItem.win).is.eq(10);
        expect(historyItem.bet).is.eq(100);
        await this.assertDeferredUpdate();
    }

    @test("deferred updates only with analytics")
    public async updateContextOnlyWithAnalytics() {
        const flow = await this.createForInit();
        injectRandomGenerator(flow);

        flow.deferredUpdate({
            analytics: {
                type: "bi-analytics",
                data: { type: "some_data" }
            }
        });

        await flow.commitDeferredUpdate();
        const context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.roundEnded).to.be.true;
    }

    @test("update context, history and payment")
    public async updateContextHistoryPayment() {
        const flow = await this.createForInit();

        const history1 = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 3, 4, 5] } };
        const analytics1 = { type: "bi-analytics-1", data: { type: "some_data-1" } };
        const paymentInfo1 = { bet: 10, win: 22 };
        const gameContext1 = {
            scene: "scene1",
        };

        const history2 = { type: "slot", version: 1, roundEnded: false, data: { positions: [2, 3, 4, 5] } };
        const analytics2 = { type: "bi-analytics-2", data: { type: "some_data-2" } };
        const paymentInfo2 = { bet: 1, win: 2 };
        const gameContext2 = {
            scene: "scene2",
        };
        const history3 = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 2, 3, 5] } };
        const analytics3 = { type: "bi-analytics-3", data: { type: "some_data-3" } };
        const paymentInfo3 = { bet: 100, win: 1 };
        const gameContext3 = {
            scene: "scene1",
        };

        await flow.update(gameContext1, paymentInfo1, history1, undefined, analytics1);
        expect(flow.info().roundId).equal("1");
        expect((await flow.gameContext())).deep.equal(gameContext1);
        let context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.gameContext).deep.equal(gameContext1);

        await flow.update(gameContext2, paymentInfo2, history2, undefined, analytics2);
        expect(flow.info().roundId).equal("1");
        expect((await flow.gameContext())).deep.equal(gameContext2);
        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.gameContext).deep.equal(gameContext2);

        await flow.update(gameContext3, paymentInfo3, history3, undefined, analytics3);
        expect(flow.info().roundId).equal("2");
        expect((await flow.gameContext())).deep.equal(gameContext3);
        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.gameContext).deep.equal(gameContext3);

        let historyItem: any = await getGameHistoryItem();
        expect(historyItem.result).deep.equal(history3.data);
        expect(historyItem.win).is.eq(paymentInfo3.win);
        expect(historyItem.bet).is.eq(paymentInfo3.bet);
        expect(historyItem.roundEnded).equal(true);

        historyItem = await getGameHistoryItem();
        expect(historyItem.result).deep.equal(history2.data);
        expect(historyItem.win).is.eq(paymentInfo2.win);
        expect(historyItem.bet).is.eq(paymentInfo2.bet);
        expect(historyItem.roundEnded).equal(false);

        historyItem = await getGameHistoryItem();
        expect(historyItem.result).deep.equal(history1.data);
        expect(historyItem.win).is.eq(paymentInfo1.win);
        expect(historyItem.bet).is.eq(paymentInfo1.bet);
        expect(historyItem.roundEnded).equal(true);

        const walletBalance = await createWalletManager(this.gameData.gameTokenData).getBalance();
        expect(walletBalance).deep.equal({
            "currency": "USD",
            "main": 914,
        });
        await this.assertUpdateContext();
    }

    protected async assertUpdateContext() {
        const analyticsHistory = await getAnalytics(0, 1000);
        expect(analyticsHistory).deep.equals([
            {
                brandId: 1,
                data: {
                    type: "some_data-3",
                },
                deviceId: "deviceId",
                eventId: 1,
                gameCode: "test_slot",
                gameId: "test_slot",
                id: analyticsHistory[0].id,
                playerCode: "PL001",
                roundId: "1",
                type: "bi-analytics-3",
                ts: BaseGameFlowSpec.clockMock.now
            },
            {
                brandId: 1,
                data: {
                    type: "some_data-2",
                },
                deviceId: "deviceId",
                eventId: 0,
                gameCode: "test_slot",
                gameId: "test_slot",
                id: analyticsHistory[1].id,
                playerCode: "PL001",
                roundId: "1",
                type: "bi-analytics-2",
                ts: BaseGameFlowSpec.clockMock.now
            },
            {
                brandId: 1,
                data: {
                    type: "some_data-1",
                },
                deviceId: "deviceId",
                eventId: 0,
                gameCode: "test_slot",
                gameId: "test_slot",
                id: analyticsHistory[2].id,
                playerCode: "PL001",
                roundId: "0",
                type: "bi-analytics-1",
                ts: BaseGameFlowSpec.clockMock.now
            }
        ]);
    }

    protected async assertDeferredUpdate() {
        const analyticsHistory = await getAnalytics(0, 1000);
        expect(analyticsHistory).deep.equals([
            {
                brandId: 1,
                data: {
                    type: "some_data",
                },
                deviceId: "deviceId",
                eventId: 0,
                gameCode: "test_slot",
                gameId: "test_slot",
                id: analyticsHistory[0].id,
                playerCode: "PL001",
                roundId: "0",
                type: "bi-analytics",
                ts: GameFlowSendAnalyticsSpec.clockMock.now
            }
        ]);
    }
}

@suite("GameFlow.Send online analytics")
class GameFlowSendAnalyticsOnlineSpec extends GameFlowSendAnalyticsSpec {
    public static prevValue: boolean;
    public static stub: SinonStub;

    public static async before() {
        GameFlowSendAnalyticsOnlineSpec.prevValue = config.analytics.sendOnline;
        config.analytics.sendOnline = true;
        GameFlowSendAnalyticsOnlineSpec.stub = stub(AnalyticsService.prototype, "send");
        return GameFlowSendAnalyticsSpec.before();
    }

    public static after() {
        config.analytics.sendOnline = GameFlowSendAnalyticsOnlineSpec.prevValue;
        GameFlowSendAnalyticsOnlineSpec.stub.restore();
        return GameFlowSendAnalyticsSpec.after();
    }

    public async before() {
        GameFlowSendAnalyticsOnlineSpec.stub.reset();
        return super.before();
    }

    @test("deferred updates with analytics and send online")
    public async deferredUpdate() {
        return super.deferredUpdate();
    }

    @test("update context, history and payment and send online")
    public async updateContextHistoryPayment() {
        return super.updateContextHistoryPayment();
    }

    @test("deferred updates only with analytics and send online")
    public async updateContextOnlyWithAnalytics() {
        return super.updateContextOnlyWithAnalytics();
    }

    protected async assertUpdateContext() {
        expect(GameFlowSendAnalyticsOnlineSpec.stub.args).deep.equals([
            [
                {
                    brandId: 1,
                    data: {
                        type: "some_data-1",
                    },
                    deviceId: "deviceId",
                    eventId: 0,
                    gameCode: "test_slot",
                    gameId: "test_slot",
                    id: GameFlowSendAnalyticsOnlineSpec.stub.args[0][0].id,
                    playerCode: "PL001",
                    roundId: "0",
                    type: "bi-analytics-1",
                    ts: BaseGameFlowSpec.clockMock.now
                }
            ],
            [
                {
                    brandId: 1,
                    data: {
                        type: "some_data-2",
                    },
                    deviceId: "deviceId",
                    eventId: 0,
                    gameCode: "test_slot",
                    gameId: "test_slot",
                    id: GameFlowSendAnalyticsOnlineSpec.stub.args[1][0].id,
                    playerCode: "PL001",
                    roundId: "1",
                    type: "bi-analytics-2",
                    ts: BaseGameFlowSpec.clockMock.now
                }
            ],
            [
                {
                    brandId: 1,
                    data: {
                        type: "some_data-3",
                    },
                    deviceId: "deviceId",
                    eventId: 1,
                    gameCode: "test_slot",
                    gameId: "test_slot",
                    id: GameFlowSendAnalyticsOnlineSpec.stub.args[2][0].id,
                    playerCode: "PL001",
                    roundId: "1",
                    type: "bi-analytics-3",
                    ts: BaseGameFlowSpec.clockMock.now
                }
            ],
        ]);
    }

    protected async assertDeferredUpdate() {
        expect(GameFlowSendAnalyticsOnlineSpec.stub.args).deep.equals([
            [
                {
                    brandId: 1,
                    data: {
                        type: "some_data",
                    },
                    deviceId: "deviceId",
                    eventId: 0,
                    gameCode: "test_slot",
                    gameId: "test_slot",
                    id: GameFlowSendAnalyticsOnlineSpec.stub.args[0][0].id,
                    playerCode: "PL001",
                    roundId: "0",
                    type: "bi-analytics",
                    ts: BaseGameFlowSpec.clockMock.now
                }
            ]
        ]);
    }
}
