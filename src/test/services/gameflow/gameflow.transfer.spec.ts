import { expect, should, use } from "chai";
import { createGameToken, flushAll, getGameHistoryItem, getRoundHistory, TEST_MODULE_NAME } from "../../helper";
import { createWalletManager, TransferOperationType } from "../../../skywind/services/wallet";
import { ConversionService } from "@skywind-group/sw-game-core";
import { generateStartGameToken, Limits } from "../../../skywind/services/tokens";
import { GameContextID } from "../../../skywind/services/contextIds";
import config from "../../../skywind/config";
import { GameFlowFactory } from "../../../skywind/services/gameFlowFactory";
import { GameData } from "../../../skywind/services/auth";
import { getGameContextModel } from "../../../skywind/services/offlinestorage/models";
import { LoadedGameResult } from "../../../skywind/services/game/game";
import { DumyGame } from "../../testGames";
import { testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import { InsufficientBalanceToMakeTransferError } from "../../../skywind/errors";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;
import status200 = testing.status200;

const chaiAsPromise = require("chai-as-promised");

should();
use(chaiAsPromise);

describe("GameFlow transfer", () => {
    const gameTokenWithTransfer: any = {
        playerCode: "PL0011",
        gameCode: "test_slot",
        brandId: 1,
        currency: "USD",
        module: "test_slot@latest",
        playmode: "real"
    };
    const gameDataWithTransfer: GameData = {
        gameTokenData: gameTokenWithTransfer,
        authToken: "",
        playerCode: "PL0011",
        gameCode: "test_slot",
        brandId: 1,
        currency: "USD",
        gameMode: gameTokenWithTransfer.playmode,
        limits: {
            coins: [1],
            defaultCoin: 1,
            maxTotalStake: 500,
            stakeAll: [0.1, 0.5, 1, 2, 3, 5],
            stakeDef: 1,
            stakeMax: 10,
            stakeMin: 0.1,
            winMax: 3000000,
            currencyMultiplier: 100,
        } as Limits,
        settings: {
            transferEnabled: true,
        },
        module: "test_slot@latest",
    } as GameData;

    let startTokenWithTransfer: string;
    const startTokenDataWithTransfer = {
        playerCode: "PL0011",
        gameCode: "test_slot",
        brandId: 1,
        settings: {
            transferEnabled: true,
        },
    };

    const gameID: GameContextID = GameContextID.create("test_slot", 1, "PL0011", "deviceId");

    const initRequestWithTransfer: any = {
        request: "init",

        gameId: gameID.gameCode,
        deviceId: gameID.deviceId,
        startGameToken: startTokenWithTransfer,
    };

    let prevWalletThroughAPI;
    let request: RequestMock;

    after(() => {
        config.walletThroughAPI = prevWalletThroughAPI;
        request.unmock(superagent);
    });

    before(async () => {
        prevWalletThroughAPI = config.walletThroughAPI;
        config.walletThroughAPI = true;
        gameTokenWithTransfer.token = await createGameToken(gameTokenWithTransfer);
        startTokenWithTransfer = await generateStartGameToken(startTokenDataWithTransfer);
        request = requestMock(superagent);
    });

    beforeEach(async () => {
        request.clearRoutes();
        await getGameContextModel().truncate();
        await flushAll();
    });

    const conversionService: any = {
        toGameAmount: (gameInfo, settings, amount: number): number => {
            return amount * 100;
        },
        fromGameAmount: (gameInfo, settings, amount: number): number => {
            return amount / 100;
        },
    };

    const testTransfer = async (transferType: TransferOperationType,
                                amount: number,
                                conversion?: ConversionService<any>, roundedAmount?: number) => {
        request.put("http://api:3006//v2/play/transfer", status200({
            "currency": "USD",
            "main": 1000,
            "external": 1000,
        }));

        request.post("http://api:3006//v2/play/payment/transactionId", status200({
            "transactionId": "1"
        }));

        const flow = await GameFlowFactory.createForInit(await getGameFlowContextManager().findGameContextById(gameID),
            Object.assign({}, gameDataWithTransfer, {
                balance: {
                    "currency": "USD",
                    "main": 1000,
                    "external": 1000
                }
            }),
            initRequestWithTransfer,
            new LoadedGameResult(conversion as any || new DumyGame(), TEST_MODULE_NAME));

        const betsCount = 4;
        if (transferType === "transfer-out") {
            if (!flow.flowContext.round) {
                (flow.flowContext as any).round = { betsCount };
            } else {
                flow.flowContext.round.betsCount = betsCount;
            }
        }

        const balance = await flow.transfer({ operation: transferType, amount: amount });
        expect(balance).deep.equal({
            "amount": conversion ? conversion.toGameAmount(undefined, undefined, 1000) : 1000,
            "bonus": {
                "amount": 0,
            },
            "currency": "USD",
            "external": {
                "amount": conversion ? conversion.toGameAmount(undefined, undefined, 1000) : 1000,
            },
            "real": {
                "amount": conversion ? conversion.toGameAmount(undefined, undefined, 1000) : 1000,
            }
        });
        if (transferType === "transfer-out" && request.args.length === 2) {
            expect(request.args[1].body.betsCount).to.equal(betsCount);
        }
        const historyItem = await getGameHistoryItem();
        expect(historyItem.type).equal(transferType);
        const fromGameAmount = conversion ? conversion.fromGameAmount(undefined, undefined, amount) : amount;
        const normalizedAmount = roundedAmount !== undefined ? roundedAmount : fromGameAmount;
        expect(historyItem.result).deep.equal({
            request: transferType,
            amount: normalizedAmount,
            coinsAmount: amount,
        });
        expect(historyItem.walletTransactionId).is.eq("1");
        expect(historyItem.roundId).is.eq("0");
        expect(historyItem.roundEnded).is.eq(transferType === "transfer-out");
        expect(historyItem.win).is.eq(0);
        expect(historyItem.bet).is.eq(0);
        if (transferType === "transfer-in") {
            expect(historyItem.debit).to.equal(normalizedAmount);
        } else {
            expect(historyItem.credit).to.equal(normalizedAmount);
        }
        request.get("http://api:3006//v2/play/balance", status200({
            "currency": "USD",
            "external": 1000,
            "main": 1000,
        }));

        const walletBalance = await createWalletManager(gameDataWithTransfer.gameTokenData).getBalance();
        expect(walletBalance).deep.equal({
            "currency": "USD",
            "external": 1000,
            "main": 1000,
        });

        expect(request.args[2]).deep.equal({
            "url": "http://api:3006//v2/play/balance",
            "query": {
                "gameToken": gameDataWithTransfer.gameTokenData.token,
            },
            "method": "GET",
            "headers": {
                "content-type": "application/json",
                "x-gs": ""
            },
            "body": {}
        });
    };

    it("balance in", () => {
        return testTransfer("transfer-in", 100);
    });

    it("balance in - failed, insufficient balance", async () => {
        request.put("http://api:3006//v2/play/transfer", status200({
            "currency": "USD",
            "main": 1000,
            "external": 1000,
        }));

        request.post("http://api:3006//v2/play/payment/transactionId", status200({
            "transactionId": "1"
        }));

        const flow = await GameFlowFactory.createForInit(undefined,
            Object.assign({}, gameDataWithTransfer, {
                balance: {
                    "currency": "USD",
                    "main": 1000,
                    "external": 10
                }
            }),
            initRequestWithTransfer,
            new LoadedGameResult(new DumyGame(), TEST_MODULE_NAME));

        await expect(flow.transfer({ operation: "transfer-in", amount: 100 }))
            .rejectedWith(InsufficientBalanceToMakeTransferError);

        const historyItem = await getGameHistoryItem();
        expect(historyItem).is.undefined;
    });

    it("balance out", async () => {
        return testTransfer("transfer-out", 100);
    });

    it("all balance out", async () => {
        request.get("http://api:3006//v2/play/balance", status200({
            "currency": "USD",
            "external": 500,
            "main": 750,
        }));

        request.put("http://api:3006//v2/play/transfer", status200({
            "currency": "USD",
            "main": 0,
            "external": 1250,
        }));

        request.post("http://api:3006//v2/play/payment/transactionId", status200({
            "transactionId": "1"
        }));

        const flow = await GameFlowFactory.createForInit(undefined,
            gameDataWithTransfer,
            initRequestWithTransfer,
            new LoadedGameResult(new DumyGame(), TEST_MODULE_NAME));

        await flow.transferAllOut();

        const historyItem = await getGameHistoryItem();
        expect(historyItem.type).equal("transfer-out");
        expect(historyItem.result).deep.equal({ request: "transfer-out", amount: 750, coinsAmount: 750 });
        expect(historyItem.walletTransactionId).is.eq("1");
        expect(historyItem.win).is.eq(0);
        expect(historyItem.roundId).is.eq("0");
        expect(historyItem.roundEnded).is.true;
        expect(historyItem.bet).is.eq(0);
        expect(historyItem.credit).is.eq(750);
    });

    it("zero balance out", async () => {
        request.get("http://api:3006//v2/play/balance", status200({
            "currency": "USD",
            "external": 0,
            "main": 0,
        }));

        request.put("http://api:3006//v2/play/transfer", status200({
            "currency": "USD",
            "main": 0,
            "external": 0,
        }));

        request.post("http://api:3006//v2/play/payment/transactionId", status200({
            "transactionId": "1"
        }));

        const flow = await GameFlowFactory.createForInit(undefined,
            gameDataWithTransfer,
            initRequestWithTransfer,
            new LoadedGameResult(new DumyGame(), TEST_MODULE_NAME));

        await flow.transferAllOut();

        const historyItem = await getGameHistoryItem();
        expect(historyItem.type).equal("transfer-out");
        expect(historyItem.result).deep.equal({ request: "transfer-out", amount: 0, coinsAmount: 0 });
        expect(historyItem.walletTransactionId).is.eq("1");
        expect(historyItem.win).is.eq(0);
        expect(historyItem.roundId).is.eq("0");
        expect(historyItem.roundEnded).is.true;
        expect(historyItem.bet).is.eq(0);
    });

    it("transfer in with coins conversion", async () => {
        return testTransfer("transfer-in", 10000, conversionService);
    });

    it("transfer out with coins conversion", async () => {
        return testTransfer("transfer-out", 10000, conversionService);
    });

    it("two transfer ins one transfer out ", async () => {
        await testTransfer("transfer-in", 10000, conversionService);
        await testTransfer("transfer-in", 5000, conversionService);
        await testTransfer("transfer-out", 13000, conversionService);
        const roundHistory = await getRoundHistory(0, 0);
        expect(roundHistory[0]).deep.include({
            debit: 150,
            credit: 130,
            totalWin: 0,
            totalBet: 0
        });
    });

    it("balance in with normalization", () => {
        return testTransfer("transfer-in", 100.00099999, undefined, 100);
    });

    it("balance out with normalization", async () => {
        return testTransfer("transfer-out", 0.12566666, undefined, 0.13);
    });

});
