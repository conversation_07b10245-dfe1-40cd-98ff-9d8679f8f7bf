import { expect, should, use } from "chai";
import {
    createGameToken,
    flushAll,
    getGameHistoryItem,
    omitUndefined,
    syncModels,
    TEST_MODULE_NAME
} from "../../helper";
import { GameHistory, GameInitRequest, MiniGame, WinJackpot } from "@skywind-group/sw-game-core";
import { generateStartGameToken, Limits } from "../../../skywind/services/tokens";
import { GameContextID } from "../../../skywind/services/contextIds";
import config from "../../../skywind/config";
import { SinonStub, stub } from "sinon";
import { getJPNServer } from "../../../skywind/services/jpn/jpnserver";
import {
    JackpotDisabledError,
    JackpotStatusNotValid,
    JPNInternalServerError,
    ManagementAPIBrokenIntegration,
    ManagementAPITransientError
} from "../../../skywind/errors";
import { GameFlowContext, SpecialState } from "../../../skywind/services/context/gamecontext";
import { GameFlowFactory } from "../../../skywind/services/gameFlowFactory";
import { init as initCurrencyExchange } from "../../../skywind/services/currencyexchange";
import { JackpotShortInfo } from "@skywind-group/sw-jpn-core";
import { LoadedGameResult } from "../../../skywind/services/game/game";
import { GameData } from "../../../skywind/services/auth";
import { suite, test } from "mocha-typescript";
import { EngineGameFlow } from "../../../skywind/services/gameflow";
import { getGameContextModel } from "../../../skywind/services/offlinestorage/models";
import { DumyGame } from "../../testGames";
import { ContextManager } from "../../../skywind/services/contextmanager/contextManager";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { publicId, testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import { setRandomGeneratorFactory } from "../../..";
import { RandomGeneratorFactoryImpl } from "../../testRandomFactory";
import RequestMock = testing.RequestMock;
import status200 = testing.status200;
import requestMock = testing.requestMock;
import onCall = testing.onCall;

const chaiAsPromise = require("chai-as-promised");

should();
use(chaiAsPromise);

export class BaseGameFlowJPNSpec {
    public gameTokenData: any = {
        playerCode: "PL0011",
        gameCode: "test_slot",
        brandId: 1,
        currency: "USD",
        module: "test_slot@latest",
        playmode: "real"
    };
    public startGame: GameData = {
        gameTokenData: this.gameTokenData,
        limits: {} as Limits,
        settings: {
            jackpotId: "jp-id",
        },
        module: "test_slot@latest",
        balance: {
            currency: "USD",
            main: 100
        },
        gameMode: this.gameTokenData.playmode
    } as GameData;

    public startToken: string;
    public startTokenData = {
        playerCode: "PL0011",
        gameCode: "test_slot",
        brandId: 1,
    };

    public gameID: GameContextID = GameContextID.create("test_slot", 1, "PL0011", "deviceId");
    public contextManager: ContextManager = getGameFlowContextManager();

    public loadedGame: LoadedGameResult = new LoadedGameResult(new DumyGame(), TEST_MODULE_NAME);

    public jpnContext = {
        token: "jpn-auth-token",
        jackpotsInfo: [
            {
                id: "jp-id", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                isGameOwned: true, paymentStatisticEnabled: true, gameHistoryEnabled: true
            }
        ],
        contributionEnabled: true
    };

    public initRequest: GameInitRequest;
    public prevWalletThroughAPI;

    public request: RequestMock;
    public ticker: SinonStub;
    public contribute: SinonStub;
    public updateMiniGame: SinonStub;
    public confirmWin: SinonStub;
    public contributionTrxId: SinonStub;
    public winJackpot: SinonStub;
    public jpAuth: SinonStub;

    public async before() {
        await syncModels();
        await initCurrencyExchange();
        await flushAll();
        this.prevWalletThroughAPI = config.walletThroughAPI;
        config.walletThroughAPI = true;
        this.gameTokenData.token = await createGameToken(this.gameTokenData);
        this.startToken = await generateStartGameToken(this.startTokenData);
        this.initRequest = {
            requestId: undefined,
            request: "init",
            name: undefined,
            gameId: this.gameID.gameCode,
            deviceId: this.gameID.deviceId,
            startGameToken: this.startToken,
        };

        this.request = requestMock(superagent);

        const jpnServer = getJPNServer();
        this.jpAuth = stub(jpnServer, "auth");
        this.ticker = stub(jpnServer, "getTickers");
        this.contribute = stub(jpnServer, "contribute");
        this.updateMiniGame = stub(jpnServer, "updateMiniGame");
        this.confirmWin = stub(jpnServer, "confirmWin");
        this.contributionTrxId = stub(jpnServer, "generateTransactionId");

        this.winJackpot = stub(jpnServer, "winJackpot");
        setRandomGeneratorFactory(new RandomGeneratorFactoryImpl());
    }

    public async after() {
        config.walletThroughAPI = this.prevWalletThroughAPI;
        await getGameContextModel().truncate();
        await flushAll();
        this.request.unmock(superagent);

        this.jpAuth.restore();
        this.ticker.restore();
        this.contribute.restore();
        this.updateMiniGame.restore();
        this.confirmWin.restore();
        this.contributionTrxId.restore();

        this.winJackpot.restore();
    }

    protected async createFlow(ctx?: GameFlowContext): Promise<EngineGameFlow<GameInitRequest>> {
        return await GameFlowFactory.createForInit(ctx,
            this.startGame, this.initRequest, this.loadedGame, undefined, this.jpnContext);
    }

    protected async findContext(): Promise<GameFlowContext> {
        return this.contextManager.findGameContextById(this.gameID);
    }

    protected mockGenerateTrxId() {
        this.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            "transactionId": "1"
        }));
    }

    protected mockCommitPayment() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            "currency": "USD",
            "main": 100,
            "previousValue": 50
        }));
    }

    protected mockContributionTrxId() {
        this.contributionTrxId.returns({
            transactionId: "100",
        });
    }
}

@suite("GameFlow JPN")
export class GameFlowJPNSpec extends BaseGameFlowJPNSpec {

    @test("gets jackpot ticker")
    public async getsJackpotTicker() {
        const flow = await this.createFlow();

        const response = [
            {
                jackpotId: "jp-id",
                pools: {
                    small: { amount: 10 },
                    medium: { amount: 100 },
                    large: { amount: 1000 }
                }
            }
        ];
        this.ticker.returns(response);

        const tickerResponse = await flow.jackpotTickers();
        expect(tickerResponse).to.deep.equal(response);
    }

    @test("gets multiple jackpot tickers")
    public async getsMultipleJackpotTickers() {
        const jpContext = {
            token: "jpn-auth-token",
            jackpotsInfo: [
                {
                    id: "jp-id-1", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, gameHistoryEnabled: true, paymentStatisticEnabled: true
                },
                {
                    id: "jp-id-2", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, gameHistoryEnabled: true, paymentStatisticEnabled: true
                }
            ],
            contributionEnabled: true
        };
        const flow = await GameFlowFactory.createForInit(
            undefined, this.startGame, this.initRequest, this.loadedGame, undefined, jpContext);

        const response = [
            {
                jackpotId: "jp-id-1",
                pools: {
                    small: { amount: 10 },
                    medium: { amount: 100 },
                    large: { amount: 1000 }
                }
            },
            {
                jackpotId: "jp-id-2",
                pools: {
                    small: { amount: 20 },
                    medium: { amount: 200 },
                    large: { amount: 2000 }
                }
            }
        ];
        this.ticker.returns(response);

        const tickerResponse = await flow.jackpotTickers();
        expect(tickerResponse).to.deep.equal(response);
    }

    @test("jackpot is disabled")
    public async getsJackpotTickerDisabled() {
        const flow = await this.createFlow();

        const response = [
            {
                jackpotId: "jp-id",
                isDisabled: true,
                pools: {
                    small: { amount: 10 },
                    medium: { amount: 100 },
                    large: { amount: 1000 }
                }
            }
        ];
        this.ticker.returns(response);

        await expect(flow.jackpotTickers()).to.be.rejectedWith(JackpotDisabledError);
    }

    @test("jackpot not found")
    public async jackpotNotFound() {
        const flow = await GameFlowFactory.createForInit(undefined, this.startGame, this.initRequest, this.loadedGame);
        const tickerResponse = await flow.jackpotTickers();
        expect(tickerResponse).to.not.exist;
    }

    @test("sends contribution")
    public async sendContribution() {
        this.mockCommitPayment();
        this.mockGenerateTrxId();
        this.mockContributionTrxId();

        this.contribute.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ]
                }
            ],
            contributionPrecision: []
        });

        const flow = await this.createFlow();

        const history = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 3, 4, 5] } };
        const paymentInfo = { bet: 10, win: 22 };
        const gameContext = {
            scene: "scene1",
        };
        const contribution = { type: "contribution", amount: 10 };

        await flow.update(gameContext, paymentInfo, history, contribution);

        const context = await this.findContext();
        expect(context.gameContext).deep.equal(gameContext);
        expect(context.jpContext).deep.equal(this.jpnContext);
        expect(context.pendingModification).to.not.exist;

        // verify payments
        expect(this.request.args.length).to.equal(2);
        expect(this.request.args[1].body).to.deep.equal({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "ts": this.request.args[1].body.ts,
            "bet": undefined,
            "win": undefined,
            "eventId": 0,
            "totalBet": 10,
            "totalWin": 22,
            "totalEventId": 0,
            "transactionId": "1",
            "actions": [
                {
                    "action": "debit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 22,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ]
        });

        expect(this.request.args[1].body.ts).is.not.undefined;
        expect(this.contribute.args.length).to.equal(1);
        expect(this.contribute.args[0][0]).to.deep.equal({
            "type": "contribution",
            "amount": 10,
            "contributionPrecision": undefined,
            "externalId": "1",
            "transactionId": "100",
            "exchangeRates": { "EUR": 0.87224 },
            "roundId": "0",
            "deferredContribution": undefined,
        });
    }

    @test("sends contribution to disabled jackpot")
    public async sendContributionDisabled() {
        this.mockCommitPayment();
        this.mockGenerateTrxId();
        this.mockContributionTrxId();

        this.contribute.returns({
            events: [],
            contributionPrecision: []
        });

        const flow = await this.createFlow();

        const history = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 3, 4, 5] } };
        const paymentInfo = { bet: 10, win: 22 };
        const gameContext = {
            scene: "scene1",
        };
        const contribution = { type: "contribution", amount: 10 };

        await expect(flow.update(gameContext, paymentInfo, history, contribution))
            .to.be.rejectedWith(JackpotDisabledError);
    }

    @test("sends contribution and calculate jp statistics")
    public async sendContributionsAndCollectJPStatistics() {
        this.mockCommitPayment();
        this.mockGenerateTrxId();
        this.mockContributionTrxId();

        this.contribute.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ]
                }
            ],
        });

        const flow1 = await this.createFlow();

        const history1 = { type: "slot", version: 1, roundEnded: false, data: { positions: [1, 3, 4, 5] } };
        const paymentInfo1 = { bet: 10, win: 22 };
        const gameContext1 = {
            scene: "scene1",
        };
        const contribution1 = { type: "contribution", amount: 10 };

        await flow1.update(gameContext1, paymentInfo1, history1, contribution1);

        const context1 = await this.findContext();
        expect(context1.gameContext).deep.equal(gameContext1);
        expect(context1.jpContext).deep.equal(this.jpnContext);
        expect(context1.pendingModification).to.not.exist;
        expect(context1.round.jpStatistic).deep.equal({
            "jp-id":
                {
                    small: { contribution: { seed: 1, progressive: 2 }, win: 0 },
                    medium: { contribution: { seed: 3, progressive: 4 }, win: 0 },
                    large: { contribution: { seed: 4, progressive: 5 }, win: 0 }
                }
        });

        // verify payments
        expect(this.request.args.length).to.equal(2);
        expect(this.request.args[1].body).to.deep.equal({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context1.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": false,
            "roundId": "0",
            "ts": this.request.args[1].body.ts,
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context1.session.sessionId,
            "bet": undefined,
            "win": undefined,
            "eventId": 0,
            "totalEventId": 0,
            "transactionId": "1",
            "actions": [
                {
                    "action": "debit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 22,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ]
        });
        expect(this.contribute.args.length).to.equal(1);
        expect(this.contribute.args[0][0]).to.deep.equal({
            "type": "contribution",
            "contributionPrecision": undefined,
            "amount": 10,
            "externalId": "1",
            "transactionId": "100",
            "exchangeRates": { "EUR": 0.87224 },
            "roundId": "0",
            "deferredContribution": undefined,
        });

        const flow2 = await this.createFlow(flow1.flowContext);

        const history2 = { type: "slot", version: 1, roundEnded: false, data: { positions: [4, 5, 6, 7] } };
        const paymentInfo2 = { bet: 10, win: 1 };
        const gameContext2 = {
            scene: "scene2",
        };
        const contribution2 = { type: "contribution", amount: 1 };

        await flow2.update(gameContext2, paymentInfo2, history2, contribution2);

        const context2 = await this.findContext();
        expect(context2.gameContext).deep.equal(gameContext2);
        expect(context2.jpContext).deep.equal(this.jpnContext);
        expect(context2.pendingModification).to.not.exist;
        expect(context2.round.jpStatistic).deep.equal({
            "jp-id":
                {
                    small: { contribution: { seed: 2, progressive: 4 }, win: 0 },
                    medium: { contribution: { seed: 6, progressive: 8 }, win: 0 },
                    large: { contribution: { seed: 8, progressive: 10 }, win: 0 }
                }
        });
    }

    @test("sends contribution with jackpot ids")
    public async sendContributionWithJpIds() {
        this.mockCommitPayment();
        this.mockGenerateTrxId();
        this.mockContributionTrxId();

        this.contribute.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id-1",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ]
                },
                {
                    type: "contribution",
                    jackpotId: "jp-id-2",
                    pools: {
                        small: { amount: 20 },
                        medium: { amount: 200 },
                        large: { amount: 2000 }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ]
                }
            ]
        });

        const jpContext = {
            token: "jpn-auth-token",
            jackpotsInfo: [
                {
                    id: "jp-id-1", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, gameHistoryEnabled: true, paymentStatisticEnabled: true
                },
                {
                    id: "jp-id-2", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, gameHistoryEnabled: true, paymentStatisticEnabled: true
                }
            ],
            contributionEnabled: true
        };
        const flow = await GameFlowFactory.createForInit(
            undefined, this.startGame, this.initRequest, this.loadedGame, undefined, jpContext);

        const history = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 3, 4, 5] } };
        const paymentInfo = { bet: 10, win: 22 };
        const gameContext = {
            scene: "scene1",
        };
        const contribution = { type: "contribution", amount: 10, jackpotIds: ["jp-id-1", "jp-id-2"] };

        await flow.update(gameContext, paymentInfo, history, contribution);

        const context = await this.findContext();
        expect(context.gameContext).deep.equal(gameContext);
        expect(context.jpContext).deep.equal(jpContext);
        expect(context.pendingModification).to.not.exist;

        // verify payments
        expect(this.request.args.length).to.equal(2);
        expect(this.request.args[1].body).to.deep.equal({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": flow.flowContext.session.sessionId,
            "ts": this.request.args[1].body.ts,
            "bet": undefined,
            "win": undefined,
            "totalBet": 10,
            "totalWin": 22,
            "transactionId": "1",
            "eventId": 0,
            "totalEventId": 0,
            "actions": [
                {
                    "action": "debit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 22,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ]
        });
        expect(this.contribute.args.length).to.equal(1);
        expect(this.contribute.args[0][0]).to.deep.equal({
            "type": "contribution",
            "contributionPrecision": undefined,
            "amount": 10,
            "externalId": "1",
            "jackpotIds": ["jp-id-1", "jp-id-2"],
            "transactionId": "100",
            "exchangeRates": { "EUR": 0.87224 },
            "roundId": "0",
            "deferredContribution": undefined,
        });
    }

    @test("retries contribution if JPN not available")
    public async retriesContributionIfJpUnavailable() {
        this.mockCommitPayment();
        this.mockGenerateTrxId();
        this.mockContributionTrxId();

        this.contribute.throws(new JPNInternalServerError());

        const flow = await this.createFlow();

        const history = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 3, 4, 5] } };
        const paymentInfo = { bet: 10, win: 22 };
        const gameContext = {
            scene: "scene1",
        };
        const contribution = { type: "contribution", amount: 10 };

        await expect(flow.update(gameContext, paymentInfo, history, contribution))
            .to.be.rejectedWith(JPNInternalServerError);
        let context = await this.findContext();
        expect(context.pendingModification).to.deep.equal({
            "history": {
                "data": {
                    "positions": [1, 3, 4, 5],
                },
                "roundEnded": true,
                "type": "slot",
                "version": 1,
            },
            "newState": {
                "scene": "scene1",
            },
            "walletOperation": {
                "bet": 10,
                "currency": "USD",
                "deviceId": "deviceId",
                "operation": "payment",
                "roundEnded": true,
                "roundId": "0",
                "roundPID": publicId.instance.encode(0),
                "gameSessionId": context.session.sessionId,
                "ts": context.pendingModification.walletOperation.ts,
                "transactionId": "1",
                "eventId": 0,
                "totalEventId": 0,
                "win": 22,
                "totalBet": 10,
                "totalWin": 22,
                "actions": [
                    {
                        "action": "debit",
                        "amount": 10,
                        "attribute": "balance",
                        "changeType": "bet",
                    },
                    {
                        "action": "credit",
                        "amount": 22,
                        "attribute": "balance",
                        "changeType": "win",
                    }
                ]
            },
        });

        expect(context.jackpotPending).to.deep.equal({
            "jackpotOperation": {
                "payload": {
                    "type": "contribution",
                    "externalId": "1",
                    "transactionId": "100",
                    "amount": 10,
                    "exchangeRates": { "EUR": 0.87224 },
                    "roundId": "0"
                },
                "type": "contribution"
            }
        });

        // retry on init

        this.contribute.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ]
                }
            ],
        });

        await this.createFlow(flow.flowContext);

        context = await this.findContext();
        expect(context.gameContext).deep.equal(gameContext);
        expect(context.jpContext).deep.equal(this.jpnContext);
        expect(context.pendingModification).to.not.exist;

        // verify payments
        expect(this.request.args.length).to.equal(3);
        const expectedPayment = {
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": "0",
            "ts": this.request.args[1].body.ts,
            "bet": undefined,
            "win": undefined,
            "totalBet": 10,
            "totalWin": 22,
            "transactionId": "1",
            "eventId": 0,
            "totalEventId": 0,
            "actions": [
                {
                    "action": "debit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "bet"
                },
                {
                    "action": "credit",
                    "amount": 22,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ]
        };
        expect(this.request.args[1].body).to.deep.equal(expectedPayment);
        expectedPayment.ts = this.request.args[2].body.ts;
        expect(this.request.args[2].body).to.deep.equal(expectedPayment);
        expect(this.request.args[1].body.ts).is.not.undefined;
        expect(this.contribute.args.length).to.equal(2);
        const expectedContribute1 = {
            "type": "contribution",
            "amount": 10,
            "externalId": "1",
            "transactionId": "100",
            "exchangeRates": { "EUR": 0.87224 },
            "roundId": "0",
            "contributionPrecision": undefined
        };
        const expectedContribute2 = {
            "type": "contribution",
            "amount": 10,
            "externalId": "1",
            "transactionId": "100",
            "exchangeRates": { "EUR": 0.87224 },
            "roundId": "0"
        };
        expect(this.contribute.args[0][0]).deep.include(expectedContribute1);
        expect(this.contribute.args[1][0]).deep.include(expectedContribute2);
    }

    @test("starts mini game after contribution")
    public async startsMiniGameAfterContribution() {
        this.mockCommitPayment();
        this.mockGenerateTrxId();
        this.mockContributionTrxId();

        this.contribute.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ]
                }, {
                    type: "start-mini-game",
                    jackpotId: "jp-id",
                    transactionId: "100",
                }
            ],
        });

        const flow = await this.createFlow();

        const history = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 3, 4, 5] } };
        const paymentInfo = { bet: 10, win: 22 };
        const gameContext = {
            scene: "scene1",
        };
        const contribution = { type: "contribution", amount: 10 };

        await flow.update(gameContext, paymentInfo, history, contribution);

        const context = await this.findContext();
        expect(context.gameContext).deep.equal(gameContext);
        expect(context.jpContext).deep.equal({
            ...this.jpnContext,
            "jackpot": {
                "transactionId": "100",
                "result": [
                    {
                        "transactionId": "100",
                        "event": "start-mini-game",
                        "jackpotId": "jp-id"
                    }
                ]
            },
        });
        expect(context.pendingModification).to.not.exist;

        const jackpot = await flow.jackpotResult();
        expect(omitUndefined(jackpot)).deep.equal({
            "event": "start-mini-game",
            "jackpotId": "jp-id",
        });

        // verify payments
        expect(this.request.args.length).to.equal(2);
        expect(this.request.args[1].body).to.deep.equal({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "ts": this.request.args[1].body.ts,
            "bet": undefined,
            "win": undefined,
            "totalBet": 10,
            "totalWin": 22,
            "eventId": 0,
            "totalEventId": 0,
            "transactionId": "1",
            "actions": [
                {
                    "action": "debit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 22,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ]
        });
        expect(this.contribute.args.length).to.equal(1);
        expect(+this.request.args[1].body.ts).is.not.undefined;
        expect(this.contribute.args[0][0]).to.deep.equal({
            "type": "contribution",
            "contributionPrecision": undefined,
            "amount": 10,
            "externalId": "1",
            "transactionId": "100",
            "exchangeRates": { "EUR": 0.87224 },
            "roundId": "0",
            "deferredContribution": undefined,
        });
    }

    @test("ends mini game after updating mini game status")
    public async endsMinigameAfterUpdateStatus() {
        this.updateMiniGame.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                }, {
                    type: "end-mini-game",
                    jackpotId: "jp-id",
                    transactionId: "123",
                }
            ],
        });

        const oldContext = {
            token: "jpn-auth-token",
            jackpotsInfo: [
                {
                    id: "jp-id", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, gameHistoryEnabled: true, paymentStatisticEnabled: true
                }
            ],
            contributionEnabled: true,
            jackpot: {
                transactionId: "123",
                result: [
                    {
                        jackpotId: "jp-id",
                        event: "start-mini-game"
                    }
                ]
            },
        };

        const flow = await GameFlowFactory.createForInit(undefined, this.startGame,
            this.initRequest,
            this.loadedGame,
            undefined,
            oldContext);

        const gameContext = {
            scene: "scene1",
        };
        const miniGame: MiniGame = { type: "mini-game", choice: "1" };

        await flow.updateMiniGame(gameContext, miniGame);

        const context = await this.findContext();
        expect(context.gameContext).deep.equal(gameContext);
        expect(context.jpContext).deep.equal({
            "token": "jpn-auth-token",
            "jackpotsInfo": [
                {
                    "id": "jp-id", "jpGameId": "test", "currency": "EUR", "type": "test", "baseType": "base",
                    "isGameOwned": true, "gameHistoryEnabled": true, "paymentStatisticEnabled": true
                }
            ],
            "contributionEnabled": true,
        });
        expect(context.pendingModification).to.not.exist;

        const jackpot = await flow.jackpotResult();
        expect(jackpot).is.undefined;
    }

    @test("rejects to update mini game status when there is no mini game")
    public async rejectsToUpdateMiniGame() {
        const flow = await this.createFlow();

        const gameContext = {
            scene: "scene1",
        };
        const miniGame: MiniGame = { type: "mini-game", choice: "1" };

        await expect(flow.updateMiniGame(gameContext, miniGame)).to.be.rejectedWith(JackpotStatusNotValid);
    }

    @test("triggers jackpot during spin")
    public async triggersJackpot() {
        this.mockCommitPayment();
        this.mockGenerateTrxId();
        this.mockContributionTrxId();

        this.winJackpot.returns({
            events: [
                {
                    type: "win",
                    jackpotId: "jp-id",
                    transactionId: "100",
                    amount: 5000,
                    pool: "small",
                    seed: 1000,
                    progressive: 4000,
                }
            ],
        });

        const flow = await this.createFlow();

        const history = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 3, 4, 5] } };
        const paymentInfo = { bet: 10, win: 22 };
        const gameContext = {
            scene: "scene1",
        };
        const triggerWin = { type: "win-jackpot", jackpotId: "jp-id" };

        this.confirmWin.returns({
            events: [
                {
                    type: "win-confirm",
                    jackpotId: "jp-id",
                    transactionId: "100",
                }
            ],
        });

        await flow.update(gameContext, paymentInfo, history, triggerWin);

        const jackpot = await flow.jackpotResult();
        expect(omitUndefined(jackpot)).deep.equal({
            "jackpotId": "jp-id",
            "event": "win",
            "amount": 5000,
            "progressiveWin": 4000,
            "seedWin": 1000,
            "pool": "small",
        });

        const context = await this.findContext();
        expect(context.gameContext).deep.equal(gameContext);
        expect(context.jpContext).deep.equal(this.jpnContext);
        expect(context.jpContext.jackpot).is.undefined;
        expect(context.pendingModification).to.not.exist;

        // verify payments
        expect(this.request.args.length).to.equal(4);
        expect(this.request.args[1].body).to.deep.equal({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "bet": undefined,
            "win": undefined,
            "ts": this.request.args[1].body.ts,
            "transactionId": "1",
            "eventId": 0,
            "totalEventId": 0,
            "totalBet": 10,
            "totalWin": 22,
            "actions": [
                {
                    "action": "debit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 22,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ],
        });

        expect(+this.request.args[1].body.ts).is.not.undefined;
        expect(this.request.args[3].body).to.deep.equal({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "eventId": 1,
            "totalEventId": 1,
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "ts": this.request.args[3].body.ts,
            "bet": undefined,
            "win": undefined,
            "totalBet": 10,
            "totalWin": 5022,
            "transactionId": "1",
            "extTransactionId": "100",
            "actions": [
                {
                    "action": "credit",
                    "amount": 5000,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "jp-id",
                    "pool": "small"
                }
            ],
            "jackpotDetails": {
                "jackpotTypes": {
                    "test": [
                        "jp-id"
                    ]
                },
                "contributionPrecision": undefined,
                "jackpots": {
                    "jp-id": {
                        "small": {
                            "contribution": {
                                "progressive": 0,
                                "seed": 0
                            },
                            "win": 5000,
                            "progressiveWin": 4000,
                            "seedWin": 1000
                        }
                    }
                }
            },
            "totalJpContribution": 0,
            "totalJpWin": 5000
        });
        expect(+this.request.args[3].body.ts).is.not.undefined;
        expect(this.winJackpot.args.length).to.equal(1);
        expect(this.winJackpot.args[0][0]).to.deep.equal({
            "type": "win-jackpot",
            "jackpotId": "jp-id",
            "externalId": "1",
            "transactionId": "100",
            "amount": undefined,
            betAmount: undefined,
            "exchangeRates": { "EUR": 0.87224 },
            "roundId": "0"
        });

        // verify history
        const jpHistory: any = await getGameHistoryItem();

        expect(jpHistory).to.contain({
            "bet": 0,
            "roundEnded": true,
            "roundId": "0",
            "eventId": 1,
            "type": "jackpot-win",
            "win": 5000,
            "balanceAfter": 100,
            "balanceBefore": 50
        });
        expect(jpHistory.result).to.deep.equal({
            "transactionId": "100",
            "result": [
                {
                    "jackpotId": "jp-id",
                    "amount": 5000,
                    "progressiveWin": 4000,
                    "seedWin": 1000,
                    "event": "win",
                    "pool": "small",
                    "transactionId": "100",
                }
            ]
        });

        const spinHistory: any = await getGameHistoryItem();
        expect(spinHistory).to.contain({
            "bet": 10,
            "roundEnded": false,
            "roundId": "0",
            "eventId": 0,
            "type": "slot",
            "win": 22,
            "balanceAfter": 100,
            "balanceBefore": 50
        });

        expect(spinHistory.result).to.deep.equal({
            "positions": [1, 3, 4, 5],
        });
    }

    @test("processes jackpot win")
    public async processesJackpotWin() {
        this.mockCommitPayment();
        this.mockGenerateTrxId();

        this.mockContributionTrxId();

        this.contribute.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ]
                }, {
                    type: "win",
                    jackpotId: "jp-id",
                    transactionId: "100",
                    amount: 5000,
                    pool: "small",
                }
            ],
        });

        const flow = await this.createFlow();

        const history = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 3, 4, 5] } };
        const paymentInfo = { bet: 10, win: 22 };
        const gameContext = {
            scene: "scene1",
        };
        const contribution = { type: "contribution", amount: 10 };

        this.confirmWin.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                }, {
                    type: "win-confirm",
                    jackpotId: "jp-id",
                    transactionId: "100",
                }
            ],
        });

        await flow.update(gameContext, paymentInfo, history, contribution);

        const jackpot = await flow.jackpotResult();
        expect(omitUndefined(jackpot)).deep.equal({
            "jackpotId": "jp-id",
            "event": "win",
            "amount": 5000,
            "pool": "small",
        });

        const context = await this.findContext();
        expect(context.gameContext).deep.equal(gameContext);
        expect(context.jpContext).deep.equal(this.jpnContext);
        expect(context.jpContext.jackpot).is.undefined;
        expect(context.pendingModification).to.not.exist;

        // verify payments
        expect(this.request.args.length).to.equal(4);
        expect(this.request.args[1].body).to.deep.include({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "ts": this.request.args[1].body.ts,
            "eventId": 0,
            "bet": undefined,
            "win": undefined,
            "transactionId": "1",
            "actions": [
                {
                    "action": "debit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 22,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ]
        });
        expect(+this.request.args[1].body.ts).is.not.undefined;
        expect(this.request.args[3].body).to.deep.include({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "ts": this.request.args[3].body.ts,
            "bet": undefined,
            "win": undefined,
            "transactionId": "1",
            "extTransactionId": "100",
            "actions": [
                {
                    "action": "credit",
                    "amount": 5000,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "jp-id",
                    "pool": "small"
                }
            ]
        });
        expect(+this.request.args[3].body.ts).is.not.undefined;
        expect(this.contribute.args.length).to.equal(1);
        expect(this.contribute.args[0][0]).to.deep.equal({
            "type": "contribution",
            "contributionPrecision": undefined,
            "amount": 10,
            "externalId": "1",
            "transactionId": "100",
            "exchangeRates": { "EUR": 0.87224 },
            "roundId": "0",
            "deferredContribution": undefined,
        });

        // verify history
        const jpHistory: any = await getGameHistoryItem();

        expect(jpHistory).to.contain({
            "bet": 0,
            "roundEnded": true,
            "roundId": "0",
            "eventId": 1,
            "type": "jackpot-win",
            "win": 5000,
            "balanceAfter": 100,
            "balanceBefore": 50
        });
        expect(jpHistory.result).to.deep.equal({
            "transactionId": "100",
            "result": [
                {
                    "jackpotId": "jp-id",
                    "amount": 5000,
                    "event": "win",
                    "pool": "small",
                    "transactionId": "100",
                }
            ]
        });

        const spinHistory: any = await getGameHistoryItem();
        expect(spinHistory).to.contain({
            "bet": 10,
            "roundEnded": false,
            "roundId": "0",
            "eventId": 0,
            "type": "slot",
            "win": 22,
            "balanceAfter": 100,
            "balanceBefore": 50
        });

        expect(spinHistory.result).to.deep.equal({
            "positions": [1, 3, 4, 5],
        });
    }

    @test("processes jackpot win and collect jp statistics")
    public async processesJackpotWinAndCollectStatistics() {
        this.mockCommitPayment();
        this.mockGenerateTrxId();

        this.mockContributionTrxId();

        this.contribute.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ]
                }, {
                    type: "win",
                    jackpotId: "jp-id",
                    transactionId: "100",
                    amount: 5000,
                    pool: "small",
                }
            ],
        });

        const flow = await this.createFlow();

        const history = { type: "slot", version: 1, roundEnded: false, data: { positions: [1, 3, 4, 5] } };
        const paymentInfo = { bet: 10, win: 22, gameStatus: "freegame" };
        const gameContext = {
            scene: "scene1",
        };
        const contribution = { type: "contribution", amount: 10 };

        this.confirmWin.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                }, {
                    type: "win-confirm",
                    jackpotId: "jp-id",
                    transactionId: "100",
                }
            ],
        });

        await flow.update(gameContext, paymentInfo, history, contribution);

        const jackpot = await flow.jackpotResult();
        expect(omitUndefined(jackpot)).deep.equal({
            "jackpotId": "jp-id",
            "event": "win",
            "amount": 5000,
            "pool": "small",
        });

        const context = await this.findContext();
        expect(context.gameContext).deep.equal(gameContext);
        expect(context.jpContext).deep.equal(this.jpnContext);
        expect(context.jpContext.jackpot).is.undefined;
        expect(context.pendingModification).to.not.exist;
        expect(context.round.jpStatistic).deep.equal({
            "jp-id":
                {
                    small: { contribution: { seed: 1, progressive: 2 }, win: 5000 },
                    medium: { contribution: { seed: 3, progressive: 4 }, win: 0 },
                    large: { contribution: { seed: 4, progressive: 5 }, win: 0 }
                }
        });

        // verify payments
        expect(this.request.args.length).to.equal(4);
        expect(this.request.args[1].body).to.deep.equal({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": false,
            "roundId": "0",
            "ts": this.request.args[1].body.ts,
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "eventId": 0,
            "totalEventId": 0,
            "bet": undefined,
            "win": undefined,
            "transactionId": "1",
            "actions": [
                {
                    "action": "debit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 22,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ],
            gameStatus: "freegame"
        });
        expect(this.request.args[3].body).to.deep.equal({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": false,
            "ts": this.request.args[3].body.ts,
            "roundId": "0",
            "eventId": 1,
            "totalEventId": 1,
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "bet": undefined,
            "win": undefined,
            "totalBet": 10,
            "totalWin": 5022,
            "transactionId": "1",
            "extTransactionId": "100",
            "actions": [
                {
                    "action": "credit",
                    "amount": 5000,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "jp-id",
                    "pool": "small"
                }
            ],
            gameStatus: "freegame",
            "jackpotDetails": {
                "contributionPrecision": undefined,
                "jackpotTypes": {
                    "test": [
                        "jp-id"
                    ]
                },
                "jackpots": {
                    "jp-id": {
                        "small": {
                            "contribution": {
                                "progressive": 2,
                                "seed": 1
                            },
                            "win": 5000
                        },
                        "medium": {
                            "contribution": {
                                "progressive": 4,
                                "seed": 3
                            },
                            "win": 0
                        },
                        "large": {
                            "contribution": {
                                "progressive": 5,
                                "seed": 4
                            },
                            "win": 0
                        }
                    }
                }
            },
            "totalJpContribution": 0,
            "totalJpWin": 5000
        });
        expect(this.contribute.args.length).to.equal(1);
        expect(this.contribute.args[0][0]).to.deep.equal({
            "type": "contribution",
            "contributionPrecision": undefined,
            "amount": 10,
            "externalId": "1",
            "transactionId": "100",
            "exchangeRates": { "EUR": 0.87224 },
            "roundId": "0",
            "deferredContribution": undefined,
        });

        // verify history
        const jpHistory: any = await getGameHistoryItem();

        expect(jpHistory).to.contain({
            "bet": 0,
            "roundEnded": false,
            "roundId": "0",
            "eventId": 1,
            "type": "jackpot-win",
            "win": 5000,
            "balanceAfter": 100,
            "balanceBefore": 50
        });
        expect(jpHistory.result).to.deep.equal({
            "transactionId": "100",
            "result": [
                {
                    "jackpotId": "jp-id",
                    "amount": 5000,
                    "event": "win",
                    "pool": "small",
                    "transactionId": "100",
                }
            ]
        });

        const spinHistory: any = await getGameHistoryItem();
        expect(spinHistory).to.contain({
            "bet": 10,
            "roundEnded": false,
            "roundId": "0",
            "eventId": 0,
            "type": "slot",
            "win": 22,
            "balanceAfter": 100,
            "balanceBefore": 50
        });

        expect(spinHistory.result).to.deep.equal({
            "positions": [1, 3, 4, 5],
        });
    }

    @test("processes multiple jackpot win")
    public async processesMultipleJackpotWins() {
        this.mockCommitPayment();
        this.mockGenerateTrxId();
        this.mockContributionTrxId();

        this.contribute.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ]
                }, {
                    type: "win",
                    jackpotId: "jp-id1",
                    transactionId: "100",
                    amount: 5000,
                    pool: "small",
                }, {
                    type: "win",
                    jackpotId: "jp-id2",
                    transactionId: "100",
                    amount: 7000,
                    pool: "medium",
                }
            ],
        });

        const jpContext = {
            token: "jpn-auth-token",
            jackpotsInfo: [
                {
                    id: "jp-id", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, paymentStatisticEnabled: true, gameHistoryEnabled: true
                },
                {
                    id: "jp-id1", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, paymentStatisticEnabled: true, gameHistoryEnabled: true
                },
                {
                    id: "jp-id2", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, paymentStatisticEnabled: true, gameHistoryEnabled: true
                }
            ],
            contributionEnabled: true
        };
        const flow = await GameFlowFactory.createForInit(
            undefined, this.startGame, this.initRequest, this.loadedGame, undefined, jpContext);

        const history = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 3, 4, 5] } };
        const paymentInfo = { bet: 10, win: 22 };
        const gameContext = {
            scene: "scene1",
        };
        const contribution = { type: "contribution", amount: 10 };

        this.confirmWin.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                }, {
                    type: "win-confirm",
                    jackpotId: "jp-id1",
                    transactionId: "100",
                }, {
                    type: "win-confirm",
                    jackpotId: "jp-id2",
                    transactionId: "100",
                }
            ],
        });

        await flow.update(gameContext, paymentInfo, history, contribution);

        const jackpot = await flow.jackpotResult();
        expect(omitUndefined(jackpot)).deep.equal([
            {
                "jackpotId": "jp-id1",
                "event": "win",
                "amount": 5000,
                "pool": "small",
            }, {
                "jackpotId": "jp-id2",
                "event": "win",
                "amount": 7000,
                "pool": "medium",
            }
        ]);

        const context = await this.findContext();
        expect(context.gameContext).deep.equal(gameContext);
        expect(context.jpContext).deep.equal(jpContext);

        expect(context.jpContext.jackpot).is.undefined;
        expect(context.pendingModification).to.not.exist;

        // verify payments
        expect(this.request.args.length).to.equal(4);
        expect(this.request.args[1].body).to.deep.equal({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "ts": this.request.args[1].body.ts,
            "bet": undefined,
            "win": undefined,
            "totalBet": 10,
            "totalWin": 22,
            "transactionId": "1",
            "eventId": 0,
            "totalEventId": 0,
            "actions": [
                {
                    "action": "debit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 22,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ]
        });
        expect(+this.request.args[1].body.ts).is.not.undefined;
        expect(this.request.args[3].body).to.deep.equal({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "eventId": 1,
            "totalEventId": 1,
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "ts": this.request.args[3].body.ts,
            "bet": undefined,
            "win": undefined,
            "totalBet": 10,
            "totalWin": 12022,
            "transactionId": "1",
            "extTransactionId": "100",
            "actions": [
                {
                    "action": "credit",
                    "amount": 5000,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "jp-id1",
                    "pool": "small"
                },
                {
                    "action": "credit",
                    "amount": 7000,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "jp-id2",
                    "pool": "medium"
                }
            ],
            "jackpotDetails": {
                "contributionPrecision": undefined,
                "jackpotTypes": {
                    "test": [
                        "jp-id",
                        "jp-id1",
                        "jp-id2"
                    ]
                },
                "jackpots": {
                    "jp-id": {
                        "small": {
                            "contribution": {
                                "progressive": 2,
                                "seed": 1
                            },
                            "win": 0
                        },
                        "medium": {
                            "contribution": {
                                "progressive": 4,
                                "seed": 3
                            },
                            "win": 0
                        },
                        "large": {
                            "contribution": {
                                "progressive": 5,
                                "seed": 4
                            },
                            "win": 0
                        }
                    },
                    "jp-id1": {
                        "small": {
                            "contribution": {
                                "progressive": 0,
                                "seed": 0
                            },
                            "win": 5000
                        },
                    },
                    "jp-id2": {
                        "medium": {
                            "contribution": {
                                "progressive": 0,
                                "seed": 0
                            },
                            "win": 7000
                        },
                    },
                }
            },
            "totalJpContribution": 0,
            "totalJpWin": 12000
        });
        expect(this.request.args[3].body.ts).is.not.undefined;
        expect(this.contribute.args.length).to.equal(1);
        expect(this.contribute.args[0][0]).to.deep.equal({
            "type": "contribution",
            "contributionPrecision": undefined,
            "amount": 10,
            "externalId": "1",
            "transactionId": "100",
            "exchangeRates": { "EUR": 0.87224 },
            "roundId": "0",
            "deferredContribution": undefined,
        });

        // verify history
        const jpHistory: any = await getGameHistoryItem();

        expect(jpHistory).to.contain({
            "bet": 0,
            "roundEnded": true,
            "roundId": "0",
            "eventId": 1,
            "type": "jackpot-win",
            "win": 12000,
            "balanceAfter": 100,
            "balanceBefore": 50
        });
        expect(jpHistory.result).to.deep.equal({
            "transactionId": "100",
            "result": [
                {
                    "jackpotId": "jp-id1",
                    "amount": 5000,
                    "event": "win",
                    "pool": "small",
                    "transactionId": "100",
                }, {
                    "jackpotId": "jp-id2",
                    "amount": 7000,
                    "event": "win",
                    "pool": "medium",
                    "transactionId": "100",
                }
            ]
        });

        const spinHistory: any = await getGameHistoryItem();
        expect(spinHistory).to.contain({
            "bet": 10,
            "roundEnded": false,
            "roundId": "0",
            "eventId": 0,
            "type": "slot",
            "win": 22,
            "balanceAfter": 100,
            "balanceBefore": 50
        });

        expect(spinHistory.result).to.deep.equal({
            "positions": [1, 3, 4, 5],
        });
    }

    @test("processes multiple jackpot win and collect jp statistics")
    public async processesMultipleJackpotWinsAndCollectStatistics() {
        this.mockCommitPayment();
        this.mockGenerateTrxId();
        this.mockContributionTrxId();

        this.contribute.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ]
                }, {
                    type: "win",
                    jackpotId: "jp-id1",
                    transactionId: "100",
                    amount: 5000,
                    pool: "small",
                }, {
                    type: "win",
                    jackpotId: "jp-id2",
                    transactionId: "100",
                    amount: 7000,
                    pool: "medium",
                }
            ],
        });

        const jpContext = {
            token: "jpn-auth-token",
            jackpotsInfo: [
                {
                    id: "jp-id", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, paymentStatisticEnabled: true, gameHistoryEnabled: true
                },
                {
                    id: "jp-id1", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, paymentStatisticEnabled: true, gameHistoryEnabled: true
                },
                {
                    id: "jp-id2", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, paymentStatisticEnabled: true, gameHistoryEnabled: true
                }
            ],
            contributionEnabled: true
        };
        const flow = await GameFlowFactory.createForInit(
            undefined, this.startGame, this.initRequest, this.loadedGame, undefined, jpContext);

        const history = { type: "slot", version: 1, roundEnded: false, data: { positions: [1, 3, 4, 5] } };
        const paymentInfo = { bet: 10, win: 22 };
        const gameContext = {
            scene: "scene1",
        };
        const contribution = { type: "contribution", amount: 10 };

        this.confirmWin.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                }, {
                    type: "win-confirm",
                    jackpotId: "jp-id1",
                    transactionId: "100",
                }, {
                    type: "win-confirm",
                    jackpotId: "jp-id2",
                    transactionId: "100",
                }
            ],
        });

        await flow.update(gameContext, paymentInfo, history, contribution);

        const jackpot = await flow.jackpotResult();
        expect(omitUndefined(jackpot)).deep.equal([
            {
                "jackpotId": "jp-id1",
                "event": "win",
                "amount": 5000,
                "pool": "small",
            }, {
                "jackpotId": "jp-id2",
                "event": "win",
                "amount": 7000,
                "pool": "medium",
            }
        ]);

        const context = await this.findContext();
        expect(context.gameContext).deep.equal(gameContext);
        expect(context.jpContext).deep.equal(jpContext);

        expect(context.jpContext.jackpot).is.undefined;
        expect(context.pendingModification).to.not.exist;
        expect(context.round.jpStatistic).deep.equal({
            "jp-id":
                {
                    small: { contribution: { seed: 1, progressive: 2 }, win: 0 },
                    medium: { contribution: { seed: 3, progressive: 4 }, win: 0 },
                    large: { contribution: { seed: 4, progressive: 5 }, win: 0 }
                },
            "jp-id1":
                { small: { contribution: { seed: 0, progressive: 0 }, win: 5000 } },
            "jp-id2":
                { medium: { contribution: { seed: 0, progressive: 0 }, win: 7000 } }
        });

        // verify payments
        expect(this.request.args.length).to.equal(4);
        expect(this.request.args[1].body).to.deep.equal({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": false,
            "roundId": "0",
            "ts": this.request.args[1].body.ts,
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "bet": undefined,
            "win": undefined,
            "transactionId": "1",
            "eventId": 0,
            "totalEventId": 0,
            "actions": [
                {
                    "action": "debit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 22,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ],
        });
        expect(this.request.args[3].body).to.deep.equal({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": false,
            "roundId": "0",
            "eventId": 1,
            "totalEventId": 1,
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "ts": this.request.args[3].body.ts,
            "bet": undefined,
            "win": undefined,
            "totalBet": 10,
            "totalWin": 12022,
            "transactionId": "1",
            "extTransactionId": "100",
            "actions": [
                {
                    "action": "credit",
                    "amount": 5000,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "jp-id1",
                    "pool": "small"
                },
                {
                    "action": "credit",
                    "amount": 7000,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "jp-id2",
                    "pool": "medium"
                }
            ],
            "jackpotDetails": {
                "contributionPrecision": undefined,
                "jackpotTypes": {
                    "test": [
                        "jp-id",
                        "jp-id1",
                        "jp-id2"
                    ]
                },
                "jackpots": {
                    "jp-id": {
                        "small": {
                            "contribution": {
                                "progressive": 2,
                                "seed": 1
                            },
                            "win": 0
                        },
                        "medium": {
                            "contribution": {
                                "progressive": 4,
                                "seed": 3
                            },
                            "win": 0
                        },
                        "large": {
                            "contribution": {
                                "progressive": 5,
                                "seed": 4
                            },
                            "win": 0
                        }
                    },
                    "jp-id1": {
                        "small": {
                            "contribution": {
                                "progressive": 0,
                                "seed": 0
                            },
                            "win": 5000
                        },
                    },
                    "jp-id2": {
                        "medium": {
                            "contribution": {
                                "progressive": 0,
                                "seed": 0
                            },
                            "win": 7000
                        },
                    },
                }
            },
            "totalJpContribution": 0,
            "totalJpWin": 12000
        });
        expect(this.contribute.args.length).to.equal(1);
        expect(this.contribute.args[0][0]).to.deep.equal({
            "type": "contribution",
            "contributionPrecision": undefined,
            "amount": 10,
            "externalId": "1",
            "transactionId": "100",
            "exchangeRates": { "EUR": 0.87224 },
            "roundId": "0",
            "deferredContribution": undefined,
        });

        // verify history
        const jpHistory: any = await getGameHistoryItem();

        expect(jpHistory).to.contain({
            "bet": 0,
            "roundEnded": false,
            "roundId": "0",
            "eventId": 1,
            "type": "jackpot-win",
            "win": 12000,
            "balanceAfter": 100,
            "balanceBefore": 50
        });
        expect(jpHistory.result).to.deep.equal({
            "transactionId": "100",
            "result": [
                {
                    "jackpotId": "jp-id1",
                    "amount": 5000,
                    "event": "win",
                    "pool": "small",
                    "transactionId": "100",
                }, {
                    "jackpotId": "jp-id2",
                    "amount": 7000,
                    "event": "win",
                    "pool": "medium",
                    "transactionId": "100",
                }
            ]
        });

        const spinHistory: any = await getGameHistoryItem();
        expect(spinHistory).to.contain({
            "bet": 10,
            "roundEnded": false,
            "roundId": "0",
            "eventId": 0,
            "type": "slot",
            "win": 22,
            "balanceAfter": 100,
            "balanceBefore": 50
        });

        expect(spinHistory.result).to.deep.equal({
            "positions": [1, 3, 4, 5],
        });
    }

    @test("processes jackpot win with conversion service")
    public async processesJackpotWithConversionService() {
        this.mockCommitPayment();
        this.mockGenerateTrxId();
        this.mockContributionTrxId();

        this.contribute.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ]
                }, {
                    type: "win",
                    jackpotId: "jp-id",
                    transactionId: "100",
                    amount: 5000,
                    pool: "small",
                }
            ],
        });

        const conversion: any = {
            toGameAmount: (gameInfo, settings, amount: number): number => {
                return amount * 100;
            },
            fromGameAmount: (gameInfo, settings, amount: number): number => {
                return amount / 100;
            },
        };
        const jpContext = {
            token: "jpn-auth-token",
            jackpotsInfo: [
                {
                    id: "jp-id", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, paymentStatisticEnabled: true, gameHistoryEnabled: true
                }
            ],
            contributionEnabled: true
        };
        const flow = await GameFlowFactory.createForInit(undefined, this.startGame, this.initRequest,
            new LoadedGameResult(conversion, TEST_MODULE_NAME), undefined, jpContext);

        const history = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 3, 4, 5] } };
        const paymentInfo = { bet: 1000, win: 2200 };
        const gameContext = {
            scene: "scene1",
        };
        const contribution = { type: "contribution", amount: 1000 };

        this.confirmWin.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    jackpotType: "test",
                    jackpotBaseType: "base",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                }, {
                    type: "win-confirm",
                    jackpotId: "jp-id",
                    jackpotType: "test",
                    jackpotBaseType: "base",
                    transactionId: "100",
                }
            ],
        });

        await flow.update(gameContext, paymentInfo, history, contribution);

        const jackpot = await flow.jackpotResult();
        expect(omitUndefined(jackpot)).deep.equal({
            "jackpotId": "jp-id",
            "event": "win",
            "amount": 5000,
            "pool": "small",
        });

        const context = await this.findContext();
        expect(context.gameContext).deep.equal(gameContext);
        expect(context.jpContext).deep.equal(jpContext);
        expect(context.jpContext.jackpot).is.undefined;
        expect(context.pendingModification).to.not.exist;

        // verify payments
        expect(this.request.args.length).to.equal(4);
        expect(this.request.args[1].body).to.deep.equal({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "ts": this.request.args[1].body.ts,
            "bet": undefined,
            "win": undefined,
            "totalBet": 10,
            "totalWin": 22,
            "transactionId": "1",
            "eventId": 0,
            "totalEventId": 0,
            "actions": [
                {
                    "action": "debit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 22,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ]
        });
        expect(this.request.args[1].body.ts).is.not.undefined;
        expect(this.request.args[3].body).to.deep.equal({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "eventId": 1,
            "totalEventId": 1,
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "ts": this.request.args[3].body.ts,
            "bet": undefined,
            "win": undefined,
            "totalBet": 10,
            "totalWin": 5022,
            "transactionId": "1",
            "extTransactionId": "100",
            "actions": [
                {
                    "action": "credit",
                    "amount": 5000,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "jp-id",
                    "pool": "small"
                }
            ],
            "jackpotDetails": {
                "contributionPrecision": undefined,
                "jackpotTypes": {
                    "test": [
                        "jp-id"
                    ]
                },
                "jackpots": {
                    "jp-id": {
                        "small": {
                            "contribution": {
                                "progressive": 2,
                                "seed": 1
                            },
                            "win": 5000
                        },
                        "medium": {
                            "contribution": {
                                "progressive": 4,
                                "seed": 3
                            },
                            "win": 0
                        },
                        "large": {
                            "contribution": {
                                "progressive": 5,
                                "seed": 4
                            },
                            "win": 0
                        }
                    }
                }
            },
            "totalJpContribution": 0,
            "totalJpWin": 5000
        });
        expect(this.request.args[1].body.ts).is.not.undefined;
        expect(this.contribute.args.length).to.equal(1);
        expect(this.contribute.args[0][0]).to.deep.equal({
            "type": "contribution",
            "contributionPrecision": undefined,
            "amount": 10,
            "externalId": "1",
            "transactionId": "100",
            "exchangeRates": { "EUR": 0.87224 },
            "roundId": "0",
            "deferredContribution": undefined,
        });

        const jpHistory: any = await getGameHistoryItem();
        expect(jpHistory).to.contain({
            "bet": 0,
            "roundEnded": true,
            "roundId": "0",
            "eventId": 1,
            "type": "jackpot-win",
            "win": 5000,
            "balanceAfter": 100,
            "balanceBefore": 50
        });
        expect(jpHistory.result).to.deep.equal({
            "transactionId": "100",
            "result": [
                {
                    "jackpotId": "jp-id",
                    "amount": 5000,
                    "event": "win",
                    "pool": "small",
                    "transactionId": "100",
                }
            ]
        });

        // verify history
        const spinHistory: any = await getGameHistoryItem();
        expect(spinHistory).to.contain({
            "bet": 10,
            "roundEnded": false,
            "roundId": "0",
            "eventId": 0,
            "type": "slot",
            "win": 22,
            "balanceAfter": 100,
            "balanceBefore": 50
        });
        expect(spinHistory.result).to.deep.equal({
            "positions": [1, 3, 4, 5],
        });
    }

    @test("processes jackpot win after mini-game")
    public async processesJackpotWinAfterMiniGame() {
        this.mockCommitPayment();
        this.mockGenerateTrxId();
        this.mockContributionTrxId();

        this.contribute.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ]
                }, {
                    type: "start-mini-game",
                    jackpotId: "jp-id",
                    transactionId: "100",
                }
            ],
        });

        const flow = await this.createFlow();

        const history = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: { positions: [1, 3, 4, 5] }
        };

        const paymentInfo = { bet: 10, win: 22 };
        const gameContext = {
            scene: "scene1",
        };
        const contribution = { type: "contribution", amount: 10 };

        await flow.update(gameContext, paymentInfo, history, contribution);

        let context = await this.findContext();
        expect(context.gameContext).deep.equal(gameContext);
        expect(context.jpContext).deep.equal({
            ...this.jpnContext,
            "jackpot": {
                "transactionId": "100",
                "result": [
                    {
                        "transactionId": "100",
                        "event": "start-mini-game",
                        "jackpotId": "jp-id",
                    }
                ]
            },
        });
        expect(context.pendingModification).to.not.exist;

        this.updateMiniGame.returns({
            events: [
                {
                    type: "win",
                    jackpotId: "jp-id",
                    transactionId: "100",
                    amount: 5000,
                    pool: "small",
                }, {
                    type: "end-mini-game",
                    jackpotId: "jp-id",
                    transactionId: "100",
                }
            ],
        });

        const miniGame: MiniGame = { type: "mini-game", choice: "1" };

        this.confirmWin.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                }, {
                    type: "win-confirm",
                    jackpotId: "jp-id",
                    transactionId: "100",
                }
            ],
        });

        await flow.updateMiniGame(gameContext, miniGame);

        const jackpot = await flow.jackpotResult();
        expect(omitUndefined(jackpot)).deep.equal({
            "jackpotId": "jp-id",
            "event": "win",
            "amount": 5000,
            "pool": "small",
        });

        context = await this.findContext();
        expect(context.gameContext).deep.equal(gameContext);
        expect(context.jpContext).deep.equal(this.jpnContext);
        expect(context.jpContext.jackpot).is.undefined;
        expect(context.pendingModification).to.not.exist;

        // verify payments
        expect(this.request.args.length).to.equal(4);
        expect(this.request.args[1].body).to.deep.equal({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "ts": this.request.args[1].body.ts,
            "bet": undefined,
            "win": undefined,
            "totalBet": 10,
            "totalWin": 22,
            "eventId": 0,
            "totalEventId": 0,
            "transactionId": "1",
            "actions": [
                {
                    "action": "debit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 22,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ]
        });
        expect(this.request.args[1].body.ts).is.not.undefined;
        expect(this.request.args[3].body).to.deep.equal({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "1",
            "eventId": 0,
            "totalEventId": 1,
            "roundPID": publicId.instance.encode(1),
            "gameSessionId": context.session.sessionId,
            "ts": this.request.args[3].body.ts,
            "bet": undefined,
            "win": undefined,
            "totalBet": 0,
            "totalWin": 5000,
            "transactionId": "1",
            "extTransactionId": "100",
            "actions": [
                {
                    "action": "credit",
                    "amount": 5000,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "jp-id",
                    "pool": "small"
                }
            ],
            "jackpotDetails": {
                "contributionPrecision": undefined,
                "jackpotTypes": {
                    "test": [
                        "jp-id"
                    ]
                },
                "jackpots": {
                    "jp-id": {
                        "small": {
                            "contribution": {
                                "progressive": 0,
                                "seed": 0
                            },
                            "win": 5000
                        },
                    }
                }
            },
            "totalJpContribution": 0,
            "totalJpWin": 5000
        });
        expect(this.request.args[3].body.ts).is.not.undefined;
        expect(this.contribute.args.length).to.equal(1);
        expect(this.contribute.args[0][0]).to.deep.equal({
            "type": "contribution",
            "contributionPrecision": undefined,
            "amount": 10,
            "externalId": "1",
            "transactionId": "100",
            "exchangeRates": { "EUR": 0.87224 },
            "roundId": "0",
            "deferredContribution": undefined,
        });

        const jpHistory: any = await getGameHistoryItem();
        expect(jpHistory).to.contain({
            "bet": 0,
            "roundEnded": false,
            "roundId": "1",
            "eventId": 0,
            "type": "jackpot-win",
            "win": 5000,
            "balanceAfter": 100,
            "balanceBefore": 50
        });
        expect(jpHistory.result).to.deep.equal({
            "transactionId": "100",
            "result": [
                {
                    "jackpotId": "jp-id",
                    "amount": 5000,
                    "event": "win",
                    "pool": "small",
                    "transactionId": "100",
                }
            ]
        });

        const spinHistory: any = await getGameHistoryItem();
        expect(spinHistory).to.contain({
            "bet": 10,
            "roundEnded": true,
            "roundId": "0",
            "eventId": 0,
            "type": "slot",
            "win": 22,
            "balanceAfter": 100,
            "balanceBefore": 50
        });
        expect(spinHistory.result).to.deep.equal({
            "positions": [1, 3, 4, 5],
        });
    }

    @test("retries jackpot win payment if JPN not available")
    public async retriesJackpotWinIfJpnNUnavailable() {
        this.mockCommitPayment();
        this.mockGenerateTrxId();
        this.mockContributionTrxId();

        this.contribute.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ]
                }, {
                    type: "win",
                    jackpotId: "jp-id",
                    transactionId: "100",
                    amount: 5000,
                    pool: "small",
                }
            ],
        });

        let flow = await this.createFlow();

        const history = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 3, 4, 5] } };
        const paymentInfo = { bet: 10, win: 22 };
        const gameContext = {
            scene: "scene1",
        };
        const contribution = { type: "contribution", amount: 10 };

        this.confirmWin.throws(new JPNInternalServerError());

        await expect(flow.update(gameContext, paymentInfo, history, contribution))
            .to.be.rejectedWith(JPNInternalServerError);

        let context = await this.findContext();
        expect(context.gameContext).deep.equal(undefined);
        expect(context.jpContext).deep.equal({
            ...this.jpnContext,
            "jackpot": {
                "transactionId": "100",
                "result": [
                    {
                        "jackpotId": "jp-id",
                        "amount": 5000,
                        "event": "win",
                        "pool": "small",
                        "transactionId": "100",
                    }
                ]
            },
        });

        expect(context.pendingModification).to.deep.equal({
            "history": {
                "data": {
                    "positions": [1, 3, 4, 5],
                },
                "roundEnded": true,
                "type": "slot",
                "version": 1,
            },
            "newState": {
                "scene": "scene1",
            },
            "walletOperation": {
                "bet": 10,
                "currency": "USD",
                "deviceId": "deviceId",
                "operation": "payment",
                "roundEnded": true,
                "roundId": "0",
                "roundPID": publicId.instance.encode(0),
                "gameSessionId": context.session.sessionId,
                "ts": context.pendingModification.walletOperation.ts,
                "transactionId": "1",
                "win": 22,
                "totalBet": 10,
                "totalWin": 22,
                "eventId": 0,
                "totalEventId": 0,
                "actions": [
                    {
                        "action": "debit",
                        "amount": 10,
                        "attribute": "balance",
                        "changeType": "bet",
                    },
                    {
                        "action": "credit",
                        "amount": 22,
                        "attribute": "balance",
                        "changeType": "win",
                    }
                ]
            },
        });

        expect(context.jackpotPending).deep.equal({
            walletOperation:
                {
                    operation: "payment",
                    currency: "USD",
                    transactionId: "1",
                    deviceId: "deviceId",
                    roundId: "0",
                    eventId: 1,
                    totalEventId: 1,
                    roundPID: publicId.instance.encode(0),
                    gameSessionId: context.session.sessionId,
                    ts: context.jackpotPending.walletOperation.ts,
                    roundEnded: true,
                    extTransactionId: "100",
                    actions:
                        [
                            {
                                action: "credit",
                                attribute: "balance",
                                amount: 5000,
                                changeType: "jackpot_win",
                                jackpotId: "jp-id",
                                pool: "small"
                            }
                        ],
                    win: 5000,
                    totalBet: 10,
                    totalWin: 5022,
                    jackpotDetails: {
                        "jackpotTypes": {
                            "test": [
                                "jp-id"
                            ]
                        },
                        jackpots: {
                            "jp-id": {
                                "small": {
                                    "contribution": {
                                        "progressive": 2,
                                        "seed": 1
                                    },
                                    "win": 5000
                                },
                                "medium": {
                                    "contribution": {
                                        "progressive": 4,
                                        "seed": 3
                                    },
                                    "win": 0
                                },
                                "large": {
                                    "contribution": {
                                        "progressive": 5,
                                        "seed": 4
                                    },
                                    "win": 0
                                }
                            }
                        }
                    },
                    totalJpContribution: 0,
                    totalJpWin: 5000
                },
            history:
                {
                    type: "jackpot-win",
                    roundEnded: false,
                    data:
                        {
                            transactionId: "100",
                            result:
                                [
                                    {
                                        jackpotId: "jp-id",
                                        event: "win",
                                        transactionId: "100",
                                        amount: 5000,
                                        pool: "small"
                                    }
                                ]
                        }
                },
            jackpotOperation:
                {
                    type: "win-confirm",
                    payload: { transactionId: "100", jackpotId: ["jp-id"], roundId: "0" },

                    previousResult: {
                        jackpot: {
                            result: [
                                {
                                    amount: 5000,
                                    event: "win",
                                    jackpotId: "jp-id",
                                    pool: "small",
                                    transactionId: "100"
                                }
                            ],
                            transactionId: "100"
                        },
                        tickers: [
                            {
                                jackpotId: "jp-id",
                                pools: {
                                    large: {
                                        amount: 1000,
                                    },
                                    medium: {
                                        amount: 100
                                    },
                                    small: {
                                        amount: 10
                                    }
                                }
                            }
                        ],
                        totalJpContribution: 0,
                        totalJpWin: 5000,
                        contributionResult: [
                            {
                                jackpotId: "jp-id",
                                pool: "small",
                                progressive: 2,
                                seed: 1,
                            },
                            {
                                jackpotId: "jp-id",
                                pool: "medium",
                                progressive: 4,
                                seed: 3,
                            },
                            {
                                jackpotId: "jp-id",
                                pool: "large",
                                progressive: 5,
                                seed: 4
                            }
                        ]
                    }
                }
        });

        // retry on init

        this.confirmWin.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                }, {
                    type: "win-confirm",
                    jackpotId: "jp-id",
                    transactionId: "100",
                }
            ],
        });

        flow = await this.createFlow(await getGameFlowContextManager().findGameContextById(flow.flowContext.id));

        const jackpot = await flow.jackpotResult();
        expect(omitUndefined(jackpot)).deep.equal({
            "jackpotId": "jp-id",
            "event": "win",
            "amount": 5000,
            "pool": "small",
        });

        context = await this.findContext();
        expect(context.gameContext).deep.equal(gameContext);
        expect(context.jpContext).deep.equal(this.jpnContext);
        expect(context.pendingModification).to.not.exist;

        // verify payments

        expect(this.request.args.length).to.equal(6);
        const expectedPayment = {
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": "0",
            "ts": this.request.args[1].body.ts,
            "bet": undefined,
            "win": undefined,
            "totalBet": 10,
            "totalWin": 22,
            "transactionId": "1",
            "eventId": 0,
            "totalEventId": 0,
            "actions": [
                {
                    "action": "debit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 22,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ]
        };
        expect(this.request.args[1].body).to.deep.equal(expectedPayment);
        expect(this.request.args[1].body.ts).is.not.undefined;
        expectedPayment.ts = this.request.args[4].body.ts;
        expect(this.request.args[4].body).to.deep.equal(expectedPayment);
        const expectedWinPayment = {
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "eventId": 1,
            "totalEventId": 1,
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": "0",
            "ts": this.request.args[3].body.ts,
            "bet": undefined,
            "win": undefined,
            "totalBet": 10,
            "totalWin": 5022,
            "transactionId": "1",
            "extTransactionId": "100",
            "actions": [
                {
                    "action": "credit",
                    "amount": 5000,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "jp-id",
                    "pool": "small"
                }
            ],
            "jackpotDetails": {
                "contributionPrecision": undefined,
                "jackpotTypes": {
                    "test": [
                        "jp-id"
                    ]
                },
                "jackpots": {
                    "jp-id": {
                        "small": {
                            "contribution": {
                                "progressive": 2,
                                "seed": 1
                            },
                            "win": 5000
                        },
                        "medium": {
                            "contribution": {
                                "progressive": 4,
                                "seed": 3
                            },
                            "win": 0
                        },
                        "large": {
                            "contribution": {
                                "progressive": 5,
                                "seed": 4
                            },
                            "win": 0
                        }
                    }
                }
            },
            "totalJpContribution": 0,
            "totalJpWin": 5000
        };
        expect(this.request.args[3].body.ts).is.not.undefined;
        expect(this.request.args[3].body).to.deep.equal(expectedWinPayment);

        expectedWinPayment.ts = this.request.args[5].body.ts;
        delete expectedWinPayment.jackpotDetails.contributionPrecision;
        expect(this.request.args[5].body).to.deep.equal(expectedWinPayment);
        expect(this.contribute.args.length).to.equal(1);
        expect(this.contribute.args[0][0]).to.deep.equal({
            "type": "contribution",
            "amount": 10,
            "contributionPrecision": undefined,
            "externalId": "1",
            "transactionId": "100",
            "exchangeRates": { "EUR": 0.87224 },
            "roundId": "0",
            "deferredContribution": undefined,
        });

        // verify history
        const jpHistory: any = await getGameHistoryItem();
        expect(jpHistory).to.contain({
            "bet": 0,
            "roundEnded": true,
            "roundId": "0",
            "eventId": 1,
            "type": "jackpot-win",
            "win": 5000,
            "balanceAfter": 100,
            "balanceBefore": 50
        });
        expect(jpHistory.result).to.deep.equal({
            "transactionId": "100",
            "result": [
                {
                    "jackpotId": "jp-id",
                    "amount": 5000,
                    "event": "win",
                    "pool": "small",
                    "transactionId": "100",
                }
            ]
        });

        const spinHistory: any = await getGameHistoryItem();
        expect(spinHistory).to.contain({
            "bet": 10,
            "roundEnded": false,
            "roundId": "0",
            "eventId": 0,
            "type": "slot",
            "win": 22,
        });
        expect(spinHistory.result).to.deep.equal({
            "positions": [1, 3, 4, 5],
        });
    }

    @test("win jackpot")
    public async winsJackpot() {
        this.mockCommitPayment();
        this.mockGenerateTrxId();
        this.mockContributionTrxId();

        this.winJackpot.returns({
            events: [
                {
                    type: "win",
                    transactionId: "100",
                    pool: "small",
                    title: "friendly name",
                    amount: 10,
                    jackpotId: "jp-id",
                },
            ],
        });

        const flow = await this.createFlow();

        const gameContext = {
            scene: "scene1",
        };

        this.confirmWin.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                }, {
                    type: "win-confirm",
                    jackpotId: "jp-id",
                    transactionId: "100"
                }
            ],
        });

        const winJackpotRequest: WinJackpot = {
            type: "win-jackpot",
            jackpotId: "jp-id",
        };

        const gameHistory: GameHistory = {
            type: "win-jackpot",
            roundEnded: true,
            data: "ANY DATA",
        };

        await flow.winJackpot(gameContext, winJackpotRequest, gameHistory);

        const jackpot = await flow.jackpotResult();
        expect(omitUndefined(jackpot)).deep.equal({
            "amount": 10,
            "event": "win",
            "jackpotId": "jp-id",
            "pool": "small",
            "title": "friendly name"
        });

        const context = await this.findContext();
        expect(context.gameContext).deep.equal(gameContext);
        expect(context.jpContext).deep.equal(this.jpnContext);
        expect(context.jpContext.jackpot).is.undefined;
        expect(context.pendingModification).to.not.exist;

        // verify payments
        expect(this.request.args.length).to.equal(2);
        expect(this.request.args[1].body).to.deep.equal({
            "actions": [
                {
                    "action": "credit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "jp-id",
                    "pool": "small"
                }
            ],
            "currency": "USD",
            "deviceId": "deviceId",
            "extTransactionId": "100",
            "gameToken": context.gameData.gameTokenData.token,
            "ts": this.request.args[1].body.ts,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "eventId": 1,
            "totalEventId": 1,
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "bet": undefined,
            "win": undefined,
            "totalBet": 0,
            "totalWin": 10,
            "transactionId": "1",
            "jackpotDetails": {
                "contributionPrecision": undefined,
                "jackpotTypes": {
                    "test": [
                        "jp-id"
                    ]
                },
                "jackpots": {
                    "jp-id": {
                        "small": {
                            "contribution": {
                                "progressive": 0,
                                "seed": 0
                            },
                            "win": 10
                        },
                    }
                }
            },
            "totalJpContribution": 0,
            "totalJpWin": 10
        });
        expect(this.request.args[1].body.ts).is.not.undefined;
        // verify history
        const jpHistory: any = await getGameHistoryItem();

        expect(jpHistory).to.contain({
            "bet": 0,
            "roundEnded": true,
            "roundId": "0",
            "eventId": 1,
            "type": "jackpot-win",
            "win": 10,
            "balanceAfter": 100,
            "balanceBefore": 50
        });
        expect(jpHistory.result).to.deep.equal({
            "transactionId": "100",
            "result": [
                {
                    "amount": 10,
                    "event": "win",
                    "jackpotId": "jp-id",
                    "pool": "small",
                    "title": "friendly name",
                    "transactionId": "100",
                }
            ]
        });

        const spinHistory: any = await getGameHistoryItem();
        expect(spinHistory).to.contain({
            "bet": 0,
            "roundEnded": false,
            "roundId": "0",
            "eventId": 0,
            "type": "win-jackpot"
        });

        expect(spinHistory.result).to.deep.equal("ANY DATA");
    }

    @test("win jackpot with retry")
    public async winJackpotWithRetries() {
        this.mockCommitPayment();
        this.mockGenerateTrxId();
        this.mockContributionTrxId();

        this.winJackpot.returns({
            events: [
                {
                    type: "win",
                    transactionId: "100",
                    pool: "small",
                    amount: 10,
                    jackpotId: "jp-id",
                },
            ],
        });

        const flow = await this.createFlow();

        const gameContext = {
            scene: "scene1",
        };

        this.confirmWin.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                }, {
                    type: "win-confirm",
                    jackpotId: "jp-id",
                    transactionId: "100",
                }
            ],
        });

        const winJackpotRequest: WinJackpot = {
            type: "win-jackpot",
            jackpotId: "jp-id"
        };

        const gameHistory: GameHistory = {
            type: "win-jackpot",

            roundEnded: true,
            data: "ANY DATA",
        };

        this.winJackpot.throws(new JPNInternalServerError());
        await expect(flow.winJackpot(gameContext, winJackpotRequest, gameHistory))
            .to.be.rejectedWith(JPNInternalServerError);

        const brokenContext = await this.findContext();
        expect(brokenContext.gameContext).deep.equal(undefined);
        expect(brokenContext.jpContext).deep.equal(this.jpnContext);

        expect(brokenContext.pendingModification).to.deep.equal({
            "history": {
                "data": "ANY DATA",
                "roundEnded": true,
                "type": "win-jackpot",
                "ts": brokenContext.pendingModification.history.ts
            },
            "newState": {
                "scene": "scene1",
            },
        });
        expect(brokenContext.jackpotPending).to.deep.equal({
            "jackpotOperation": {
                "payload": {
                    "type": "win-jackpot",
                    "jackpotId": "jp-id",
                    "transactionId": "100",
                    "exchangeRates": { "EUR": 0.87224 },
                    "roundId": "0"
                },
                "type": "win-jackpot",
            },
        });
        this.winJackpot.returns({
            events: [
                {
                    type: "win",
                    transactionId: "100",
                    pool: "small",
                    amount: 10,
                    jackpotId: "jp-id",
                },
            ],
        });

        // Retry
        await flow.checkPending();

        const jackpot = await flow.jackpotResult();
        expect(omitUndefined(jackpot)).deep.equal({
            "amount": 10,
            "event": "win",
            "jackpotId": "jp-id",
            "pool": "small",
        });

        const context = await this.findContext();
        expect(context.gameContext).deep.equal(gameContext);
        expect(context.jpContext).deep.equal(this.jpnContext);
        expect(context.jpContext.jackpot).is.undefined;
        expect(context.pendingModification).to.not.exist;

        // verify payments
        expect(this.request.args.length).to.equal(2);
        expect(this.request.args[1].body).to.deep.equal({
            "actions": [
                {
                    "action": "credit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "jp-id",
                    "pool": "small"
                }
            ],
            "currency": "USD",
            "deviceId": "deviceId",
            "extTransactionId": "100",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "eventId": 1,
            "totalEventId": 1,
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "ts": this.request.args[1].body.ts,
            "bet": undefined,
            "win": undefined,
            "totalBet": 0,
            "totalWin": 10,
            "transactionId": "1",
            "jackpotDetails": {
                "contributionPrecision": undefined,
                "jackpotTypes": {
                    "test": [
                        "jp-id"
                    ]
                },
                "jackpots": {
                    "jp-id": {
                        "small": {
                            "contribution": {
                                "progressive": 0,
                                "seed": 0
                            },
                            "win": 10
                        },
                    }
                }
            },
            "totalJpContribution": 0,
            "totalJpWin": 10
        });
        expect(this.request.args[1].body.ts).is.not.undefined;
        // verify history
        const jpHistory: any = await getGameHistoryItem();

        expect(jpHistory).to.contain({
            "bet": 0,
            "roundEnded": true,
            "roundId": "0",
            "eventId": 1,
            "type": "jackpot-win",
            "win": 10,
            "balanceAfter": 100,
            "balanceBefore": 50
        });
        expect(jpHistory.result).to.deep.equal({
            "transactionId": "100",
            "result": [
                {
                    "amount": 10,
                    "event": "win",
                    "jackpotId": "jp-id",
                    "pool": "small",
                    "transactionId": "100",
                }
            ]
        });

        const spinHistory: any = await getGameHistoryItem();
        expect(spinHistory).to.contain({
            "bet": 0,
            "roundEnded": false,
            "roundId": "0",
            "eventId": 0,
            "type": "win-jackpot"
        });

        expect(spinHistory.result).to.deep.equal("ANY DATA");
    }

    @test("win jackpot with amount")
    public async winsJackpotWithAmount() {
        this.mockCommitPayment();
        this.mockGenerateTrxId();
        this.mockContributionTrxId();

        this.winJackpot.returns({
            events: [
                {
                    type: "win",
                    transactionId: "100",
                    pool: "small",
                    amount: 200,
                    jackpotId: "jp-id",
                },
            ],
        });

        const flow = await this.createFlow();

        const gameContext = {
            scene: "scene1",
        };

        this.confirmWin.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                }, {
                    type: "win-confirm",
                    jackpotId: "jp-id",
                    transactionId: "100",
                }
            ],
        });

        const winJackpotRequest: WinJackpot = {
            type: "win-jackpot",
            jackpotId: "jp-id",
            amount: 200
        };

        const gameHistory: GameHistory = {
            type: "win-jackpot",
            roundEnded: true,
            data: "ANY DATA",
        };

        await flow.winJackpot(gameContext, winJackpotRequest, gameHistory);

        const jackpot = await flow.jackpotResult();
        expect(omitUndefined(jackpot)).deep.equal({
            "amount": 200,
            "event": "win",
            "jackpotId": "jp-id",
            "pool": "small",
        });

        const context = await this.findContext();
        expect(context.gameContext).deep.equal(gameContext);
        expect(context.jpContext).deep.equal(this.jpnContext);
        expect(context.jpContext.jackpot).is.undefined;
        expect(context.pendingModification).to.not.exist;

        expect(this.winJackpot.args.length).to.equal(1);
        expect(this.winJackpot.args[0][0]).contains({
            "amount": 200,
            "jackpotId": "jp-id",
            "transactionId": "100",
            "type": "win-jackpot"
        });

        // verify payments
        expect(this.request.args.length).to.equal(2);
        expect(this.request.args[1].body).to.deep.equal({
            "actions": [
                {
                    "action": "credit",
                    "amount": 200,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "jp-id",
                    "pool": "small"
                }
            ],
            "currency": "USD",
            "deviceId": "deviceId",
            "extTransactionId": "100",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "eventId": 1,
            "totalEventId": 1,
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "ts": this.request.args[1].body.ts,
            "bet": undefined,
            "win": undefined,
            "totalBet": 0,
            "totalWin": 200,
            "transactionId": "1",
            "jackpotDetails": {
                "contributionPrecision": undefined,
                "jackpotTypes": {
                    "test": [
                        "jp-id"
                    ]
                },
                "jackpots": {
                    "jp-id": {
                        "small": {
                            "contribution": {
                                "progressive": 0,
                                "seed": 0
                            },
                            "win": 200
                        },
                    }
                }
            },
            "totalJpContribution": 0,
            "totalJpWin": 200
        });

        expect(this.request.args[1].body.ts).is.not.undefined;
        // verify history
        const jpHistory: any = await getGameHistoryItem();
        const spinHistory: any = await getGameHistoryItem();

        expect(jpHistory).to.contain({
            "bet": 0,
            "roundEnded": true,
            "roundId": "0",
            "eventId": 1,
            "type": "jackpot-win",
            "win": 200,
            "balanceAfter": 100,
            "balanceBefore": 50
        });
        expect(jpHistory.result).to.deep.equal({
            "transactionId": "100",
            "result": [
                {
                    "amount": 200,
                    "event": "win",
                    "jackpotId": "jp-id",
                    "pool": "small",
                    "transactionId": "100",
                }
            ]
        });
        expect(spinHistory).to.contain({
            "bet": 0,
            "roundEnded": false,
            "roundId": "0",
            "eventId": 0,
            "type": "win-jackpot"
        });

        expect(spinHistory.result).to.deep.equal("ANY DATA");
    }

    @test("win pending jackpot after disable")
    public async winsPendingJackpotAfterDisable() {
        this.mockCommitPayment();
        this.mockGenerateTrxId();
        this.mockContributionTrxId();

        const flow = await this.createFlow();

        await flow.updateJackpotPendingModification({
            "jackpotOperation": {
                "payload": {
                    "type": "win-jackpot",
                    "jackpotId": "disabled-jp-id",
                    "transactionId": "100",
                    "exchangeRates": { "EUR": 0.87224 },
                    "roundId": "0"
                },
                "type": "win-jackpot",
            },
        });

        this.jpAuth.returns({
            token: "token_xxx_disabled",
            jackpots: [
                {
                    id: "disabled-jp-id",
                    currency: "USD",
                    jpGameId: "jp",
                    type: "test",
                    baseType: "base",
                }
            ]
        });

        this.winJackpot.returns({
            events: [
                {
                    type: "win",
                    transactionId: "100",
                    pool: "small",
                    amount: 10,
                    jackpotId: "disabled-jp-id",
                },
            ],
        });

        this.confirmWin.returns({
            events: [
                {
                    type: "win-confirm",
                    jackpotId: "disabled-jp-id",
                    transactionId: "100",
                }
            ],
        });

        await flow.checkPending();

        const jackpot = await flow.jackpotResult();
        expect(omitUndefined(jackpot)).deep.equal(undefined);

        const context = await this.findContext();
        expect(context.jpContext).deep.equal({
            "contributionEnabled": true,
            "jackpotsInfo": [
                {
                    "currency": "EUR",
                    "gameHistoryEnabled": true,
                    "id": "jp-id",
                    "isGameOwned": true,
                    "jpGameId": "test",
                    "type": "test",
                    "baseType": "base",
                    "paymentStatisticEnabled": true
                },
                {
                    "currency": "USD",
                    "gameHistoryEnabled": false,
                    "id": "disabled-jp-id",
                    "isGameOwned": false,
                    "jpGameId": "jp",
                    "type": "test",
                    "baseType": "base",
                    "paymentStatisticEnabled": false
                }
            ],
            "token": "jpn-auth-token"
        });
        expect(context.jpContext.jackpot).is.undefined;
        expect(context.pendingModification).to.not.exist;

        // verify payments
        expect(this.request.args.length).to.equal(2);
        expect(this.request.args[1].body).to.deep.equal({
            "actions": [
                {
                    "action": "credit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "disabled-jp-id",
                    "pool": "small"
                }
            ],
            "currency": "USD",
            "deviceId": "deviceId",
            "extTransactionId": "100",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "eventId": 0,
            "totalEventId": 0,
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "ts": this.request.args[1].body.ts,
            "bet": undefined,
            "win": undefined,
            "totalBet": 0,
            "totalWin": 10,
            "transactionId": "1",
            "jackpotDetails": {
                "contributionPrecision": undefined,
                "jackpotTypes": {
                    "test": [
                        "jp-id",
                        "disabled-jp-id"
                    ]
                },
                "jackpots": {}
            },
            "totalJpContribution": 0,
            "totalJpWin": 0
        });
        expect(this.request.args[1].body.ts).is.not.undefined;
        // verify history
        const jpHistory: any = await getGameHistoryItem();

        expect(jpHistory).to.contain({
            "bet": 0,
            "roundEnded": true,
            "roundId": "0",
            "eventId": 0,
            "type": "jackpot-win",
            "win": 10,
            "balanceAfter": 100,
            "balanceBefore": 50
        });
        expect(jpHistory.result).to.deep.equal({
            "transactionId": "100",
            "result": [
                {
                    "amount": 10,
                    "event": "win",
                    "jackpotId": "disabled-jp-id",
                    "pool": "small",
                    "transactionId": "100",
                }
            ]
        });
    }

    @test("win pending jackpot when jp context is empty")
    public async winsPendingJackpotWhenJpContextIsEmpty() {
        this.mockCommitPayment();
        this.mockGenerateTrxId();
        this.mockContributionTrxId();

        const flow = await GameFlowFactory.createForInit(
            undefined, this.startGame, this.initRequest, this.loadedGame, undefined, undefined);

        await flow.updateJackpotPendingModification({
            "jackpotOperation": {
                "payload": {
                    "type": "win-jackpot",
                    "jackpotId": "disabled-jp-id",
                    "transactionId": "100",
                    "exchangeRates": { "EUR": 0.87224 },
                    "roundId": "0"
                },
                "type": "win-jackpot",
            },
        });

        this.jpAuth.returns({
            token: "token_xxx_disabled",
            jackpots: [
                {
                    id: "disabled-jp-id",
                    currency: "USD",
                    jpGameId: "jp",
                    isDisabled: true
                }
            ]
        });

        this.winJackpot.returns({
            events: [
                {
                    type: "win",
                    transactionId: "100",
                    pool: "small",
                    amount: 10,
                    jackpotId: "disabled-jp-id",
                },
            ],
        });

        this.confirmWin.returns({
            events: [
                {
                    type: "win-confirm",
                    jackpotId: "disabled-jp-id",
                    transactionId: "100",
                }
            ],
        });

        await flow.checkPending();

        const jackpot = await flow.jackpotResult();
        expect(omitUndefined(jackpot)).deep.equal({
            "amount": 10,
            "event": "win",
            "jackpotId": "disabled-jp-id",
            "pool": "small",
        });

        const context = await this.findContext();
        expect(context.jpContext).is.undefined;
        expect(context.pendingModification).to.not.exist;

        // verify payments
        expect(this.request.args.length).to.equal(2);
        expect(this.request.args[1].body).to.deep.equal({
            "actions": [
                {
                    "action": "credit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "disabled-jp-id",
                    "pool": "small"
                }
            ],
            "currency": "USD",
            "deviceId": "deviceId",
            "extTransactionId": "100",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "eventId": 0,
            "totalEventId": 0,
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "ts": this.request.args[1].body.ts,
            "bet": undefined,
            "win": undefined,
            "totalBet": 0,
            "totalWin": 10,
            "transactionId": "1",
            "jackpotDetails": {
                "contributionPrecision": undefined,
                "jackpotTypes": {},
                "jackpots": {}
            },
            "totalJpContribution": 0,
            "totalJpWin": 0
        });
        expect(this.request.args[1].body.ts).is.not.undefined;
        // verify history
        const jpHistory: any = await getGameHistoryItem();

        expect(jpHistory).to.contain({
            "bet": 0,
            "roundEnded": true,
            "roundId": "0",
            "eventId": 0,
            "type": "jackpot-win",
            "win": 10,
            "balanceAfter": 100,
            "balanceBefore": 50
        });
        expect(jpHistory.result).to.deep.equal({
            "transactionId": "100",
            "result": [
                {
                    "amount": 10,
                    "event": "win",
                    "jackpotId": "disabled-jp-id",
                    "pool": "small",
                    "transactionId": "100",
                }
            ]
        });
    }

    @test("Create gameflow with jackpots short info")
    public async createFlowWithShortInfo() {

        const jackpots: JackpotShortInfo[] = [
            { id: "firstId", currency: "USD", jpGameId: "first_GI", type: "test", baseType: "base" },
            { id: "secondId", currency: "EUR", jpGameId: "second_GI", type: "test", baseType: "base" },
        ];

        const flow = await GameFlowFactory.createForInit(undefined,
            this.startGame,
            this.initRequest as GameInitRequest,
            this.loadedGame,
            undefined,
            this.jpnContext,
            jackpots);

        expect({
            "coins": [
                1,
            ],
            "currencyMultiplier": 100,
            "defaultCoin": 1,
            "jackpotId": "jp-id",
            "jackpots": jackpots,
        }).deep.eq(flow.settings());

    }

    @test("processes 501 code on jp win")
    public async brokenIntegrationOnJPWin() {
        this.request.put("http://api:3006//v2/play/payment", onCall(status200({
            "currency": "USD",
            "main": 100,
            "previousValue": 50
        })).onCall(testing.status(501, {})));
        this.mockGenerateTrxId();

        this.mockContributionTrxId();

        this.contribute.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ]
                }, {
                    type: "win",
                    jackpotId: "jp-id",
                    transactionId: "100",
                    amount: 5000,
                    pool: "small",
                }
            ],
        });

        const flow = await this.createFlow();

        const history = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 3, 4, 5] } };
        const paymentInfo = { bet: 10, win: 22 };
        const gameContext = {
            scene: "scene1",
        };
        const contribution = { type: "contribution", amount: 10 };

        this.confirmWin.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                }, {
                    type: "win-confirm",
                    jackpotId: "jp-id",
                    transactionId: "100",
                }
            ],
        });

        await expect(flow.update(gameContext, paymentInfo, history, contribution))
            .to.be.rejectedWith(ManagementAPIBrokenIntegration);

        const jackpot = await flow.jackpotResult();
        expect(omitUndefined(jackpot)).deep.equal({
            "jackpotId": "jp-id",
            "event": "win",
            "amount": 5000,
            "pool": "small",
        });

        const context = await this.findContext();
        expect(context.pendingModification).exist;
        expect(context.jackpotPending).exist;
        expect(context.specialState).equal(SpecialState.BROKEN_INTEGRATION);

        // verify payments
        expect(this.request.args.length).to.equal(4);
        expect(this.request.args[1].body).to.deep.include({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "ts": this.request.args[1].body.ts,
            "eventId": 0,
            "bet": undefined,
            "win": undefined,
            "transactionId": "1",
            "actions": [
                {
                    "action": "debit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 22,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ]
        });
        expect(+this.request.args[1].body.ts).is.not.undefined;
        expect(this.request.args[3].body).to.deep.include({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "ts": this.request.args[3].body.ts,
            "bet": undefined,
            "win": undefined,
            "transactionId": "1",
            "extTransactionId": "100",
            "actions": [
                {
                    "action": "credit",
                    "amount": 5000,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "jp-id",
                    "pool": "small"
                }
            ]
        });
        expect(+this.request.args[3].body.ts).is.not.undefined;
        expect(this.contribute.args.length).to.equal(1);
        expect(this.contribute.args[0][0]).to.deep.equal({
            "type": "contribution",
            "contributionPrecision": undefined,
            "amount": 10,
            "externalId": "1",
            "transactionId": "100",
            "exchangeRates": { "EUR": 0.87224 },
            "roundId": "0",
            "deferredContribution": undefined,
        });

        // verify history
        const item: any = await getGameHistoryItem();
        expect(item).is.undefined;
    }

    @test("processes 500 code on jp win")
    public async processJPWinPaymentRetries() {
        this.request.put("http://api:3006//v2/play/payment", onCall(status200({
            "currency": "USD",
            "main": 100,
            "previousValue": 50
        })).onCall(testing.status(500, {})));
        this.mockGenerateTrxId();

        this.mockContributionTrxId();

        this.contribute.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ]
                }, {
                    type: "win",
                    jackpotId: "jp-id",
                    transactionId: "100",
                    amount: 5000,
                    pool: "small",
                }
            ],
        });

        const flow = await this.createFlow();

        const history = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 3, 4, 5] } };
        const paymentInfo = { bet: 10, win: 22 };
        const gameContext = {
            scene: "scene1",
        };
        const contribution = { type: "contribution", amount: 10 };

        this.confirmWin.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                }, {
                    type: "win-confirm",
                    jackpotId: "jp-id",
                    transactionId: "100",
                }
            ],
        });

        await expect(flow.update(gameContext, paymentInfo, history, contribution))
            .to.be.rejectedWith(ManagementAPITransientError);

        const jackpot = await flow.jackpotResult();
        expect(omitUndefined(jackpot)).deep.equal({
            "jackpotId": "jp-id",
            "event": "win",
            "amount": 5000,
            "pool": "small",
        });

        const context = await this.findContext();
        expect(context.pendingModification).exist;
        expect(context.pendingModification.walletOperation.retry).is.undefined;
        expect(context.jackpotPending.walletOperation.retry).eq(1);

        // verify payments
        expect(this.request.args.length).to.equal(4);
        expect(this.request.args[1].body).to.deep.include({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "ts": this.request.args[1].body.ts,
            "eventId": 0,
            "bet": undefined,
            "win": undefined,
            "transactionId": "1",
            "actions": [
                {
                    "action": "debit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 22,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ]
        });
        expect(+this.request.args[1].body.ts).is.not.undefined;
        expect(this.request.args[3].body).to.deep.include({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "ts": this.request.args[3].body.ts,
            "bet": undefined,
            "win": undefined,
            "transactionId": "1",
            "extTransactionId": "100",
            "actions": [
                {
                    "action": "credit",
                    "amount": 5000,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "jp-id",
                    "pool": "small"
                }
            ]
        });
        expect(+this.request.args[3].body.ts).is.not.undefined;
        expect(this.contribute.args.length).to.equal(1);
        expect(this.contribute.args[0][0]).to.deep.equal({
            "type": "contribution",
            "contributionPrecision": undefined,
            "amount": 10,
            "externalId": "1",
            "transactionId": "100",
            "exchangeRates": { "EUR": 0.87224 },
            "roundId": "0",
            "deferredContribution": undefined,
        });

        // verify history
        const item: any = await getGameHistoryItem();
        expect(item).is.undefined;
    }

    @test("processes 'CannotCompletePaymentЭ on JP Win")
    public async cannotCompletePaymentOnJPWin() {
        this.request.put("http://api:3006//v2/play/payment", onCall(status200({
            "currency": "USD",
            "main": 100,
            "previousValue": 50
        })).onCall(testing.status(500, { code: 806 })));
        this.mockGenerateTrxId();

        this.mockContributionTrxId();

        this.contribute.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                    contributions: [
                        { pool: "small", seed: 1, progressive: 2 },
                        { pool: "medium", seed: 3, progressive: 4 },
                        { pool: "large", seed: 4, progressive: 5 },
                    ]
                }, {
                    type: "win",
                    jackpotId: "jp-id",
                    transactionId: "100",
                    amount: 5000,
                    pool: "small",
                }
            ],
        });

        const flow = await this.createFlow();

        const history = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 3, 4, 5] } };
        const paymentInfo = { bet: 10, win: 22 };
        const gameContext = {
            scene: "scene1",
        };
        const contribution = { type: "contribution", amount: 10 };

        this.confirmWin.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: {
                        small: { amount: 10 },
                        medium: { amount: 100 },
                        large: { amount: 1000 }
                    },
                }, {
                    type: "win-confirm",
                    jackpotId: "jp-id",
                    transactionId: "100",
                }
            ],
        });

        await expect(flow.update(gameContext, paymentInfo, history, contribution))
            .to.be.rejectedWith(ManagementAPITransientError);

        const jackpot = await flow.jackpotResult();
        expect(omitUndefined(jackpot)).deep.equal({
            "jackpotId": "jp-id",
            "event": "win",
            "amount": 5000,
            "pool": "small",
        });

        const context = await this.findContext();
        expect(context.pendingModification).exist;
        expect(context.jackpotPending).exist;
        expect(context.specialState).equal(SpecialState.CANNOT_COMPLETE_PAYMENT);

        // verify payments
        expect(this.request.args.length).to.equal(4);
        expect(this.request.args[1].body).to.deep.include({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "ts": this.request.args[1].body.ts,
            "eventId": 0,
            "bet": undefined,
            "win": undefined,
            "transactionId": "1",
            "actions": [
                {
                    "action": "debit",
                    "amount": 10,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 22,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ]
        });
        expect(+this.request.args[1].body.ts).is.not.undefined;
        expect(this.request.args[3].body).to.deep.include({
            "currency": "USD",
            "deviceId": "deviceId",
            "gameToken": context.gameData.gameTokenData.token,
            "operation": "payment",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "ts": this.request.args[3].body.ts,
            "bet": undefined,
            "win": undefined,
            "transactionId": "1",
            "extTransactionId": "100",
            "actions": [
                {
                    "action": "credit",
                    "amount": 5000,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "jp-id",
                    "pool": "small"
                }
            ]
        });
        expect(+this.request.args[3].body.ts).is.not.undefined;
        expect(this.contribute.args.length).to.equal(1);
        expect(this.contribute.args[0][0]).to.deep.equal({
            "type": "contribution",
            "contributionPrecision": undefined,
            "amount": 10,
            "externalId": "1",
            "transactionId": "100",
            "exchangeRates": { "EUR": 0.87224 },
            "roundId": "0",
            "deferredContribution": undefined,
        });

        // verify history
        const item: any = await getGameHistoryItem();
        expect(item).is.undefined;

    }
}
