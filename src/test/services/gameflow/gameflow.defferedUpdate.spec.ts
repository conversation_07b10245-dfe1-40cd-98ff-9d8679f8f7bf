import { expect, should, use } from "chai";
import { injectRandomGenerator } from "../../../skywind/services/random";
import { suite, test } from "mocha-typescript";
import { BaseGameFlowSpec } from "./gameflow.spec";
import { getGameHistoryItem } from "../../helper";

const chaiAsPromise = require("chai-as-promised");

should();
use(chaiAsPromise);

@suite("GameFlow.deferredUpdate")
class GameFlowWithDeferredUpdateSpec extends BaseGameFlowSpec {

    @test("updates context")
    public async updateContext() {
        const flow = await this.createForInit();
        injectRandomGenerator(flow);

        flow.deferredUpdate({ context: { data: "FOR_TEST_REASON" } });
        await flow.commitDeferredUpdate();

        const context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.gameContext).deep.equals({ data: "FOR_TEST_REASON" });
    }

    @test("rewrites update")
    public async rewriteUpdate() {
        const flow = await this.createForInit();
        injectRandomGenerator(flow);

        flow.deferredUpdate({ context: { data: "FOR_TEST_REASON" } });
        flow.deferredUpdate({ context: { data: "FOR_TEST_REASON1" } });
        await flow.commitDeferredUpdate();

        const context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.gameContext).deep.equals({ data: "FOR_TEST_REASON1" });
    }

    @test("make payments")
    public async makesPaymentAndStoreHistory() {
        const flow = await this.createForInit();
        injectRandomGenerator(flow);

        let balance = await flow.getBalance();
        expect(balance).deep.equal({
            "amount": 1000,
            "bonus": {
                "amount": 0,
            },
            "currency": "USD",
            "real": {
                "amount": 1000,
            }
        });

        flow.deferredUpdate({
            payment: { win: 10, bet: 100 },
            history: { type: "slot", roundEnded: true, data: { field: "some_history" } }
        });
        flow.deferredUpdate({ context: { data: "FOR_TEST_REASON" } });
        await flow.commitDeferredUpdate();

        const newBalance = await flow.getBalance();
        balance = await flow.getBalance();
        expect(balance).deep.equal({
            "amount": 910,
            "bonus": {
                "amount": 0,
            },
            "currency": "USD",
            "real": {
                "amount": 910,
            }
        });
        expect(balance).deep.equal(newBalance);

        const context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.gameContext).deep.equals({ data: "FOR_TEST_REASON" });

        const historyItem: any = await getGameHistoryItem();
        expect(historyItem.result).deep.equal({ field: "some_history" });
        expect(historyItem.win).is.eq(10);
        expect(historyItem.bet).is.eq(100);
    }

    @test("player ip should be added to history")
    public async playerIpShouldBeAddedToHistory() {
        const flow: any = await this.createForInit();
        flow.engineGameFlowContext.gameData = { ...flow.engineGameFlowContext.gameData, ip: "256.255.254.253" };

        injectRandomGenerator(flow);

        const balance = await flow.getBalance();
        expect(balance).deep.equal({
            "amount": 1000,
            "bonus": {
                "amount": 0,
            },
            "currency": "USD",
            "real": {
                "amount": 1000,
            }
        });

        flow.deferredUpdate({
            payment: { win: 10, bet: 100 },
            history: { type: "slot", roundEnded: true, data: { field: "some_history" } }
        });
        flow.deferredUpdate({ context: { data: "FOR_TEST_REASON" } });
        await flow.commitDeferredUpdate();

        const historyItem: any = await getGameHistoryItem();
        expect(historyItem.result).deep.equal({ field: "some_history" });
        expect(historyItem.win).is.eq(10);
        expect(historyItem.bet).is.eq(100);
        expect(historyItem.playerIp).is.equal("256.255.254.253");
    }
}
