import { suite, test } from "mocha-typescript";
import { GameTokenData, generateStartGameToken, Limits } from "../../../skywind/services/tokens";
import { GameData } from "../../../skywind/services/auth";
import { GameFlowFactory } from "../../../skywind/services/gameFlowFactory";
import { LoadedGameResult } from "../../../skywind/services/game/game";
import { createGameToken, flushAll, syncModels, TEST_MODULE_NAME } from "../../helper";
import { GameContextID } from "../../../skywind/services/contextIds";
import { DumyGame } from "../../testGames";
import { Contribution, GameHistory, GameInitRequest, PaymentInfo } from "@skywind-group/sw-game-core";
import { RequestContext } from "../../../skywind/services/context/gamecontext";
import { expect, use } from "chai";
import config from "../../../skywind/config";
import { injectRandomGenerator } from "../../../skywind/services/random";
import * as wallet from "../../../skywind/services/wallet";
import {
    Balance,
    createWalletManager,
    PaymentOperation,
    PlayerWalletManager,
    WalletOperation
} from "../../../skywind/services/wallet";
import { SinonSpy, SinonStub, spy, stub } from "sinon";
import { getJPNServer } from "../../../skywind/services/jpn/jpnserver";
import { init as initCurrencyExchange } from "../../../skywind/services/currencyexchange";
import { JackpotContext } from "../../../skywind/services/jpn/jackpot";
import { RecoveryType } from "../../../skywind/services/offlinestorage/offlineCommands";
import { JackpotPendingProcessResult } from "../../../skywind/services/jpn/jackpotFlow";
import * as History from "../../../skywind/history/history";
import { createRoundHistoryEvent, RoundHistory } from "../../../skywind/history/history";
import { ManagementAPITransientError } from "../../../skywind/errors";

use(require("chai-as-promised"));

@suite("GameFlow.combined.payment")
export class SplitPaymentSpec {

    public static walletThroughAPIPrevValue: boolean;
    public ticker: SinonStub;
    public contribute: SinonStub;
    public updateMiniGame: SinonStub;
    public confirmWin: SinonStub;
    public contributionTrxId: SinonStub;
    public winJackpot: SinonStub;
    public createRoundHistoryEventStub: SinonSpy;
    public createGameHistoryEventStub: SinonSpy;
    public createJackpotGameHistoryEventStub: SinonSpy;
    private walletManager: PlayerWalletManager;
    private createWalletManager: SinonStub;

    // Before All
    public static async before() {
        this.walletThroughAPIPrevValue = config.walletThroughAPI;
        config.walletThroughAPI = false;
        await syncModels();
        await initCurrencyExchange();
    }

    // After All
    public static after() {
        config.walletThroughAPI = this.walletThroughAPIPrevValue;
    }

    public async before() {
        await flushAll();

        this.walletManager = wallet.createWalletManager(createMockedGameTokenData());
        this.createWalletManager = stub(wallet, "createWalletManager");

        const jpnServer = getJPNServer();
        this.ticker = stub(jpnServer, "getTickers");
        this.contribute = stub(jpnServer, "contribute");
        this.updateMiniGame = stub(jpnServer, "updateMiniGame");
        this.confirmWin = stub(jpnServer, "confirmWin");
        this.contributionTrxId = stub(jpnServer, "generateTransactionId");
        this.winJackpot = stub(jpnServer, "winJackpot");

        this.createRoundHistoryEventStub = spy(History, "createRoundHistoryEvent");
        this.createGameHistoryEventStub = spy(History, "createGameHistoryEvent");
        this.createJackpotGameHistoryEventStub = spy(History, "createJackpotGameHistoryEvent");

    }

    public async after() {
        this.createWalletManager.restore();

        this.ticker.restore();
        this.contribute.restore();
        this.updateMiniGame.restore();
        this.confirmWin.restore();
        this.contributionTrxId.restore();
        this.winJackpot.restore();

        this.createRoundHistoryEventStub.restore();
        this.createGameHistoryEventStub.restore();
        this.createJackpotGameHistoryEventStub.restore();
    }

    @test("Combined payment with jackpot operation")
    public async splitPaymentWithJpOperation() {
        let commitOperationCallback = (data: WalletOperation) => { /* Void */
        };
        const commitOperationIntrinsic = (data: WalletOperation) => {
            commitOperationCallback(data);
        };

        const originalCommitOperation = this.walletManager.commitOperation;
        this.walletManager.commitOperation = async (data: WalletOperation): Promise<Balance> => {
            const balance = await originalCommitOperation.call(this.walletManager, data);
            commitOperationIntrinsic.call(this.walletManager, data);
            return balance;
        };

        this.createWalletManager.returns(this.walletManager);

        const gameTokenData = createMockedGameTokenData();
        const gameData = createMockedGameDataWithSplitPayment(gameTokenData);
        const initRequest = await createMockedInitRequest(gameTokenData);

        await makePlayerCredit(gameTokenData, 1000);

        const jpnContext = {
            token: "jpn-auth-token",
            jackpotsInfo: [{ id: "jp-id", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                isGameOwned: true, gameHistoryEnabled: true, paymentStatisticEnabled: true }],
            contributionEnabled: true
        };
        const flow = await GameFlowFactory.createForInit(undefined,
            gameData,
            initRequest,
            new LoadedGameResult(new DumyGame(), TEST_MODULE_NAME),
            undefined,
            jpnContext);

        injectRandomGenerator(flow);

        this.contributionTrxId.returns({ transactionId: "JP-TRX" });

        this.contribute.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: { small: { amount: 10 }, medium: { amount: 100 }, large: { amount: 1000 } },
                    totalContribution: 5
                },
                {
                    type: "win",
                    jackpotId: "jp-id",
                    pool: "test_pool",
                    amount: 555
                }
            ],
        });

        this.confirmWin.returns({
            events: [
                {
                    type: "win-confirm",
                    jackpotId: "jp-id",
                    transactionId: "JP-TRX",
                }
            ],
        });

        expect(await flow.getBalance()).deep.equal({
            "amount": 1000,
            "bonus": { "amount": 0 },
            "currency": "USD",
            "real": { "amount": 1000 }
        });

        const payment: PaymentInfo = {
            actions: [
                { action: "debit", attribute: "balance", amount: 100 },
                { action: "credit", attribute: "balance", amount: 10 }
            ],
            gameStatus: "settled"
        };

        const jackpotAction: Contribution = {
            type: "contribution",
            amount: 5
        };

        const history: GameHistory = {
            type: "spin",
            roundEnded: true,
            data: {}
        };

        let callCount = 0;
        commitOperationCallback = (data: PaymentOperation) => {
            callCount++;

            switch (callCount) {
                case 1 : // Bet + WIN
                    expect(data.actions).deep.equals([
                        {
                            action: "debit",
                            amount: 100,
                            attribute: "balance"
                        },
                        {
                            action: "credit",
                            amount: 10,
                            attribute: "balance"
                        }
                    ]);
                    expect(data.win).deep.equals(10);
                    expect(data.bet).deep.equals(100);
                    expect(data.roundEnded).to.equal(true);
                    break;
                case 2: // JP Win
                    expect(data.actions).deep.equals([
                        {
                            action: "credit",
                            amount: 555,
                            attribute: "balance",
                            changeType: "jackpot_win",
                            jackpotId: "jp-id",
                            pool: "test_pool"
                        }
                    ]);
                    expect(data.win).deep.equals(555);
                    expect(data.bet).deep.equals(0);
                    expect(data.roundEnded).to.equal(true);
                    break;
            }
        };

        flow.deferredUpdate({ payment: payment, jackpotAction: jackpotAction, history: history });
        await flow.commitDeferredUpdate();

        expect(callCount).eq(2); // 1 calls to wallet + 1 wallet call to jp

        expect(await flow.getBalance()).deep.equal({
            "amount": 910 + 555,
            "bonus": { "amount": 0 },
            "currency": "USD",
            "real": { "amount": 910 + 555 }
        });

        expect(this.createRoundHistoryEventStub.calledOnce).to.be.true;
        const roundHistory = this.createRoundHistoryEventStub.returnValues[0] as RoundHistory;

        expect(roundHistory).deep.include({
            balanceBefore: 1000,
            balanceAfter: 1465,
            totalBet: 100,
            totalWin: 565,
            totalJpContribution: 5,
            totalJpWin: 555
        });

        expect(this.createGameHistoryEventStub.calledOnce).to.be.true;
        const spinHistory = this.createGameHistoryEventStub.returnValues[0];

        expect(spinHistory).deep.include({
            balanceBefore: 1000,
            balanceAfter: 910,
            bet: 100,
            win: 10,
            totalJpContribution: 5,
            totalJpWin: 0,
            roundEnded: false
        });
        expect(this.createJackpotGameHistoryEventStub.calledOnce).is.true;
        const jpHistory = this.createJackpotGameHistoryEventStub.returnValues[0];
        expect(jpHistory).deep.include({
            balanceBefore: 910,
            balanceAfter: 1465,
            bet: 0,
            win: 555,
            totalJpContribution: 0,
            totalJpWin: 555,
            type: "jackpot-win",
            roundEnded: true
        });
    }

    @test("Correct Balance before/after with combined payment")
    public async correctBalanceHistoryWithCombinedPayment() {

        let commitOperationCallback = (data: WalletOperation) => { /* Void */
        };
        const commitOperationIntrinsic = (data: WalletOperation) => {
            commitOperationCallback(data);
        };

        const originalCommitOperation = this.walletManager.commitOperation;
        this.walletManager.commitOperation = async (data: WalletOperation): Promise<Balance> => {
            const balance = await originalCommitOperation.call(this.walletManager, data);
            commitOperationIntrinsic.call(this.walletManager, data);
            return balance;
        };

        this.createWalletManager.returns(this.walletManager);

        const gameTokenData = createMockedGameTokenData();
        const gameData = createMockedGameDataWithSplitPayment(gameTokenData);
        const initRequest = await createMockedInitRequest(gameTokenData);

        await makePlayerCredit(gameTokenData, 1000);

        const flow = await GameFlowFactory.createForInit(undefined,
            gameData,
            initRequest,
            new LoadedGameResult(new DumyGame(), TEST_MODULE_NAME));

        flow.flowContext.commitPendingModification = function(gameResultBalance?: Balance,
                                                              jackpotResult?: JackpotPendingProcessResult,
                                                              newJackpotContext?: JackpotContext,
                                                              recoveryType?: RecoveryType) {

            expect(gameResultBalance).deep.equal({
                currency: "USD",
                main: 910,
                previousValue: 1000
            });

            return undefined;

        };

        injectRandomGenerator(flow);

        expect(flow.settings().splitPayment).deep.equal(false);

        expect(await flow.getBalance()).deep.equal({
            "amount": 1000,
            "bonus": { "amount": 0 },
            "currency": "USD",
            "real": { "amount": 1000 }
        });

        const payment: PaymentInfo = {
            actions: [
                { action: "debit", attribute: "balance", amount: 100 },
                { action: "credit", attribute: "balance", amount: 10 }
            ]
        };

        let callCount = 0;
        commitOperationCallback = (data: PaymentOperation) => {
            callCount++;

            switch (callCount) {
                case 1 :
                    expect(data.actions).deep.equals([
                        { action: "debit", amount: 100, attribute: "balance" },
                        { action: "credit", amount: 10, attribute: "balance" }
                    ]);
                    expect(data.win).deep.equals(10);
                    expect(data.bet).deep.equals(100);
                    break;
                case 2:
                    expect(data.actions).deep.equals([{ action: "credit", amount: 10, attribute: "balance" }]);
                    expect(data.win).deep.equals(10);
                    expect(data.bet).deep.equals(0);
                    break;
            }
        };

        flow.deferredUpdate({ payment: payment });
        await flow.commitDeferredUpdate();

        expect(await flow.getBalance()).deep.equal({
            "amount": 910,
            "bonus": { "amount": 0 },
            "currency": "USD",
            "real": { "amount": 910 }
        });

    }

    @test("Combined payment - require refund transfer in")
    public async combinedPaymentRequireRefundTransferIn() {

        this.walletManager.commitOperation = async (data: WalletOperation): Promise<Balance> => {
            return Promise.reject(new ManagementAPITransientError(400,
                { responseStatus: 400, code: 800, message: "Mark for refund" } as any));
        };

        this.createWalletManager.returns(this.walletManager);

        const gameTokenData = createMockedGameTokenData();
        const gameData = createMockedGameDataWithSplitPayment({
            ...gameTokenData,
            transferEnabled: true,
            settings: { transferEnabled: true }
        } as any);
        const initRequest = await createMockedInitRequest(gameTokenData);

        let flow = await GameFlowFactory.createForInit(undefined,
            {
                ...gameData,
                settings: { transferEnabled: true } },
            initRequest,
            new LoadedGameResult(new DumyGame(), TEST_MODULE_NAME));

        injectRandomGenerator(flow);

        await expect(flow.transfer({ operation: "transfer-in", amount: 100 }))
            .to.be.rejectedWith(ManagementAPITransientError);
        expect(flow.flowContext.pendingModification.walletOperation).contains({ markForRefund: true });

        this.walletManager.commitOperation = async (data: PaymentOperation): Promise<Balance> => {
            expect(data.operation).equal("refund-bet");
            expect(data.bet).equal(100);
            return {
                main: 200,
                currency: "USD",
                previousValue: 100
            };
        };

        flow = await GameFlowFactory.createForInit(flow.flowContext,
            gameData,
            initRequest,
            new LoadedGameResult(new DumyGame(), TEST_MODULE_NAME));
        expect(flow.flowContext.pendingModification).is.undefined;
    }

    @test("Combined payment with two different  jackpot operations")
    public async splitPaymentWithTwoDifferentJpOperations() {
        let commitOperationCallback = (data: WalletOperation) => { /* Void */
        };
        const commitOperationIntrinsic = (data: WalletOperation) => {
            commitOperationCallback(data);
        };

        const originalCommitOperation = this.walletManager.commitOperation;
        this.walletManager.commitOperation = async (data: WalletOperation): Promise<Balance> => {
            const balance = await originalCommitOperation.call(this.walletManager, data);
            commitOperationIntrinsic.call(this.walletManager, data);
            return balance;
        };

        this.createWalletManager.returns(this.walletManager);

        const gameTokenData = createMockedGameTokenData();
        const gameData = createMockedGameDataWithSplitPayment(gameTokenData);
        const initRequest = await createMockedInitRequest(gameTokenData);

        await makePlayerCredit(gameTokenData, 1000);

        const jpnContext = {
            token: "jpn-auth-token",
            jackpotsInfo: [
                {
                    id: "jp-id", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, gameHistoryEnabled: true, paymentStatisticEnabled: true
                },
                {
                    id: "mw-jp-id", jpGameId: "IG jp test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, gameHistoryEnabled: true, paymentStatisticEnabled: true
                }
            ],
            contributionEnabled: true
        };
        const flow = await GameFlowFactory.createForInit(undefined,
            gameData,
            initRequest,
            new LoadedGameResult(new DumyGame(), TEST_MODULE_NAME),
            undefined,
            jpnContext);
        injectRandomGenerator(flow);

        this.contributionTrxId.returns({ transactionId: "JP-TRX" });

        this.contribute.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "jp-id",
                    pools: { small: { amount: 10 }, medium: { amount: 100 }, large: { amount: 1000 } },
                    totalContribution: 5
                },
                {
                    type: "contribution",
                    jackpotId: "mw-jp-id",
                    pools: { single: { amount: 10 } },
                    totalContribution: 10
                },
                {
                    type: "start-mini-game",
                    jackpotId: "mw-jp-id"
                },
                {
                    type: "win",
                    jackpotId: "jp-id",
                    pool: "test_pool",
                    amount: 555
                }
            ],
        });

        this.confirmWin.returns({
            events: [
                {
                    type: "win-confirm",
                    jackpotId: "jp-id",
                    transactionId: "JP-TRX",
                }
            ],
        });

        expect(await flow.getBalance()).deep.equal({
            "amount": 1000,
            "bonus": { "amount": 0 },
            "currency": "USD",
            "real": { "amount": 1000 }
        });

        const payment: PaymentInfo = {
            actions: [
                { action: "debit", attribute: "balance", amount: 100 },
                { action: "credit", attribute: "balance", amount: 10 }
            ],
            gameStatus: "settled"
        };

        const jackpotAction: Contribution = {
            type: "contribution",
            amount: 5
        };

        const history: GameHistory = {
            type: "spin",
            roundEnded: true,
            data: {}
        };

        let callCount = 0;
        commitOperationCallback = (data: PaymentOperation) => {
            callCount++;

            switch (callCount) {
                case 1 : // Bet + WIN
                    expect(data.actions).deep.equals([
                        {
                            action: "debit",
                            amount: 100,
                            attribute: "balance"
                        },
                        {
                            action: "credit",
                            amount: 10,
                            attribute: "balance"
                        }
                    ]);
                    expect(data.win).deep.equals(10);
                    expect(data.bet).deep.equals(100);
                    expect(data.roundEnded).to.equal(true);
                    break;
                case 2: // JP Win
                    expect(data.actions).deep.equals([
                        {
                            action: "credit",
                            amount: 555,
                            attribute: "balance",
                            changeType: "jackpot_win",
                            jackpotId: "jp-id",
                            pool: "test_pool"
                        }
                    ]);
                    expect(data.win).deep.equals(555);
                    expect(data.bet).deep.equals(0);
                    expect(data.roundEnded).to.equal(true);
                    break;
            }
        };

        flow.deferredUpdate({ payment: payment, jackpotAction: jackpotAction, history: history });
        await flow.commitDeferredUpdate();

        expect(callCount).eq(2); // 1 calls to wallet + 1 wallet call to jp

        expect(await flow.getBalance()).deep.equal({
            "amount": 910 + 555,
            "bonus": { "amount": 0 },
            "currency": "USD",
            "real": { "amount": 910 + 555 }
        });

        expect(this.createRoundHistoryEventStub.calledOnce).to.be.true;
        const roundHistory = this.createRoundHistoryEventStub.returnValues[0] as RoundHistory;

        expect(roundHistory).deep.include({
            balanceBefore: 1000,
            balanceAfter: 1465,
            totalBet: 100,
            totalWin: 565,
            totalJpContribution: 15,
            totalJpWin: 555
        });

        expect(this.createGameHistoryEventStub.calledOnce).to.be.true;
        const spinHistory = this.createGameHistoryEventStub.returnValues[0];

        expect(spinHistory).deep.include({
            balanceBefore: 1000,
            balanceAfter: 910,
            bet: 100,
            win: 10,
            totalJpContribution: 15,
            totalJpWin: 0,
            roundEnded: false
        });
        expect(this.createJackpotGameHistoryEventStub.calledOnce).is.true;
        const jpHistory = this.createJackpotGameHistoryEventStub.returnValues[0];
        expect(jpHistory).deep.include({
            balanceBefore: 910,
            balanceAfter: 1465,
            bet: 0,
            win: 555,
            totalJpContribution: 0,
            totalJpWin: 555,
            type: "jackpot-win",
            roundEnded: true
        });
        const jpContext =  flow.jackpotContext;
        expect(jpContext.jackpot.result).is.not.equal(undefined);
    }

}

function createMockedGameTokenData(): GameTokenData {
    const gameTokenData: GameTokenData = {
        playerCode: "PL001",
        gameCode: "test_slot",
        brandId: 1,
        currency: "USD",
        playmode: "real"
    };

    return gameTokenData;
}

function getMockedGameData(gameTokenData: GameTokenData): GameData {
    const gameData: GameData = {
        gameTokenData: gameTokenData,
        playerCode: "PL001",
        gameMode: gameTokenData.playmode,
        gameCode: "test_slot",
        brandId: 1,
        currency: "USD",
        jrsdSettings: { a: 1 },
        limits: {
            coins: [1],
            defaultCoin: 1,
            maxTotalStake: 500,
            stakeAll: [0.1, 0.5, 1, 2, 3, 5],
            stakeDef: 1,
            stakeMax: 10,
            stakeMin: 0.1,
            winMax: 3000000,
            currencyMultiplier: 100
        } as Limits,
        gameId: "test_slot"
    } as GameData;

    return gameData;
}

function createMockedGameDataWithSplitPayment(gameTokenData: GameTokenData): GameData {
    const gameData = getMockedGameData(gameTokenData);
    gameData.settings = gameData.settings || {};
    gameData.settings.splitPayment = false;
    gameData.settings.contributionPrecision = 5;

    return gameData;
}

async function createMockedInitRequest(gameTokenData: GameTokenData): Promise<GameInitRequest & RequestContext> {
    const gameID: GameContextID = GameContextID.create("test_slot", 1, "PL001", "deviceId");
    const startTokenData = {
        playerCode: "PL001",
        gameCode: "test_slot",
        brandId: 1,
        token: undefined
    };

    startTokenData.token = await createGameToken(gameTokenData);
    const startToken = await generateStartGameToken(startTokenData);

    const initRequest: GameInitRequest = {
        request: "init",
        name: "game",
        gameId: gameID.gameCode,
        deviceId: gameID.deviceId,
        startGameToken: startToken
    };

    return initRequest;
}

async function makePlayerCredit(gameTokenData: GameTokenData, amount: number): Promise<Balance> {
    const walletManager = createWalletManager(gameTokenData);
    const trxId = await walletManager.generateTransactionId();
    const balance = await walletManager.commitOperation({
        operation: "payment",
        transactionId: trxId,
        currency: "USD",
        bet: 0,
        win: amount
    } as PaymentOperation);

    return balance;
}
