import { expect, should, use } from "chai";
import { mock, SinonFakeTimers, useFakeTimers } from "sinon";
import {
    createGameToken,
    flushAll,
    getGameHistoryItem,
    getRoundHistory,
    syncModels,
    TEST_MODULE_NAME
} from "../../helper";
import { createWalletManager, PaymentOperation } from "../../../skywind/services/wallet";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import {
    GameContextNotExists,
    GameContextStateIsBroken,
    GameFlowIllegalInvocationException,
    NeedToRestartTheGame
} from "../../../skywind/errors";
import { GameContextPersistencePolicy, GameInitRequest, GamePlayRequest, LogLevel } from "@skywind-group/sw-game-core";
import { GameTokenData, generateStartGameToken, Limits, StartGameTokenData } from "../../../skywind/services/tokens";
import { GameContextID } from "../../../skywind/services/contextIds";
import config from "../../../skywind/config";
import { getCurrencyExchange, init as initCurrencyExchange } from "../../../skywind/services/currencyexchange";
import { DEFAULT_CONVERSION_SERVICE, GameFlowFactory } from "../../../skywind/services/gameFlowFactory";
import { EngineGameFlow } from "../../../skywind/services/gameflow";
import { GameData } from "../../../skywind/services/auth";
import { injectRandomGenerator, setRandomGeneratorFactory } from "../../../skywind/services/random";
import { GameSession } from "../../../skywind/services/gameSession";
import { suite, test } from "mocha-typescript";
import { getGameContextModel } from "../../../skywind/services/offlinestorage/models";
import { DumyGame } from "../../testGames";
import { LoadedGameResult } from "../../../skywind/services/game/game";
import { GameFlowContextImpl } from "../../../skywind/services/context/gameContextImpl";
import { ContextManager } from "../../../skywind/services/contextmanager/contextManager";
import { GameFlowInfoImpl } from "../../../skywind/services/context/gameFlowInfo";
import { RandomGeneratorFactoryImpl } from "../../testRandomFactory";
import * as _ from "lodash";

const chaiAsPromise = require("chai-as-promised");

should();
use(chaiAsPromise);

export class BaseGameFlowSpec {
    public static walletThroughAPIPrevValue: boolean;
    public static clockMock: SinonFakeTimers;
    public contextManager: ContextManager = getGameFlowContextManager();
    public gameTokenData = {
        playerCode: "PL001",
        gameCode: "test_slot",
        brandId: 1,
        currency: "USD",
        playmode: "real"
    } as GameTokenData;
    public gameData: GameData = {
        gameTokenData: this.gameTokenData,
        playerCode: "PL001",
        gameMode: this.gameTokenData.playmode,
        gameCode: "test_slot",
        brandId: 1,
        currency: "USD",
        jrsdSettings: { a: 1 },
        gameSettings: { b: 1 },
        brandSettings: { turbo: true, fastPlay: true, turboPlus: true },
        limits: {
            coins: [1],
            defaultCoin: 1,
            maxTotalStake: 500,
            stakeAll: [0.1, 0.5, 1, 2, 3, 5],
            stakeDef: 1,
            stakeMax: 10,
            stakeMin: 0.1,
            winMax: 3000000,
            currencyMultiplier: 100
        } as Limits,
        gameId: "test_slot"
    } as GameData;
    public gameDataWithJrsd: GameData = {
        gameTokenData: this.gameTokenData,
        gameMode: this.gameTokenData.playmode,
        playerCode: "PL001",
        gameCode: "test_slot",
        brandId: 1,
        currency: "USD",
        limits: {} as Limits,
        jrsdSettings: { a: 1 }
    } as GameData;
    public startToken: string = "";
    public startTokenData: Partial<StartGameTokenData> = {
        playerCode: "PL001",
        gameCode: "test_slot",
        brandId: 1
    };
    public gameID: GameContextID = GameContextID.create("test_slot", 1, "PL001", "deviceId");
    public initRequest: any = {
        request: "init",

        gameId: this.gameID.gameCode,
        deviceId: this.gameID.deviceId,
        startGameToken: this.startToken
    };

    public static async before() {
        this.walletThroughAPIPrevValue = config.walletThroughAPI;
        config.walletThroughAPI = false;
        await syncModels();
        await initCurrencyExchange();
        BaseGameFlowSpec.clockMock = useFakeTimers(0);
        setRandomGeneratorFactory(new RandomGeneratorFactoryImpl());
    }

    public static after() {
        config.walletThroughAPI = this.walletThroughAPIPrevValue;
        BaseGameFlowSpec.clockMock.restore();
    }

    public async before() {
        await getGameContextModel().truncate();
        await flushAll();

        this.gameTokenData.token = await createGameToken(this.gameTokenData);
        this.startToken = await generateStartGameToken(this.startTokenData);

        const walletManager = createWalletManager(this.gameData.gameTokenData);
        const trxId = await walletManager.generateTransactionId();
        await walletManager.commitOperation({
            operation: "payment",
            transactionId: trxId,
            currency: "USD",
            bet: 0,
            win: 1000
        } as PaymentOperation);
    }

    protected async createForInit() {
        return await GameFlowFactory.createForInit(undefined,
            this.gameData,
            this.initRequest,
            new LoadedGameResult(new DumyGame(), TEST_MODULE_NAME));
    }
}

@suite("GameFlow")
class GameFlowSpec extends BaseGameFlowSpec {

    @test("is initialized")
    public async isInitialized() {
        const flow = await this.createForInit();
        injectRandomGenerator(flow);
        let context = await this.contextManager.findGameContextById(this.gameID);
        const session = context.session;
        expect(flow.request()).deep.equal(this.initRequest);
        expect(flow.rng()).to.exist;
        expect(flow.settings()).deep.equal(this.gameData.limits);
        expect(flow.info().toJSON()).deep.equal({
            "brandId": this.gameID.brandId,
            "currency": "USD",
            "deviceId": this.gameID.deviceId,
            "gameId": this.gameID.gameCode,
            "gameCode": this.gameID.gameCode,
            "gameSessionId": session.id,
            "gameToken": this.gameData.gameTokenData.token,
            "playerCode": this.gameID.playerCode,
            "roundId": "0",
            "gameMode": "real",
            "jrsdSettings": { a: 1 },
            gameSettings: { b: 1 },
            brandSettings: { turbo: true, fastPlay: true, turboPlus: true },
            "roundPID": "doRlLRrM",
            "isTestPlayer": undefined
        });
        flow.log(LogLevel.Info, "sessionId: %s", session.id);
        flow.log(LogLevel.Info, { sessionId: session.id });
        flow.log(LogLevel.Info, 1000);
        expect(flow.pushService()).is.undefined;
        const balance = await flow.getBalance();
        expect(balance).deep.equal({
            "amount": 1000,
            "bonus": {
                "amount": 0,
            },
            "currency": "USD",
            "real": {
                "amount": 1000,
            }
        });
        const gameContext = await flow.gameContext();
        expect(gameContext).is.undefined;

        const playRequest: any = { request: "myRequest", requestId: 1, gameSession: session.id };

        const playFlow = await GameFlowFactory.createForRequest(
            playRequest as GamePlayRequest);
        injectRandomGenerator(playFlow);
        expect(playFlow.request()).deep.equal(playRequest);
        expect(playFlow.rng()).to.exist;
        expect(playFlow.settings()).deep.equal(this.gameData.limits);
        expect(playFlow.info().toJSON()).deep.equal({
            "brandId": this.gameID.brandId,
            "currency": "USD",
            "deviceId": this.gameID.deviceId,
            "gameId": this.gameID.gameCode,
            "gameCode": this.gameID.gameCode,
            "gameSessionId": session.id,
            "gameToken": this.gameData.gameTokenData.token,
            "playerCode": this.gameID.playerCode,
            gameSettings: { b: 1 },
            brandSettings: { turbo: true, fastPlay: true, turboPlus: true },
            "roundId": "0",
            "roundPID": "doRlLRrM",
            "gameMode": "real",
            "jrsdSettings": { a: 1 },
            "isTestPlayer": undefined
        });

        context = await this.contextManager.findGameContextById(this.gameID);
        const contextFlow = await GameFlowFactory.createFromContext(context, playRequest, new DumyGame());
        injectRandomGenerator(contextFlow);
        expect(contextFlow.request()).deep.equal(playRequest);
        expect(contextFlow.rng()).to.exist;
        expect(contextFlow.settings()).deep.equal(this.gameData.limits);
        expect(contextFlow.info().toJSON()).deep.equal({
            "brandId": this.gameID.brandId,
            "currency": "USD",
            "deviceId": this.gameID.deviceId,
            "gameId": this.gameID.gameCode,
            "gameCode": this.gameID.gameCode,
            "gameSessionId": context.session.id,
            "gameToken": this.gameData.gameTokenData.token,
            "playerCode": this.gameID.playerCode,
            gameSettings: { b: 1 },
            brandSettings: { turbo: true, fastPlay: true, turboPlus: true },
            "roundId": "0",
            "roundPID": "doRlLRrM",
            "gameMode": "real",
            "jrsdSettings": { a: 1 },
            "isTestPlayer": undefined
        });
    }

    @test("is initialized with jurisdiction settings")
    public async isInitializedWithJurisdiction() {
        const flow = await await this.createForInit();
        injectRandomGenerator(flow);
        let context = await this.contextManager.findGameContextById(this.gameID);
        const sessionId = context.session.id;
        expect(flow.request()).deep.equal(this.initRequest);
        expect(flow.rng()).to.exist;
        expect(flow.info().toJSON()).deep.equal({
            "brandId": this.gameID.brandId,
            "currency": "USD",
            "deviceId": this.gameID.deviceId,
            "gameId": this.gameID.gameCode,
            "gameCode": this.gameID.gameCode,
            "gameSessionId": sessionId,
            "gameToken": this.gameDataWithJrsd.gameTokenData.token,
            "playerCode": this.gameID.playerCode,
            gameSettings: { b: 1 },
            brandSettings: { turbo: true, fastPlay: true, turboPlus: true },
            "roundId": "0",
            "roundPID": "doRlLRrM",
            "gameMode": "real",
            "jrsdSettings": { "a": 1 },
            "isTestPlayer": undefined
        });
        flow.log(LogLevel.Info, "sessionId: %s", sessionId);
        expect(flow.pushService()).is.undefined;
        const balance = await flow.getBalance();
        expect(balance).deep.equal({
            "amount": 1000,
            "bonus": {
                "amount": 0,
            },
            "currency": "USD",
            "real": {
                "amount": 1000,
            }
        });
        const gameContext = await flow.gameContext();
        expect(gameContext).is.undefined;

        const playRequest: any = { request: "myRequest", requestId: 1, gameSession: sessionId };

        const playFlow = await GameFlowFactory.createForRequest(
            playRequest as GamePlayRequest);
        injectRandomGenerator(playFlow);
        expect(playFlow.request()).deep.equal(playRequest);
        expect(playFlow.rng()).to.exist;
        expect(playFlow.info().toJSON()).deep.equal({
            "brandId": this.gameID.brandId,
            "currency": "USD",
            "deviceId": this.gameID.deviceId,
            "gameId": this.gameID.gameCode,
            "gameCode": this.gameID.gameCode,
            "gameSessionId": sessionId,
            "gameToken": this.gameDataWithJrsd.gameTokenData.token,
            "playerCode": this.gameID.playerCode,
            "roundId": "0",
            "roundPID": "doRlLRrM",
            gameSettings: { b: 1 },
            brandSettings: { turbo: true, fastPlay: true, turboPlus: true },
            "gameMode": "real",
            "jrsdSettings": { a: 1 },
            "isTestPlayer": undefined,
        });

        context = await this.contextManager.findGameContextById(this.gameID);
        const contextFlow = await GameFlowFactory.createFromContext(context, playRequest, new DumyGame());
        injectRandomGenerator(contextFlow);
        expect(contextFlow.request()).deep.equal(playRequest);
        expect(contextFlow.rng()).to.exist;
        expect(contextFlow.info().toJSON()).deep.equal({
            "brandId": this.gameID.brandId,
            "currency": "USD",
            "deviceId": this.gameID.deviceId,
            "gameId": this.gameID.gameCode,
            "gameCode": this.gameID.gameCode,
            "gameSessionId": context.session.id,
            "gameToken": this.gameDataWithJrsd.gameTokenData.token,
            "playerCode": this.gameID.playerCode,
            "roundId": "0",
            "roundPID": "doRlLRrM",
            gameSettings: { b: 1 },
            brandSettings: { turbo: true, fastPlay: true, turboPlus: true },
            "gameMode": "real",
            "jrsdSettings": { a: 1 },
            "isTestPlayer": undefined
        });
    }

    /**
     * Unit test for bug SWS-889
     */
    @test("provides gameContext that that is copy")
    public async providesCopyOfGameContext() {
        let flow = await this.createForInit();

        await flow.updateGameContext({ scene: "test" });

        const context = await this.contextManager.findGameContextById(this.gameID);
        flow = await GameFlowFactory.createFromContext(
            context,
            this.initRequest as GameInitRequest,
            new DumyGame(), undefined, false);

        expect(await flow.gameContext()).deep.equal(context.gameContext);
        // check that we provide for game a clone
        expect(await flow.gameContext()).is.not.eq(context.gameContext);

        const ctx = await flow.updateGameContext({ scene: "newScene" });

        expect(await flow.gameContext()).deep.equal(context.gameContext);
        expect(await flow.gameContext()).deep.equal({ scene: "newScene" });
        expect(await flow.gameContext()).is.eq(ctx);
        // check that we provide for game a clone
        expect(await flow.gameContext()).is.not.eq(context.gameContext);

        await flow.update({ scene: "newScene1" }, undefined, undefined, undefined);
        expect(await flow.gameContext()).deep.equal(context.gameContext);
        expect(await flow.gameContext()).deep.equal({ scene: "newScene1" });
        // check that we provide for game a clone
        expect(await flow.gameContext()).is.not.eq(context.gameContext);

    }

    @test("isn't initialized for play request - context not exists")
    public async isNotInitialized_DoesntExist() {
        await this.createForInit();
        const context = await this.contextManager.findGameContextById(this.gameID);
        await context.remove();

        const playRequest: any = { request: "myRequest", requestId: 1, gameSession: context.session.id };

        return GameFlowFactory.createForRequest(playRequest as GamePlayRequest)
            .should
            .eventually
            .rejectedWith(GameContextNotExists);
    }

    @test("isn't initialized for play request - concurrent request")
    public async IsNotInitialized_CouncurrentRequest() {
        await this.createForInit();
        const context = await this.contextManager.findGameContextById(this.gameID);
        await context.remove();

        const playRequest: any = { request: "myRequest", requestId: 2, gameSession: context.session.id };

        return GameFlowFactory.createForRequest(playRequest as GamePlayRequest)
            .should
            .eventually
            .rejectedWith(GameContextNotExists);
    }

    @test("is initialized after offloading")
    public async isNotInitializedAfterOffloading() {
        let flow = await this.createForInit();
        const gameContext = { scene: "scene1" };
        await flow.updateGameContext(gameContext);
        const context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.gameContext).deep.equal(gameContext);

        await this.contextManager.saveGameContextOffline([context]);
        await context.remove();

        try {
            await this.contextManager.findGameContextById(this.gameID);
            expect.fail("Expected exception");
            // tslint:disable-next-line:no-empty
        } catch (err) {
        }

        flow = await GameFlowFactory.createForInit(await this.contextManager.findOrRestoreGameContext(context.id),
            this.gameData,
            this.initRequest,
            { moduleName: TEST_MODULE_NAME, game: undefined });

        const restoredContext = await flow.gameContext();
        expect(restoredContext).deep.equal(gameContext);
    }

    @test("processes broken rounds correctly")
    public async processesBrokenRoundsCorrectly() {
        let flow = await this.createForInit();
        const gameContext = { scene: "scene1" };
        await flow.update(gameContext, null, {
            type: "slot",
            roundEnded: false,
            data: {},
        });
        const context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.gameContext).deep.equal(gameContext);

        context.round.broken = true;
        await this.contextManager.saveGameContextOffline([context]);
        await context.remove();

        try {
            await this.contextManager.findGameContextById(this.gameID);
            expect.fail("Expected exception");
            // tslint:disable-next-line:no-empty
        } catch (err) {
        }

        let rounds = await getRoundHistory(0, 1000);
        // check that round is unfinished but not marked as broken
        expect(rounds.length).eq(1);
        expect(rounds[0].finishedAt).is.undefined;
        expect(rounds[0].broken).is.true;

        flow = await GameFlowFactory.createForInit(await this.contextManager.findOrRestoreGameContext(context.id),
            this.gameData,
            this.initRequest,
            new LoadedGameResult(new DumyGame(), TEST_MODULE_NAME));

        const restoredContext = await flow.gameContext();

        await flow.update(gameContext, null, {
            type: "slot",
            roundEnded: true,
            data: {},
        });

        expect(restoredContext).deep.equal(gameContext);

        rounds = await getRoundHistory(0, 1);
        // check that round is finished and marked as broken
        expect(rounds[0].finishedAt).is.not.undefined;
        expect(rounds[0].broken).is.true;
    }

    @test("prevent updating of context with pending")
    public async preventUpdatingOfPendedContext() {
        const flow = await this.createForInit();
        const gameContext = { scene: "scene1" };
        await flow.update(gameContext, null, {
            type: "slot",
            roundEnded: false,
            data: {},
        });
        let context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.gameContext).deep.equal(gameContext);

        await context.updatePendingModification({} as any, {} as any, {} as any);

        const nextFlow = await GameFlowFactory.createForRequest({
                gameSession: context.session.id,
                request: "jp-ticker",
                requestId: 1
            },
            () => Promise.resolve(undefined),
            false,
            false);

        const restoredContext = await nextFlow.gameContext();
        expect(restoredContext).deep.equal(gameContext);

        await expect(nextFlow.update(gameContext, null, {
            type: "slot",
            roundEnded: true,
            data: {},
        })).is.rejectedWith(NeedToRestartTheGame);

        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.gameContext).deep.equal(gameContext);
    }

    @test("updates gameContext")
    public async updatesContext() {
        const flow = await this.createForInit();

        const ctx = {
            scene: "current",
            nextScene: "next",
        };
        await flow.updateGameContext(ctx);
        const context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.gameContext).deep.equal(ctx);
    }

    @test("makes payment")
    public async makesPayment() {
        const flow = await this.createForInit();

        let balance = await flow.getBalance();
        expect(balance).deep.equal({
            "amount": 1000,
            "bonus": {
                "amount": 0,
            },
            "currency": "USD",
            "real": {
                "amount": 1000,
            }
        });
        const newBalance = await flow.payment({ win: 10, bet: 100 });
        balance = await flow.getBalance();
        expect(balance).deep.equal({
            "amount": 910,
            "bonus": {
                "amount": 0,
            },
            "currency": "USD",
            "real": {
                "amount": 910,
            }
        });
        expect(balance).deep.equal(newBalance);
        const historyItem: any = await getGameHistoryItem();
        expect(historyItem.result).deep.equal({});
    }

    @test("makes payment with conversion rate")
    public async makesPaymentWithConversionRate() {
        const conversion: any = {
            toGameAmount: (gameInfo: GameFlowInfoImpl, settings: any, amount: number): number => {
                expect(gameInfo.toJSON()).deep.equal({
                    "brandId": this.gameID.brandId,
                    "currency": "USD",
                    "deviceId": this.gameID.deviceId,
                    "gameId": this.gameID.gameCode,
                    "gameCode": this.gameID.gameCode,
                    "gameSessionId": gameInfo.gameSessionId,
                    "gameToken": this.gameData.gameTokenData.token,
                    "playerCode": this.gameID.playerCode,
                    "roundId": "0",
                    gameSettings: { b: 1 },
                    brandSettings: { turbo: true, fastPlay: true, turboPlus: true },
                    "roundPID": "doRlLRrM",
                    "gameMode": "real",
                    "jrsdSettings": { a: 1 },
                    "isTestPlayer": undefined
                });

                expect(settings).deep.equal(this.gameData.limits);

                return amount * 10;
            },
            fromGameAmount: (gameInfo: GameFlowInfoImpl, settings: any, amount: number): number => {
                expect(gameInfo.toJSON()).deep.equal({
                    "brandId": this.gameID.brandId,
                    "currency": "USD",
                    "deviceId": this.gameID.deviceId,
                    "gameId": this.gameID.gameCode,
                    "gameCode": this.gameID.gameCode,
                    "gameSessionId": gameInfo.gameSessionId,
                    "gameToken": this.gameData.gameTokenData.token,
                    "playerCode": this.gameID.playerCode,
                    "roundId": "0",
                    gameSettings: { b: 1 },
                    brandSettings: { turbo: true, fastPlay: true, turboPlus: true },
                    "roundPID": "doRlLRrM",
                    "gameMode": "real",
                    "jrsdSettings": { a: 1 },
                    "isTestPlayer": undefined
                });

                expect(settings).deep.equal(this.gameData.limits);
                return amount / 10;
            }
        };

        const flow = await GameFlowFactory.createForInit(undefined,
            this.gameData,
            this.initRequest,
            new LoadedGameResult(conversion, TEST_MODULE_NAME));

        let balance = await flow.getBalance();
        expect(balance).deep.equal({
            "amount": 10000,
            "bonus": {
                "amount": 0,
            },
            "currency": "USD",
            "real": {
                "amount": 10000,
            }
        });
        const newBalance = await flow.payment({ win: 10, bet: 100 });
        balance = await flow.getBalance();
        expect(balance).deep.equal({
            "amount": 9910,
            "bonus": {
                "amount": 0,
            },
            "currency": "USD",
            "real": {
                "amount": 9910,
            }
        });
        expect(balance).deep.equal(newBalance);

        const historyItem: any = await getGameHistoryItem();
        expect(historyItem.bet).equal(10);
        expect(historyItem.win).equal(1);
        expect(historyItem.roundEnded).equal(false);

        const walletBalance = await createWalletManager(this.gameData.gameTokenData).getBalance();
        expect(walletBalance).deep.equal({
            "currency": "USD",
            "main": 991,
        });
    }

    @test("stores history")
    public async storesHistory() {
        const flow = await this.createForInit();

        const history1 = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 3, 4, 5] } };
        const history2 = { type: "slot", version: 1, roundEnded: false, data: { positions: [2, 3, 4, 5] } };
        const history3 = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 2, 3, 5] } };
        await flow.storeHistory(history1);
        expect(flow.info().roundId).equal("1");
        await flow.storeHistory(history2);
        expect(flow.info().roundId).equal("1");
        await flow.storeHistory(history3);
        expect(flow.info().roundId).equal("2");

        let historyItem: any = await getGameHistoryItem();
        expect(historyItem.result).deep.equal(history3.data);
        expect(historyItem.win).is.eq(0);
        expect(historyItem.bet).is.eq(0);
        expect(historyItem.roundEnded).equal(true);
        historyItem = await getGameHistoryItem();
        expect(historyItem.result).deep.equal(history2.data);
        expect(historyItem.win).is.eq(0);
        expect(historyItem.bet).is.eq(0);
        expect(historyItem.roundEnded).equal(false);
        historyItem = await getGameHistoryItem();
        expect(historyItem.result).deep.equal(history1.data);
        expect(historyItem.win).is.eq(0);
        expect(historyItem.bet).is.eq(0);
        expect(historyItem.roundEnded).equal(true);

        const walletBalance = await createWalletManager(this.gameData.gameTokenData).getBalance();
        expect(walletBalance).deep.equal({
            "currency": "USD",
            "main": 1000,
        });
    }

    @test("stores history - no game history data")
    public async storesHistory_NoGameHistoryData() {
        const flow = await this.createForInit();

        await expect(flow.storeHistory({ type: "slot", roundEnded: true, data: undefined }))
            .to
            .be
            .rejectedWith(GameFlowIllegalInvocationException);
    }

    @test("update context, history and payment")
    public async updateContextHistoryPayment() {
        const flow = await this.createForInit();

        const history1 = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 3, 4, 5] } };
        const paymentInfo1 = { bet: 10, win: 22 };
        const gameContext1 = {
            scene: "scene1",
        };

        const history2 = { type: "slot", version: 1, roundEnded: false, data: { positions: [2, 3, 4, 5] } };
        const paymentInfo2 = { bet: 1, win: 2 };
        const gameContext2 = {
            scene: "scene2",
        };
        const history3 = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 2, 3, 5] } };
        const paymentInfo3 = { bet: 100, win: 1 };
        const gameContext3 = {
            scene: "scene1",
        };

        await flow.update(gameContext1, paymentInfo1, history1);
        expect(flow.info().roundId).equal("1");
        expect((await flow.gameContext())).deep.equal(gameContext1);
        let context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.gameContext).deep.equal(gameContext1);

        await flow.update(gameContext2, paymentInfo2, history2);
        expect(flow.info().roundId).equal("1");
        expect((await flow.gameContext())).deep.equal(gameContext2);
        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.gameContext).deep.equal(gameContext2);

        await flow.update(gameContext3, paymentInfo3, history3);
        expect(flow.info().roundId).equal("2");
        expect((await flow.gameContext())).deep.equal(gameContext3);
        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.gameContext).deep.equal(gameContext3);

        let historyItem: any = await getGameHistoryItem();
        expect(historyItem.result).deep.equal(history3.data);
        expect(historyItem.win).is.eq(paymentInfo3.win);
        expect(historyItem.bet).is.eq(paymentInfo3.bet);
        expect(historyItem.roundEnded).equal(true);

        historyItem = await getGameHistoryItem();
        expect(historyItem.result).deep.equal(history2.data);
        expect(historyItem.win).is.eq(paymentInfo2.win);
        expect(historyItem.bet).is.eq(paymentInfo2.bet);
        expect(historyItem.roundEnded).equal(false);

        historyItem = await getGameHistoryItem();
        expect(historyItem.result).deep.equal(history1.data);
        expect(historyItem.win).is.eq(paymentInfo1.win);
        expect(historyItem.bet).is.eq(paymentInfo1.bet);
        expect(historyItem.roundEnded).equal(true);

        const walletBalance = await createWalletManager(this.gameData.gameTokenData).getBalance();
        expect(walletBalance).deep.equal({
            "currency": "USD",
            "main": 914,
        });
    }

    @test("exhanges currency")
    public async exchangesCurrency() {
        const flow = await this.createForInit();
        let amount = flow.exchange(100, "USD");
        expect(amount).to.equal(100);
        amount = flow.exchange(100, "EUR");
        expect(amount).to.equal(87.22);
    }

    @test("returns extra balances")
    public async returnsExtraBalances() {
        const walletManager = {
            getBalance: mock()
        };

        const mockObj: any = {};
        const flow = new EngineGameFlow(
            {} as any, mockObj, mockObj,
            {
                id: GameContextID.create("gameId", 1, "playerCode", "deviceId"),
                session: GameSession.create("sessionId"),
                gameData: { gameTokenData: { currency: "USD" } } as any,
                settings: {}
            } as any,
            DEFAULT_CONVERSION_SERVICE, walletManager as any);

        walletManager.getBalance.once().returns(Promise.resolve({
            currency: "USD",
            main: 1000,
            extraBalances: {
                xp: 2000,
                vip: 4
            }
        }));
        const balance = await flow.getBalance();
        expect(balance).deep.equal({
            "amount": 1000,
            "bonus": {
                "amount": 0,
            },
            "currency": "USD",
            "extraBalances": {
                "vip": {
                    "amount": 4
                },
                "xp": {
                    "amount": 2000
                }
            },
            "real": {
                "amount": 1000,
            }
        });
    }

    @test("updates persistence policy")
    public async updatePersistencePolicy() {
        const flow = await this.createForInit();

        expect(flow.persistencePolicy()).eq(GameContextPersistencePolicy.NORMAL);
        await flow.updateGameContext({ scene: "test" });
        let context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.persistencePolicy).equal(0);

        flow.setPersistencePolicy(GameContextPersistencePolicy.LONG_TERM);
        await flow.updateGameContext({ scene: "test" });
        expect(flow.persistencePolicy()).eq(GameContextPersistencePolicy.LONG_TERM);

        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.persistencePolicy).eq(GameContextPersistencePolicy.LONG_TERM);
    }

    @test("fails to create from context if it was corrupted")
    public async failsToCreateCorruptedContext() {
        const flow = await this.createForInit();
        (flow.flowContext as GameFlowContextImpl).corrupt();
        await expect(GameFlowFactory.createFromContext(flow.flowContext, undefined, new DumyGame()))
            .to.be.rejectedWith(GameContextStateIsBroken);
    }

    @test("exchanges money")
    public async exchanges() {
        const flow = await this.createForInit();

        let result = flow.exchange(100, "EUR");
        expect(result).equals(getCurrencyExchange().exchange(100, "USD", "EUR"));

        result = flow.exchange(100, "EUR", "GBP");
        expect(result).equals(getCurrencyExchange().exchange(100, "GBP", "EUR"));
    }

    @test("attempt to win without bet")
    public async winWithoutBet() {
        const gameData = _.cloneDeep(this.gameData);
        gameData.settings = { zeroBetCheckEnabled: true };
        const flow = await GameFlowFactory.createForInit(undefined,
            gameData,
            this.initRequest,
            new LoadedGameResult(new DumyGame(), TEST_MODULE_NAME));

        flow.update({}, { win: 10}, undefined).should
            .eventually
            .rejectedWith(GameFlowIllegalInvocationException);
    }

    @test("win without bet, but totalBet in round")
    public async winWithoutButBetInRoundBet() {
        const gameData = _.cloneDeep(this.gameData);
        gameData.settings = { zeroBetCheckEnabled: true };
        const flow: any = await GameFlowFactory.createForInit(undefined,
            gameData,
            this.initRequest,
            new LoadedGameResult(new DumyGame(), TEST_MODULE_NAME));
        flow.engineGameFlowContext.round = { totalBet: 1 };
        await flow.update({}, { win: 10}, undefined);
    }

    @test("win with bet validation enabled")
    public async winWithBet() {
        const gameData = _.cloneDeep(this.gameData);
        gameData.settings = { zeroBetCheckEnabled: true };
        const flow = await GameFlowFactory.createForInit(undefined,
            gameData,
            this.initRequest,
            new LoadedGameResult(new DumyGame(), TEST_MODULE_NAME));

        await flow.update({}, { win: 10, bet: 0.5}, undefined);
    }
}
