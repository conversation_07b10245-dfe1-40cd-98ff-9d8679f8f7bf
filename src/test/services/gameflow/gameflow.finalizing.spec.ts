import { suite, test } from "mocha-typescript";
import config from "../../../skywind/config";
import { createGameToken, flushAll, syncModels, TEST_MODULE_NAME } from "../../helper";
import { init as initCurrencyExchange } from "../../../skywind/services/currencyexchange";
import { BaseGameFlowSpec } from "./gameflow.spec";
import { ContextManager } from "../../../skywind/services/contextmanager/contextManager";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { GameTokenData, generateStartGameToken, Limits } from "../../../skywind/services/tokens";
import { GameData } from "../../../skywind/services/auth";
import { GameContextID } from "../../../skywind/services/contextIds";
import { GameFlowFactory } from "../../../skywind/services/gameFlowFactory";
import { LoadedGameResult } from "../../../skywind/services/game/game";
import { DumyGame } from "../../testGames";
import { getGameContextModel } from "../../../skywind/services/offlinestorage/models";
import { testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import { GameFinalizedError } from "../../../skywind/errors";
import { expect } from "chai";
import { GameFlowContext, SpecialState } from "../../../skywind/services/context/gamecontext";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;
import status200 = testing.status200;

@suite("GameFlow.check FINALIZING status")
export class GameFlowFinalizingSpec extends BaseGameFlowSpec {
    private static request: RequestMock;
    public contextManager: ContextManager = getGameFlowContextManager();
    public gameTokenData = {
        playerCode: "PL001",
        gameCode: "test_slot",
        brandId: 1,
        currency: "USD",
        playmode: "real"
    } as GameTokenData;
    public gameData: GameData = {
        gameTokenData: this.gameTokenData,
        playerCode: "PL001",
        gameMode: this.gameTokenData.playmode,
        gameCode: "test_slot",
        brandId: 1,
        currency: "USD",
        jrsdSettings: { a: 1 },
        limits: {
            coins: [1],
            defaultCoin: 1,
            maxTotalStake: 500,
            stakeAll: [0.1, 0.5, 1, 2, 3, 5],
            stakeDef: 1,
            stakeMax: 10,
            stakeMin: 0.1,
            winMax: 3000000,
            currencyMultiplier: 100
        } as Limits,
        gameId: "test_slot"
    } as GameData;
    public startToken: string;
    public startTokenData = {
        playerCode: "PL001",
        gameCode: "test_slot",
        brandId: 1
    };
    public gameID: GameContextID = GameContextID.create("test_slot", 1, "PL001", "deviceId");
    public initRequest: any = {
        request: "init",

        gameId: this.gameID.gameCode,
        deviceId: this.gameID.deviceId,
        startGameToken: this.startToken
    };

    public static async before() {
        this.walletThroughAPIPrevValue = config.walletThroughAPI;
        config.walletThroughAPI = true;
        await syncModels();
        await initCurrencyExchange();
        GameFlowFinalizingSpec.request = requestMock(superagent);
    }

    public static after() {
        config.walletThroughAPI = this.walletThroughAPIPrevValue;
        GameFlowFinalizingSpec.request.unmock(superagent);
    }

    public async before() {
        await getGameContextModel().truncate();
        await flushAll();
        GameFlowFinalizingSpec.request.clearRoutes();
        this.gameTokenData.token = await createGameToken(this.gameTokenData);
        this.startToken = await generateStartGameToken(this.startTokenData);
    }

    @test("catch GameFinalizedError")
    public async makesPayment() {
        const flow = await this.createForInit();
        GameFlowFinalizingSpec.request.post("http://api:3006//v2/play/payment/transactionId", status200({
            "transactionId": "1"
        }));

        GameFlowFinalizingSpec.request.put("http://api:3006//v2/play/payment", testing.status(501, {
            "code": 111
        }));

        flow.flowContext.setSpecialState(SpecialState.FINALIZING);
        await expect(this.createForInit(flow.flowContext)).to.be.rejectedWith(GameFinalizedError);

        await expect(GameFlowFactory.createForRequest(
            { request: "play", gameSession: flow.flowContext.session.id, requestId: 1 }
        )).to.be.rejectedWith(GameFinalizedError);

        const context = await getGameFlowContextManager().findGameContextById(flow.flowContext.id);
        expect(context.specialState).equal(SpecialState.FINALIZING);
    }

    protected async createForInit(ctx?: GameFlowContext) {
        return await GameFlowFactory.createForInit(ctx,
            this.gameData,
            this.initRequest,
            new LoadedGameResult(new DumyGame(), TEST_MODULE_NAME));
    }
}
