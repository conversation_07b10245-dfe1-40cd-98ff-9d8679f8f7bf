import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { GameFlowFactory } from "../../../skywind/services/gameFlowFactory";
import { DumyGame } from "../../testGames";
import { GameContextNotExists } from "../../../skywind/errors";
import { TEST_MODULE_NAME } from "../../helper";
import { BaseGameFlowSpec } from "./gameflow.spec";
import { LoadedGameResult } from "../../../skywind/services/game/game";
import { GameFlowContext } from "../../../skywind/services/context/gamecontext";

@suite("GameFlow.isNewSession")
class GameFlowSpec extends BaseGameFlowSpec {

    @test("forbids to create context if isNewSession:false and not context in Redis/PG")
    public async failedWhenNotNewSessionAndNotContext() {
        await expect(this.init(false)).to.be.rejectedWith(GameContextNotExists);

        const context = await this.contextManager.findGameContextById(this.gameID);
        expect(context).is.undefined;
    }

    @test("allows to initialize context if isNewSession:true or context in Redis")
    public async isInitializedWhenSessionNotExists() {
        const flow = await this.init(true);

        let context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.id).deep.equal(this.gameID);

        await this.init(true, flow.flowContext);
        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.id).deep.equal(this.gameID);
    }

    @test("allows to initialize context if isNewSession:false and context in PG")
    public async isInitializedAfterOffloading() {
        const flow = await this.init(true);
        const gameContext = { scene: "scene1" };
        await flow.updateGameContext(gameContext);
        let context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.gameContext).deep.equal(gameContext);

        await this.contextManager.saveGameContextOffline([context]);
        await context.remove();

        try {
            await this.contextManager.findGameContextById(this.gameID);
            expect.fail("Expected exception");
            // tslint:disable-next-line:no-empty
        } catch (err) {
        }

        await this.init(false, await this.contextManager.findOrRestoreGameContext(flow.flowContext.id));
        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context.id).deep.equal(this.gameID);
    }

    private async init(isNewSession: boolean, ctx?: GameFlowContext) {
        return await GameFlowFactory.createForInit(ctx,
            this.gameData,
            this.initRequest,
            new LoadedGameResult(new DumyGame(), TEST_MODULE_NAME), undefined, undefined, undefined, isNewSession);
    }
}
