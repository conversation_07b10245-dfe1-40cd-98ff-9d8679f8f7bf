import { expect, should, use } from "chai";
import { mock } from "sinon";
import { createWalletOperationFactory, WalletOperationFactory } from "../../skywind/services/walletOperationFactory";
import { GameHistory } from "@skywind-group/sw-game-core";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

describe("WalletOperationFactory", () => {

    it("creates PaymentOperation by PaymentInfo", async () => {
        const walletManager = {
            generateTransactionId: mock()
        };

        const flow = {
            fromGameAmount: mock(),
            info: mock(),
            flowContext: {
                gameSerialNumber: 0,
                session: {
                    sessionId: "1"
                }
            },
            isSplitPayment: () => false,
            walletManager
        };

        flow.info.once().returns({
            roundId: 777,
            roundPID: "roundPID",
            currency: "USD",
            deviceId: "web"
        });
        flow.fromGameAmount.twice().onFirstCall().returns(10000).onSecondCall().returns(2000);
        walletManager.generateTransactionId.once().returns(Promise.resolve("999"));
        const factory = createWalletOperationFactory(flow as any);
        const result = await factory.createPaymentOperation({
            bet: 1000,
            win: 200,
            scene: "currentScene"
        }, { roundEnded: false } as GameHistory);

        expect(result).deep.include({
            "actions": [
                {
                    "action": "debit",
                    "amount": 10000,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 2000,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ],
            "bet": 10000,
            "currency": "USD",
            "deviceId": "web",
            "operation": "payment",
            "roundEnded": false,
            "roundId": 777,
            "roundPID": "roundPID",
            "gameSessionId": "1",
            "scene": "currentScene",
            "transactionId": "999",
            "win": 2000,
        });
        flow.info.verify();
        walletManager.generateTransactionId.verify();
        flow.fromGameAmount.verify();
    });

    it("creates PaymentOperation by actions", async () => {
        const walletManager = {
            generateTransactionId: mock()
        };
        const flow = {
            fromGameAmount: mock(),
            info: mock(),
            flowContext: {
                gameSerialNumber: 0,
                session: {
                    sessionId: "1"
                }
            },
            isSplitPayment: () => false,
            walletManager
        };

        flow.info.once().returns({
            roundId: 777,
            roundPID: "roundPID",
            currency: "USD",
            deviceId: "web"
        });
        flow.fromGameAmount.twice().onFirstCall().returns(10000).onSecondCall().returns(3000);
        walletManager.generateTransactionId.once().returns(Promise.resolve("999"));
        const factory = createWalletOperationFactory(flow as any);
        const result = await factory.createPaymentOperation({
            actions: [
                { action: "debit", attribute: "balance", amount: 1000 },
                { action: "credit", attribute: "balance", amount: 300 },
                { action: "credit", attribute: "xp", amount: 1 },
                { action: "credit", attribute: "vip", amount: 1 },
            ]
        }, { roundEnded: false } as GameHistory);

        expect(result).deep.include({
            "actions": [
                {
                    "action": "debit",
                    "amount": 10000,
                    "attribute": "balance",
                },
                {
                    "action": "credit",
                    "amount": 3000,
                    "attribute": "balance",
                },
                {
                    "action": "credit",
                    "amount": 1,
                    "attribute": "xp",
                },
                {
                    "action": "credit",
                    "amount": 1,
                    "attribute": "vip",
                }
            ],
            "bet": 10000,
            "currency": "USD",
            "deviceId": "web",
            "operation": "payment",
            "roundEnded": false,
            "roundId": 777,
            "roundPID": "roundPID",
            "gameSessionId": "1",
            "transactionId": "999",
            "win": 3000,
        });
        flow.info.verify();
        walletManager.generateTransactionId.verify();
        flow.fromGameAmount.verify();
    });

    it("creates PaymentOperation on disconnect", async () => {
        const walletManager = {
            generateTransactionId: mock()
        };
        const flow = {
            fromGameAmount: mock(),
            info: mock(),
            flowContext: {
                gameSerialNumber: 0,
                session: {
                    sessionId: "1"
                }
            },
            isSplitPayment: () => false,
            walletManager
        };

        flow.info.once().returns({
            roundId: 777,
            roundPID: "roundPID",
            currency: "USD",
            deviceId: "web"
        });
        flow.fromGameAmount.twice().onFirstCall().returns(10000).onSecondCall().returns(3000);
        walletManager.generateTransactionId.once().returns(Promise.resolve("999"));
        const factory = createWalletOperationFactory(flow as any);
        const result = await factory.createPaymentOperation({
            actions: [
                { action: "debit", attribute: "balance", amount: 1000 },
                { action: "credit", attribute: "balance", amount: 300 },
            ]
        }, { roundEnded: false } as GameHistory);

        expect(result).deep.include({
            "actions": [
                {
                    "action": "debit",
                    "amount": 10000,
                    "attribute": "balance",
                },
                {
                    "action": "credit",
                    "amount": 3000,
                    "attribute": "balance",
                },
            ],
            "bet": 10000,
            "currency": "USD",
            "deviceId": "web",
            "operation": "payment",
            "roundEnded": false,
            "roundId": 777,
            "gameSessionId": "1",
            "roundPID": "roundPID",
            "transactionId": "999",
            "win": 3000,
        });
        flow.info.verify();
        walletManager.generateTransactionId.verify();
        flow.fromGameAmount.verify();
    });

    it("creates transfer operation", async () => {
        const walletManager = {
            generateTransactionId: mock()
        };
        const flow = {
            fromGameAmount: mock(),
            info: mock(),
            flowContext: {
                gameSerialNumber: 0,
                session: {
                    sessionId: "1"
                }
            },
            isSplitPayment: () => false,
            walletManager
        };

        flow.info.once().returns({
            roundId: 777,
            roundPID: "roundPID",
            currency: "USD",
            deviceId: "web"
        });
        flow.fromGameAmount.once().returns(10000);
        walletManager.generateTransactionId.once().returns(Promise.resolve("999"));
        const factory = createWalletOperationFactory(flow as any);
        const result = await factory.createTransferOperation({
            operation: "transfer-in",
            amount: 1000,
        }, false);
        expect(result).deep.include({
            "amount": 10000,
            "currency": "USD",
            "operation": "transfer-in",
            "roundId": 777,
            "roundPID": "roundPID",
            "gameSessionId": "1",
            "deviceId": "web",
            "transactionId": "999",
            "roundEnded": false
        });
        flow.info.verify();
        walletManager.generateTransactionId.verify();
        flow.fromGameAmount.verify();
    });

    it("creates transfer out operation", async () => {
        const walletManager = {
            generateTransactionId: mock()
        };
        const flow = {
            fromGameAmount: mock(),
            info: () => {
                return {
                    roundId: 777,
                    roundPID: "roundPID",
                    currency: "USD",
                    deviceId: "web"
                };
            },
            flowContext: {
                gameSerialNumber: 0,
                session: {
                    sessionId: "1"
                },
                settings: {}
            },
            isSplitPayment: () => false,
            walletManager
        };

        flow.fromGameAmount.once().returns(10000);
        walletManager.generateTransactionId.once().returns(Promise.resolve("999"));
        const factory = createWalletOperationFactory(flow as any);
        const result = await factory.createTransferOperation({
            operation: "transfer-out",
            amount: 1000,
        }, true);
        expect(result).deep.include({
            totalJpWin: 0,
            totalJpContribution: 0,
            operation: "transfer-out",
            roundEnded: true
        });
        expect(result.jackpotDetails).to.not.equal(undefined);
    });

    it("creates jackpot payment operation", async () => {
        const walletManager = {
            generateTransactionId: mock()
        };
        const flow = {
            info: mock(),
            isSplitPayment: () => false,
            walletManager,
            jackpotContext: {
                token: "some_token",
                jackpotId: ["test"],
                jackpotsInfo: [
                    {
                        id: "test", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                        isGameOwned: true, gameHistoryEnabled: true, paymentStatisticEnabled: true
                    }
                ],
                contributionEnabled: true
            },
            flowContext: {
                gameSerialNumber: 0,
                session: {
                    sessionId: "1"
                }
            },
        };

        flow.info.once().returns({
            roundId: 777,
            roundPID: "roundPID",
            currency: "USD",
            deviceId: "web"
        });

        walletManager.generateTransactionId.once().returns(Promise.resolve("999"));
        const factory = createWalletOperationFactory(flow as any);
        const result = await factory.createJackpotWinPaymentOperation({
            transactionId: "888",
            result: [
                {
                    event: "win",
                    jackpotId: "test",
                    amount: 4000,
                    pool: "pool1"
                }
            ]
        }, flow.jackpotContext);
        expect(result).deep.include({
            "actions": [
                {
                    "action": "credit",
                    "amount": 4000,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "test",
                    "pool": "pool1"
                }
            ],
            "currency": "USD",
            "deviceId": "web",
            "extTransactionId": "888",
            "operation": "payment",
            "roundEnded": true,
            "roundId": 777,
            "roundPID": "roundPID",
            "gameSessionId": "1",
            "transactionId": "999",
            "win": 4000
        });
        flow.info.verify();
        walletManager.generateTransactionId.verify();
    });

    it("creates jackpot payment operation with change type", async () => {
        const walletManager = {
            generateTransactionId: mock()
        };
        const flow = {
            info: mock(),
            isSplitPayment: () => false,
            walletManager,
            jackpotContext: {
                token: "some_token",
                jackpotId: ["test"],
                jackpotsInfo: [
                    {
                        id: "test", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                        isGameOwned: false, gameHistoryEnabled: false, paymentStatisticEnabled: false,
                        winPaymentType: "global_jackpot_win"
                    }
                ],
                contributionEnabled: true
            },
            flowContext: {
                gameSerialNumber: 0,
                session: {
                    sessionId: "1"
                }
            },
        };

        flow.info.once().returns({
            roundId: 777,
            roundPID: "roundPID",
            currency: "USD",
            deviceId: "web"
        });

        walletManager.generateTransactionId.once().returns(Promise.resolve("999"));
        const factory = createWalletOperationFactory(flow as any);
        const result = await factory.createJackpotWinPaymentOperation({
            transactionId: "888",
            result: [
                {
                    event: "win",
                    jackpotId: "test",
                    amount: 4000,
                    pool: "pool1"
                }
            ]
        }, flow.jackpotContext);
        expect(result).deep.include({
            "actions": [
                {
                    "action": "credit",
                    "amount": 4000,
                    "attribute": "balance",
                    "changeType": "global_jackpot_win",
                    "jackpotId": "test",
                    "pool": "pool1"
                }
            ],
            "currency": "USD",
            "deviceId": "web",
            "extTransactionId": "888",
            "operation": "payment",
            "roundEnded": true,
            "roundId": 777,
            "gameSessionId": "1",
            "roundPID": "roundPID",
            "transactionId": "999",
            "win": 4000
        });
        flow.info.verify();
        walletManager.generateTransactionId.verify();
    });

    it("creates multiple jackpot payment operation", async () => {
        const walletManager = {
            generateTransactionId: mock()
        };
        const flow = {
            info: mock(),
            isSplitPayment: () => false,
            walletManager,
            jackpotContext: {
                token: "some_token",
                jackpotId: ["test"],
                jackpotsInfo: [
                    {
                        id: "test", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                        isGameOwned: true, gameHistoryEnabled: true, paymentStatisticEnabled: true
                    }
                ],
                contributionEnabled: true
            },
            flowContext: {
                gameSerialNumber: 0,
                session: {
                    sessionId: "1"
                }
            },
        };

        flow.info.once().returns({
            roundId: 777,
            roundPID: "roundPID",
            currency: "USD",
            deviceId: "web"
        });

        walletManager.generateTransactionId.once().returns(Promise.resolve("999"));
        const factory = createWalletOperationFactory(flow as any);
        const result = await factory.createJackpotWinPaymentOperation({
            transactionId: "888",
            result: [
                {
                    event: "win",
                    jackpotId: "test",
                    amount: 4000,
                    pool: "pool1"
                }, {
                    event: "win",
                    jackpotId: "test",
                    amount: 8000,
                    pool: "pool2"
                }
            ]
        }, flow.jackpotContext);
        expect(result).deep.include({
            "actions": [
                {
                    "action": "credit",
                    "amount": 4000,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "test",
                    "pool": "pool1"
                },
                {
                    "action": "credit",
                    "amount": 8000,
                    "attribute": "balance",
                    "changeType": "jackpot_win",
                    "jackpotId": "test",
                    "pool": "pool2"
                }
            ],
            "currency": "USD",
            "deviceId": "web",
            "extTransactionId": "888",
            "operation": "payment",
            "roundEnded": true,
            "roundId": 777,
            "roundPID": "roundPID",
            "transactionId": "999",
            "gameSessionId": "1",
            "win": 12000
        });
        flow.info.verify();
        walletManager.generateTransactionId.verify();
    });

    it("creates redeem-bns operation", async () => {
        const walletManager = {
            generateTransactionId: mock()
        };
        const flow = {
            info: mock(),
            flowContext: {
                gameData: {
                    gameTokenData: {
                        playmode: "bns",
                        currency: "USD",
                    }
                },
                gameSerialNumber: 0,
                session: {
                    sessionId: "1"
                }
            },
            isSplitPayment: () => false,
            walletManager
        };

        flow.info.once().returns({
            roundId: 777,
            roundPID: "roundPID",
            currency: "USD",
            deviceId: "web"
        });
        walletManager.generateTransactionId.once().returns(Promise.resolve("999"));
        const factory = createWalletOperationFactory(flow as any);
        const result = await factory.createRedeemBNSOperation({
            amount: 10000,
            rewardedAmount: 100,
            redeemMinAmount: 5000,
            redeemBalance: 200,
            redeemCurrency: "USD",
            expireAt: "12345",
            expireCountdown: 1,
            promoId: "1"
        });
        expect(result).deep.include({
            "amount": 200,
            "currency": "USD",
            "deviceId": "web",
            "operation": "redeem-bns",
            "promoId": "1",
            "roundId": 777,
            "roundPID": "roundPID",
            "gameSessionId": "1",
            "transactionId": "999",
        });
        flow.info.verify();
        walletManager.generateTransactionId.verify();
    });

    it("creates split PaymentOperation by actions", async () => {
        const walletManager = {
            generateTransactionId: mock()
        };
        const flow = {
            fromGameAmount: mock(),
            info: mock(),
            flowContext: {
                gameSerialNumber: 0,
                session: {
                    sessionId: "1"
                }
            },
            isSplitPayment: () => true,
            walletManager
        };

        flow.info.once().returns({
            roundId: 777,
            roundPID: "round_pid",
            currency: "USD",
            deviceId: "web"
        });
        flow.fromGameAmount.twice().onFirstCall().returns(10000).onSecondCall().returns(3000);
        walletManager.generateTransactionId.once().onFirstCall().returns(Promise.resolve("debit_trx"));
        const factory = createWalletOperationFactory(flow as any);
        const result = await factory.createPaymentOperation({
            actions: [
                { action: "debit", attribute: "balance", amount: 1000 },
                { action: "credit", attribute: "balance", amount: 300 },
                { action: "credit", attribute: "xp", amount: 1 },
                { action: "credit", attribute: "vip", amount: 1 },
            ]
        }, { roundEnded: false } as GameHistory);

        expect(result).deep.include({
            "actions": undefined,
            "bet": 10000,
            "creditActions": [
                {
                    "action": "credit",
                    "amount": 3000,
                    "attribute": "balance"
                },
                {
                    "action": "credit",
                    "amount": 1,
                    "attribute": "xp",
                },
                {
                    "action": "credit",
                    "amount": 1,
                    "attribute": "vip",
                }
            ],
            "currency": "USD",
            "debitActions": [
                {
                    "action": "debit",
                    "amount": 10000,
                    "attribute": "balance"
                }
            ],
            "deviceId": "web",
            "operation": "split-payment",
            "roundEnded": false,
            "roundId": 777,
            "gameSessionId": "1",
            "roundPID": "round_pid",
            "transactionId": "debit_trx",
            "win": 3000
        });
        flow.info.verify();
        walletManager.generateTransactionId.verify();
        flow.fromGameAmount.verify();
    });

    it("creates PaymentOperation by legacy PaymentInfo and normalizes values - combined payment", async () => {
        const walletManager = {
            generateTransactionId: mock()
        };

        const flow = {
            fromGameAmount: v => v,
            info: mock(),
            flowContext: {
                gameSerialNumber: 0,
                session: {
                    sessionId: "1"
                }
            },
            isSplitPayment: () => false,
            walletManager
        };

        flow.info.once().returns({
            roundId: 777,
            roundPID: "roundPID",
            currency: "USD",
            deviceId: "web"
        });
        walletManager.generateTransactionId.once().returns(Promise.resolve("999"));
        const factory = createWalletOperationFactory(flow as any);
        const result = await factory.createPaymentOperation({
            bet: 999.99999991,
            win: 1999.999999999991,
            scene: "currentScene"
        }, { roundEnded: false } as GameHistory);

        expect(result).deep.include({
            "actions": [
                {
                    "action": "debit",
                    "amount": 1000,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 2000,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ],
            "bet": 1000,
            "currency": "USD",
            "deviceId": "web",
            "operation": "payment",
            "roundEnded": false,
            "roundId": 777,
            "roundPID": "roundPID",
            "gameSessionId": "1",
            "scene": "currentScene",
            "transactionId": "999",
            "win": 2000,
        });
        flow.info.verify();
        walletManager.generateTransactionId.verify();
    });

    it("creates PaymentOperation by legacy PaymentInfo and normalizes values - split payment", async () => {
        const walletManager = {
            generateTransactionId: mock()
        };

        const flow = {
            fromGameAmount: v => v,
            info: mock(),
            flowContext: {
                gameSerialNumber: 0,
                session: {
                    sessionId: "1"
                }
            },
            isSplitPayment: () => true,
            walletManager
        };

        flow.info.once().returns({
            roundId: 777,
            roundPID: "roundPID",
            currency: "USD",
            deviceId: "web"
        });
        walletManager.generateTransactionId.once().returns(Promise.resolve("999"));
        const factory = createWalletOperationFactory(flow as any);
        const result = await factory.createPaymentOperation({
            bet: 999.99999991,
            win: 1999.999999999991,
            scene: "currentScene"
        }, { roundEnded: false } as GameHistory);

        expect(result).deep.include({
            "actions": undefined,
            "bet": 1000,
            "creditActions": [
                {
                    "action": "credit",
                    "amount": 2000,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ],
            "debitActions": [
                {
                    "action": "debit",
                    "amount": 1000,
                    "attribute": "balance",
                    "changeType": "bet"
                }
            ],
            "operation": "split-payment",
            "win": 2000
        });
    });

    it("creates PaymentOperation by PaymentInfo and normalizes values - combined payment", async () => {
        const walletManager = {
            generateTransactionId: mock()
        };

        const flow = {
            fromGameAmount: v => v,
            info: mock(),
            flowContext: {
                gameSerialNumber: 0,
                session: {
                    sessionId: "1"
                }
            },
            isSplitPayment: () => false,
            walletManager
        };

        flow.info.once().returns({
            roundId: 777,
            roundPID: "roundPID",
            currency: "USD",
            deviceId: "web"
        });
        walletManager.generateTransactionId.once().returns(Promise.resolve("999"));
        const factory = createWalletOperationFactory(flow as any);
        const result = await factory.createPaymentOperation({
            gameType: "buggy game",
            roundEnded: true,
            actions: [
                {
                    action: "debit",
                    attribute: "balance",
                    amount: 1.2000000000000002,
                    changeType: "bet"
                },
                {
                    action: "freebet",
                    attribute: "balance",
                    amount: 1.0100000000000002,
                    changeType: "bet"
                } as any,
                {
                    action: "credit",
                    attribute: "balance",
                    amount: 3.5999999999999996,
                    changeType: "win"
                }
            ]
        }, { roundEnded: false } as GameHistory);

        expect(result).deep.include({
            "actions": [
                {
                    "action": "debit",
                    "amount": 1.2,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "freebet",
                    "amount": 1.01,
                    "attribute": "balance",
                    "changeType": "bet"
                },
                {
                    "action": "credit",
                    "amount": 3.6,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ],
            "bet": 1.2,
            "operation": "payment",
            "roundEnded": false,
            "win": 3.6,
        });
    });

    it("creates PaymentOperation by PaymentInfo and normalizes values - split payment", async () => {
        const walletManager = {
            generateTransactionId: mock()
        };

        const flow = {
            fromGameAmount: v => v,
            info: mock(),
            flowContext: {
                gameSerialNumber: 0,
                session: {
                    sessionId: "1"
                }
            },
            isSplitPayment: () => true,
            walletManager
        };

        flow.info.once().returns({
            roundId: 777,
            roundPID: "roundPID",
            currency: "USD",
            deviceId: "web"
        });
        walletManager.generateTransactionId.once().returns(Promise.resolve("999"));
        const factory = createWalletOperationFactory(flow as any);
        const result = await factory.createPaymentOperation({
            gameType: "buggy game",
            roundEnded: true,
            actions: [
                {
                    action: "debit",
                    attribute: "balance",
                    amount: 1.02000000000000002,
                    changeType: "bet"
                },
                {
                    action: "freebet",
                    attribute: "balance",
                    amount: 0.0100000000000002,
                    changeType: "bet"
                } as any,
                {
                    action: "credit",
                    attribute: "balance",
                    amount: 3.5999999999999996,
                    changeType: "win"
                }
            ]
        }, { roundEnded: false } as GameHistory);

        expect(result).deep.include({
            "creditActions": [
                {
                    "action": "credit",
                    "amount": 3.6,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ],
            "debitActions": [
                {
                    "action": "debit",
                    "amount": 1.02,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "freebet",
                    "amount": 0.01,
                    "attribute": "balance",
                    "changeType": "bet"
                },

            ],
            "bet": 1.02,
            "operation": "split-payment",
            "win": 3.6,
        });
    });

    it("creates PaymentOperation mixed mode (SRT) and normalizes values - combined payment", async () => {
        const walletManager = {
            generateTransactionId: mock()
        };

        const flow = {
            fromGameAmount: v => v,
            info: mock(),
            flowContext: {
                gameSerialNumber: 0,
                session: {
                    sessionId: "1"
                }
            },
            isSplitPayment: () => false,
            walletManager
        };

        flow.info.once().returns({
            roundId: 777,
            roundPID: "roundPID",
            currency: "USD",
            deviceId: "web"
        });
        walletManager.generateTransactionId.once().returns(Promise.resolve("999"));
        const factory = createWalletOperationFactory(flow as any);
        const result = await factory.createPaymentOperation({
            bet: 999.99999991,
            win: 1999.999999999991,
            actions: [
                { action: "debit", attribute: "balance", amount: 1000 },
                { action: "credit", attribute: "balance", amount: 300 },
                { action: "credit", attribute: "xp", amount: 1 },
                { action: "credit", attribute: "vip", amount: 1 },
            ],
            scene: "currentScene"
        }, { roundEnded: false } as GameHistory);

        expect(result).deep.include({
            "actions": [
                { action: "credit", attribute: "xp", amount: 1 },
                { action: "credit", attribute: "vip", amount: 1 },
                {
                    "action": "debit",
                    "amount": 1000 + 1000,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 2000 + 300,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ],
            "bet": 2000,
            "currency": "USD",
            "deviceId": "web",
            "operation": "payment",
            "roundEnded": false,
            "roundId": 777,
            "roundPID": "roundPID",
            "gameSessionId": "1",
            "scene": "currentScene",
            "transactionId": "999",
            "win": 2300,
        });
        flow.info.verify();
        walletManager.generateTransactionId.verify();
    });

    it("creates PaymentOperation mixed mode (SRT) and normalizes values - split payment", async () => {
        const walletManager = {
            generateTransactionId: mock()
        };

        const flow = {
            fromGameAmount: v => v,
            info: mock(),
            flowContext: {
                gameSerialNumber: 0,
                session: {
                    sessionId: "1"
                }
            },
            isSplitPayment: () => true,
            walletManager
        };

        flow.info.once().returns({
            roundId: 777,
            roundPID: "roundPID",
            currency: "USD",
            deviceId: "web"
        });
        walletManager.generateTransactionId.once().returns(Promise.resolve("999"));
        const factory = createWalletOperationFactory(flow as any);
        const result = await factory.createPaymentOperation({
            "win": 0,
            "bet": 18,
            "gameType": "normal",
            "gameStatus": "settled",
            "roundEnded": true,
            "actions": [
                {
                    "action": "credit",
                    "attribute": "levelPrize",
                    "amount": 270000
                },
                {
                    "action": "credit",
                    "attribute": "stars",
                    "amount": 630000
                },
                {
                    "action": "credit",
                    "attribute": "xp",
                    "amount": 1800000
                },
                {
                    "action": "credit",
                    "attribute": "vipContribution",
                    "amount": 1800
                }
            ]
        }, { roundEnded: false } as GameHistory);

        expect(result).deep.include({
            "actions": undefined,
            "bet": 18,
            "creditActions": [
                {
                    "action": "credit",
                    "attribute": "levelPrize",
                    "amount": 270000
                },
                {
                    "action": "credit",
                    "attribute": "stars",
                    "amount": 630000
                },
                {
                    "action": "credit",
                    "attribute": "xp",
                    "amount": 1800000
                },
                {
                    "action": "credit",
                    "attribute": "vipContribution",
                    "amount": 1800
                },
                {
                    "action": "credit",
                    "amount": 0,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ],
            "debitActions": [
                {
                    "action": "debit",
                    "amount": 18,
                    "attribute": "balance",
                    "changeType": "bet"
                }
            ],
            "operation": "split-payment",
            "win": 0
        });
    });

    it("creates PaymentOperation by PaymentInfo without roundEnded in history", async () => {
        const walletManager = {
            generateTransactionId: mock()
        };

        const flow = {
            fromGameAmount: mock(),
            info: mock(),
            flowContext: {
                gameSerialNumber: 0,
                session: {
                    sessionId: "1"
                }
            },
            isSplitPayment: () => false,
            walletManager
        };

        flow.info.once().returns({
            roundId: 777,
            roundPID: "roundPID",
            currency: "USD",
            deviceId: "web"
        });
        flow.fromGameAmount.twice().onFirstCall().returns(10000).onSecondCall().returns(2000);
        walletManager.generateTransactionId.once().returns(Promise.resolve("999"));
        const factory = createWalletOperationFactory(flow as any);
        const result = await factory.createPaymentOperation({
            bet: 1000,
            win: 200,
            scene: "currentScene"
        }, {} as GameHistory);

        expect(result).deep.include({
            "actions": [
                {
                    "action": "debit",
                    "amount": 10000,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 2000,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ],
            "bet": 10000,
            "currency": "USD",
            "deviceId": "web",
            "operation": "payment",
            "roundEnded": false,
            "roundId": 777,
            "roundPID": "roundPID",
            "gameSessionId": "1",
            "scene": "currentScene",
            "transactionId": "999",
            "win": 2000,
        });
        flow.info.verify();
        walletManager.generateTransactionId.verify();
        flow.fromGameAmount.verify();
    });
});
