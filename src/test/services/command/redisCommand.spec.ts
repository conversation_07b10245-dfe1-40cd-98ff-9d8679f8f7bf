import { suite, test } from "mocha-typescript";
import { CommandExecutor } from "../../../skywind/services/command";
import { Redis } from "../../../skywind/storage/redis";
import { flushAll } from "../../helper";
import { expect } from "chai";
import { RedisCommandExecutor, RedisProc } from "../../../skywind/services/redisCommandExecutor";

@suite()
class RedisCommandSpec {
    private redisProc1 = new RedisProc(
        __dirname + "/../../../../resources/test/cmd1.lua");
    private redisProc2 = new RedisProc(
        __dirname + "/../../../../resources/test/cmd2.lua");
    private executor: CommandExecutor = new RedisCommandExecutor(Redis);

    public async before() {
        await flushAll();
    }

    @test()
    public async testPipeline() {
        const cmds = [
            this.redisProc1.cmd(["test:*"], ["1"]),
            this.redisProc2.cmd(["test_2_1", "test_2_2", "test_2_3"], ["1", "2", "3"]),
            this.redisProc2.cmd(["test_3_1"], ["4"]),
            this.redisProc1.cmd(["test*"])
        ];

        const result: any = await this.executor.execute(cmds);
        result[3].sort();
        expect(result).deep.equal([
            [],
            "{\"test_2_3\":\"3\",\"test_2_2\":\"2\",\"test_2_1\":\"1\"}",
            "{\"test_3_1\":\"4\"}",
            ["test_2_1", "test_2_2", "test_2_3", "test_3_1"].sort()
        ]);
    }

    @test()
    public async testOne() {
        const cmds1 = [
            this.redisProc1.cmd<any>(["test*"])
        ];

        const result1 = await this.executor.execute(cmds1);
        expect(result1).deep.equal([[]]);

        const cmds2 = [
            this.redisProc2.cmd(["test_2_1", "test_2_2", "test_2_3"], ["1", "2", "3"])
        ];

        const result2 = await this.executor.execute(cmds2);
        expect(result2).deep.equal(["{\"test_2_3\":\"3\",\"test_2_2\":\"2\",\"test_2_1\":\"1\"}"]);

        const cmds3 = [
            this.redisProc1.cmd(["test*"])
        ];

        const result3: any = await this.executor.execute(cmds3);
        result3[0].sort();
        expect(result3).deep.equal([["test_2_1", "test_2_2", "test_2_3"].sort()]);
    }

    @test()
    public async testOneWithoutArray() {
        const result1 = await this.executor.execute(this.redisProc1.cmd(["test*"]));
        expect(result1).deep.equal([]);

        const result2 = await this.executor.execute(this.redisProc2.cmd(["test_2_1", "test_2_2", "test_2_3"],
            ["1", "2", "3"]));
        expect(result2).deep.equal("{\"test_2_3\":\"3\",\"test_2_2\":\"2\",\"test_2_1\":\"1\"}");

        const result3: any = await this.executor.execute(this.redisProc1.cmd(["test*"]));
        expect(result3.sort()).deep.equal(["test_2_1", "test_2_2", "test_2_3"].sort());
    }

    @test()
    public async testWithUndefined() {
        const cmds1 = [
            undefined,
            this.redisProc1.cmd(["test:*"], ["1"]),
            undefined,
            this.redisProc2.cmd(["test_2_1", "test_2_2", "test_2_3"], ["1", "2", "3"]),
            undefined,
            this.redisProc2.cmd(["test_3_1"], ["4"]),
            undefined,
            this.redisProc1.cmd(["test*"]),
            undefined,
        ];

        const result1: any = await this.executor.execute(cmds1);
        result1[7].sort();
        expect(result1).deep.equal([
            undefined,
            [],
            undefined,
            "{\"test_2_3\":\"3\",\"test_2_2\":\"2\",\"test_2_1\":\"1\"}",
            undefined,
            "{\"test_3_1\":\"4\"}",
            undefined,
            ["test_2_1", "test_2_2", "test_2_3", "test_3_1"].sort(),
            undefined
        ]);

        const cmds2 = [
            undefined,
            this.redisProc1.cmd(["test*"], ["1"]),
        ];

        const result2: any = await this.executor.execute(cmds2);
        result2[1].sort();
        expect(result2).deep.equal([
            undefined,
            ["test_2_1", "test_2_2", "test_2_3", "test_3_1"].sort()
        ]);

        const cmds3 = [
            undefined,
        ];

        const result3 = await this.executor.execute(cmds3);
        expect(result3).deep.equal([
            undefined,
        ]);

        const cmds4 = undefined;

        const result4 = await this.executor.execute(cmds4);
        expect(result4).is.undefined;
    }
}
