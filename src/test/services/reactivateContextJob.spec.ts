import { suite, test, timeout } from "mocha-typescript";
import { ReactivateContextJob } from "../../skywind/services/reactivateContextJob";
import { getGameContextModel } from "../../skywind/services/offlinestorage/models";
import { GameTokenData, verifyInternalToken } from "../../skywind/services/tokens";
import { GameContextID } from "../../skywind/services/contextIds";
import { createGameToken, flushAll, TEST_MODULE_NAME } from "../helper";
import { Currency } from "@skywind-group/sw-game-core";
import { GameSession } from "../../skywind/services/gameSession";
import { CleanupGameContextService } from "../../skywind/services/cleanup/cleanupGameContextService";
import { GameFlowContextImpl } from "../../skywind/services/context/gameContextImpl";
import { getGameFlowContextManager } from "../../skywind/services/contextmanager/contextManagerImpl";
import * as superagent from "superagent";
import { testing } from "@skywind-group/sw-utils";
import { expect } from "chai";
import requestMock = testing.requestMock;
import RequestMock = testing.RequestMock;
import status200 = testing.status200;
import status500 = testing.status500;

@suite("ReactivateContextJob")
@timeout(15000)
class ReactivateContextJobSpec {
    private requestMock: RequestMock;

    public async before() {
        await flushAll();
        await getGameContextModel().truncate();
        this.requestMock = requestMock(superagent);
    }

    public async after() {
        this.requestMock.unmock(superagent);
    }

    @test()
    public async findsRequiringCompletionAndReactivatesThem() {
        this.requestMock.post("http://api:4004/reactivate-game", status200({}));

        const job = new ReactivateContextJob(getGameFlowContextManager());

        await this.createContext("PLAYER1", "1", false);

        await this.createContext("PLAYER2", "2", true);
        await this.createContext("PLAYER3", "3", false);

        await new CleanupGameContextService().forceCleanUp(1, 10);
        await job.fire();
        expect(this.requestMock.args[0].url).equal("http://api:4004/reactivate-game");
        expect(this.requestMock.args[1].url).equal("http://api:4004/reactivate-game");
        expect(this.requestMock.args.length).equal(2);
        const request1: any = await verifyInternalToken(this.requestMock.args[0].body.token);
        const request2: any = await verifyInternalToken(this.requestMock.args[1].body.token);
        expect([request1.gameContextId, request2.gameContextId])
            .deep
            .members(["games:context:1:PLAYER1:game_id:web", "games:context:1:PLAYER3:game_id:web"]);
    }

    @test()
    public async retriesReactivateGameContextsRequiringCompletion() {
        this.requestMock.post("http://api:4004/reactivate-game", status500({}));

        const job = new ReactivateContextJob(getGameFlowContextManager());

        await this.createContext("PLAYER1", "1", false);

        await new CleanupGameContextService().forceCleanUp(1, 10);
        await job.fire();
        expect(this.requestMock.args.length).equal(1);
        expect(this.requestMock.args[0].url).equal("http://api:4004/reactivate-game");
        const request1: any = await verifyInternalToken(this.requestMock.args[0].body.token);
        expect([request1.gameContextId])
            .deep
            .equal(["games:context:1:PLAYER1:game_id:web"]);

        await job.fire();
        expect(this.requestMock.args.length).equal(2);
        expect(this.requestMock.args[1].url).equal("http://api:4004/reactivate-game");
        const request2: any = await verifyInternalToken(this.requestMock.args[1].body.token);
        expect([request2.gameContextId])
            .deep
            .equal(["games:context:1:PLAYER1:game_id:web"]);
    }

    private async createContext(playerCode: string, roundId: string, roundEnded: boolean) {
        const currency: Currency = 0;
        const settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: currency,
            stakeAll: [1, 2, 3, 4],
            stakeDef: currency,
            stakeMax: currency,
            stakeMin: currency,
            winMax: currency,
            currencyMultiplier: 100,
        };

        const gameID: GameContextID = GameContextID.create("gameId", 1, "playerId", "deviceId");

        const gameData = {
            gameTokenData: undefined,
            gameId: "gameId",
            limits: settings
        };

        const gameTokenData: GameTokenData = {
            playerCode: gameID.playerCode,
            gameCode: "GM001",
            brandId: gameID.brandId,
            currency: "USD",
            playmode: "real"
        };

        gameTokenData.token = await createGameToken(gameTokenData);
        gameData.gameTokenData = gameTokenData;
        const sessionId = await GameSession.generate(gameID, "real");
        const newSessionId = await GameSession.generate(gameID, "real");

        const id = GameContextID.create("game_id", 1, playerCode, "web");
        const context: GameFlowContextImpl = new GameFlowContextImpl(id);
        context.gameVersion = "1";
        context.gameContext = {
            scenesState: {},
            currentScene: "scene",
            nextScene: "next",
            history: [],
            stake: null,
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };
        context.session = sessionId;
        context.lastRequestId = 1;
        context.gameSerialNumber = 1;
        context.totalEventId = 1;
        context.settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: 0,
            stakeAll: [1, 2, 3, 4],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 100,
            transferEnabled: false,
        };
        context.version = 3;
        context.gameData = gameData;
        context.round = {
            totalBet: 0,
            totalWin: 0,
            totalEvents: 0,
            balanceBefore: 0,
            balanceAfter: 0
        };
        context.roundId = roundId;
        context.roundEnded = roundEnded;
        context.createdAt = new Date();
        context.updatedAt = new Date();

        const newSettings = {
            coins: [5, 6],
            defaultCoin: 5,
            maxTotalStake: 0,
            stakeAll: [5, 6, 7, 8],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 1000,
            transferEnabled: false,
        };

        return getGameFlowContextManager().createContextFrom(id,
            context,
            newSessionId,
            gameData,
            TEST_MODULE_NAME,
            newSettings);
    }
}
