import { expect, should, use } from "chai";
import { getAnalytics, getGameHistoryItem, getRoundHistory, TEST_MODULE_NAME } from "../helper";
import { suite, test } from "mocha-typescript";
import { BaseGameContextSpec } from "./gameContext.spec";
import config from "../../skywind/config";
import { GameFlowInfoImpl } from "../../skywind/services/context/gameFlowInfo";

const chaiAsPromise = require("chai-as-promised");
import _ = require("lodash");

should();
use(chaiAsPromise);

@suite("GameContext, update analytics")
class GameContextUpdateAnalyticsSpec extends BaseGameContextSpec {

    @test("is updated & committed pending modification and store analytics in queue")
    public async testUpdateAndCommitPending() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        let context = await this.contextManager.findGameContextById(this.gameID);
        const request = { request: "req", requestId: 1 };
        const state1 = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };

        const history1: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2, 3],
            }
        };

        const walletOperation1: any = {
            operation: "payment",
            transactionId: "1",
            bet: 1900,
            win: 200,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history1.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, state1, request, history1);

        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };

        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        walletOperation1.ts = walletOperation1.ts.toISOString();
        expect(context.pendingModification).deep.equal({
            "history": history1,
            "newState": state1,
            "walletOperation": walletOperation1,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state1);
        expect(context.pendingModification).is.undefined;

        let result: any = await getGameHistoryItem();
        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            ctrl: result.ctrl,
            eventId: 0,
            roundId: "0",
            sessionId: this.sessionId.sessionId,
            result: history1.data,
            type: history1.type,
            gameVersion: TEST_MODULE_NAME.version,
            walletTransactionId: "1",
            currency: "USD",
            win: walletOperation1.win,
            bet: walletOperation1.bet,
            roundEnded: true,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 1900,
            "totalRoundWin": 200
        });

        const history2: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2],
            }
        };

        const state2 = _.cloneDeep(state1);
        state2["nextScene"] = "SOMESCENE2";

        const walletOperation2: any = {
            operation: "payment",
            transactionId: "2",
            bet: 2000,
            win: 100,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history2.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        const analytics2 = {
            type: "bi-analytics",
            data: {
                type: "some_type"
            }
        };
        await context.updatePendingModification(walletOperation2, state2, request, history2, undefined, analytics2);
        walletOperation2.ts = walletOperation2.ts.toISOString();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state1);
        const uuid1 = context.pendingModification.analytics.id;
        expect(context.pendingModification).deep.equal({
            "analytics": {
                "data": {
                    "type": "some_type"
                },
                "id": uuid1,
                "type": "bi-analytics",
                "deviceId": "deviceId",
                "eventId": 0,
                "gameCode": "gameId",
                "gameId": "gameId",
                "playerCode": "playerId",
                "roundId": "1",
                "brandId": 1,
                ts: GameContextUpdateAnalyticsSpec.clock.now
            },
            "history": history2,
            "newState": state2,
            "walletOperation": walletOperation2,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state2);
        expect(context.pendingModification).is.undefined;

        result = await getGameHistoryItem();

        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 0,
            ctrl: result.ctrl,
            gameVersion: TEST_MODULE_NAME.version,
            result: history2.data,
            type: history2.type,
            roundEnded: true,
            roundId: "1",
            sessionId: this.sessionId.sessionId,
            walletTransactionId: "2",
            currency: "USD",
            win: walletOperation2.win,
            bet: walletOperation2.bet,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 2000,
            totalRoundWin: 100
        });
        const history3: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2, 3, 4],
            }
        };

        const state3 = _.cloneDeep(state2);
        state3["nextScene"] = "SOMESCENE3";
        const walletOperation3: any = {
            operation: "payment",
            transactionId: "3",
            bet: 100,
            win: 200,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history3.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        const analytics3 = {
            type: "bi-analytics-3",
            data: {
                type: "some_type-3"
            }
        };
        await context.updatePendingModification(walletOperation3, state3, request, history3, undefined, analytics3);

        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state2);

        const uuid2 = context.pendingModification.analytics.id;
        expect(context.pendingModification).deep.equal({
            "analytics": {
                "data": {
                    "type": "some_type-3"
                },
                "id": uuid2,
                "type": "bi-analytics-3",
                "deviceId": "deviceId",
                "eventId": 0,
                "gameCode": "gameId",
                "gameId": "gameId",
                "playerCode": "playerId",
                "roundId": "2",
                "brandId": 1,
                ts: GameContextUpdateAnalyticsSpec.clock.now
            },
            "history": history3,
            "newState": state3,
            "walletOperation": walletOperation3,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state3);
        expect(context.pendingModification).undefined;

        result = await getGameHistoryItem();
        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameTokenData.gameCode,
            eventId: 0,
            ctrl: result.ctrl,
            result: history3.data,
            type: history3.type,
            gameVersion: TEST_MODULE_NAME.version,
            roundEnded: true,
            roundId: "2",
            sessionId: this.sessionId.sessionId,
            walletTransactionId: "3",
            currency: "USD",
            win: walletOperation3.win,
            bet: walletOperation3.bet,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 100,
            totalRoundWin: 200
        });

        const rounds = await getRoundHistory(0, 1000);
        expect(rounds.map((item) => _.omit(item, "finishedAt", "startedAt", "ctrl"))).deep.equals([
            {
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "gameId",
                gameId: "gameId",
                id: "2",
                playerCode: "playerId",
                sessionId: this.sessionId.sessionId,
                totalBet: 100,
                totalEvents: 1,
                totalWin: 200,
                totalJpContribution: 0,
                totalJpWin: 0,
            },
            {
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "gameId",
                gameId: "gameId",
                id: "1",
                playerCode: "playerId",
                sessionId: this.sessionId.sessionId,
                totalBet: 2000,
                totalEvents: 1,
                totalWin: 100,
                totalJpContribution: 0,
                totalJpWin: 0,
            },
            {
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "gameId",
                gameId: "gameId",
                id: "0",
                playerCode: "playerId",
                sessionId: this.sessionId.sessionId,
                totalBet: 1900,
                totalEvents: 1,
                totalWin: 200,
                totalJpContribution: 0,
                totalJpWin: 0,
            }
        ]);

        const analyticsHistory = await getAnalytics(0, 1000);
        expect(analyticsHistory).deep.equals([
            {
                "brandId": 1,
                "data": {
                    "type": "some_type-3"
                },
                "deviceId": "deviceId",
                "eventId": 0,
                "gameCode": "gameId",
                "gameId": "gameId",
                "id": uuid2,
                "playerCode": "playerId",
                "roundId": "2",
                "type": "bi-analytics-3",
                ts: GameContextUpdateAnalyticsSpec.clock.now
            },
            {
                "brandId": 1,
                "data": {
                    "type": "some_type"
                },
                "deviceId": "deviceId",
                "eventId": 0,
                "gameCode": "gameId",
                "gameId": "gameId",
                "id": uuid1,
                "playerCode": "playerId",
                "roundId": "1",
                "type": "bi-analytics",
                ts: GameContextUpdateAnalyticsSpec.clock.now
            }
        ]);
    }
}

@suite("GameContext, skip update analytics and config.sendOnline=true")
class GameContextSkipUpdateAnalyticsSpec extends BaseGameContextSpec {
    public static prevValue: boolean;

    public static before() {
        GameContextSkipUpdateAnalyticsSpec.prevValue = config.analytics.sendOnline;
        config.analytics.sendOnline = true;
        BaseGameContextSpec.before();
    }

    public static after() {
        config.analytics.sendOnline = GameContextSkipUpdateAnalyticsSpec.prevValue;
        BaseGameContextSpec.after();
    }

    @test("is updated & committed pending modification")
    public async testUpdateAndCommitPending() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        let context = await this.contextManager.findGameContextById(this.gameID);
        const request = { request: "req", requestId: 1 };
        const state1 = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };

        const history1: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2, 3],
            }
        };

        const walletOperation1: any = {
            operation: "payment",
            transactionId: "1",
            bet: 1900,
            win: 200,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history1.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, state1, request, history1);

        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };

        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        walletOperation1.ts = walletOperation1.ts.toISOString();
        expect(context.pendingModification).deep.equal({
            "history": history1,
            "newState": state1,
            "walletOperation": walletOperation1,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state1);
        expect(context.pendingModification).is.undefined;

        let result: any = await getGameHistoryItem();
        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 0,
            roundId: "0",
            sessionId: this.sessionId.sessionId,
            result: history1.data,
            type: history1.type,
            gameVersion: TEST_MODULE_NAME.version,
            walletTransactionId: "1",
            currency: "USD",
            win: walletOperation1.win,
            bet: walletOperation1.bet,
            roundEnded: true,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            ctrl: result.ctrl,
            totalRoundBet: 1900,
            totalRoundWin: 200
        });

        const history2: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2],
            }
        };

        const state2 = _.cloneDeep(state1);
        state2["nextScene"] = "SOMESCENE2";

        const walletOperation2: any = {
            operation: "payment",
            transactionId: "2",
            bet: 2000,
            win: 100,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history2.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        const analytics2 = {
            type: "bi-analytics",
            data: {
                type: "some_type"
            }
        };
        await context.updatePendingModification(walletOperation2, state2, request, history2, undefined, analytics2);

        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state1);
        const uuid1 = context.pendingModification.analytics.id;
        expect(context.pendingModification).deep.equal({
            "analytics": {
                "data": {
                    "type": "some_type"
                },
                "id": uuid1,
                "type": "bi-analytics",
                "deviceId": "deviceId",
                "eventId": 0,
                "gameCode": "gameId",
                "gameId": "gameId",
                "playerCode": "playerId",
                "roundId": "1",
                "brandId": 1,
                ts: GameContextSkipUpdateAnalyticsSpec.clock.now
            },
            "history": history2,
            "newState": state2,
            "walletOperation": walletOperation2,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state2);
        expect(context.pendingModification).is.undefined;

        result = await getGameHistoryItem();

        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 0,
            gameVersion: TEST_MODULE_NAME.version,
            result: history2.data,
            type: history2.type,
            roundEnded: true,
            roundId: "1",
            sessionId: this.sessionId.sessionId,
            walletTransactionId: "2",
            currency: "USD",
            win: walletOperation2.win,
            bet: walletOperation2.bet,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            ctrl: result.ctrl,
            totalRoundBet: 2000,
            totalRoundWin: 100
        });
        const history3: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2, 3, 4],
            }
        };

        const state3 = _.cloneDeep(state2);
        state3["nextScene"] = "SOMESCENE3";
        const walletOperation3: any = {
            operation: "payment",
            transactionId: "3",
            bet: 100,
            win: 200,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history3.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        const analytics3 = {
            type: "bi-analytics-3",
            data: {
                type: "some_type-3"
            }
        };
        await context.updatePendingModification(walletOperation3, state3, request, history3, undefined, analytics3);

        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state2);

        const uuid2 = context.pendingModification.analytics.id;
        expect(context.pendingModification).deep.equal({
            "analytics": {
                "data": {
                    "type": "some_type-3"
                },
                "id": uuid2,
                "type": "bi-analytics-3",
                "deviceId": "deviceId",
                "eventId": 0,
                "gameCode": "gameId",
                "gameId": "gameId",
                "playerCode": "playerId",
                "roundId": "2",
                "brandId": 1,
                ts: BaseGameContextSpec.clock.now
            },
            "history": history3,
            "newState": state3,
            "walletOperation": walletOperation3,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state3);
        expect(context.pendingModification).undefined;

        result = await getGameHistoryItem();
        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameTokenData.gameCode,
            eventId: 0,
            result: history3.data,
            type: history3.type,
            gameVersion: TEST_MODULE_NAME.version,
            roundEnded: true,
            roundId: "2",
            sessionId: this.sessionId.sessionId,
            walletTransactionId: "3",
            currency: "USD",
            win: walletOperation3.win,
            bet: walletOperation3.bet,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            ctrl: result.ctrl,
            totalRoundBet: 100,
            totalRoundWin: 200
        });

        const rounds = await getRoundHistory(0, 1000);
        expect(rounds.map((item) => _.omit(item, "finishedAt", "startedAt", "ctrl"))).deep.equals([
            {
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "gameId",
                gameId: "gameId",
                id: "2",
                playerCode: "playerId",
                sessionId: this.sessionId.sessionId,
                totalBet: 100,
                totalEvents: 1,
                totalWin: 200,
                totalJpContribution: 0,
                totalJpWin: 0,
            },
            {
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "gameId",
                gameId: "gameId",
                id: "1",
                playerCode: "playerId",
                sessionId: this.sessionId.sessionId,
                totalBet: 2000,
                totalEvents: 1,
                totalWin: 100,
                totalJpContribution: 0,
                totalJpWin: 0,
            },
            {
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "gameId",
                gameId: "gameId",
                id: "0",
                playerCode: "playerId",
                sessionId: this.sessionId.sessionId,
                totalBet: 1900,
                totalEvents: 1,
                totalWin: 200,
                totalJpContribution: 0,
                totalJpWin: 0,
            }
        ]);

        const analyticsHistory = await getAnalytics(0, 1000);
        expect(analyticsHistory).deep.equals([]);
    }
}
