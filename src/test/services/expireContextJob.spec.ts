import { suite, test, timeout } from "mocha-typescript";
import { ExpireContextJob } from "../../skywind/services/expireContextJob";
import { RoundStatistics } from "../../skywind/services/context/gamecontext";
import { getGameContextModel } from "../../skywind/services/offlinestorage/models";
import { GameTokenData, verifyInternalToken } from "../../skywind/services/tokens";
import { GameContextID } from "../../skywind/services/contextIds";
import { createGameToken, flushAll, TEST_MODULE_NAME } from "../helper";
import { Currency } from "@skywind-group/sw-game-core";
import { GameSession } from "../../skywind/services/gameSession";
import { CleanupGameContextService } from "../../skywind/services/cleanup/cleanupGameContextService";
import { GameFlowContextImpl } from "../../skywind/services/context/gameContextImpl";
import { getGameFlowContextManager } from "../../skywind/services/contextmanager/contextManagerImpl";
import * as superagent from "superagent";
import { testing } from "@skywind-group/sw-utils";
import { expect } from "chai";
import requestMock = testing.requestMock;
import RequestMock = testing.RequestMock;
import status200 = testing.status200;
import status500 = testing.status500;

@suite("ExpireContextJob")
@timeout(15000)
class ExpireContextJobSpec {
    private requestMock: RequestMock;

    public async before() {
        await flushAll();
        await getGameContextModel().truncate();
        this.requestMock = requestMock(superagent);
    }

    public async after() {
        this.requestMock.unmock(superagent);
    }

    @test()
    public async findsExpiredAndRemoveThem() {
        this.requestMock.post("http://api:4004/expire-game", status200({}));

        const job = new ExpireContextJob(getGameFlowContextManager());

        const expiredAt = new Date().getTime();
        await this.createContext("PLAYER1", "1", {
            startedAt: new Date(),
            finishedAt: new Date(),
            roundEnded: false
        } as any, expiredAt);

        await this.createContext("PLAYER2", undefined, undefined, expiredAt);
        await this.createContext("PLAYER3", "2", {
            startedAt: new Date(),
            finishedAt: new Date(),
            roundEnded: false
        } as any, undefined);

        await new CleanupGameContextService().forceCleanUp(1, 10);
        await job.fire();
        expect(this.requestMock.args[0].url).equal("http://api:4004/expire-game");
        expect(this.requestMock.args[1].url).equal("http://api:4004/expire-game");
        expect(this.requestMock.args.length).equal(2);
        const t1: any = await verifyInternalToken(this.requestMock.args[0].body.token);
        const t2: any = await verifyInternalToken(this.requestMock.args[1].body.token);
        expect([t1.gameContextId, t2.gameContextId])
            .deep
            .equal(["games:bns:1:PLAYER1:game_id:web", "games:bns:1:PLAYER2:game_id:web"]);
    }

    @test()
    public async testUpdateExpired() {
        this.requestMock.post("http://api:4004/expire-game", status500({}));

        const job = new ExpireContextJob(getGameFlowContextManager());

        const expiredAt = new Date();
        await this.createContext("PLAYER1", "1", {
            startedAt: new Date(),
            finishedAt: new Date(),
            roundEnded: false
        } as any, expiredAt.getTime());

        await new CleanupGameContextService().forceCleanUp(1, 10);
        await job.fire();
        expect(this.requestMock.args[0].url).equal("http://api:4004/expire-game");
        expect(this.requestMock.args.length).equal(1);
        const t: any = await verifyInternalToken(this.requestMock.args[0].body.token);
        expect([t.gameContextId])
            .deep
            .equal(["games:bns:1:PLAYER1:game_id:web"]);
        const context = await getGameContextModel().findOne({ where: { id: "games:bns:1:PLAYER1:game_id:web" } });
        expect(context.expireAt).is.not.equal(expiredAt);
    }

    private async createContext(playerCode: string, roundId: string, round: RoundStatistics, expireAt: number) {
        const currency: Currency = 0;
        const settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: currency,
            stakeAll: [1, 2, 3, 4],
            stakeDef: currency,
            stakeMax: currency,
            stakeMin: currency,
            winMax: currency,
            currencyMultiplier: 100,
        };

        const gameID: GameContextID = GameContextID.create("gameId", 1, "playerId", "deviceId");

        const gameData = {
            gameTokenData: undefined,
            gameId: "gameId",
            limits: settings,
            bnsPromotion: {
                promoId: "1",
                expireAt: expireAt
            }
        };

        const gameTokenData: GameTokenData = {
            playerCode: gameID.playerCode,
            gameCode: "GM001",
            brandId: gameID.brandId,
            currency: "USD",
            playmode: "bns"
        };

        gameTokenData.token = await createGameToken(gameTokenData);
        gameData.gameTokenData = gameTokenData;
        const sessionId = await GameSession.generate(gameID, "bns");
        const newSessionId = await GameSession.generate(gameID, "bns");

        const id = GameContextID.create("game_id", 1, playerCode, "web", "bns");
        const context: GameFlowContextImpl = new GameFlowContextImpl(id);
        context.gameVersion = "1";
        context.gameContext = {
            scenesState: {},
            currentScene: "scene",
            nextScene: "next",
            history: [],
            stake: null,
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };
        context.session = sessionId;
        context.lastRequestId = 1;
        context.gameSerialNumber = 1;
        context.totalEventId = 1;
        context.settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: 0,
            stakeAll: [1, 2, 3, 4],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 100,
            transferEnabled: false,
        };
        context.version = 3;
        context.gameData = gameData;
        context.round = round;
        context.roundId = roundId;
        context.createdAt = new Date();
        context.updatedAt = new Date();

        const newSettings = {
            coins: [5, 6],
            defaultCoin: 5,
            maxTotalStake: 0,
            stakeAll: [5, 6, 7, 8],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 1000,
            transferEnabled: false,
        };

        return getGameFlowContextManager().createContextFrom(id,
            context,
            newSessionId,
            gameData,
            TEST_MODULE_NAME,
            newSettings);
    }

}
