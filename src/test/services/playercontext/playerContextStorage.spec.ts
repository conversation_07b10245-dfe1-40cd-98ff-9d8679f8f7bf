import { suite, test } from "mocha-typescript";
import { flushAll } from "../../helper";
import { RedisCommandExecutor } from "../../../skywind/services/redisCommandExecutor";
import { Redis } from "../../../skywind/storage/redis";
import { GameContextID, PlayerContextID } from "../../../skywind/services/contextIds";
import { expect } from "chai";
import { PlayerContextImpl } from "../../../skywind/services/playercontext/playerContext";
import {
    FunPlayerContextCommands,
    PlayerContextCommandsImpl
} from "../../../skywind/services/onlinestorage/playerContextCommands";
import config from "../../../skywind/config";

@suite()
class PlayerContextStorageSpec {
    private executor = new RedisCommandExecutor(Redis);
    private commands = new PlayerContextCommandsImpl();

    public async before() {
        return flushAll();
    }

    @test()
    public async testActivateGame() {
        const playerContextId = PlayerContextID.create(1, "player001");

        await this.executor.execute(
            this.commands.activateGame(
                playerContextId,
                GameContextID.create("game001", 1, "player001", "web", "real"),
                "real")
        );

        await this.executor.execute(
            this.commands.activateGame(
                playerContextId,
                GameContextID.create("game002", 1, "player001", "web", "real"),
                "real")
        );

        const playerContext = await this.executor.execute(this.commands.load(playerContextId));

        expect(playerContext.id.asString()).equal(playerContextId.asString());
        expect(playerContext.version).equal(2);
        expect(playerContext.dataVersion).equal(1);

        expect(playerContext.brokenGames).deep.equal([]);
        expect(playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "web",
                    gameCode: "game001",
                    idValue: "games:context:1:player001:game001:web",
                    playerCode: "player001"
                },
                mode: "real"
            },
            {
                id: {
                    brandId: 1,
                    deviceId: "web",
                    gameCode: "game002",
                    idValue: "games:context:1:player001:game002:web",
                    playerCode: "player001",
                },
                mode: "real"
            }
        ]);
    }

    @test()
    public async testContextNotExists() {
        const playerContextId = PlayerContextID.create(1, "player001");
        const playerContext = await this.executor.execute(this.commands.load(playerContextId));

        expect(playerContext).is.undefined;
    }

    @test()
    public async testRemoveGameAndMoveToBroken() {
        const playerContextId = PlayerContextID.create(1, "player001");

        await this.executor.execute(
            [
                this.commands.activateGame(
                    playerContextId,
                    GameContextID.create("game001", 1, "player001", "web", "real"),
                    "bns"),
                this.commands.activateGame(
                    playerContextId,
                    GameContextID.create("game002", 1, "player001", "web", "real"),
                    "real")
            ]
        );

        await this.executor.execute(
            [
                this.commands.activateGame(
                    playerContextId,
                    GameContextID.create("game001", 1, "player001", "web", "real"),
                    "bns"),
                this.commands.activateGame(
                    playerContextId,
                    GameContextID.create("game002", 1, "player001", "web", "real"),
                    "real")
            ]
        );

        await this.executor.execute(
            [
                this.commands.removeGame(
                    playerContextId,
                    GameContextID.create("game001", 1, "player001", "web", "real"),
                    true),
                this.commands.removeGame(
                    playerContextId,
                    GameContextID.create("game002", 1, "player001", "web", "real"),
                    false)
            ]
        );

        const playerContext = await this.executor.execute(this.commands.load(playerContextId));

        expect(playerContext.id.asString()).equal(playerContextId.asString());
        expect(playerContext.version).equal(4);
        expect(playerContext.dataVersion).equal(1);

        expect(playerContext.brokenGames).deep.equal(
            [
                {
                    id: {
                        brandId: 1,
                        deviceId: "web",
                        gameCode: "game001",
                        idValue: "games:context:1:player001:game001:web",
                        playerCode: "player001",
                    },
                    mode: "bns"
                }
            ]
        );
        expect(playerContext.activeGames).deep.equal([]);
    }

    @test()
    public async testRemovePlayerContext() {
        const playerContextId = PlayerContextID.create(1, "player001");

        await this.executor.execute(
            [
                this.commands.activateGame(
                    playerContextId,
                    GameContextID.create("game001", 1, "player001", "web", "real"),
                    "bns"),
                this.commands.activateGame(
                    playerContextId,
                    GameContextID.create("game002", 1, "player001", "web", "real"),
                    "real"),
                this.commands.activateGame(
                    playerContextId,
                    GameContextID.create("game003", 1, "player001", "web", "real"),
                    "real")
            ]
        );

        await this.executor.execute(
            [
                this.commands.removeGame(
                    playerContextId,
                    GameContextID.create("game001", 1, "player001", "web", "real"),
                    true),
                this.commands.removeGame(
                    playerContextId,
                    GameContextID.create("game002", 1, "player001", "web", "real"),
                    false),
                this.commands.removeGame(
                    playerContextId,
                    GameContextID.create("game003", 1, "player001", "web", "real"),
                    false)
            ]
        );

        const ctx = await this.executor.execute(this.commands.load(playerContextId));

        expect(await this.executor.execute(this.commands.remove(ctx))).equal(1);

        const playerContext = await this.executor.execute(this.commands.load(playerContextId));
        expect(playerContext).is.undefined;
    }

    @test()
    public async testForbitRemoveWithActiveGames() {
        const playerContextId = PlayerContextID.create(1, "player001");

        await this.executor.execute(
            [
                this.commands.activateGame(
                    playerContextId,
                    GameContextID.create("game001", 1, "player001", "web", "real"),
                    "bns"),
                this.commands.activateGame(
                    playerContextId,
                    GameContextID.create("game002", 1, "player001", "web", "real"),
                    "real"),
                this.commands.activateGame(
                    playerContextId,
                    GameContextID.create("game003", 1, "player001", "web", "real"),
                    "real")
            ]
        );

        let playerContext = await this.executor.execute(this.commands.load(playerContextId));
        expect(await this.executor.execute(this.commands.remove(playerContext))).equal(-1);
        playerContext = await this.executor.execute(this.commands.load(playerContextId));
        expect(playerContext).is.not.undefined;
    }

    @test()
    public async testRemovePlayerContext_FailedOptimisticVersion() {
        const playerContextId = PlayerContextID.create(1, "player001");

        await this.executor.execute(
            [
                this.commands.activateGame(
                    playerContextId,
                    GameContextID.create("game001", 1, "player001", "web", "real"),
                    "bns"),
                this.commands.activateGame(
                    playerContextId,
                    GameContextID.create("game002", 1, "player001", "web", "real"),
                    "real"),
                this.commands.activateGame(
                    playerContextId,
                    GameContextID.create("game003", 1, "player001", "web", "real"),
                    "real")
            ]
        );

        await this.executor.execute(
            [
                this.commands.removeGame(
                    playerContextId,
                    GameContextID.create("game001", 1, "player001", "web", "real"),
                    true),
                this.commands.removeGame(
                    playerContextId,
                    GameContextID.create("game002", 1, "player001", "web", "real"),
                    false),
                this.commands.removeGame(
                    playerContextId,
                    GameContextID.create("game002", 1, "player001", "web", "real"),
                    false)
            ]
        );

        const ctx = await this.executor.execute(this.commands.load(playerContextId));
        ctx.version++;

        expect(await this.executor.execute(this.commands.remove(ctx))).equal(-1);

        const playerContext = await this.executor.execute(this.commands.load(playerContextId));
        ctx.version--;
        expect(playerContext.brokenGames).deep.equal(ctx.brokenGames);
        expect(playerContext.activeGames).deep.equal(ctx.activeGames);
    }

    @test()
    public async testCreatePlayerContext() {
        const playerContextId = PlayerContextID.create(1, "player001");

        const result = await this.executor.execute(
            this.commands.save(
                playerContextId,
                {
                    id: playerContextId.asString(),
                    games: [
                        {
                            id: GameContextID.create("game001", 1, "player001", "web", "real").asString(),
                            mode: "real"
                        },
                        {
                            id: GameContextID.create("game001", 1, "player001", "web", "bns").asString(),
                            mode: "bns"
                        },
                        {
                            id: GameContextID.create("game002", 1, "player001", "mobile", "real").asString(),
                            mode: "real"
                        }
                    ],
                    data: undefined,
                    version: 1,
                    dataVersion: 1,
                    createdAt: new Date(),
                    updatedAt: new Date()
                }));

        const playerContext = await this.executor.execute(this.commands.load(playerContextId));
        expect(playerContext.version).equals(1);
        expect(playerContext.activeGames).deep.equals([]);
        expect(playerContext.brokenGames).deep.equals([

            {
                id: {
                    brandId: 1,
                    deviceId: "web",
                    gameCode: "game001",
                    idValue: "games:context:1:player001:game001:web",
                    playerCode: "player001"
                },
                mode: "real"
            },
            {
                id: {
                    brandId: 1,
                    deviceId: "web",
                    gameCode: "game001",
                    idValue: "games:bns:1:player001:game001:web",
                    playerCode: "player001"
                },
                mode: "bns"
            },
            {
                id: {
                    brandId: 1,
                    deviceId: "mobile",
                    gameCode: "game002",
                    idValue: "games:context:1:player001:game002:mobile",
                    playerCode: "player001"
                },
                mode: "real"
            }
        ]);
        expect(result.version).deep.equal(playerContext.version);
        expect(result.brokenGames).deep.equal(playerContext.brokenGames);
        expect(result.activeGames).deep.equal(playerContext.activeGames);
    }

    @test()
    public async testActivateGameMakeActive() {
        const playerContextId = PlayerContextID.create(1, "player001");

        const result = await this.executor.execute(
            this.commands.save(
                playerContextId,
                {
                    id: playerContextId.asString(),
                    games: [
                        {
                            id: GameContextID.create("game001", 1, "player001", "web", "real").asString(),
                            mode: "real"
                        },
                        {
                            id: GameContextID.create("game001", 1, "player001", "web", "bns").asString(),
                            mode: "bns"
                        },
                        {
                            id: GameContextID.create("game002", 1, "player001", "mobile", "real").asString(),
                            mode: "real"
                        }
                    ],
                    data: undefined,
                    version: 1,
                    dataVersion: 1,
                    createdAt: new Date(),
                    updatedAt: new Date()
                }));

        await this.executor.execute(
            this.commands.activateGame(
                playerContextId,
                GameContextID.create("game001", 1, "player001", "web", "real"),
                "real")
        );

        const playerContext = await this.executor.execute(this.commands.load(playerContextId));
        expect(playerContext.version).equals(2);
        expect(playerContext.activeGames).deep.equals([
            {
                id: {
                    brandId: 1,
                    deviceId: "web",
                    gameCode: "game001",
                    idValue: "games:context:1:player001:game001:web",
                    playerCode: "player001"
                },
                mode: "real"
            }
        ]);
        expect(playerContext.brokenGames).deep.equals([
            {
                id: {
                    brandId: 1,
                    deviceId: "web",
                    gameCode: "game001",
                    idValue: "games:bns:1:player001:game001:web",
                    playerCode: "player001"
                },
                mode: "bns"
            },
            {
                id: {
                    brandId: 1,
                    deviceId: "mobile",
                    gameCode: "game002",
                    idValue: "games:context:1:player001:game002:mobile",
                    playerCode: "player001"
                },
                mode: "real"
            }
        ]);
    }

    @test()
    public async testSavePlayerContextOptimisticLocking() {
        const playerContextId = PlayerContextID.create(1, "player001");

        const result = await this.executor.execute(
            this.commands.save(
                playerContextId,
                {
                    id: playerContextId.asString(),
                    games: [
                        {
                            id: GameContextID.create("game001", 1, "player001", "web", "real").asString(),
                            mode: "real"
                        },
                        {
                            id: GameContextID.create("game001", 1, "player001", "web", "bns").asString(),
                            mode: "bns"
                        },
                        {
                            id: GameContextID.create("game002", 1, "player001", "mobile", "real").asString(),
                            mode: "real"
                        }
                    ],
                    data: undefined,
                    version: 1,
                    dataVersion: 1,
                    createdAt: new Date(),
                    updatedAt: new Date()
                }));

        const result2 = await this.executor.execute(
            this.commands.save(
                playerContextId,
                {
                    id: playerContextId.asString(),
                    games: [
                        {
                            id: GameContextID.create("game003", 1, "player001", "web", "real").asString(),
                            mode: "real"
                        },
                        {
                            id: GameContextID.create("game004", 1, "player001", "web", "bns").asString(),
                            mode: "bns"
                        },
                        {
                            id: GameContextID.create("game005", 1, "player001", "mobile", "real").asString(),
                            mode: "real"
                        }
                    ],
                    data: undefined,
                    version: 3,
                    dataVersion: 1,
                    createdAt: new Date(),
                    updatedAt: new Date()
                }));

        const playerContext = await this.executor.execute(this.commands.load(playerContextId));
        expect(playerContext.version).equals(1);
        expect(playerContext.activeGames).deep.equals(result.activeGames);
        expect(playerContext.brokenGames).deep.equals(result.brokenGames);

    }

    @test()
    public async testTrackActive() {
        const playerContextId = PlayerContextID.create(1, "player001");

        await this.executor.execute([
            this.commands.activateGame(
                playerContextId,
                GameContextID.create("game001", 1, "player001", "web", "real"),
                "real"),
            this.commands.trackActive(playerContextId, config.cleanUp.expireIn)
        ]);

        const playerContext = await this.executor.execute(this.commands.load(playerContextId));

        expect(playerContext.id.asString()).equal(playerContextId.asString());
        expect(playerContext.version).equal(1);
        expect(playerContext.dataVersion).equal(1);

        expect(playerContext.brokenGames).deep.equal([]);
        expect(playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "web",
                    gameCode: "game001",
                    idValue: "games:context:1:player001:game001:web",
                    playerCode: "player001"
                },
                mode: "real"
            }
        ]);
        const client = await Redis.get().get();
        try {
            const count = await client.zcard("games:player-last-activity");
            expect(count).equal(1);
        } finally {
            await Redis.get().release(client);
        }
    }

    @test()
    public testFunStorage() {
        const commands = new FunPlayerContextCommands();
        const playerContextID = PlayerContextID.create(1, "player001");
        const gameID = GameContextID.create("game001", 1, "player001", "web", "real");

        expect(commands.activateGame(playerContextID, gameID, "real")).is.undefined;
        expect(commands.load(playerContextID)).is.undefined;
        expect(commands.remove(new PlayerContextImpl(playerContextID))).is.undefined;
        expect(commands.removeGame(playerContextID, gameID, false)).is.undefined;
        expect(commands.save(playerContextID, {} as any)).is.undefined;
        expect(commands.trackActive(playerContextID)).is.undefined;
    }
}
