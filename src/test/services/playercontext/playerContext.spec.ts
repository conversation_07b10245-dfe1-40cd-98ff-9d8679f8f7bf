import { suite, test } from "mocha-typescript";
import { PlayerContextImpl } from "../../../skywind/services/playercontext/playerContext";
import { expect } from "chai";
import { PlayerContextID } from "../../../skywind/services/contextIds";

@suite()
class PlayerContextSpec {

    @test()
    public testActiveAndBrokenGames() {
        const ctx = new PlayerContextImpl(PlayerContextID.createFromString("games:player:2"));

        ctx.activeGamesWithMode = [
            "games:context:1:player001:game001:web",
            "real",
            "games:context:1:player001:game002:web",
            "real",
            "games:context:1:player001:game003:mobile",
            "bns"
        ];

        ctx.brokenGamesWithMode = [
            "games:context:1:player001:games004:mobile",
            "real",
            "games:context:1:player001:game005:web",
            "bns"
        ];

        expect(ctx.activeGames).deep.equals([
            {
                id:
                    {
                        gameCode: "game001",
                        brandId: 1,
                        playerCode: "player001",
                        deviceId: "web",
                        idValue: "games:context:1:player001:game001:web",
                    },
                mode: "real"
            },
            {
                id:
                    {
                        gameCode: "game002",
                        brandId: 1,
                        playerCode: "player001",
                        deviceId: "web",
                        idValue: "games:context:1:player001:game002:web",
                    },
                mode: "real"
            },
            {
                id:
                    {
                        gameCode: "game003",
                        brandId: 1,
                        playerCode: "player001",
                        deviceId: "mobile",
                        idValue: "games:context:1:player001:game003:mobile"
                    },

                mode: "bns"
            }
        ]);
        expect(ctx.brokenGames).deep.equals([
            {
                id:
                    {
                        gameCode: "games004",
                        brandId: 1,
                        playerCode: "player001",
                        deviceId: "mobile",
                        idValue: "games:context:1:player001:games004:mobile",
                    },
                mode: "real"
            },
            {
                id:
                    {
                        gameCode: "game005",
                        brandId: 1,
                        playerCode: "player001",
                        deviceId: "web",
                        idValue: "games:context:1:player001:game005:web",
                    },
                mode: "bns"
            }
        ]);

        expect(ctx.keepOffline).is.true;
        expect(ctx.activeGamesWithMode).is.undefined;
        expect(ctx.brokenGamesWithMode).is.undefined;
    }

    @test()
    public testEmptyActiveAndBrokenGames() {
        const ctx = new PlayerContextImpl(PlayerContextID.createFromString("games:player:2"));

        ctx.activeGamesWithMode = null;

        ctx.brokenGamesWithMode = null;

        expect(ctx.activeGames).deep.equals([]);
        expect(ctx.brokenGames).deep.equals([]);

        expect(ctx.keepOffline).is.false;
        expect(ctx.activeGamesWithMode).is.undefined;
        expect(ctx.brokenGamesWithMode).is.undefined;
    }

    @test()
    public testFindGameHasGame() {
        const ctx = new PlayerContextImpl(PlayerContextID.createFromString("games:player:2"));

        ctx.activeGamesWithMode = [
            "games:context:1:player001:game001:web",
            "real",
            "games:context:1:player001:game002:web",
            "real",
            "games:context:1:player001:game003:mobile",
            "bns"
        ];

        ctx.brokenGamesWithMode = [
            "games:context:1:player001:game004:mobile",
            "real",
            "games:context:1:player001:game005:web",
            "bns"
        ];

        expect(ctx.hasGame({ active: true })).is.true;
        expect(ctx.hasGame({ active: true, gameMode: "real" })).is.true;
        expect(ctx.hasGame({ active: true, gameMode: "real", gameCode: "game002" })).is.true;
        expect(ctx.hasGame({ active: true, gameMode: "real", gameCode: "game002", deviceId: "web" })).is.true;
        expect(ctx.hasGame({ active: true, gameMode: "real", gameCode: "game004" })).is.false;
        expect(ctx.hasGame({ active: true, gameMode: "real", gameCode: "game002", deviceId: "mobile" })).is.false;
        expect(ctx.hasGame({ active: true, gameMode: "fun" })).is.false;

        expect(ctx.hasGame({ active: false })).is.true;
        expect(ctx.hasGame({ active: false, gameMode: "real" })).is.true;
        expect(ctx.hasGame({ active: false, gameMode: "real", gameCode: "game004", deviceId: "mobile" })).is.true;
        expect(ctx.hasGame({ active: false, gameMode: "real", gameCode: "game005" })).is.false;
        expect(ctx.hasGame({ active: false, gameMode: "real", gameCode: "game004", deviceId: "web" })).is.false;
        expect(ctx.hasGame({ active: false, gameMode: "fun" })).is.false;

        expect(ctx.hasGame({})).is.true;
        expect(ctx.hasGame({ gameMode: "real" })).is.true;
        expect(ctx.hasGame({ gameMode: "real", gameCode: "game002" })).is.true;
        expect(ctx.hasGame({ gameMode: "real", gameCode: "game009" })).is.false;
        expect(ctx.hasGame({ gameMode: "fun" })).is.false;

        expect(ctx.findGame({ active: true, gameMode: "real", gameCode: "game002" }).id.asString())
            .equal("games:context:1:player001:game002:web");
        expect(ctx.findGame({ active: true, gameMode: "real", gameCode: "game002", deviceId: "web" }).id.asString())
            .equal("games:context:1:player001:game002:web");
        expect(ctx.findGame({ active: false, gameMode: "bns", gameCode: "game005" }).id.asString())
            .equal("games:context:1:player001:game005:web");
        expect(ctx.findGame({ active: false, gameMode: "bns", gameCode: "game005", deviceId: "web" }).id.asString())
            .equal("games:context:1:player001:game005:web");

        expect(ctx.findGame({ gameCode: "game002" }).id.asString()).equal("games:context:1:player001:game002:web");
        expect(ctx.findGame({ gameMode: "bns", gameCode: "game005" }).id.asString())
            .equal("games:context:1:player001:game005:web");
        expect(ctx.findGame({ gameCode: "game005", deviceId: "web" }).id.asString())
            .equal("games:context:1:player001:game005:web");
    }
}
