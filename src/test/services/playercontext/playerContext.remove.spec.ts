import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { expect, should, use } from "chai";

import { SinonFakeTimers, useFakeTimers } from "sinon";
import { createGameToken, flushAll, TEST_MODULE_NAME } from "../../helper";
import { GameTokenData } from "../../../skywind/services/tokens";
import { GameContextID } from "../../../skywind/services/contextIds";
import { Currency, GameMode } from "@skywind-group/sw-game-core";
import { GameData } from "../../../skywind/services/auth";
import { GameSession } from "../../../skywind/services/gameSession";
import { suite, test } from "mocha-typescript";
import { ContextManager } from "../../../skywind/services/contextmanager/contextManager";
import config from "../../../skywind/config";
import { CleanupGameContextService } from "../../../skywind/services/cleanup/cleanupGameContextService";
import { CleanupPlayerContextService } from "../../../skywind/services/cleanup/cleanupPlayerContextService";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite()
class RemovePlayerContextSpec {
    private static currentClock: SinonFakeTimers;
    private contextManager: ContextManager = getGameFlowContextManager();
    private static prevExpireIn: number;

    public static before() {
        RemovePlayerContextSpec.currentClock = useFakeTimers();
    }

    public static after() {
        RemovePlayerContextSpec.currentClock.restore();
    }

    public after() {
        RemovePlayerContextSpec.currentClock.reset();
    }

    public async before() {
        return flushAll();
    }

    private async createContext(playerCode: string, brandId: number = 1,
                                mode: GameMode = "real", customGameData: Partial<GameData> = {}) {
        const currency: Currency = 0;
        const settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: currency,
            stakeAll: [1, 2, 3, 4],
            stakeDef: currency,
            stakeMax: currency,
            stakeMin: currency,
            winMax: currency,
            currencyMultiplier: 100,
        };

        const gameID: GameContextID = GameContextID.create("gameId", brandId, playerCode, "deviceId", mode);

        const gameData: GameData = {
            ...{
                gameTokenData: undefined,
                limits: settings,
            },
            ...customGameData
        };

        const gameTokenData: GameTokenData = {
            playerCode: gameID.playerCode,
            gameCode: "GM001",
            brandId: gameID.brandId,
            currency: "USD",
            playmode: mode
        };

        gameTokenData.token = await createGameToken(gameTokenData);
        gameData.gameTokenData = gameTokenData;
        const sessionId = await GameSession.generate(gameID, "real");

        const result = await this.contextManager.findOrCreateGameContext(gameID, sessionId, gameData, TEST_MODULE_NAME);
        await result.updatePendingModification({ operation: "payment" } as any, {}, {} as any, { type: "slot" } as any);
        return result;
    }

    @test("remove player context, optimistic failure ")
    public async removePlayerContextOptimisticFailure() {
        RemovePlayerContextSpec.currentClock.setSystemTime(1000);
        let context = await this.createContext("1");

        const gameContext = await this.contextManager.findGameContextById(GameContextID.create("gameId",
            1,
            "1",
            "deviceId",
            "real"));
        await gameContext.remove();

        const playerContext = context.playerContext;
        expect(await playerContext.remove(false)).to.be.false;
        let ctx = await this.contextManager.findPlayerContextById(playerContext.id);
        expect(ctx.id).deep.equal(context.playerContext.id);

        context = await this.createContext("1");
        expect(await ctx.remove(false)).to.be.false;

        ctx = await this.contextManager.findPlayerContextById(playerContext.id);
        expect(ctx.id).deep.equal(context.playerContext.id);
    }

    @test("remove player context, optimistic failure  after restore game context")
    public async removePlayerContextOptimisticFailure_AfterRestore() {
        RemovePlayerContextSpec.currentClock.setSystemTime(1000);
        let context = await this.createContext("1");
        const id = context.id;

        await new CleanupGameContextService().cleanUp([id.asString()], true);
        const cleanupPlayerContextService = new CleanupPlayerContextService();
        await cleanupPlayerContextService.cleanUp([id.playerContextID.asString()], true);

        context = await this.contextManager.findGameContextById(id);
        expect(context).is.undefined;
        let playerContext = await this.contextManager.findPlayerContextById(id.playerContextID);
        expect(playerContext).is.undefined;

        context = await this.contextManager.findOrRestoreGameContext(id);
        expect(context).is.not.undefined;
        expect(context.playerContext).is.not.undefined;

        playerContext = await this.contextManager.findPlayerContextById(id.playerContextID);
        await cleanupPlayerContextService.enqueueContexts(Number.MAX_SAFE_INTEGER, 10);
        expect(await playerContext.remove(false)).to.be.false;
        playerContext = await this.contextManager.findPlayerContextById(playerContext.id);
        expect(playerContext.id).deep.equal(id.playerContextID);
    }
}
