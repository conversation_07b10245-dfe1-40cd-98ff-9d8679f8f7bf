import { getGameFlowContextManager } from "../../skywind/services/contextmanager/contextManagerImpl";
import { expect, should, use } from "chai";

import { SinonFakeTimers, useFakeTimers } from "sinon";
import { activityList, activityListCount, createGameToken, flushAll, TEST_MODULE_NAME } from "../helper";
import { GameTokenData } from "../../skywind/services/tokens";
import { GameContextID } from "../../skywind/services/contextIds";
import { Currency, GameMode } from "@skywind-group/sw-game-core";
import { GameData } from "../../skywind/services/auth";
import { GameSession } from "../../skywind/services/gameSession";
import { ConcurrentAccessToGameSession } from "../../skywind/errors";
import { suite, test, timeout } from "mocha-typescript";
import { FunActivityTracker } from "../../skywind/services/activitytracker";
import { ContextManager } from "../../skywind/services/contextmanager/contextManager";
import config from "../../skywind/config";
import { GameFlowContext } from "../../skywind/services/context/gamecontext";
import { Redis } from "../../skywind/storage/redis";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

export class BaseActivitytrackerSpec {
    protected static currentClock: SinonFakeTimers;
    private static prevExpireIn: number;
    protected contextManager: ContextManager = getGameFlowContextManager();

    public static before() {
        BaseActivitytrackerSpec.prevExpireIn = config.cleanUp.expireIn;
        config.cleanUp.expireIn = config.cleanUp.expireIn;
        BaseActivitytrackerSpec.currentClock = useFakeTimers();
    }

    public static after() {
        config.cleanUp.expireIn = BaseActivitytrackerSpec.prevExpireIn;
        ActivitytrackerSpec.currentClock.restore();
    }

    public after() {
        BaseActivitytrackerSpec.currentClock.reset();
    }

    public async before() {
        return flushAll();
    }

    protected async createContext(playerCode: string, brandId: number = 1,
                                  mode: GameMode = "real", customGameData: Partial<GameData> = {}) {
        const currency: Currency = 0;
        const settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: currency,
            stakeAll: [1, 2, 3, 4],
            stakeDef: currency,
            stakeMax: currency,
            stakeMin: currency,
            winMax: currency,
            currencyMultiplier: 100,
        };

        const gameID: GameContextID = GameContextID.create("gameId", brandId, playerCode, "deviceId", mode);

        const gameData: GameData = {
            ...{
                gameTokenData: undefined,
                limits: settings,
            },
            ...customGameData
        };

        const gameTokenData: GameTokenData = {
            playerCode: gameID.playerCode,
            gameCode: "GM001",
            brandId: gameID.brandId,
            currency: "USD",
            playmode: mode
        };

        gameTokenData.token = await createGameToken(gameTokenData);
        gameData.gameTokenData = gameTokenData;
        const sessionId = await GameSession.generate(gameID, "real");

        return await this.contextManager.findOrCreateGameContext(gameID, sessionId, gameData, TEST_MODULE_NAME);
    }

    protected getTimeout(ts: number) {
        return ts + config.cleanUp.expireIn * 60000;
    }

    protected getPlayerTimeout(ts: number): number {
        return this.getTimeout(ts) + 10 * 60000;
    }

}

@suite("Activity tracker")
class ActivitytrackerSpec extends BaseActivitytrackerSpec {

    public static before() {
        return BaseActivitytrackerSpec.before();
    }

    public static after() {
        return BaseActivitytrackerSpec.after();
    }

    public after() {
        return super.after();
    }

    public async before() {
        return super.before();
    }

    @test("marks last game activity")
    public async marksLastGameActivity() {
        await this.createContext("1");
        await this.createContext("2");
        await this.createContext("3");
        await this.createContext("4");

        const contextIds: string[] = await this.contextManager.getLruGameContextIDs(Number.MAX_VALUE, 4);
        expect(contextIds).deep.equal([
            "games:context:1:1:gameId:deviceId",
            "games:context:1:2:gameId:deviceId",
            "games:context:1:3:gameId:deviceId",
            "games:context:1:4:gameId:deviceId"
        ]);

        const playerIds: string[] = await this.contextManager.getLruPlayerContextIDs(Number.MAX_VALUE, 4);
        expect(playerIds).deep.equal([
            "games:player:1:1",
            "games:player:1:2",
            "games:player:1:3",
            "games:player:1:4"
        ]);
    }

    @test("remove game last activity")
    public async removeGameLastActivity() {
        ActivitytrackerSpec.currentClock.setSystemTime(1000);
        const context = await this.createContext("1");

        await this.contextManager.removeGameContextActivity(context.id.asString(), 1000);
        expect(await activityListCount()).equal(0);
    }

    @test("remove game context")
    public async removeGameContext() {
        ActivitytrackerSpec.currentClock.setSystemTime(1000);
        const context = await this.createContext("1");

        await context.remove();
        expect(await activityListCount()).equal(0);
    }

    @test("remove player context")
    public async removePlayerContext() {
        ActivitytrackerSpec.currentClock.setSystemTime(1000);
        const context = await this.createContext("1");

        expect(await context.playerContext.remove(false)).to.be.false;
        let playerIds: string[] = await this.contextManager.getLruPlayerContextIDs(Number.MAX_VALUE, 4);
        expect(playerIds).deep.equal(["games:player:1:1"]);

        expect(await context.playerContext.remove(true)).to.be.true;
        playerIds = await this.contextManager.getLruPlayerContextIDs(Number.MAX_VALUE, 4);
        expect(playerIds).deep.equal([]);
    }

    @test("remove game last activity failed because of optimistic failure")
    public async removeGameActivityOptimisticFailure() {
        ActivitytrackerSpec.currentClock.setSystemTime(1000);
        const context = await this.createContext("1");

        ActivitytrackerSpec.currentClock.setSystemTime(2000);

        await context.update({ type: "somedata" });

        const ctx = await this.contextManager.findGameContextById(context.id);

        await expect(ctx.remove(false)).rejectedWith(ConcurrentAccessToGameSession);
        expect(await activityListCount()).equal(1);
    }

    @test("procures least recently used game contexts")
    public async processLeastRecentlyUsedGameContexts() {
        ActivitytrackerSpec.currentClock.setSystemTime(10);
        await this.createContext("1");
        ActivitytrackerSpec.currentClock.setSystemTime(20);
        await this.createContext("2");
        ActivitytrackerSpec.currentClock.setSystemTime(30);
        await this.createContext("3");
        ActivitytrackerSpec.currentClock.setSystemTime(40);
        await this.createContext("4");

        let contextIds: string[] = await this.contextManager.getLruGameContextIDs(this.getTimeout(10), 1);
        let playerContextIds: string[] = await this.contextManager.getLruPlayerContextIDs(this.getPlayerTimeout(10), 1);

        expect(contextIds).deep.equal(["games:context:1:1:gameId:deviceId"]);
        expect(playerContextIds).deep.equal(["games:player:1:1"]);

        contextIds = await this.contextManager.getLruGameContextIDs(this.getTimeout(20), 1);
        playerContextIds = await this.contextManager.getLruPlayerContextIDs(this.getPlayerTimeout(20), 1);
        expect(contextIds).deep.equal(["games:context:1:1:gameId:deviceId"]);
        expect(playerContextIds).deep.equal(["games:player:1:1"]);

        contextIds = await this.contextManager.getLruGameContextIDs(this.getTimeout(20), 2);
        playerContextIds = await this.contextManager.getLruPlayerContextIDs(this.getPlayerTimeout(20), 2);
        expect(contextIds).deep.equal(["games:context:1:1:gameId:deviceId", "games:context:1:2:gameId:deviceId"]);
        expect(playerContextIds).deep.equal(["games:player:1:1", "games:player:1:2"]);

        contextIds = await this.contextManager.getLruGameContextIDs(this.getTimeout(30), 3);
        playerContextIds = await this.contextManager.getLruPlayerContextIDs(this.getPlayerTimeout(30), 3);
        expect(contextIds)
            .deep
            .equal([
                "games:context:1:1:gameId:deviceId",
                "games:context:1:2:gameId:deviceId",
                "games:context:1:3:gameId:deviceId"
            ]);

        expect(playerContextIds)
            .deep
            .equal([
                "games:player:1:1",
                "games:player:1:2",
                "games:player:1:3"
            ]);

        contextIds = await this.contextManager.getLruGameContextIDs(this.getTimeout(100), 100);
        playerContextIds = await this.contextManager.getLruPlayerContextIDs(this.getPlayerTimeout(100), 100);
        expect(contextIds).deep.equal([
            "games:context:1:1:gameId:deviceId",
            "games:context:1:2:gameId:deviceId",
            "games:context:1:3:gameId:deviceId",
            "games:context:1:4:gameId:deviceId"
        ]);
        expect(playerContextIds).deep.equal([
            "games:player:1:1",
            "games:player:1:2",
            "games:player:1:3",
            "games:player:1:4"
        ]);
    }

    @test("removes game activity by id")
    public async removesGameActivityById() {
        ActivitytrackerSpec.currentClock.setSystemTime(10);
        const context1 = await this.createContext("1");
        ActivitytrackerSpec.currentClock.setSystemTime(20);
        await this.createContext("2");
        ActivitytrackerSpec.currentClock.setSystemTime(30);
        const context3 = await this.createContext("3");
        ActivitytrackerSpec.currentClock.setSystemTime(40);
        const context4 = await this.createContext("4");
        ActivitytrackerSpec.currentClock.setSystemTime(50);
        const context5 = await this.createContext("5");

        let ids: string[] = await this.contextManager.getLruGameContextIDs(this.getTimeout(100), 100);
        expect(ids).deep.equal([
            "games:context:1:1:gameId:deviceId",
            "games:context:1:2:gameId:deviceId",
            "games:context:1:3:gameId:deviceId",
            "games:context:1:4:gameId:deviceId",
            "games:context:1:5:gameId:deviceId",
        ]);

        await context3.remove();
        ids = await this.contextManager.getLruGameContextIDs(this.getTimeout(100), 100);
        expect(ids).deep.equal([
            "games:context:1:1:gameId:deviceId",
            "games:context:1:2:gameId:deviceId",
            "games:context:1:4:gameId:deviceId",
            "games:context:1:5:gameId:deviceId"
        ]);
        await context1.remove();
        await context5.remove();
        ids = await this.contextManager.getLruGameContextIDs(this.getTimeout(100), 100);
        expect(ids).deep.equal([
            "games:context:1:2:gameId:deviceId",
            "games:context:1:4:gameId:deviceId"
        ]);
    }

    @test("removes player activity by id")
    public async removesPlayerActivityById() {
        ActivitytrackerSpec.currentClock.setSystemTime(10);
        const context1 = await this.createContext("1");
        ActivitytrackerSpec.currentClock.setSystemTime(20);
        await this.createContext("2");
        ActivitytrackerSpec.currentClock.setSystemTime(30);
        const context3 = await this.createContext("3");
        ActivitytrackerSpec.currentClock.setSystemTime(40);
        const context4 = await this.createContext("4");
        ActivitytrackerSpec.currentClock.setSystemTime(50);
        const context5 = await this.createContext("5");

        let ids: string[] = await this.contextManager.getLruPlayerContextIDs(this.getPlayerTimeout(100), 100);
        expect(ids).deep.equal([
            "games:player:1:1",
            "games:player:1:2",
            "games:player:1:3",
            "games:player:1:4",
            "games:player:1:5",
        ]);

        await context3.playerContext.remove(true);
        ids = await this.contextManager.getLruPlayerContextIDs(this.getPlayerTimeout(100), 100);
        expect(ids).deep.equal([
            "games:player:1:1",
            "games:player:1:2",
            "games:player:1:4",
            "games:player:1:5"
        ]);
        await context1.playerContext.remove(true);
        await context5.playerContext.remove(true);
        ids = await this.contextManager.getLruPlayerContextIDs(this.getPlayerTimeout(100), 100);
        expect(ids).deep.equal([
            "games:player:1:2",
            "games:player:1:4"
        ]);
    }

    @test("can remarks activity after removal")
    public async canRemarksActivityAfterRemoval() {
        ActivitytrackerSpec.currentClock.setSystemTime(10);
        await this.createContext("1");
        ActivitytrackerSpec.currentClock.setSystemTime(20);
        await this.createContext("2");
        ActivitytrackerSpec.currentClock.setSystemTime(30);
        const context3 = await this.createContext("3");
        ActivitytrackerSpec.currentClock.setSystemTime(40);
        await this.createContext("4");
        ActivitytrackerSpec.currentClock.setSystemTime(50);
        await this.createContext("5");

        let ids: string[] = await this.contextManager.getLruGameContextIDs(this.getTimeout(100), 100);
        expect(ids).deep.equal([
            "games:context:1:1:gameId:deviceId",
            "games:context:1:2:gameId:deviceId",
            "games:context:1:3:gameId:deviceId",
            "games:context:1:4:gameId:deviceId",
            "games:context:1:5:gameId:deviceId"
        ]);

        await context3.remove();
        ids = await this.contextManager.getLruGameContextIDs(this.getTimeout(100), 100);
        expect(ids).deep.equal([
            "games:context:1:1:gameId:deviceId",
            "games:context:1:2:gameId:deviceId",
            "games:context:1:4:gameId:deviceId",
            "games:context:1:5:gameId:deviceId"
        ]);

        ActivitytrackerSpec.currentClock.setSystemTime(80);
        await this.createContext("3");
        ids = await this.contextManager.getLruGameContextIDs(this.getTimeout(100), 100);
        expect(ids).deep.equal([
            "games:context:1:1:gameId:deviceId",
            "games:context:1:2:gameId:deviceId",
            "games:context:1:4:gameId:deviceId",
            "games:context:1:5:gameId:deviceId",
            "games:context:1:3:gameId:deviceId",
        ]);
    }

    @test("finds active games by brand, check performance")
    @timeout(65000)
    public async findsTopActiveGamesForBrand_Perf() {
        let client = await Redis.get().get();
        try {
            const multi = client.multi();
            for (let i = 0; i < 100000; i++) {
                const gameID: GameContextID = GameContextID.create("gameId", 1, i.toString(), "deviceId", "real");
                multi.zadd(config.namespaces.lastGameActivityKey + "-lexi", "0", gameID.asString());
            }

            await multi.exec();
        } finally {
            await Redis.get().release(client);
        }

        client = await Redis.get().get();
        try {
            const multi = client.multi();
            for (let i = 0; i < 5; i++) {
                const gameID: GameContextID = GameContextID.create("gameId", 2, i.toString(), "deviceId", "real");
                multi.zadd(config.namespaces.lastGameActivityKey + "-lexi", "0", gameID.asString());
            }
            await multi.exec();
        } finally {
            await Redis.get().release(client);
        }

        client = await Redis.get().get();
        try {
            const multi = client.multi();
            for (let i = 0; i < 100000; i++) {
                const gameID: GameContextID = GameContextID.create("gameId", 1, i.toString(), "deviceId", "bns");
                multi.zadd(config.namespaces.lastGameActivityKey + "-lexi", "0", gameID.asString());
            }

            await multi.exec();
        } finally {
            await Redis.get().release(client);
        }

        client = await Redis.get().get();
        try {
            const multi = client.multi();

            for (let i = 0; i < 5; i++) {
                const gameID: GameContextID = GameContextID.create("gameId", 2, i.toString(), "deviceId", "bns");
                multi.zadd(config.namespaces.lastGameActivityKey + "-lexi", "0", gameID.asString());
            }

            await multi.exec();
        } finally {
            await Redis.get().release(client);
        }

        expect((await toArray(this.contextManager.findTopActiveGameContextIDsByBrand(2, 100))).sort()).deep.equal([
            "games:context:2:0:gameId:deviceId",
            "games:context:2:1:gameId:deviceId",
            "games:context:2:2:gameId:deviceId",
            "games:context:2:3:gameId:deviceId",
            "games:context:2:4:gameId:deviceId",
            "games:bns:2:0:gameId:deviceId",
            "games:bns:2:1:gameId:deviceId",
            "games:bns:2:2:gameId:deviceId",
            "games:bns:2:3:gameId:deviceId",
            "games:bns:2:4:gameId:deviceId",
        ].sort());

        expect((await toArray(this.contextManager.findTopActiveGameContextIDsByBrand(1, 1000000))).length)
            .equals(200000);
    }

    @test("finds active games by brand")
    @timeout(65000)
    public async findsTopActiveGamesForBrand() {
        for (let i = 0; i < 1000; i++) {
            await this.createContext(i.toString(), 1);
        }

        for (let i = 0; i < 5; i++) {
            await this.createContext(i.toString(), 2, "real");
        }

        for (let i = 0; i < 5; i++) {
            await this.createContext(i.toString(), 20, "real");
        }

        for (let i = 0; i < 5; i++) {
            await this.createContext(i.toString(), 29, "real");
        }

        for (let i = 0; i < 100; i++) {
            await this.createContext(i.toString(), 1, "bns");
        }

        for (let i = 0; i < 5; i++) {
            await this.createContext(i.toString(), 2, "bns");
        }

        for (let i = 0; i < 5; i++) {
            await this.createContext(i.toString(), 20, "bns");
        }

        for (let i = 0; i < 5; i++) {
            await this.createContext(i.toString(), 29, "bns");
        }

        const ids: string[] = await toArray(this.contextManager.findTopActiveGameContextIDsByBrand(2, 100));
        expect(ids.sort()).deep.equal([
            "games:context:2:0:gameId:deviceId",
            "games:context:2:1:gameId:deviceId",
            "games:context:2:2:gameId:deviceId",
            "games:context:2:3:gameId:deviceId",
            "games:context:2:4:gameId:deviceId",
            "games:bns:2:0:gameId:deviceId",
            "games:bns:2:1:gameId:deviceId",
            "games:bns:2:2:gameId:deviceId",
            "games:bns:2:3:gameId:deviceId",
            "games:bns:2:4:gameId:deviceId",
        ].sort());

        expect((await toArray(this.contextManager.findTopActiveGameContextIDsByBrand(1, 1000000))).length)
            .equals(1100);
    }

    @test("finds active players by brand")
    @timeout(15000)
    public async findsTopActivePlayersForBrand() {
        for (let i = 0; i < 1000; i++) {
            await this.createContext(i.toString(), 1);
        }

        for (let i = 0; i < 5; i++) {
            await this.createContext(i.toString(), 2, "real");
        }

        for (let i = 0; i < 5; i++) {
            await this.createContext(i.toString(), 20, "real");
        }

        for (let i = 0; i < 5; i++) {
            await this.createContext(i.toString(), 20, "real");
        }

        for (let i = 0; i < 100; i++) {
            await this.createContext(i.toString(), 1, "bns");
        }

        for (let i = 0; i < 5; i++) {
            await this.createContext(i.toString(), 2, "bns");
        }

        for (let i = 0; i < 5; i++) {
            await this.createContext(i.toString(), 20, "bns");
        }

        for (let i = 0; i < 5; i++) {
            await this.createContext(i.toString(), 29, "bns");
        }

        expect((await toArray(this.contextManager.findTopActivePlayerContextIDsByBrand(2, 100))).sort()).deep.equal([
            "games:player:2:0",
            "games:player:2:1",
            "games:player:2:2",
            "games:player:2:3",
            "games:player:2:4",
        ].sort());

        expect((await toArray(this.contextManager.findTopActivePlayerContextIDsByBrand(20, 100))).sort()).deep.equal([
            "games:player:20:0",
            "games:player:20:1",
            "games:player:20:2",
            "games:player:20:3",
            "games:player:20:4",
        ].sort());

        expect((await toArray(this.contextManager.findTopActivePlayerContextIDsByBrand(29, 100))).sort()).deep.equal([
            "games:player:29:0",
            "games:player:29:1",
            "games:player:29:2",
            "games:player:29:3",
            "games:player:29:4",
        ].sort());

        expect((await toArray(this.contextManager.findTopActiveGameContextIDsByBrand(1, 2000))).length).equals(1100);
    }

    @test("unsupported for fun")
    public async testFunActivityTracker() {
        const tracker = new FunActivityTracker();
        expect(await tracker.findTopActiveGameContextIDsByBrand(1, 1)).is.undefined;
        expect(await tracker.findTopActivePlayerContextIDsByBrand(1, 1)).is.undefined;
        await expect(tracker.getLruGameContextIDs(1, 1)).rejectedWith(Error);
        await expect(tracker.getLruPlayerContextIDs(1, 1)).rejectedWith(Error);
        await expect(tracker.removeGameContextActivity("test", 1)).rejectedWith(Error);
    }

    @test("re-activate game context")
    public async reactivateContext() {
        ActivitytrackerSpec.currentClock.setSystemTime(1000);
        const context: GameFlowContext = await this.createContext("1");
        expect(await activityList()).deep.equal([
            "games:context:1:1:gameId:deviceId",
            "1801000"
        ]);
        await this.contextManager.removeGameContextActivity(context.id.asString(), 1000);
        expect(await activityList()).deep.equal([]);
        await context.incrementRetryAttempts();
        await context.activateNextRetryAttempt();
        expect(await activityList()).deep.equal([
            "games:context:1:1:gameId:deviceId",
            "121000"
        ]);
        await context.incrementRetryAttempts();
        await context.activateNextRetryAttempt();
        expect(await activityList()).deep.equal([
            "games:context:1:1:gameId:deviceId",
            "241000"
        ]);
    }
}

async function toArray<T extends { asString(): string; }>(result: Promise<T[]>): Promise<string[]> {
    return await result.then(r => r.map(id => id.asString()));
}
