import { expect, should, use } from "chai";
import * as Errors from "../../skywind/errors";
import * as jwt from "jsonwebtoken";
import {
    initMerchantGame,
    PlayerGameURLInfo,
    getService as getPlayerGameInfoService
} from "../../skywind/services/playerGameInfo";
import { testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;
import status200 = testing.status200;

should();
use(require("chai-as-promised"));

describe("Player Game Info", () => {
    let startToken: string;
    let request: RequestMock;

    const startTokenData = {
        playerCode: "PL001",
        gameCode: "test_slot",
        providerGameCode: "test_slot",
        brandId: 1,
    };

    after(() => {
        request.unmock(superagent);
    });

    before(async () => {
        request = requestMock(superagent);
        startToken = await jwt.sign(startTokenData, "SOMESECRETKEY");
    });

    afterEach(() => {
        request.clearRoutes();
    });

    describe("Merchant's player", () => {
        it("Inits merchant game", async () => {

            const response = {
                url: "http://game.io",
                token: "token_xxx",
            };

            request.post("http://game-auth-api:3007//v1/merchants/game/url", status200(response));

            const urlInfo: PlayerGameURLInfo = await initMerchantGame("ipm", {
                merchantCode: "mid",
                gameCode: "test_game",
                ip: "127.0.0.1",
                ticket: "ipm_ticket",
            });

            expect(urlInfo).deep.equal(response);
        });

        it("Fails to init game for merchant w/t merchantCode", async () => {
            await initMerchantGame("ipm", {})
                .should.be.rejectedWith(Errors.MerchantGameInitError);
        });

        it("Fails to init game for merchant w/t gameCode", async () => {
            await initMerchantGame("ipm", { merchantCode: "code" })
                .should.be.rejectedWith(Errors.MerchantGameInitError);
        });
    });

    describe("Player Game info", async () => {

        it("Get new game url and token", async () => {
            const response = {
                "token": "test",
                "url": "http://test.com"
            };

            request.post("http://game-auth-api:3007//v1/game/url", status200(response));

            const result = await getPlayerGameInfoService().getPlayerGameURLInfo({
                request: "choose-game",
                startGameToken: startToken,
                gameId: "sw_ff",
                playmode: "real",
                requestId: 0
            });

            expect(result).to.deep.equal(response);
        });

        it("Get available games", async () => {
            const response = {
                "games": ["test"]
            };

            request.post("http://game-auth-api:3007//v1/games", status200(response));

            const result = await getPlayerGameInfoService().getAvailableGames({
                request: "get-games",
                startGameToken: startToken,
                requestId: 0
            });

            expect(result).to.deep.equal(response);
        });
    });

});
