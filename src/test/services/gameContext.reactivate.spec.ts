import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { BaseGameContextSpec } from "./gameContext.spec";
import { GameFlowContext } from "../../skywind/services/context/gamecontext";
import { activityList, TEST_MODULE_NAME } from "../helper";
import { CleanupGameContextService } from "../../skywind/services/cleanup/cleanupGameContextService";

@suite()
class GameContextAttemptsSpec extends BaseGameContextSpec {
    protected service: CleanupGameContextService = new CleanupGameContextService();

    @test()
    public async restReactivate() {
        let context: GameFlowContext = await this.contextManager
            .findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);

        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);
        expect(context.playerContext.brokenGames).deep.equal([]);

        await this.service.enqueueContexts(Number.MAX_SAFE_INTEGER, 1000);
        expect(await activityList((false))).deep.equal([]);
        await context.reactivate();
        expect(await activityList((false))).deep.equal([this.gameID.asString()]);
        context = await this.contextManager.findGameContextById(this.gameID, false);

        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);
        expect(context.playerContext.brokenGames).deep.equal([]);
    }

}
