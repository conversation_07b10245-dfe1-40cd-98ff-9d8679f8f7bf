import { expect, use } from "chai";
import { GameHistoryQueue } from "../../skywind/services/queue/redisQueue";
import { Redis } from "../../skywind/storage/redis";
import { flushAll, getGameHistory, getRange } from "../helper";
import { GameFlowContextImpl } from "../../skywind/services/context/gameContextImpl";
import { GameContextID } from "../../skywind/services/contextIds";
import { GameEventHistory } from "../../skywind/history/history";
import { sleep } from "@skywind-group/sw-utils";

use(require("chai-as-promised"));

describe("GameHistoryQueue", () => {

    const data: any = {
        gameId: "test",
        brandId: 1,
        type: "slot",
        version: 1,
        playerCode: "player0001",
        deviceId: "web",
        gameCode: "sw_game",
        roundEnded: true,
        walletTransactionId: "trx_id",
        eventId: 1,
        currency: "USD",
        win: 1,
        bet: 5,
        roundId: 1,
        ts: new Date().toISOString(),
        result: {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: { value: "ANYVALUE" },
        },
    };

    const saveData = async (queue: GameHistoryQueue) => {
        const redisClient = await Redis.get().get();
        const trx = redisClient.multi();
        try {

            queue.save(trx, data);

            await new Promise<void>((resolve, reject) => {
                trx.exec((err, result) => {
                    if (err) {
                        return reject(err);
                    }
                    return resolve(undefined);
                });
            });
        } finally {
            await Redis.get().release(redisClient);
        }
    };

    beforeEach(() => {
        return flushAll();
    });

    it("pop, save, remove", async () => {
        const queue = new GameHistoryQueue();
        await saveData(queue);

        const history = await getGameHistory(0, 1000);
        expect(history).deep.equal([data]);

        const value = await queue.pop(1);
        expect(value.map(i => i.data)).deep.equal([data]);

        const history1 = await getGameHistory(0, 1000);
        expect(history1).deep.equal([]);

        const workers = await getRange(queue.workerList, 0, 1000);
        expect(workers).deep.equal([data]);

        await queue.commit();

        const workers1 = await getRange(queue.workerList, 0, 1000);
        expect(workers1).deep.equal([]);

    });

    it("pop with waiting for", async function() {
        this.timeout(10000);
        const queue = new GameHistoryQueue();

        const history = await getGameHistory(0, 1000);
        expect(history).deep.equal([]);

        const promise = queue.pop(1);
        await sleep(600);
        await saveData(queue);

        const value = await promise;

        expect(value.map(i => i.data)).deep.equal([data]);

        const history1 = await getGameHistory(0, 1000);
        expect(history1).deep.equal([]);
        const workers = await getRange(queue.workerList, 0, 1000);
        expect(workers).deep.equal([data]);
    });

    it("illegal state", async () => {
        const queue = new GameHistoryQueue();
        await saveData(queue);

        expect(await getGameHistory(0, 1000)).deep.equal([data]);

        const value = await queue.pop(1);
        expect(value.map(i => i.data)).deep.equal([data]);
        await expect(queue.pop()).to.be.rejectedWith(Error);
        await expect(queue.pop()).to.be.rejectedWith(Error);

        expect(await getGameHistory(0, 1000)).deep.equal([]);

        expect(await getRange(queue.workerList, 0, 1000)).deep.equal([data]);

        await queue.repair();

        expect(await getRange(queue.workerList, 0, 1000)).deep.equal([]);
        expect(await getGameHistory(0, 1000)).deep.equal([data]);

        await queue.pop();
        await queue.commit();
        expect(await getRange(queue.workerList, 0, 1000)).deep.equal([]);
        expect(await getGameHistory(0, 1000)).deep.equal([]);
    });

    it("empty commit", async () => {
        const queue = new GameHistoryQueue();
        await saveData(queue);
        await queue.commit();

        expect(await getGameHistory(0, 1000)).deep.equal([data]);
        expect(await getRange(queue.workerList, 0, 1000)).deep.equal([]);

        const value = await queue.pop(1);
        expect(value.map(i => i.data)).deep.equal([data]);
        await queue.commit();

        expect(await getRange(queue.workerList, 0, 1000)).deep.equal([]);
        expect(await getGameHistory(0, 1000)).deep.equal([]);
    });

    it("spin pop", async function() {
        this.timeout(10000);
        const queue = new GameHistoryQueue();

        const history = await getGameHistory(0, 1000);
        expect(history).deep.equal([]);

        const promise = queue.pop(100);
        await sleep(100);
        await saveData(queue);

        const value = await promise;

        expect(value.map(i => i.data)).deep.equal([data]);

        const history1 = await getGameHistory(0, 1000);
        expect(history1).deep.equal([]);
        const workers = await getRange(queue.workerList, 0, 1000);
        expect(workers).deep.equal([data]);
    });

    it("repairs old orphan workers", async () => {
        const queue = new GameHistoryQueue();
        await saveData(queue);
        const history = await getGameHistory(0, 1000);
        expect(history).deep.equal([data]);

        const value = await queue.pop(1);
        expect(value.map(i => i.data)).deep.equal([data]);

        const history1 = await getGameHistory(0, 1000);
        expect(history1).deep.equal([]);

        const newQueue = new GameHistoryQueue();
        await newQueue.tryToRepairOrphanWorkers(Date.now());

        const history2 = await getGameHistory(0, 1000);
        expect(history2).deep.equal([data]);
        const workers1 = await getRange(queue.workerList, 0, 1000);
        expect(workers1).deep.equal([]);
    });
});

describe("Spin history in round", () => {
    const context: GameFlowContextImpl = new GameFlowContextImpl(GameContextID.createFromString(
        "sw-slot-engine:context:2:playerId1:gameId:deviceId"));
    let roundId: string = "0";

    beforeEach(() => {
        roundId = (+roundId + 1).toString();
        context.beginNewRound(roundId);
    });

    it("1 spin in round, correct balance before / after", async () => {

        const spin1: GameEventHistory = {
            win: 0,
            bet: 1,
            balanceBefore: 100,
            balanceAfter: 99,
            ts: new Date(1),
            currency: "USD"
        } as GameEventHistory;

        await context.updateRoundStatistic(spin1);
        context.roundEnded = true;

        expect(context.round).deep.equal({
            "balanceAfter": 99,
            "balanceBefore": 100,
            "startedAt": new Date(1),
            "totalBet": 1,
            "totalEvents": 1,
            "totalWin": 0,
            "totalJpContribution": 0,
            "totalJpWin": 0,
            currentBet: 1,
            betsCount: 1
        });
    });

    it("2 spins in round, correct balance before / after", async () => {

        const spin1: GameEventHistory = {
            win: 0,
            bet: 1,
            balanceBefore: 100,
            balanceAfter: undefined,
            ts: new Date(1),
            currency: "USD"
        } as GameEventHistory;

        const spin2: GameEventHistory = {
            win: 0,
            bet: 1,
            balanceBefore: undefined,
            balanceAfter: 98,
            ts: new Date(1),
            currency: "USD"
        } as GameEventHistory;

        await context.updateRoundStatistic(spin1);
        await context.updateRoundStatistic(spin2);
        context.roundEnded = true;

        expect(context.round).deep.equal({
            "balanceAfter": 98,
            "balanceBefore": 100,
            "startedAt": new Date(1),
            "totalBet": 2,
            "totalEvents": 2,
            "totalWin": 0,
            "totalJpContribution": 0,
            "totalJpWin": 0,
            currentBet: 1,
            betsCount: 2
        });
    });

    it("2 spins in round, correct balance after, incorrect first balanceBefore", async () => {

        const spin1: GameEventHistory = {
            win: 0,
            bet: 1,
            balanceBefore: undefined,
            balanceAfter: undefined,
            ts: new Date(1),
            currency: "USD"
        } as GameEventHistory;

        const spin2: GameEventHistory = {
            win: 0,
            bet: 1,
            balanceBefore: 100,
            balanceAfter: 98,
            ts: new Date(1),
            currency: "USD"
        } as GameEventHistory;

        await context.updateRoundStatistic(spin1);
        await context.updateRoundStatistic(spin2);
        context.roundEnded = true;

        expect(context.round).deep.equal({
            "balanceAfter": 98,
            "balanceBefore": 100,
            "startedAt": new Date(1),
            "totalBet": 2,
            "totalEvents": 2,
            "totalWin": 0,
            "totalJpContribution": 0,
            "totalJpWin": 0,
            currentBet: 1,
            betsCount: 2
        });
    });

    it("3 spins in round, correct balance before, incorrect last balance after", async () => {

        const spin1: GameEventHistory = {
            win: 0,
            bet: 1,
            balanceBefore: 100,
            balanceAfter: undefined,
            ts: new Date(1),
            currency: "USD"
        } as GameEventHistory;

        const spin2: GameEventHistory = {
            win: 0,
            bet: 1,
            balanceBefore: undefined,
            balanceAfter: 98,
            ts: new Date(1),
            currency: "USD"
        } as GameEventHistory;

        const spin3: GameEventHistory = {
            win: 0,
            bet: 1,
            balanceBefore: undefined,
            balanceAfter: undefined,
            ts: new Date(1),
            currency: "USD"
        } as GameEventHistory;

        await context.updateRoundStatistic(spin1);
        await context.updateRoundStatistic(spin2);
        await context.updateRoundStatistic(spin3);
        context.roundEnded = true;

        expect(context.round).deep.equal({
            "balanceAfter": 98,
            "balanceBefore": 100,
            "startedAt": new Date(1),
            "totalBet": 3,
            "totalEvents": 3,
            "totalWin": 0,
            "totalJpContribution": 0,
            "totalJpWin": 0,
            currentBet: 1,
            betsCount: 3
        });
    });

    it("3 spins in round, correct balance before / after, incorrect balance before in middle", async () => {

        const spin1: GameEventHistory = {
            win: 0,
            bet: 1,
            balanceBefore: 100,
            balanceAfter: undefined,
            ts: new Date(1),
            currency: "USD"
        } as GameEventHistory;

        const spin2: GameEventHistory = {
            win: 0,
            bet: 1,
            balanceBefore: 666,
            balanceAfter: 98,
            ts: new Date(1),
            currency: "USD"
        } as GameEventHistory;

        const spin3: GameEventHistory = {
            win: 0,
            bet: 1,
            balanceBefore: undefined,
            balanceAfter: undefined,
            ts: new Date(1),
            currency: "USD"
        } as GameEventHistory;

        await context.updateRoundStatistic(spin1);
        await context.updateRoundStatistic(spin2);
        await context.updateRoundStatistic(spin3);
        context.roundEnded = true;

        expect(context.round).deep.equal({
            "balanceAfter": 98,
            "balanceBefore": 100,
            "startedAt": new Date(1),
            "totalBet": 3,
            "totalEvents": 3,
            "totalWin": 0,
            "totalJpContribution": 0,
            "totalJpWin": 0,
            currentBet: 1,
            betsCount: 3
        });
    });

    it("2 spins in round, correct total win / bet", async () => {

        const spin1: GameEventHistory = {
            win: 3.6,
            bet: 0.18,
            balanceBefore: 100,
            balanceAfter: undefined,
            ts: new Date(1),
            currency: "USD"
        } as GameEventHistory;

        const spin2: GameEventHistory = {
            win: 0.18,
            bet: 1.44,
            balanceBefore: undefined,
            balanceAfter: 98,
            ts: new Date(1),
            currency: "USD"
        } as GameEventHistory;

        await context.updateRoundStatistic(spin1);
        await context.updateRoundStatistic(spin2);
        context.roundEnded = true;

        expect(context.round).deep.equal({
            "balanceAfter": 98,
            "balanceBefore": 100,
            "startedAt": new Date(1),
            "totalBet": 1.62,
            "totalEvents": 2,
            "totalWin": 3.78,
            "totalJpContribution": 0,
            "totalJpWin": 0,
            currentBet: 1.44,
            betsCount: 2
        });
    });
});
