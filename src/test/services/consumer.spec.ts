import { expect, should, use } from "chai";
import { flushAll, TEST_MODULE_NAME } from "../helper";
import { getOldRoundHistoryModels } from "../../skywind/history/model/oldRoundHistory";
import { getGameHistoryModels } from "../../skywind/history/model/gameHistory";
import { GameHistoryConsumer } from "../../skywind/history/consumers/gameHistoryConsumer";
import { RoundHistoryConsumer } from "../../skywind/history/consumers/roundHistoryConsumer";
import { AloomaHistoryConsumer } from "../../skywind/history/consumers/aloomaHistoryConsumer";
import { getSessionHistoryModels } from "../../skywind/history/model/sessionHistory";
import { SessionHistoryConsumer } from "../../skywind/history/consumers/sessionHistoryConsumer";
import {
    asEventHistory, createSessionHistory, GameEventHistory, SessionHistory,
    SessionInterruptionReason
} from "../../skywind/history/history";
import config from "../../skywind/config";
import { createGameHistoryConsumers } from "../../skywind/history/consumers/consumersFactory";
import { testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import { getRoundsHistoryModels } from "../../skywind/history/model/roundHistory";
import * as _ from "lodash";
import { CompositeConsumer } from "../../skywind/history/unloadHistory";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;
import status200 = testing.status200;
import onCall = testing.onCall;
import status500 = testing.status500;

should();
use(require("chai-as-promised"));

describe("Consumers", () => {

    const eventTemplate: any = {
        gameId: "test",
        brandId: 1,
        type: "slot",
        gameVersion: TEST_MODULE_NAME.version,
        playerCode: "player0001",
        deviceId: "web",
        gameCode: "sw_game",
        roundEnded: true,
        walletTransactionId: "trx_id",
        eventId: 1,
        sessionId: "1",
        currency: "USD",
        win: "1",
        bet: "5",
        balanceBefore: "10",
        balanceAfter: "6",
        roundId: "1",
        ts: new Date(),
        result: {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: { value: "ANYVALUE" },
        },
        test: false,
        totalJpContribution: null,
        totalJpWin: null,
        extraData: null,
        credit: "1",
        debit: null,
        ctrl: 255,
        freeBetCoin: null,
        lobbySessionId: null,
        isHidden: null
    };

    const roundTemplate = {
        id: "1",
        sessionId: "1",
        gameId: "sw_game",
        brandId: 1,
        playerCode: "player001",
        deviceId: "web",
        gameCode: "sw_game_001",
        currency: "USD",
        test: false,
        totalBet: "100",
        totalWin: "20",
        recoveryType: null,
        totalEvents: "10",
        balanceBefore: "200",
        balanceAfter: "120",
        startedAt: new Date(),
        finishedAt: new Date(),
        totalJpContribution: null,
        totalJpWin: null,
        credit: null,
        debit: "100",
        ctrl: 255,
        extraData: null,
        operatorSiteId: 1
    };

    const sessionTemplate: SessionHistory = {
        sessionId: "1",
        gameId: "sw_game",
        brandId: 1,
        playerCode: "player001",
        deviceId: "web",
        gameCode: "sw_game_001",
        gameVersion: "0.1.0",
        currency: "USD",
        country: "US",
        language: "EN",
        browser: "Chrome",
        os: "Linux",
        platform: "Unix",
        screenSize: "small",
        test: false,
        broken: false,
        startedAt: new Date(),
        finishedAt: new Date(),
        ip: "127.0.0.1",
        playedFromCountry: "UK",
        operatorCountry: null,
        operatorPlayerCountry: null,
        browserVersion: "6.0",
        ctrl: 255,
        interruptionReason: SessionInterruptionReason.SESSION_EXPIRED,
        extSessionId: "operator_session_id",
        operatorSiteId: 1
    };

    let gameHistoryConsumer: GameHistoryConsumer;
    let roundHistoryConsumer: RoundHistoryConsumer;
    let oldRoundHistoryConsumer: RoundHistoryConsumer;
    let sessionHistoryConsumer: SessionHistoryConsumer;
    let aloomaHistoryConsumer: AloomaHistoryConsumer;

    beforeEach(async () => {
        await getGameHistoryModels().original.drop({ cascade: true });
        await getGameHistoryModels().duplicates.drop({ cascade: true });
        await getOldRoundHistoryModels().original.drop({ cascade: true });
        await getOldRoundHistoryModels().duplicates.drop({ cascade: true });
        await getRoundsHistoryModels().original.drop({ cascade: true });
        await getRoundsHistoryModels().unfinished.drop({ cascade: true });
        await getSessionHistoryModels().original.drop({ cascade: true });
        await getSessionHistoryModels().duplicates.drop({ cascade: true });
        await getGameHistoryModels().sync();
        await getOldRoundHistoryModels().sync();
        await getRoundsHistoryModels().sync();
        await getSessionHistoryModels().sync();
        await flushAll();

        gameHistoryConsumer = await GameHistoryConsumer.create();
        roundHistoryConsumer = await RoundHistoryConsumer.create();
        oldRoundHistoryConsumer = await RoundHistoryConsumer.createOld();
        sessionHistoryConsumer = await SessionHistoryConsumer.create();
        aloomaHistoryConsumer = await AloomaHistoryConsumer.create();
    });

    it("GameHistoryConsumer saves events", async () => {
        const items = [];
        for (let i = 0; i < 100; i++) {
            const newItem = _.cloneDeep(eventTemplate);
            newItem.eventId = i;
            newItem.roundId = i.toString();
            items.push(newItem);
        }

        await gameHistoryConsumer.save(items);
        const result = await getGameHistoryModels().original.findAll();
        expect(result.map((item, i) => {
            items[i].ts = item.toJSON().ts;
            items[i].insertedAt = item.toJSON().insertedAt;
            return item.toJSON();
        })).deep.equal(items);
    });

    it("OldRoundHistoryConsumer saves rounds", async () => {
        const items = [];
        for (let i = 0; i < 100; i++) {
            const newItem = _.cloneDeep(roundTemplate);
            newItem.id = i.toString();
            items.push(newItem as any);
        }
        await oldRoundHistoryConsumer.save(items);
        const result = await getOldRoundHistoryModels().original.findAll();
        expect(result.map((item, i) => {
            items[i].sessionId = items[i].sessionId.toString();
            items[i].startedAt = item.toJSON().startedAt;
            items[i].finishedAt = item.toJSON().finishedAt;
            items[i].insertedAt = item.toJSON().insertedAt;
            return item.toJSON();
        })).deep.equal(items);
    });

    it("OldRoundHistoryConsumer saves rounds with updating broken", async () => {
        let items = [];
        const allItems = [];
        for (let i = 0; i < 100; i++) {
            const newItem = _.cloneDeep(roundTemplate);
            newItem.id = i.toString();
            items.push(newItem);
            allItems.push(newItem);
        }

        const broken = items[50];
        broken.finishedAt = undefined;

        const clonedItems = _.cloneDeep(items);
        await oldRoundHistoryConsumer.save(items);
        let result = await getOldRoundHistoryModels().original.findAll({ order: ["id"] });
        expect(result.map((item, i) => {
            clonedItems[i].sessionId = clonedItems[i].sessionId.toString();
            clonedItems[i].startedAt = item.toJSON().startedAt;
            clonedItems[i].finishedAt = item.toJSON().finishedAt;
            clonedItems[i].insertedAt = item.toJSON().insertedAt;
            return item.toJSON();
        })).deep.equal(clonedItems);

        items = [];

        for (let i = 100; i < 200; i++) {
            const newItem = _.cloneDeep(roundTemplate);
            newItem.id = i.toString();
            items.push(newItem);
            allItems.push(newItem);
        }

        broken.broken = true;
        items.push(broken);

        await oldRoundHistoryConsumer.save(items);
        result = await getOldRoundHistoryModels().original.findAll({ order: ["id"] });
        delete broken.broken;
        expect(result.map((item, i) => {
            allItems[i].sessionId = item.sessionId.toString();
            allItems[i].startedAt = item.toJSON().startedAt;
            allItems[i].finishedAt = item.toJSON().finishedAt;
            allItems[i].insertedAt = item.toJSON().insertedAt;
            return item.toJSON();
        })).deep.equal(allItems);
    });

    it("OldRoundHistoryConsumer don't remove finished rounds", async () => {
        const newItem1: any = _.cloneDeep(roundTemplate);
        newItem1.id = 1;
        newItem1.broken = true;
        newItem1.totalEvents = 10;
        newItem1.finishedAt = new Date();

        await oldRoundHistoryConsumer.save([newItem1]);
        let result = await getOldRoundHistoryModels().original.findAll();
        expect(result.length).eq(1);
        expect(result[0].startedAt).is.not.undefined;
        expect(result[0].finishedAt).is.not.undefined;
        expect(result[0].totalEvents).is.eq("10");

        const newItem2: any = _.cloneDeep(newItem1);
        newItem2.broken = true;
        newItem1.totalEvents = 5;
        newItem2.finishedAt = undefined;

        await oldRoundHistoryConsumer.save([newItem2]);
        result = await getOldRoundHistoryModels().original.findAll();
        expect(result.length).eq(1);
        expect(result[0].startedAt).is.not.undefined;
        expect(result[0].finishedAt).is.not.undefined;
        expect(result[0].totalEvents).is.eq("10");
    });

    it("OldRoundHistoryConsumer updates double broken rounds", async () => {
        const newItem1: any = _.cloneDeep(roundTemplate);
        newItem1.id = 1;
        newItem1.totalEvents = 2;
        newItem1.finishedAt = undefined;

        await oldRoundHistoryConsumer.save([newItem1]);
        let result = await getOldRoundHistoryModels().original.findAll();
        expect(result.length).eq(1);
        expect(result[0].startedAt).is.not.undefined;
        expect(result[0].finishedAt).is.null;
        expect(result[0].totalEvents).is.eq("2");

        const newItem2: any = _.cloneDeep(roundTemplate);
        newItem2.id = 1;
        newItem2.totalEvents = 5;
        newItem2.broken = true;
        newItem2.finishedAt = undefined;
        await oldRoundHistoryConsumer.save([newItem2]);
        result = await getOldRoundHistoryModels().original.findAll();
        expect(result.length).eq(1);
        expect(result[0].startedAt).is.not.undefined;
        expect(result[0].finishedAt).is.null;
        expect(result[0].totalEvents).is.eq("5");

        const newItem3: any = _.cloneDeep(roundTemplate);
        newItem3.id = 1;
        newItem3.totalEvents = 10;
        newItem3.broken = true;
        newItem3.finishedAt = undefined;
        await oldRoundHistoryConsumer.save([newItem3]);
        result = await getOldRoundHistoryModels().original.findAll();
        expect(result.length).eq(1);
        expect(result[0].startedAt).is.not.undefined;
        expect(result[0].finishedAt).is.null;
        expect(result[0].totalEvents).is.eq("10");

        const newItem4: any = _.cloneDeep(roundTemplate);
        newItem4.id = 1;
        newItem4.totalEvents = 20;
        newItem4.broken = true;
        newItem4.finishedAt = new Date();
        await oldRoundHistoryConsumer.save([newItem4]);
        result = await getOldRoundHistoryModels().original.findAll();
        expect(result.length).eq(1);
        expect(result[0].startedAt).is.not.undefined;
        expect(result[0].finishedAt).is.not.undefined;
        expect(result[0].totalEvents).is.eq("20");
    });

    it("RoundHistoryConsumer saves rounds", async () => {
        const items = [];
        for (let i = 0; i < 100; i++) {
            const newItem = _.cloneDeep(roundTemplate);
            newItem.id = i.toString();
            items.push(newItem as any);
        }
        await roundHistoryConsumer.save(items);
        const result = await getRoundsHistoryModels().original.findAll();
        expect(result.map((item, i) => {
            items[i].sessionId = items[i].sessionId.toString();
            items[i].startedAt = item.toJSON().startedAt;
            items[i].finishedAt = item.toJSON().finishedAt;
            items[i].insertedAt = item.toJSON().insertedAt;
            return item.toJSON();
        })).deep.equal(items);

        expect(await getRoundsHistoryModels().unfinished.findAll()).deep.equal([]);
    });

    it("RoundHistoryConsumer saves rounds with updating broken", async () => {
        let items = [];
        const allItems = [];
        for (let i = 0; i < 100; i++) {
            const newItem = _.cloneDeep(roundTemplate);
            newItem.id = i.toString();
            items.push(newItem);
            allItems.push(newItem);
        }

        const broken = items[50];
        broken.finishedAt = undefined;
        broken.broken = true;

        const clonedItems = _.cloneDeep(items);
        clonedItems.splice(50, 1);
        await roundHistoryConsumer.save(items);
        let finished = await getRoundsHistoryModels().original.findAll({ order: ["id"] });
        expect(finished.map((item, i) => {
            clonedItems[i].sessionId = clonedItems[i].sessionId.toString();
            clonedItems[i].startedAt = item.toJSON().startedAt;
            clonedItems[i].finishedAt = item.toJSON().finishedAt;
            clonedItems[i].insertedAt = item.toJSON().insertedAt;
            return item.toJSON();
        })).deep.equal(clonedItems);

        let unFinished = await getRoundsHistoryModels().unfinished.findAll({ order: ["id"] });
        const clonedBroken = _.cloneDeep(broken);
        delete clonedBroken.finishedAt;
        delete clonedBroken.broken;
        expect(unFinished.map((item, i) => {
            clonedBroken.sessionId = clonedItems[i].sessionId.toString();
            clonedBroken.startedAt = item.toJSON().startedAt;
            clonedBroken.insertedAt = item.toJSON().insertedAt;
            return item.toJSON();
        })).deep.equal([clonedBroken]);

        items = [];

        for (let i = 100; i < 200; i++) {
            const newItem = _.cloneDeep(roundTemplate);
            newItem.id = i.toString();
            items.push(newItem);
            allItems.push(newItem);
        }

        broken.broken = true;
        broken.finishedAt = new Date();
        items.push(broken);

        await roundHistoryConsumer.save(items);
        finished = await getRoundsHistoryModels().original.findAll({ order: ["id"] });
        delete broken.broken;
        expect(finished.map((item, i) => {
            allItems[i].sessionId = item.sessionId.toString();
            allItems[i].startedAt = item.toJSON().startedAt;
            allItems[i].finishedAt = item.toJSON().finishedAt;
            allItems[i].insertedAt = item.toJSON().insertedAt;
            return item.toJSON();
        })).deep.equal(allItems);

        unFinished = await getRoundsHistoryModels().unfinished.findAll({ order: ["id"] });
        expect(unFinished).deep.equal([]);
    });

    it("RoundHistoryConsumer don't remove finished rounds", async () => {
        const newItem1: any = _.cloneDeep(roundTemplate);
        newItem1.id = 1;
        newItem1.broken = true;
        newItem1.totalEvents = 10;
        newItem1.finishedAt = new Date();

        await roundHistoryConsumer.save([newItem1]);
        let result = await getRoundsHistoryModels().original.findAll();
        expect(result.length).eq(1);
        expect(result[0].startedAt).is.not.undefined;
        expect(result[0].finishedAt).is.not.undefined;
        expect(result[0].totalEvents).is.eq("10");

        const newItem2: any = _.cloneDeep(newItem1);
        newItem2.broken = true;
        newItem1.totalEvents = 5;
        newItem2.finishedAt = undefined;

        await roundHistoryConsumer.save([newItem2]);
        result = await getRoundsHistoryModels().original.findAll();
        expect(result.length).eq(1);
        expect(result[0].startedAt).is.not.undefined;
        expect(result[0].finishedAt).is.not.undefined;
        expect(result[0].totalEvents).is.eq("10");
    });

    it("RoundHistoryConsumer updates double broken rounds", async () => {
        const newItem1: any = _.cloneDeep(roundTemplate);
        newItem1.id = 1;
        newItem1.totalEvents = 2;
        newItem1.broken = true;
        newItem1.finishedAt = undefined;

        await roundHistoryConsumer.save([newItem1]);
        let result = await getRoundsHistoryModels().unfinished.findAll();
        expect(result.length).eq(1);
        expect(result[0].startedAt).is.not.undefined;
        expect(result[0].totalEvents).is.eq("2");

        const newItem2: any = _.cloneDeep(roundTemplate);
        newItem2.id = 1;
        newItem2.totalEvents = 5;
        newItem2.broken = true;
        newItem2.finishedAt = undefined;
        await roundHistoryConsumer.save([newItem2]);
        result = await getRoundsHistoryModels().unfinished.findAll();
        expect(result.length).eq(1);
        expect(result[0].startedAt).is.not.undefined;
        expect(result[0].totalEvents).is.eq("5");

        const newItem3: any = _.cloneDeep(roundTemplate);
        newItem3.id = 1;
        newItem3.totalEvents = 10;
        newItem3.broken = true;
        newItem3.finishedAt = undefined;
        await roundHistoryConsumer.save([newItem3]);
        result = await getRoundsHistoryModels().unfinished.findAll();
        expect(result.length).eq(1);
        expect(result[0].startedAt).is.not.undefined;
        expect(result[0].totalEvents).is.eq("10");

        const newItem4: any = _.cloneDeep(roundTemplate);
        newItem4.id = 1;
        newItem4.totalEvents = 20;
        newItem4.broken = true;
        newItem4.finishedAt = new Date();
        await roundHistoryConsumer.save([newItem4]);
        result = await getRoundsHistoryModels().unfinished.findAll();
        expect(result.length).eq(0);
        result = await getRoundsHistoryModels().original.findAll();
        expect(result.length).eq(1);
        expect(result[0].startedAt).is.not.undefined;
        expect(result[0].finishedAt).is.not.undefined;
        expect(result[0].totalEvents).is.eq("20");
    });

    it("SessionHistoryConsumer saves sessions", async () => {
        const sessionsStarted = [];
        for (let i = 0; i < 100; i++) {
            const newItem = _.cloneDeep(sessionTemplate);
            newItem.sessionId = i.toString();
            newItem.finishedAt = undefined;
            sessionsStarted.push(newItem as any);
        }
        const sessionsFinished = [];
        for (let i = 0; i < 100; i++) {
            const newItem = _.cloneDeep(sessionTemplate);
            newItem.sessionId = i.toString();
            newItem.referrer = null;
            sessionsFinished.push(newItem as any);
        }
        await sessionHistoryConsumer.save([...sessionsStarted, ...sessionsFinished]);
        const result = await getSessionHistoryModels().original.findAll();
        result.sort((i1, i2) => +i1.sessionId - +i2.sessionId);
        expect(result.map((item, i) => {
            sessionsFinished[i].sessionId = sessionsFinished[i].sessionId.toString();
            sessionsFinished[i].startedAt = item.toJSON().startedAt;
            sessionsFinished[i].finishedAt = item.toJSON().finishedAt;
            sessionsFinished[i].insertedAt = item.toJSON().insertedAt;
            return item.toJSON();
        })).deep.equal(sessionsFinished);
    });

    it("SessionHistoryConsumer saves session with a first ip in an ip string", async () => {
        const item = {
            session: { sessionId: 1 },
            id: { gameCode: "AAA", brandId: 4, playerCode: "PL0001", deviceId: "mobile" },
            gameVersion: 3,
            gameData: {
                gameId: 2,
                gameTokenData: { gameCode: "AAA", currency: "USD", merchantSessionId: "operator_session_id" },
                referrer: "google.com",
                "operatorSiteId": 1
            },
            requestContext: { ip: "*********, 127.0.0.1" },
            createdAt: "createdAt",
            referrer: "google.com"
        };

        const historyItem = createSessionHistory(item as any);
        expect(historyItem).deep.equal({
            brandId: 4,
            broken: undefined,
            browser: undefined,
            country: undefined,
            ctrl: historyItem.ctrl,
            browserVersion: undefined,
            currency: "USD",
            deviceId: "mobile",
            finishedAt: undefined,
            gameCode: "AAA",
            gameId: 2,
            gameVersion: 3,
            ip: "*********",
            language: undefined,
            os: undefined,
            platform: undefined,
            playedFromCountry: undefined,
            operatorCountry: undefined,
            operatorPlayerCountry: undefined,
            playerCode: "PL0001",
            screenSize: undefined,
            sessionId: 1,
            startedAt: "createdAt",
            test: undefined,
            interruptionReason: undefined,
            referrer: "google.com",
            operatorSiteId: 1,
            extSessionId: "operator_session_id"
        });
    });

    it("SessionHistoryConsumer saves session with platform from gameTokenData", async () => {
        const item = {
            session: { sessionId: 1 },
            id: { gameCode: "AAA", brandId: 4, playerCode: "PL0001", deviceId: "mobile" },
            gameVersion: 3,
            gameData: {
                gameId: 2,
                gameTokenData: {
                    gameCode: "AAA",
                    currency: "USD",
                    platform: "android",
                    merchantSessionId: "operator_session_id"
                },
                "operatorSiteId": 1,
            },
            requestContext: { ip: "*********, 127.0.0.1" },
            createdAt: "createdAt"
        };

        const historyItem = createSessionHistory(item as any);
        expect(historyItem).deep.equal({
            brandId: 4,
            broken: undefined,
            browser: undefined,
            country: undefined,
            ctrl: historyItem.ctrl,
            browserVersion: undefined,
            currency: "USD",
            deviceId: "mobile",
            finishedAt: undefined,
            gameCode: "AAA",
            gameId: 2,
            gameVersion: 3,
            ip: "*********",
            language: undefined,
            os: undefined,
            platform: "android",
            playedFromCountry: undefined,
            operatorCountry: undefined,
            operatorPlayerCountry: undefined,
            playerCode: "PL0001",
            screenSize: undefined,
            sessionId: 1,
            startedAt: "createdAt",
            test: undefined,
            interruptionReason: undefined,
            referrer: undefined,
            operatorSiteId: 1,
            extSessionId: "operator_session_id"
        });
    });

    describe("AloomaHistoryConsumer", () => {
        let request: RequestMock;
        after(() => {
            request.unmock(superagent);
        });

        before(() => {
            request = requestMock(superagent);
        });

        afterEach(() => {
            request.clearRoutes();
        });

        it("sends requests to alooma.com", async () => {
            const items = [];
            for (let i = 0; i < 100; i++) {
                const newItem = _.cloneDeep(eventTemplate);
                newItem.eventId = i;
                newItem.roundId = i;
                items.push(newItem);
            }

            request.post(`https://inputs.alooma.com/rest/${config.unloader.aloomaConsumer.token}`, status200());
            await aloomaHistoryConsumer.save(items);
            expect(request.args).deep.equal([
                {
                    "headers": {
                        "content-type": "application/json"
                    },
                    "method": "POST",
                    "query": {},
                    "url": `https://inputs.alooma.com/rest/${config.unloader.aloomaConsumer.token}`,
                    "body": {
                        "type": "game_history",
                        "items": items.map(item => asEventHistory(item))
                    }
                }
            ]);
        });

        it("makes retries", async () => {
            const items = [];
            for (let i = 0; i < 100; i++) {
                const newItem = _.cloneDeep(eventTemplate);
                newItem.eventId = i;
                newItem.roundId = i;
                items.push(newItem);
            }

            request.post(`https://inputs.alooma.com/rest/${config.unloader.aloomaConsumer.token}`,
                onCall(status500())
                    .onCall(status500())
                    .onCall(status200()));

            await aloomaHistoryConsumer.save(items);
            for (const a of request.args) {
                expect(a).deep.equal(
                    {
                        "headers": {
                            "content-type": "application/json"
                        },
                        "method": "POST",
                        "query": {},
                        "url": `https://inputs.alooma.com/rest/${config.unloader.aloomaConsumer.token}`,
                        "body": {
                            "type": "game_history",
                            "items": items.map(item => asEventHistory(item))
                        }
                    }
                );
            }
        });

        it("fails to make retries", async () => {
            const items = [];
            for (let i = 0; i < 100; i++) {
                const newItem = _.cloneDeep(eventTemplate);
                newItem.eventId = i;
                newItem.roundId = i;
                items.push(newItem);
            }

            request.post(`https://inputs.alooma.com/rest/${config.unloader.aloomaConsumer.token}`, status500());
            return aloomaHistoryConsumer.save(items).should.eventually.rejectedWith("Can't move data to Alooma!");
        });

    });

    describe("ConsumerFactory", () => {
        it("creates consumers", async () => {
            const result = (await createGameHistoryConsumers([
                "alooma",
                "redshift",
                "gamehistory",
                "events",
                "fake"
            ], false, { enabled: false } as any) as CompositeConsumer<GameEventHistory>);

            expect(result.consumers.length).eq(2);
            expect(result.consumers[0] instanceof AloomaHistoryConsumer).is.true;
            expect(result.consumers[1] instanceof GameHistoryConsumer).is.true;
        });
    });
});
