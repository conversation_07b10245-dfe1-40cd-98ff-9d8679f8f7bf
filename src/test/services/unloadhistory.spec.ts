import { expect } from "chai";
import { Currency } from "@skywind-group/sw-game-core";
import { GameTokenData } from "../../skywind/services/tokens";
import { GameContextID } from "../../skywind/services/contextIds";
import {
    createGameToken,
    flushAll,
    getGameHistory,
    getGameHistoryDuplicates,
    getRange,
    getRoundHistoryDuplicates,
    TEST_MODULE_NAME
} from "../helper";
import { RequestContext } from "../../skywind/services/context/gamecontext";
import { PaymentOperation } from "../../skywind/services/wallet";
import { UnloadEventHistoryService } from "../../skywind/history/unloadHistory";
import { UnloadRoundService } from "../../skywind/history/unloadRounds";
import { UnloadSessionService } from "../../skywind/history/unloadSessions";
import { getOldRoundHistoryModels } from "../../skywind/history/model/oldRoundHistory";
import { getGameHistoryModels } from "../../skywind/history/model/gameHistory";
import { getSessionHistoryModels } from "../../skywind/history/model/sessionHistory";
import { GameHistoryConsumer } from "../../skywind/history/consumers/gameHistoryConsumer";
import { RoundHistoryConsumer } from "../../skywind/history/consumers/roundHistoryConsumer";
import { SessionHistoryConsumer } from "../../skywind/history/consumers/sessionHistoryConsumer";
import {
    GameHistoryQueue,
    GameRoundQueue,
    GameSessionQueue,
    RedisQueue
} from "../../skywind/services/queue/redisQueue";
import { Redis } from "../../skywind/storage/redis";
import { GameEventHistory, RoundHistory, SessionHistory } from "../../skywind/history/history";
import { GameData } from "../../skywind/services/auth";
import { GameSession } from "../../skywind/services/gameSession";
import { getGameFlowContextManager } from "../../skywind/services/contextmanager/contextManagerImpl";
import { getRoundsHistoryModels } from "../../skywind/history/model/roundHistory";
import { GameFlowInfoImpl } from "../../skywind/services/context/gameFlowInfo";
import config from "../../skywind/config";
import * as  _ from "lodash";
import { deepClone } from "../../skywind/utils/cloner";
import { UnloadService } from "../../skywind/history/unloadService";
import { stub } from "sinon";

describe("Unload history job", () => {
    const contextManager = getGameFlowContextManager();
    const currency: Currency = 0;
    const settings = {
        coins: [3, 4],
        defaultCoin: 4,
        maxTotalStake: currency,
        stakeAll: [1, 2, 3, 4],
        stakeDef: currency,
        stakeMax: currency,
        stakeMin: currency,
        winMax: currency,
        currencyMultiplier: 100,
    };
    const predefinedGHEvents: GameEventHistory[] = [
        {
            roundId: "1",
            sessionId: "1",
            eventId: 1,
            type: "slot",
            gameVersion: "0.1.0",
            gameId: "game_id",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            walletTransactionId: "trx_id",
            bet: 100,
            win: 1000,
            balanceBefore: 200,
            balanceAfter: 1100,
            roundEnded: true,
            result: {
                spinResult: "spin_result",
            },
            ts: new Date(),
            test: false,
            ctrl: 100,
        },
        {
            sessionId: "2",
            roundId: "2",
            eventId: 2,
            type: "slot",
            gameVersion: "0.1.0",
            gameId: "game_id",
            brandId: 2,
            playerCode: "player002",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            walletTransactionId: "trx_id",
            bet: 10,
            win: 4000,
            balanceBefore: 50,
            balanceAfter: 4040,
            roundEnded: true,
            result: {
                spinResult: "spin_result",
            },
            ts: new Date(),
            test: false,
            ctrl: 200,
        },
        {
            sessionId: "2",
            roundId: "3",
            eventId: 3,
            type: "slot",
            gameVersion: "0.1.0",
            gameId: "game_id",
            brandId: 2,
            playerCode: "player003",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            walletTransactionId: "trx_id",
            bet: 500,
            win: 4,
            balanceBefore: 3544,
            balanceAfter: 4040,
            roundEnded: true,
            result: {
                spinResult: "spin_result",
            },
            ts: new Date(),
            test: false,
            ctrl: 300,
        }
    ];
    const predefinedRounds: RoundHistory[] = [
        {
            id: "0",
            sessionId: "1",
            gameId: "game_id",
            brandId: 4,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 90,
            totalWin: 9,
            totalEvents: 7,
            balanceBefore: 3,
            balanceAfter: 84,
            startedAt: new Date(),
            test: false,
            ctrl: 10,
            broken: true
        },
        {
            id: "2",
            sessionId: "1",
            gameId: "game_id",
            brandId: 3,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 40,
            totalWin: 10,
            balanceBefore: 100,
            balanceAfter: 70,
            totalEvents: 10,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 100
        },
        {
            id: "3",
            sessionId: "1",
            gameId: "game_id",
            brandId: 4,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 90,
            totalWin: 9,
            totalEvents: 7,
            balanceBefore: 3,
            balanceAfter: 84,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 100
        }
    ];

    const gameID: GameContextID = GameContextID.create("GM001", 1, "playerId", "deviceId");

    const requestContext: RequestContext = {
        userAgent: {
            browser: "Chrome",
            os: "Linux",
            platform: "Unix",
            browserVersion: "63.02.51253"
        },
        screenSize: "small",
        country: "US",
        language: "EN"
    };

    const gameTokenData: GameTokenData = {
        playerCode: gameID.playerCode,
        gameCode: "GM001",
        brandId: gameID.brandId,
        currency: "USD",
        playmode: "real",
        merchantSessionId: "operator_session_id"
    };

    const gameData: GameData = {
        gameTokenData: gameTokenData,
        limits: settings,
        gameId: "gameId",
        operatorSiteId: 1
    };

    let session: GameSession;
    let prevMaxSpinPopDuration;

    before(async () => {
        gameTokenData.token = await createGameToken(gameTokenData);
        prevMaxSpinPopDuration = config.unloader.maxSpinPopDuration;
        config.unloader.maxSpinPopDuration = -1;
    });

    after(async () => {
        config.unloader.maxSpinPopDuration = prevMaxSpinPopDuration;
    });

    beforeEach(async () => {
        await getGameHistoryModels().original.drop({ cascade: true });
        await getGameHistoryModels().duplicates.drop({ cascade: true });
        await getOldRoundHistoryModels().original.drop({ cascade: true });
        await getRoundsHistoryModels().original.drop({ cascade: true });
        await getRoundsHistoryModels().unfinished.drop({ cascade: true });
        await getRoundsHistoryModels().duplicates.drop({ cascade: true });
        await getSessionHistoryModels().original.drop({ cascade: true });
        await getSessionHistoryModels().duplicates.drop({ cascade: true });
        await getGameHistoryModels().sync();
        await getOldRoundHistoryModels().sync();
        await getRoundsHistoryModels().sync();
        await getSessionHistoryModels().sync();
        await flushAll();
        session = await GameSession.generate(gameID, "real");
    });

    it("moves spins, rounds and sessions to database (old)", async () => {
        await contextManager.findOrCreateGameContext(
            gameID, session, gameData, TEST_MODULE_NAME, requestContext);
        const context = await contextManager.findGameContextById(gameID);
        context.gameContext = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}
                }
            }
        };

        context.lastRequestId = 2;

        const request = { request: "req", requestId: 1 };

        // round 0
        const history1 = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: { positions: [1, 2, 3] }
        };

        const newState1 = _.cloneDeep(context.gameContext);
        newState1["currentScene"] = "NEWSCENE1";
        const walletOperation1: PaymentOperation = {
            operation: "payment",
            transactionId: "1",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history1.roundEnded,
            bet: 1000,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, newState1, request, history1);
        await context.commitPendingModification(
            { currency: "USD", main: 19800, previousValue: 20000 });

        // round 1
        const history2 = {
            type: "slot",
            version: 1,
            roundEnded: true,
            credit: 5,
            data: { positions: [1, 2, 3, 4] }
        };

        const newState2 = _.cloneDeep(newState1);
        newState2["currentScene"] = "NEWSCENE2";
        const walletOperation2: PaymentOperation = {
            operation: "payment",
            transactionId: "2",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history2.roundEnded,
            bet: 2000,
            win: 100,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation2, newState2, request, history2);
        await context.commitPendingModification({ currency: "USD", main: 18700, previousValue: 19800 });

        // round 2
        const history3 = {
            type: "slot",
            version: 1,
            roundEnded: false,
            debit: 10,
            data: { positions: [5, 3, 4] }
        };

        const newState3 = _.cloneDeep(newState2);
        newState3["currentScene"] = "NEWSCENE3";
        const walletOperation3: PaymentOperation = {
            operation: "payment",
            transactionId: "3",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history3.roundEnded,
            bet: 0,
            win: 100,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation3, newState3, request, history3);
        await context.commitPendingModification({ currency: "USD", main: 18800, previousValue: 19800 });

        const history4 = {
            type: "slot",
            version: 1,
            roundEnded: false,
            data: { positions: [5, 3, 4, 6, 7] }
        };

        const newState4 = _.cloneDeep(newState3);
        newState4["currentScene"] = "NEWSCENE4";
        const walletOperation4: PaymentOperation = {
            operation: "payment",
            transactionId: "4",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history4.roundEnded,
            bet: 0,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation4, newState4, request, history4);
        await context.commitPendingModification({ currency: "USD", main: 19000, previousValue: 18800 });

        const history5 = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: { positions: [5, 3, 4, 9] }
        };

        const newState5 = _.cloneDeep(newState4);
        newState5["currentScene"] = "NEWSCENE5";
        const walletOperation5: PaymentOperation = {
            operation: "payment",
            transactionId: "5",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history5.roundEnded,
            bet: 0,
            win: 10,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation5, newState5, request, history5);
        await context.commitPendingModification({ currency: "USD", main: 19010, previousValue: 19000 });

        // round 3

        const history6 = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: { positions: [1, 2, 3, 4, 9] }
        };

        const newState6 = _.cloneDeep(newState5);
        newState6["currentScene"] = "NEWSCENE6";
        const walletOperation6: PaymentOperation = {
            operation: "payment",
            transactionId: "6",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            ts: new Date(),
            roundEnded: history6.roundEnded,
            bet: 20,
            win: 300,
            currency: "USD"
        };
        await context.updatePendingModification(walletOperation6, newState6, request, history6);
        await context.commitPendingModification({ currency: "USD", main: 21090, previousValue: 19010 });

        // Round 4
        // check commit without previousValue

        const history7 = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: { positions: [1, 2, 3, 4, 9] }
        };

        const newState7 = _.cloneDeep(newState6);
        newState7["currentScene"] = "NEWSCENE7";
        const walletOperation7: PaymentOperation = {
            operation: "payment",
            transactionId: "7",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history7.roundEnded,
            bet: 0,
            win: 0,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation7, newState7, request, history7);
        await context.commitPendingModification({ currency: "USD", main: 21090 });

        await context.remove(true);

        const gameHistoryConsumer = await GameHistoryConsumer.create();
        const unloadHistoryService = new UnloadEventHistoryService(gameHistoryConsumer);
        await unloadHistoryService.unloadData();
        const historyWorkerItems = await getRange(unloadHistoryService.workerList, 0, 1000);
        expect(historyWorkerItems.length).is.equal(0);

        const roundHistoryConsumer = await RoundHistoryConsumer.createOld();
        const unloadRoundService = new UnloadRoundService(roundHistoryConsumer);
        await unloadRoundService.unloadData();
        const roundWorkerItems = await getRange(unloadRoundService.workerList, 0, 1000);
        expect(roundWorkerItems.length).is.equal(0);

        const sessionHistoryConsumer = await SessionHistoryConsumer.create();
        const unloadSessionService = new UnloadSessionService(sessionHistoryConsumer);
        await unloadSessionService.unloadData();
        const sessionWorkerItems = await getRange(unloadSessionService.workerList, 0, 1000);
        expect(sessionWorkerItems.length).is.equal(0);

        const events = await getGameHistoryModels().original.findAll();
        expect(events.length).to.equal(7);
        events.sort((i1, i2) => parseInt(i1.walletTransactionId, 10) - parseInt(i2.walletTransactionId, 10));
        expect(events.map((x) => _.omit(x.toJSON(), "insertedAt", "ctrl"))).deep.equal([
            {
                "sessionId": "0",
                "bet": "1000",
                "brandId": 1,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "GM001",
                "gameId": "gameId",
                "playerCode": "playerId",
                "result": history1.data,
                "roundEnded": true,
                "roundId": "0",
                "eventId": 0,
                "ts": events[0].ts,
                "walletTransactionId": "1",
                "win": "200",
                "balanceAfter": "19800",
                "balanceBefore": "20000",
                "test": false,
                "totalJpContribution": "0",
                "totalJpWin": "0",
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            },
            {
                "sessionId": "0",
                "bet": "2000",
                "brandId": 1,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "GM001",
                "gameId": "gameId",
                "playerCode": "playerId",
                "result": history2.data,
                "roundEnded": true,
                "roundId": "1",
                "eventId": 0,
                "ts": events[1].ts,
                "walletTransactionId": "2",
                "win": "100",
                "balanceAfter": "18700",
                "balanceBefore": "19800",
                "test": false,
                "totalJpContribution": "0",
                "totalJpWin": "0",
                "extraData": null,
                "credit": "5",
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            },
            {
                "sessionId": "0",
                "bet": "0",
                "brandId": 1,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "GM001",
                "gameId": "gameId",
                "playerCode": "playerId",
                "result": history3.data,
                "roundEnded": false,
                "roundId": "2",
                "eventId": 0,
                "ts": events[2].ts,
                "walletTransactionId": "3",
                "win": "100",
                "balanceAfter": "18800",
                "balanceBefore": "19800",
                "test": false,
                "totalJpContribution": "0",
                "totalJpWin": "0",
                "extraData": null,
                "credit": null,
                "debit": "10",
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            },
            {
                "sessionId": "0",
                "bet": "0",
                "brandId": 1,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "GM001",
                "gameId": "gameId",
                "playerCode": "playerId",
                "result": history4.data,
                "roundEnded": false,
                "roundId": "2",
                "eventId": 1,
                "ts": events[3].ts,
                "walletTransactionId": "4",
                "win": "200",
                "balanceAfter": "19000",
                "balanceBefore": "18800",
                "test": false,
                "totalJpContribution": "0",
                "totalJpWin": "0",
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            },
            {
                "sessionId": "0",
                "bet": "0",
                "brandId": 1,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "GM001",
                "gameId": "gameId",
                "playerCode": "playerId",
                "result": history5.data,
                "roundEnded": true,
                "roundId": "2",
                "eventId": 2,
                "ts": events[4].ts,
                "walletTransactionId": "5",
                "win": "10",
                "balanceAfter": "19010",
                "balanceBefore": "19000",
                "test": false,
                "totalJpContribution": "0",
                "totalJpWin": "0",
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            },
            {
                "sessionId": "0",
                "bet": "20",
                "brandId": 1,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "GM001",
                "gameId": "gameId",
                "playerCode": "playerId",
                "result": history6.data,
                "roundEnded": true,
                "roundId": "3",
                "eventId": 0,
                "ts": events[5].ts,
                "walletTransactionId": "6",
                "win": "300",
                "balanceAfter": "21090",
                "balanceBefore": "19010",
                "test": false,
                "totalJpContribution": "0",
                "totalJpWin": "0",
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            },
            {
                "sessionId": "0",
                "bet": "0",
                "brandId": 1,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "GM001",
                "gameId": "gameId",
                "playerCode": "playerId",
                "result": history7.data,
                "roundEnded": true,
                "roundId": "4",
                "eventId": 0,
                "ts": events[6].ts,
                "walletTransactionId": "7",
                "win": "0",
                "balanceAfter": "21090",
                "balanceBefore": "21090",
                "test": false,
                "totalJpContribution": "0",
                "totalJpWin": "0",
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            }
        ]);

        const rounds = await getOldRoundHistoryModels().original.findAll();
        rounds.sort((i1, i2) => parseInt(i1.id, 10) - parseInt(i2.id, 10));
        expect(rounds.map((x) => _.omit(x.toJSON(), "finishedAt", "startedAt", "insertedAt", "ctrl", "extraData")))
            .deep
            .equal([
                {
                    "sessionId": "0",
                    "brandId": 1,
                    "currency": "USD",
                    "deviceId": "deviceId",
                    "gameCode": "GM001",
                    "gameId": "gameId",
                    "id": "0",
                    "playerCode": "playerId",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "1000",
                    "totalEvents": "1",
                    "totalWin": "200",
                    "balanceAfter": "19800",
                    "balanceBefore": "20000",
                    "totalJpContribution": "0",
                    "totalJpWin": "0",
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": 1
                },
                {
                    "sessionId": "0",
                    "brandId": 1,
                    "currency": "USD",
                    "deviceId": "deviceId",
                    "gameCode": "GM001",
                    "gameId": "gameId",
                    "id": "1",
                    "playerCode": "playerId",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "2000",
                    "totalEvents": "1",
                    "totalWin": "100",
                    "balanceAfter": "18700",
                    "balanceBefore": "19800",
                    "totalJpContribution": "0",
                    "totalJpWin": "0",
                    "credit": "5",
                    "debit": null,
                    "operatorSiteId": 1
                },
                {
                    "sessionId": "0",
                    "brandId": 1,
                    "currency": "USD",
                    "deviceId": "deviceId",
                    "gameCode": "GM001",
                    "gameId": "gameId",
                    "id": "2",
                    "playerCode": "playerId",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "0",
                    "totalEvents": "3",
                    "totalWin": "310",
                    "balanceAfter": "19010",
                    "balanceBefore": "19800",
                    "totalJpContribution": "0",
                    "totalJpWin": "0",
                    "credit": null,
                    "debit": "10",
                    "operatorSiteId": 1
                },
                {
                    "sessionId": "0",
                    "brandId": 1,
                    "currency": "USD",
                    "deviceId": "deviceId",
                    "gameCode": "GM001",
                    "gameId": "gameId",
                    "id": "3",
                    "playerCode": "playerId",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "20",
                    "totalEvents": "1",
                    "totalWin": "300",
                    "balanceAfter": "21090",
                    "balanceBefore": "19010",
                    "totalJpContribution": "0",
                    "totalJpWin": "0",
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": 1
                },
                {
                    "sessionId": "0",
                    "brandId": 1,
                    "currency": "USD",
                    "deviceId": "deviceId",
                    "gameCode": "GM001",
                    "gameId": "gameId",
                    "id": "4",
                    "playerCode": "playerId",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "0",
                    "totalEvents": "1",
                    "totalWin": "0",
                    "balanceAfter": "21090",
                    "balanceBefore": "21090",
                    "totalJpContribution": "0",
                    "totalJpWin": "0",
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": 1
                }
            ]);

        const sessions = await getSessionHistoryModels().original.findAll();
        sessions.sort((i1, i2) => parseInt(i1.sessionId, 10) - parseInt(i2.sessionId, 10));
        expect(sessions.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl"))).deep.equal([
            {
                "sessionId": "0",
                "brandId": 1,
                "currency": "USD",
                "country": "US",
                "language": "EN",
                "deviceId": "deviceId",
                "extSessionId": "operator_session_id",
                "gameCode": "GM001",
                "gameId": "gameId",
                "gameVersion": "0.1.0",
                "playerCode": "playerId",
                "broken": false,
                "screenSize": "small",
                "browser": "Chrome",
                "os": "Linux",
                "platform": "Unix",
                "test": false,
                ip: null,
                playedFromCountry: null,
                operatorCountry: null,
                operatorPlayerCountry: null,
                browserVersion: "63.02.51253",
                interruptionReason: null,
                referrer: null,
                operatorSiteId: 1
            }
        ]);

    });

    it("moves spins, rounds and sessions to database", async () => {
        await contextManager.findOrCreateGameContext(
            gameID, session, gameData, TEST_MODULE_NAME, requestContext);
        const context = await contextManager.findGameContextById(gameID);
        context.gameContext = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}
                }
            }
        };

        context.lastRequestId = 2;

        const request = { request: "req", requestId: 1 };

        // round 0
        const history1 = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: { positions: [1, 2, 3] }
        };

        const newState1 = _.cloneDeep(context.gameContext);
        newState1["currentScene"] = "NEWSCENE1";
        const walletOperation1: PaymentOperation = {
            operation: "payment",
            transactionId: "1",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history1.roundEnded,
            bet: 1000,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, newState1, request, history1);
        await context.commitPendingModification(
            { currency: "USD", main: 19800, previousValue: 20000 });

        // round 1
        const history2 = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: { positions: [1, 2, 3, 4] }
        };

        const newState2 = _.cloneDeep(newState1);
        newState2["currentScene"] = "NEWSCENE2";
        const walletOperation2: PaymentOperation = {
            operation: "payment",
            transactionId: "2",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history1.roundEnded,
            bet: 2000,
            win: 100,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation2, newState2, request, history2);
        await context.commitPendingModification({ currency: "USD", main: 18700, previousValue: 19800 });

        // round 2
        const history3 = {
            type: "slot",
            version: 1,
            roundEnded: false,
            data: { positions: [5, 3, 4] }
        };

        const newState3 = _.cloneDeep(newState2);
        newState3["currentScene"] = "NEWSCENE3";
        const walletOperation3: PaymentOperation = {
            operation: "payment",
            transactionId: "3",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history1.roundEnded,
            bet: 0,
            win: 100,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation3, newState3, request, history3);
        await context.commitPendingModification({ currency: "USD", main: 18800, previousValue: 19800 });

        const history4 = {
            type: "slot",
            version: 1,
            roundEnded: false,
            data: { positions: [5, 3, 4, 6, 7] }
        };

        const newState4 = _.cloneDeep(newState3);
        newState4["currentScene"] = "NEWSCENE4";
        const walletOperation4: PaymentOperation = {
            operation: "payment",
            transactionId: "4",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history1.roundEnded,
            bet: 0,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation4, newState4, request, history4);
        await context.commitPendingModification({ currency: "USD", main: 19000, previousValue: 18800 });

        const history5 = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: { positions: [5, 3, 4, 9] }
        };

        const newState5 = _.cloneDeep(newState4);
        newState5["currentScene"] = "NEWSCENE5";
        const walletOperation5: PaymentOperation = {
            operation: "payment",
            transactionId: "5",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history5.roundEnded,
            bet: 0,
            win: 10,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation5, newState5, request, history5);
        await context.commitPendingModification({ currency: "USD", main: 19010, previousValue: 19000 });

        // round 3

        const history6 = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: { positions: [1, 2, 3, 4, 9] }
        };

        const newState6 = _.cloneDeep(newState5);
        newState6["currentScene"] = "NEWSCENE6";
        const walletOperation6: PaymentOperation = {
            operation: "payment",
            transactionId: "6",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history6.roundEnded,
            bet: 20,
            win: 300,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation6, newState6, request, history6);
        await context.commitPendingModification({ currency: "USD", main: 21090, previousValue: 19010 });

        // Round 4
        // check commit without previousValue

        const history7 = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: { positions: [1, 2, 3, 4, 9] }
        };

        const newState7 = _.cloneDeep(newState6);
        newState7["currentScene"] = "NEWSCENE7";
        const walletOperation7: PaymentOperation = {
            operation: "payment",
            transactionId: "7",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history7.roundEnded,
            bet: 0,
            win: 0,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation7, newState7, request, history7);
        await context.commitPendingModification({ currency: "USD", main: 21090 });

        await context.remove(true);

        const gameHistoryConsumer = await GameHistoryConsumer.create();
        const unloadHistoryService = new UnloadEventHistoryService(gameHistoryConsumer);
        await unloadHistoryService.unloadData();
        const historyWorkerItems = await getRange(unloadHistoryService.workerList, 0, 1000);
        expect(historyWorkerItems.length).is.equal(0);

        const roundHistoryConsumer = await RoundHistoryConsumer.create();
        const unloadRoundService = new UnloadRoundService(roundHistoryConsumer);
        await unloadRoundService.unloadData();
        const roundWorkerItems = await getRange(unloadRoundService.workerList, 0, 1000);
        expect(roundWorkerItems.length).is.equal(0);

        const sessionHistoryConsumer = await SessionHistoryConsumer.create();
        const unloadSessionService = new UnloadSessionService(sessionHistoryConsumer);
        await unloadSessionService.unloadData();
        const sessionWorkerItems = await getRange(unloadSessionService.workerList, 0, 1000);
        expect(sessionWorkerItems.length).is.equal(0);

        const events = await getGameHistoryModels().original.findAll();
        expect(events.length).to.equal(7);
        events.sort((i1, i2) => parseInt(i1.walletTransactionId, 10) - parseInt(i2.walletTransactionId, 10));
        expect(events.map((x) => _.omit(x.toJSON(), "insertedAt", "ctrl"))).deep.equal([
            {
                "sessionId": "0",
                "bet": "1000",
                "brandId": 1,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "GM001",
                "gameId": "gameId",
                "playerCode": "playerId",
                "result": history1.data,
                "roundEnded": true,
                "roundId": "0",
                "eventId": 0,
                "ts": events[0].ts,
                "walletTransactionId": "1",
                "win": "200",
                "balanceAfter": "19800",
                "balanceBefore": "20000",
                "test": false,
                "totalJpContribution": "0",
                "totalJpWin": "0",
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            },
            {
                "sessionId": "0",
                "bet": "2000",
                "brandId": 1,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "GM001",
                "gameId": "gameId",
                "playerCode": "playerId",
                "result": history2.data,
                "roundEnded": true,
                "roundId": "1",
                "eventId": 0,
                "ts": events[1].ts,
                "walletTransactionId": "2",
                "win": "100",
                "balanceAfter": "18700",
                "balanceBefore": "19800",
                "test": false,
                "totalJpContribution": "0",
                "totalJpWin": "0",
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            },
            {
                "sessionId": "0",
                "bet": "0",
                "brandId": 1,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "GM001",
                "gameId": "gameId",
                "playerCode": "playerId",
                "result": history3.data,
                "roundEnded": false,
                "roundId": "2",
                "eventId": 0,
                "ts": events[2].ts,
                "walletTransactionId": "3",
                "win": "100",
                "balanceAfter": "18800",
                "balanceBefore": "19800",
                "test": false,
                "totalJpContribution": "0",
                "totalJpWin": "0",
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            },
            {
                "sessionId": "0",
                "bet": "0",
                "brandId": 1,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "GM001",
                "gameId": "gameId",
                "playerCode": "playerId",
                "result": history4.data,
                "roundEnded": false,
                "roundId": "2",
                "eventId": 1,
                "ts": events[3].ts,
                "walletTransactionId": "4",
                "win": "200",
                "balanceAfter": "19000",
                "balanceBefore": "18800",
                "test": false,
                "totalJpContribution": "0",
                "totalJpWin": "0",
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            },
            {
                "sessionId": "0",
                "bet": "0",
                "brandId": 1,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "GM001",
                "gameId": "gameId",
                "playerCode": "playerId",
                "result": history5.data,
                "roundEnded": true,
                "roundId": "2",
                "eventId": 2,
                "ts": events[4].ts,
                "walletTransactionId": "5",
                "win": "10",
                "balanceAfter": "19010",
                "balanceBefore": "19000",
                "test": false,
                "totalJpContribution": "0",
                "totalJpWin": "0",
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            },
            {
                "sessionId": "0",
                "bet": "20",
                "brandId": 1,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "GM001",
                "gameId": "gameId",
                "playerCode": "playerId",
                "result": history6.data,
                "roundEnded": true,
                "roundId": "3",
                "eventId": 0,
                "ts": events[5].ts,
                "walletTransactionId": "6",
                "win": "300",
                "balanceAfter": "21090",
                "balanceBefore": "19010",
                "test": false,
                "totalJpContribution": "0",
                "totalJpWin": "0",
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            },
            {
                "sessionId": "0",
                "bet": "0",
                "brandId": 1,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "GM001",
                "gameId": "gameId",
                "playerCode": "playerId",
                "result": history7.data,
                "roundEnded": true,
                "roundId": "4",
                "eventId": 0,
                "ts": events[6].ts,
                "walletTransactionId": "7",
                "win": "0",
                "balanceAfter": "21090",
                "balanceBefore": "21090",
                "test": false,
                "totalJpContribution": "0",
                "totalJpWin": "0",
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            }
        ]);

        const rounds = await getRoundsHistoryModels().original.findAll();
        rounds.sort((i1, i2) => parseInt(i1.id, 10) - parseInt(i2.id, 10));
        expect(rounds.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl", "extraData")))
            .deep
            .equal([
                {
                    "sessionId": "0",
                    "brandId": 1,
                    "currency": "USD",
                    "deviceId": "deviceId",
                    "gameCode": "GM001",
                    "gameId": "gameId",
                    "id": "0",
                    "playerCode": "playerId",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "1000",
                    "totalEvents": "1",
                    "totalWin": "200",
                    "balanceAfter": "19800",
                    "balanceBefore": "20000",
                    "totalJpContribution": "0",
                    "totalJpWin": "0",
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": 1
                },
                {
                    "sessionId": "0",
                    "brandId": 1,
                    "currency": "USD",
                    "deviceId": "deviceId",
                    "gameCode": "GM001",
                    "gameId": "gameId",
                    "id": "1",
                    "playerCode": "playerId",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "2000",
                    "totalEvents": "1",
                    "totalWin": "100",
                    "balanceAfter": "18700",
                    "balanceBefore": "19800",
                    "totalJpContribution": "0",
                    "totalJpWin": "0",
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": 1
                },
                {
                    "sessionId": "0",
                    "brandId": 1,
                    "currency": "USD",
                    "deviceId": "deviceId",
                    "gameCode": "GM001",
                    "gameId": "gameId",
                    "id": "2",
                    "playerCode": "playerId",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "0",
                    "totalEvents": "3",
                    "totalWin": "310",
                    "balanceAfter": "19010",
                    "balanceBefore": "19800",
                    "totalJpContribution": "0",
                    "totalJpWin": "0",
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": 1
                },
                {
                    "sessionId": "0",
                    "brandId": 1,
                    "currency": "USD",
                    "deviceId": "deviceId",
                    "gameCode": "GM001",
                    "gameId": "gameId",
                    "id": "3",
                    "playerCode": "playerId",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "20",
                    "totalEvents": "1",
                    "totalWin": "300",
                    "balanceAfter": "21090",
                    "balanceBefore": "19010",
                    "totalJpContribution": "0",
                    "totalJpWin": "0",
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": 1
                },
                {
                    "sessionId": "0",
                    "brandId": 1,
                    "currency": "USD",
                    "deviceId": "deviceId",
                    "gameCode": "GM001",
                    "gameId": "gameId",
                    "id": "4",
                    "playerCode": "playerId",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "0",
                    "totalEvents": "1",
                    "totalWin": "0",
                    "balanceAfter": "21090",
                    "balanceBefore": "21090",
                    "totalJpContribution": "0",
                    "totalJpWin": "0",
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": 1
                }
            ]);
        expect(await getRoundsHistoryModels().unfinished.findAll()).deep.equal([]);

        const sessions = await getSessionHistoryModels().original.findAll();
        sessions.sort((i1, i2) => parseInt(i1.sessionId, 10) - parseInt(i2.sessionId, 10));
        expect(sessions.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl"))).deep.equal([
            {
                "sessionId": "0",
                "brandId": 1,
                "currency": "USD",
                "country": "US",
                "language": "EN",
                "deviceId": "deviceId",
                "extSessionId": "operator_session_id",
                "gameCode": "GM001",
                "gameId": "gameId",
                "gameVersion": "0.1.0",
                "playerCode": "playerId",
                "broken": false,
                "screenSize": "small",
                "browser": "Chrome",
                "os": "Linux",
                "platform": "Unix",
                "test": false,
                ip: null,
                playedFromCountry: null,
                operatorCountry: null,
                operatorPlayerCountry: null,
                browserVersion: "63.02.51253",
                interruptionReason: null,
                referrer: null,
                operatorSiteId: 1
            }
        ]);

    });

    it("moves broken session to database", async () => {
        await contextManager.findOrCreateGameContext(
            gameID, session, gameData, TEST_MODULE_NAME, requestContext);
        const context = await contextManager.findGameContextById(gameID);
        context.gameContext = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}
                }
            }
        };

        context.lastRequestId = 2;

        const request = { request: "req", requestId: 1 };

        const history = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: { positions: [1, 2, 3] }
        };

        const newState = _.cloneDeep(context.gameContext);
        newState["currentScene"] = "NEWSCENE1";
        const walletOperation: PaymentOperation = {
            operation: "payment",
            transactionId: "1",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history.roundEnded,
            bet: 1000,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation, newState, request, history);

        await context.remove(true);

        const sessionHistoryConsumer = await SessionHistoryConsumer.create();
        const unloadSessionService = new UnloadSessionService(sessionHistoryConsumer);
        await unloadSessionService.unloadData();
        const sessionWorkerItems = await getRange(unloadSessionService.workerList, 0, 1000);
        expect(sessionWorkerItems.length).is.equal(0);

        const sessions = await getSessionHistoryModels().original.findAll();
        sessions.sort((i1, i2) => parseInt(i1.sessionId, 10) - parseInt(i2.sessionId, 10));
        expect(sessions.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl"))).deep.equal([
            {
                "sessionId": "0",
                "brandId": 1,
                "currency": "USD",
                "country": "US",
                "language": "EN",
                "deviceId": "deviceId",
                "extSessionId": "operator_session_id",
                "gameCode": "GM001",
                "gameId": "gameId",
                "gameVersion": "0.1.0",
                "playerCode": "playerId",
                "broken": true,
                "screenSize": "small",
                "browser": "Chrome",
                "os": "Linux",
                "platform": "Unix",
                "test": false,
                "ip": null,
                "playedFromCountry": null,
                operatorCountry: null,
                operatorPlayerCountry: null,
                "browserVersion": "63.02.51253",
                "interruptionReason": "pending_payment",
                referrer: null,
                operatorSiteId: 1
            }
        ]);

    });

    it("skip old sessions to database", async () => {
        await contextManager.findOrCreateGameContext(
            gameID, session, gameData, TEST_MODULE_NAME, requestContext);
        const context = await contextManager.findGameContextById(gameID);
        context.gameContext = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}
                }
            }
        };
        const oldSessionDate = new Date();
        oldSessionDate.setDate(new Date().getDate() - config.unloader.oldSessionHistorySkipThreshold - 1);

        context.createdAt = oldSessionDate;

        context.lastRequestId = 2;

        const request = { request: "req", requestId: 1 };

        // round 0
        const history1 = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: { positions: [1, 2, 3] }
        };

        const newState1 = _.cloneDeep(context.gameContext);
        newState1["currentScene"] = "NEWSCENE1";
        const walletOperation1: PaymentOperation = {
            operation: "payment",
            transactionId: "1",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history1.roundEnded,
            bet: 1000,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, newState1, request, history1);
        await context.commitPendingModification(
            { currency: "USD", main: 19800, previousValue: 20000 });

        await context.remove(true);

        const sessionHistoryConsumer = await SessionHistoryConsumer.create();
        const unloadSessionService = new UnloadSessionService(sessionHistoryConsumer);
        await unloadSessionService.unloadData();
        const sessionWorkerItems = await getRange(unloadSessionService.workerList, 0, 1000);
        expect(sessionWorkerItems.length).is.equal(0);

        const sessions = await getSessionHistoryModels().original.findAll();
        expect(sessions.length).to.equal(0);

    });

    const saveEvent = async (queue: RedisQueue<any>, event: any): Promise<void> => {
        const client = await Redis.get().get();
        try {
            const multi = client.multi();
            queue.save(multi, event);
            return await new Promise<void>((resolve, reject) => {
                multi.exec((err) => {
                    if (err) {
                        return reject(err);
                    }
                    return resolve(undefined);
                });
            });

        } finally {
            await Redis.get().release(client);
        }
    };

    it("processes duplicates in batch while unloading rounds", async () => {
        const unloadService = new UnloadRoundService(await RoundHistoryConsumer.create());
        const roundQueue = new GameRoundQueue();
        const round1: RoundHistory = {
            id: "1",
            sessionId: "1",
            gameId: "game_id",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 100,
            totalWin: 1000,
            totalEvents: 200,
            balanceBefore: 20,
            balanceAfter: 920,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 100,
        };
        const round2: RoundHistory = {
            id: "2",
            sessionId: "1",
            gameId: "game_id",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 100,
            totalWin: 1000,
            totalEvents: 200,
            balanceBefore: 20,
            balanceAfter: 920,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 100,
        };
        await saveEvent(roundQueue, round1);
        await saveEvent(roundQueue, round1);
        await saveEvent(roundQueue, round2);
        await saveEvent(roundQueue, { ...round2, ctrl: 99 });
        await unloadService.unloadData();
        const workerItems = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems.length).is.equal(0);

        const rounds = await getRoundsHistoryModels().original.findAll();
        rounds.sort((i1, i2) => parseInt(i1.id, 10) - parseInt(i2.id, 10));
        expect(rounds.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl", "extraData")))
            .deep
            .equal([
                {
                    "sessionId": "1",
                    "brandId": 2,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "1",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "100",
                    "totalEvents": "200",
                    "totalWin": "1000",
                    "balanceBefore": "20",
                    "balanceAfter": "920",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                },
                {
                    "sessionId": "1",
                    "brandId": 2,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "2",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "100",
                    "totalEvents": "200",
                    "totalWin": "1000",
                    "balanceBefore": "20",
                    "balanceAfter": "920",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                }
            ]);

        const duplicates = await getRoundHistoryDuplicates(0, 100);
        expect(duplicates.map(x => _.omit(x, "pkId", "insertedAt", "extraData"))).deep.equal([
            {
                balanceAfter: "920",
                balanceBefore: "20",
                brandId: 2,
                credit: null,
                ctrl: 99,
                currency: "USD",
                debit: null,
                deviceId: "device",
                finishedAt: round2.finishedAt,
                gameCode: "sw_game",
                gameId: "game_id",
                id: "2",
                playerCode: "player001",
                recoveryType: null,
                sessionId: "1",
                startedAt: round2.startedAt,
                test: false,
                totalBet: "100",
                totalEvents: "200",
                totalJpContribution: null,
                totalJpWin: null,
                totalWin: "1000",
                "operatorSiteId": null
            }
        ]);
    });

    it("processes duplicates while unloading rounds (old) - not found", async () => {
        const unloadService = new UnloadRoundService(await RoundHistoryConsumer.createOld());
        const roundQueue = new GameRoundQueue();
        const round1: RoundHistory = {
            id: "1",
            sessionId: "1",
            gameId: "game_id",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 100,
            totalWin: 1000,
            totalEvents: 200,
            balanceBefore: 20,
            balanceAfter: 920,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 100,
            "operatorSiteId": 1
        };
        await saveEvent(roundQueue, round1);
        await unloadService.unloadData();
        const workerItems = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems.length).is.equal(0);

        let rounds = await getOldRoundHistoryModels().original.findAll();
        rounds.sort((i1, i2) => parseInt(i1.id, 10) - parseInt(i2.id, 10));
        expect(rounds.map((x) => _.omit(x.toJSON(), "finishedAt", "startedAt", "insertedAt", "ctrl", "extraData")))
            .deep
            .equal([
                {
                    "sessionId": "1",
                    "brandId": 2,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "1",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "100",
                    "totalEvents": "200",
                    "totalWin": "1000",
                    "balanceBefore": "20",
                    "balanceAfter": "920",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": 1
                }
            ]);

        const round2: RoundHistory = {
            id: "2",
            sessionId: "1",
            gameId: "game_id",
            brandId: 3,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 40,
            totalWin: 10,
            balanceBefore: 100,
            balanceAfter: 70,
            totalEvents: 10,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 200
        };

        const round3: RoundHistory = {
            id: "3",
            sessionId: "1",
            gameId: "game_id",
            brandId: 4,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 90,
            totalWin: 9,
            totalEvents: 7,
            balanceBefore: 3,
            balanceAfter: 84,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 200
        };

        await saveEvent(roundQueue, round2);
        await saveEvent(roundQueue, round1);
        await saveEvent(roundQueue, round3);
        await unloadService.unloadData();
        const workerItems2 = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems2.length).is.equal(0);

        rounds = await getOldRoundHistoryModels().original.findAll();
        rounds.sort((i1, i2) => parseInt(i1.id, 10) - parseInt(i2.id, 10));
        expect(rounds.map((x) => _.omit(x.toJSON(), "insertedAt", "startedAt", "finishedAt", "ctrl", "extraData")))
            .deep
            .equal([
                {
                    "sessionId": "1",
                    "brandId": 2,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "1",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "100",
                    "totalEvents": "200",
                    "totalWin": "1000",
                    "balanceAfter": "920",
                    "balanceBefore": "20",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": 1
                },
                {
                    "sessionId": "1",
                    "brandId": 3,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "2",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "40",
                    "totalEvents": "10",
                    "totalWin": "10",
                    "balanceAfter": "70",
                    "balanceBefore": "100",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                },
                {
                    "sessionId": "1",
                    "brandId": 4,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "3",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "90",
                    "totalEvents": "7",
                    "totalWin": "9",
                    "balanceAfter": "84",
                    "balanceBefore": "3",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                }
            ]);

        const duplicates = await getRoundHistoryDuplicates(0, 100);
        expect(duplicates).deep.equal([]);
    });

    it("processes duplicates while unloading rounds (old) - found", async () => {
        const unloadService = new UnloadRoundService(await RoundHistoryConsumer.createOld());
        const roundQueue = new GameRoundQueue();
        const round1: RoundHistory = {
            id: "1",
            sessionId: "1",
            gameId: "game_id",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 100,
            totalWin: 1000,
            totalEvents: 200,
            balanceBefore: 20,
            balanceAfter: 920,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 100
        };
        await saveEvent(roundQueue, round1);
        await unloadService.unloadData();
        const workerItems = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems.length).is.equal(0);

        let rounds = await getOldRoundHistoryModels().original.findAll();
        rounds.sort((i1, i2) => parseInt(i1.id, 10) - parseInt(i2.id, 10));
        expect(rounds.map((x) => _.omit(x.toJSON(), "finishedAt", "startedAt", "insertedAt", "ctrl", "extraData")))
            .deep
            .equal([
                {
                    "sessionId": "1",
                    "brandId": 2,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "1",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "100",
                    "totalEvents": "200",
                    "totalWin": "1000",
                    "balanceBefore": "20",
                    "balanceAfter": "920",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                }
            ]);

        const round2: RoundHistory = {
            id: "2",
            sessionId: "1",
            gameId: "game_id",
            brandId: 3,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 40,
            totalWin: 10,
            balanceBefore: 100,
            balanceAfter: 70,
            totalEvents: 10,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 200
        };

        const round3: RoundHistory = {
            id: "3",
            sessionId: "1",
            gameId: "game_id",
            brandId: 4,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 90,
            totalWin: 9,
            totalEvents: 7,
            balanceBefore: 3,
            balanceAfter: 84,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 200,
        };

        await saveEvent(roundQueue, round2);
        await saveEvent(roundQueue, { ...round1, ctrl: 99 });
        await saveEvent(roundQueue, round3);
        await unloadService.unloadData();
        const workerItems2 = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems2.length).is.equal(0);

        rounds = await getOldRoundHistoryModels().original.findAll();
        rounds.sort((i1, i2) => parseInt(i1.id, 10) - parseInt(i2.id, 10));
        expect(rounds.map((x) => _.omit(x.toJSON(), "insertedAt", "startedAt", "finishedAt", "ctrl", "extraData")))
            .deep
            .equal([
                {
                    "sessionId": "1",
                    "brandId": 2,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "1",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "100",
                    "totalEvents": "200",
                    "totalWin": "1000",
                    "balanceAfter": "920",
                    "balanceBefore": "20",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                },
                {
                    "sessionId": "1",
                    "brandId": 3,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "2",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "40",
                    "totalEvents": "10",
                    "totalWin": "10",
                    "balanceAfter": "70",
                    "balanceBefore": "100",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                },
                {
                    "sessionId": "1",
                    "brandId": 4,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "3",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "90",
                    "totalEvents": "7",
                    "totalWin": "9",
                    "balanceAfter": "84",
                    "balanceBefore": "3",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                }
            ]);

        const duplicates = await getRoundHistoryDuplicates(0, 100);
        expect(duplicates.map(x => _.omit(x, "pkId", "insertedAt", "extraData"))).deep.equal([
            {
                balanceAfter: "920",
                balanceBefore: "20",
                brandId: 2,
                credit: null,
                ctrl: 99,
                currency: "USD",
                debit: null,
                deviceId: "device",
                finishedAt: round1.finishedAt,
                gameCode: "sw_game",
                gameId: "game_id",
                id: "1",
                playerCode: "player001",
                recoveryType: null,
                sessionId: "1",
                startedAt: round1.startedAt,
                test: false,
                totalBet: "100",
                totalEvents: "200",
                totalJpContribution: null,
                totalJpWin: null,
                totalWin: "1000",
                "operatorSiteId": null
            }
        ]);
    });

    it("processes duplicates while unloading rounds (old) - found without ctrl", async () => {
        const unloadService = new UnloadRoundService(await RoundHistoryConsumer.createOld());
        const roundQueue = new GameRoundQueue();
        const round1: RoundHistory = {
            id: "1",
            sessionId: "1",
            gameId: "game_id",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 100,
            totalWin: 1000,
            totalEvents: 200,
            balanceBefore: 20,
            balanceAfter: 920,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: undefined,
            "operatorSiteId": 1
        };
        await saveEvent(roundQueue, round1);
        await unloadService.unloadData();
        const workerItems = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems.length).is.equal(0);

        let rounds = await getOldRoundHistoryModels().original.findAll();
        rounds.sort((i1, i2) => parseInt(i1.id, 10) - parseInt(i2.id, 10));
        expect(rounds.map((x) => _.omit(x.toJSON(), "finishedAt", "startedAt", "insertedAt", "ctrl", "extraData")))
            .deep
            .equal([
                {
                    "sessionId": "1",
                    "brandId": 2,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "1",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "100",
                    "totalEvents": "200",
                    "totalWin": "1000",
                    "balanceBefore": "20",
                    "balanceAfter": "920",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": 1
                }
            ]);

        const round2: RoundHistory = {
            id: "2",
            sessionId: "1",
            gameId: "game_id",
            brandId: 3,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 40,
            totalWin: 10,
            balanceBefore: 100,
            balanceAfter: 70,
            totalEvents: 10,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 200
        };

        const round3: RoundHistory = {
            id: "3",
            sessionId: "1",
            gameId: "game_id",
            brandId: 4,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 90,
            totalWin: 9,
            totalEvents: 7,
            balanceBefore: 3,
            balanceAfter: 84,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 200,
        };

        await saveEvent(roundQueue, round2);
        await saveEvent(roundQueue, round1);
        await saveEvent(roundQueue, round3);
        await unloadService.unloadData();
        const workerItems2 = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems2.length).is.equal(0);

        rounds = await getOldRoundHistoryModels().original.findAll();
        rounds.sort((i1, i2) => parseInt(i1.id, 10) - parseInt(i2.id, 10));
        expect(rounds.map((x) => _.omit(x.toJSON(), "insertedAt", "startedAt", "finishedAt", "ctrl", "extraData")))
            .deep
            .equal([
                {
                    "sessionId": "1",
                    "brandId": 2,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "1",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "100",
                    "totalEvents": "200",
                    "totalWin": "1000",
                    "balanceAfter": "920",
                    "balanceBefore": "20",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": 1
                },
                {
                    "sessionId": "1",
                    "brandId": 3,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "2",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "40",
                    "totalEvents": "10",
                    "totalWin": "10",
                    "balanceAfter": "70",
                    "balanceBefore": "100",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                },
                {
                    "sessionId": "1",
                    "brandId": 4,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "3",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "90",
                    "totalEvents": "7",
                    "totalWin": "9",
                    "balanceAfter": "84",
                    "balanceBefore": "3",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                }
            ]);

        const duplicates = await getRoundHistoryDuplicates(0, 100);
        expect(duplicates.map(x => _.omit(x, "pkId", "insertedAt", "extraData"))).deep.equal([
            {
                balanceAfter: "920",
                balanceBefore: "20",
                brandId: 2,
                credit: null,
                ctrl: null,
                currency: "USD",
                debit: null,
                deviceId: "device",
                finishedAt: round1.finishedAt,
                gameCode: "sw_game",
                gameId: "game_id",
                id: "1",
                playerCode: "player001",
                recoveryType: null,
                sessionId: "1",
                startedAt: round1.startedAt,
                test: false,
                totalBet: "100",
                totalEvents: "200",
                totalJpContribution: null,
                totalJpWin: null,
                totalWin: "1000",
                "operatorSiteId": 1
            }
        ]);
    });

    it("processes duplicates while unloading rounds - not found", async () => {
        const unloadService = new UnloadRoundService(await RoundHistoryConsumer.create());
        const roundQueue = new GameRoundQueue();
        const round1: RoundHistory = {
            id: "1",
            sessionId: "1",
            gameId: "game_id",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 100,
            totalWin: 1000,
            totalEvents: 200,
            balanceBefore: 20,
            balanceAfter: 920,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 100,
            "operatorSiteId": 1
        };
        await saveEvent(roundQueue, round1);
        await unloadService.unloadData();
        const workerItems = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems.length).is.equal(0);

        let rounds = await getRoundsHistoryModels().original.findAll();
        rounds.sort((i1, i2) => parseInt(i1.id, 10) - parseInt(i2.id, 10));
        expect(rounds.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl", "extraData")))
            .deep
            .equal([
                {
                    "sessionId": "1",
                    "brandId": 2,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "1",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "100",
                    "totalEvents": "200",
                    "totalWin": "1000",
                    "balanceBefore": "20",
                    "balanceAfter": "920",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": 1
                }
            ]);

        const round2: RoundHistory = {
            id: "2",
            sessionId: "1",
            gameId: "game_id",
            brandId: 3,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 40,
            totalWin: 10,
            balanceBefore: 100,
            balanceAfter: 70,
            totalEvents: 10,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 100,
            "operatorSiteId": 1
        };

        const round3: RoundHistory = {
            id: "3",
            sessionId: "1",
            gameId: "game_id",
            brandId: 4,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 90,
            totalWin: 9,
            totalEvents: 7,
            balanceBefore: 3,
            balanceAfter: 84,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 100,
            "operatorSiteId": 1
        };

        await saveEvent(roundQueue, round2);
        await saveEvent(roundQueue, round1);
        await saveEvent(roundQueue, round3);
        await unloadService.unloadData();
        const workerItems2 = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems2.length).is.equal(0);

        rounds = await getRoundsHistoryModels().original.findAll();
        rounds.sort((i1, i2) => parseInt(i1.id, 10) - parseInt(i2.id, 10));
        expect(rounds.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl", "extraData")))
            .deep
            .equal([
                {
                    "sessionId": "1",
                    "brandId": 2,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "1",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "100",
                    "totalEvents": "200",
                    "totalWin": "1000",
                    "balanceAfter": "920",
                    "balanceBefore": "20",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": 1
                },
                {
                    "sessionId": "1",
                    "brandId": 3,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "2",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "40",
                    "totalEvents": "10",
                    "totalWin": "10",
                    "balanceAfter": "70",
                    "balanceBefore": "100",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": 1
                },
                {
                    "sessionId": "1",
                    "brandId": 4,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "3",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "90",
                    "totalEvents": "7",
                    "totalWin": "9",
                    "balanceAfter": "84",
                    "balanceBefore": "3",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": 1
                }
            ]);

        const duplicates = await getRoundHistoryDuplicates(0, 100);
        expect(duplicates).deep.equal([]);
        expect(await getRoundsHistoryModels().unfinished.findAll()).deep.equal([]);
    });

    it("processes duplicates while unloading rounds - found", async () => {
        const unloadService = new UnloadRoundService(await RoundHistoryConsumer.create());
        const roundQueue = new GameRoundQueue();
        const round0: RoundHistory = predefinedRounds[0];
        const round1: RoundHistory = {
            id: "1",
            sessionId: "1",
            gameId: "game_id",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 100,
            totalWin: 1000,
            totalEvents: 200,
            balanceBefore: 20,
            balanceAfter: 920,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 100,
        };
        await saveEvent(roundQueue, round0);
        await saveEvent(roundQueue, round1);
        await unloadService.unloadData();
        const workerItems = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems.length).is.equal(0);

        let rounds = await getRoundsHistoryModels().original.findAll();
        rounds.sort((i1, i2) => parseInt(i1.id, 10) - parseInt(i2.id, 10));
        expect(rounds.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl", "extraData")))
            .deep
            .equal([
                {
                    "sessionId": "1",
                    "brandId": 2,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "1",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "100",
                    "totalEvents": "200",
                    "totalWin": "1000",
                    "balanceBefore": "20",
                    "balanceAfter": "920",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                }
            ]);

        rounds = await getRoundsHistoryModels().unfinished.findAll();
        expect(rounds.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl", "extraData")))
            .deep
            .equal([
                {
                    "sessionId": "1",
                    "brandId": 4,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "0",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "90",
                    "totalEvents": "7",
                    "totalWin": "9",
                    "balanceAfter": "84",
                    "balanceBefore": "3",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                }
            ]);

        const round2: RoundHistory = predefinedRounds[1];

        const round3: RoundHistory = predefinedRounds[2];

        await saveEvent(roundQueue, round2);
        await saveEvent(roundQueue, { ...round0, ctrl: 9, totalEvents: 8 });
        await saveEvent(roundQueue, { ...round1, ctrl: 99 });
        await saveEvent(roundQueue, round3);
        await unloadService.unloadData();
        const workerItems2 = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems2.length).is.equal(0);

        rounds = await getRoundsHistoryModels().original.findAll();
        rounds.sort((i1, i2) => parseInt(i1.id, 10) - parseInt(i2.id, 10));
        expect(rounds.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl", "extraData")))
            .deep
            .equal([
                {
                    "sessionId": "1",
                    "brandId": 2,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "1",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "100",
                    "totalEvents": "200",
                    "totalWin": "1000",
                    "balanceAfter": "920",
                    "balanceBefore": "20",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                },
                {
                    "sessionId": "1",
                    "brandId": 3,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "2",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "40",
                    "totalEvents": "10",
                    "totalWin": "10",
                    "balanceAfter": "70",
                    "balanceBefore": "100",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                },
                {
                    "sessionId": "1",
                    "brandId": 4,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "3",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "90",
                    "totalEvents": "7",
                    "totalWin": "9",
                    "balanceAfter": "84",
                    "balanceBefore": "3",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                }
            ]);
        rounds = await getRoundsHistoryModels().unfinished.findAll();
        expect(rounds.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl", "extraData")))
            .deep
            .equal([
                {
                    "sessionId": "1",
                    "brandId": 4,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "0",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "90",
                    "totalEvents": "8",
                    "totalWin": "9",
                    "balanceAfter": "84",
                    "balanceBefore": "3",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                }
            ]);

        const duplicates = await getRoundHistoryDuplicates(0, 100);
        duplicates.sort((i1, i2) => parseInt(i1.id, 10) - parseInt(i2.id, 10));
        expect(duplicates.map(x => _.omit(x, "pkId", "insertedAt", "extraData"))).deep.equal([
            {
                balanceAfter: "920",
                balanceBefore: "20",
                brandId: 2,
                credit: null,
                ctrl: 99,
                currency: "USD",
                debit: null,
                deviceId: "device",
                finishedAt: round1.finishedAt,
                gameCode: "sw_game",
                gameId: "game_id",
                id: "1",
                playerCode: "player001",
                recoveryType: null,
                sessionId: "1",
                startedAt: round1.startedAt,
                test: false,
                totalBet: "100",
                totalEvents: "200",
                totalJpContribution: null,
                totalJpWin: null,
                totalWin: "1000",
                "operatorSiteId": null
            }
        ]);
    });

    it("processes duplicates while unloading game history - not found", async () => {
        const unloadService = new UnloadEventHistoryService(await GameHistoryConsumer.create());
        const queue = new GameHistoryQueue();
        const event1: GameEventHistory = predefinedGHEvents[0];
        await saveEvent(queue, event1);
        await unloadService.unloadData();
        const workerItems2 = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems2.length).is.equal(0);

        let result = await getGameHistoryModels().original.findAll();
        result.sort((i1, i2) => i1.eventId - i2.eventId);
        expect(result.map((x) => _.omit(x.toJSON(), "insertedAt", "ctrl"))).deep.equal([
            {
                "sessionId": "1",
                "bet": "100",
                "brandId": 2,
                "currency": "USD",
                "deviceId": "device",
                "eventId": 1,
                "gameCode": "sw_game",
                "gameId": "game_id",
                "playerCode": "player001",
                "result": {
                    "spinResult": "spin_result",
                },
                "roundEnded": true,
                "roundId": "1",
                "test": false,
                "ts": result[0].ts,
                "type": "slot",
                "balanceBefore": "200",
                "balanceAfter": "1100",
                "gameVersion": TEST_MODULE_NAME.version,
                "walletTransactionId": "trx_id",
                "win": "1000",
                "totalJpContribution": null,
                "totalJpWin": null,
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            }
        ]);

        const event2: GameEventHistory = predefinedGHEvents[1];

        const event3: GameEventHistory = predefinedGHEvents[2];

        await saveEvent(queue, event2);
        await saveEvent(queue, event1);
        await saveEvent(queue, event3);
        await unloadService.unloadData();
        const workerItems = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems.length).is.equal(0);

        result = await getGameHistoryModels().original.findAll();
        result.sort((i1, i2) => i1.eventId - i2.eventId);
        expect(result.map((x) => _.omit(x.toJSON(), "insertedAt", "ctrl"))).deep.equal([
            {
                "sessionId": "1",
                "bet": "100",
                "brandId": 2,
                "currency": "USD",
                "deviceId": "device",
                "eventId": 1,
                "gameCode": "sw_game",
                "gameId": "game_id",
                "playerCode": "player001",
                "result": {
                    "spinResult": "spin_result",
                },
                "roundEnded": true,
                "roundId": "1",
                "test": false,
                "ts": result[0].ts,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "walletTransactionId": "trx_id",
                "win": "1000",
                "balanceAfter": "1100",
                "balanceBefore": "200",
                "totalJpContribution": null,
                "totalJpWin": null,
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            },
            {
                "sessionId": "2",
                "bet": "10",
                "brandId": 2,
                "currency": "USD",
                "deviceId": "device",
                "eventId": 2,
                "gameCode": "sw_game",
                "gameId": "game_id",
                "playerCode": "player002",
                "result": {
                    "spinResult": "spin_result",
                },
                "roundEnded": true,
                "roundId": "2",
                "test": false,
                "ts": result[1].ts,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "walletTransactionId": "trx_id",
                "win": "4000",
                "balanceAfter": "4040",
                "balanceBefore": "50",
                "totalJpContribution": null,
                "totalJpWin": null,
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            },
            {
                "sessionId": "2",
                "bet": "500",
                "brandId": 2,
                "currency": "USD",
                "deviceId": "device",
                "eventId": 3,
                "gameCode": "sw_game",
                "gameId": "game_id",
                "playerCode": "player003",
                "result": {
                    "spinResult": "spin_result",
                },
                "roundEnded": true,
                "roundId": "3",
                "test": false,
                "ts": result[2].ts,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "walletTransactionId": "trx_id",
                "win": "4",
                "balanceAfter": "4040",
                "balanceBefore": "3544",
                "totalJpContribution": null,
                "totalJpWin": null,
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            }
        ]);

        const duplicates = await getGameHistoryDuplicates(0, 100);
        expect(duplicates).deep.eq([]);
    });

    it("processes duplicates while unloading game history - found", async () => {
        const unloadService = new UnloadEventHistoryService(await GameHistoryConsumer.create());
        const queue = new GameHistoryQueue();
        const event1: GameEventHistory = predefinedGHEvents[0];
        await saveEvent(queue, event1);
        await unloadService.unloadData();
        const workerItems2 = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems2.length).is.equal(0);

        let result = await getGameHistoryModels().original.findAll();
        result.sort((i1, i2) => i1.eventId - i2.eventId);
        expect(result.map((x) => _.omit(x.toJSON(), "insertedAt", "ctrl"))).deep.equal([
            {
                "sessionId": "1",
                "bet": "100",
                "brandId": 2,
                "currency": "USD",
                "deviceId": "device",
                "eventId": 1,
                "gameCode": "sw_game",
                "gameId": "game_id",
                "playerCode": "player001",
                "result": {
                    "spinResult": "spin_result",
                },
                "roundEnded": true,
                "roundId": "1",
                "test": false,
                "ts": result[0].ts,
                "type": "slot",
                "balanceBefore": "200",
                "balanceAfter": "1100",
                "gameVersion": TEST_MODULE_NAME.version,
                "walletTransactionId": "trx_id",
                "win": "1000",
                "totalJpContribution": null,
                "totalJpWin": null,
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            }
        ]);

        const event2: GameEventHistory = predefinedGHEvents[1];

        const event3: GameEventHistory = predefinedGHEvents[2];

        await saveEvent(queue, event2);
        await saveEvent(queue, { ...event1, ctrl: 99 });
        await saveEvent(queue, event3);
        await unloadService.unloadData();
        const workerItems = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems.length).is.equal(0);

        result = await getGameHistoryModels().original.findAll();
        result.sort((i1, i2) => i1.eventId - i2.eventId);
        expect(result.map((x) => _.omit(x.toJSON(), "insertedAt", "ctrl"))).deep.equal([
            {
                "sessionId": "1",
                "bet": "100",
                "brandId": 2,
                "currency": "USD",
                "deviceId": "device",
                "eventId": 1,
                "gameCode": "sw_game",
                "gameId": "game_id",
                "playerCode": "player001",
                "result": {
                    "spinResult": "spin_result",
                },
                "roundEnded": true,
                "roundId": "1",
                "test": false,
                "ts": result[0].ts,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "walletTransactionId": "trx_id",
                "win": "1000",
                "balanceAfter": "1100",
                "balanceBefore": "200",
                "totalJpContribution": null,
                "totalJpWin": null,
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            },
            {
                "sessionId": "2",
                "bet": "10",
                "brandId": 2,
                "currency": "USD",
                "deviceId": "device",
                "eventId": 2,
                "gameCode": "sw_game",
                "gameId": "game_id",
                "playerCode": "player002",
                "result": {
                    "spinResult": "spin_result",
                },
                "roundEnded": true,
                "roundId": "2",
                "test": false,
                "ts": result[1].ts,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "walletTransactionId": "trx_id",
                "win": "4000",
                "balanceAfter": "4040",
                "balanceBefore": "50",
                "totalJpContribution": null,
                "totalJpWin": null,
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            },
            {
                "sessionId": "2",
                "bet": "500",
                "brandId": 2,
                "currency": "USD",
                "deviceId": "device",
                "eventId": 3,
                "gameCode": "sw_game",
                "gameId": "game_id",
                "playerCode": "player003",
                "result": {
                    "spinResult": "spin_result",
                },
                "roundEnded": true,
                "roundId": "3",
                "test": false,
                "ts": result[2].ts,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "walletTransactionId": "trx_id",
                "win": "4",
                "balanceAfter": "4040",
                "balanceBefore": "3544",
                "totalJpContribution": null,
                "totalJpWin": null,
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            }
        ]);

        const duplicates = await getGameHistoryDuplicates(0, 100);
        expect(duplicates.map(x => _.omit(x, "insertedAt", "id"))).deep.eq([
            {
                balanceAfter: "1100",
                balanceBefore: "200",
                bet: "100",
                brandId: 2,
                credit: null,
                ctrl: 99,
                currency: "USD",
                debit: null,
                deviceId: "device",
                eventId: 1,
                extraData: null,
                gameCode: "sw_game",
                gameId: "game_id",
                gameVersion: "0.1.0",
                playerCode: "player001",
                result: {
                    spinResult: "spin_result",
                },
                roundEnded: true,
                roundId: "1",
                sessionId: "1",
                test: false,
                totalJpContribution: null,
                totalJpWin: null,
                ts: event1.ts,
                type: "slot",
                walletTransactionId: "trx_id",
                win: "1000",
                freeBetCoin: null,
                lobbySessionId: null,
                isHidden: null,
            }
        ]);
    });

    it("processes old timestamps while unloading game history", async () => {
        const unloadService = new UnloadEventHistoryService(await GameHistoryConsumer.create());
        const queue = new GameHistoryQueue();
        const event: GameEventHistory = {
            roundId: "1",
            sessionId: "1",
            eventId: 1,
            type: "slot",
            gameVersion: "0.1.0",
            gameId: "game_id",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            walletTransactionId: "trx_id",
            bet: 100,
            win: 1000,
            balanceBefore: 200,
            balanceAfter: 1100,
            roundEnded: true,
            result: {
                spinResult: "spin_result",
            },
            ts: new Date(Date.now() - 356 * 24 * 60 * 60 * 1000),   // 1 year old
            test: false,
            ctrl: 100,
        };
        await saveEvent(queue, event);
        await unloadService.unloadData();
        const workerItems = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems.length).is.equal(0);

        const result = await getGameHistoryModels().original.findAll();
        expect(result[0].ts.getTime()).to.be.approximately(Date.now(), 1000);
        expect(result.map((x) => _.omit(x.toJSON(), "insertedAt", "ctrl", "ts"))).deep.equal([
            {
                "sessionId": "1",
                "bet": "100",
                "brandId": 2,
                "currency": "USD",
                "deviceId": "device",
                "eventId": 1,
                "gameCode": "sw_game",
                "gameId": "game_id",
                "playerCode": "player001",
                "result": {
                    "spinResult": "spin_result",
                },
                "roundEnded": true,
                "roundId": "1",
                "test": false,
                "type": "slot",
                "balanceBefore": "200",
                "balanceAfter": "1100",
                "gameVersion": TEST_MODULE_NAME.version,
                "walletTransactionId": "trx_id",
                "win": "1000",
                "totalJpContribution": null,
                "totalJpWin": null,
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            }
        ]);
    });

    it("processes old timestamps while unloading round history ", async () => {
        const unloadService = new UnloadRoundService(await RoundHistoryConsumer.create());
        const queue = new GameRoundQueue();
        const event: RoundHistory = {
            id: "1",
            sessionId: "1",
            gameId: "game_id",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 100,
            totalWin: 1000,
            totalJpWin: 1,
            totalEvents: 10,
            balanceBefore: 200,
            balanceAfter: 1100,
            startedAt: new Date(Date.now() - 356 * 24 * 60 * 60 * 1000),   // 1 year old
            finishedAt: new Date(Date.now() - 356 * 24 * 60 * 60 * 1000),   // 1 year old
            test: false,
            ctrl: 100,
        };
        await saveEvent(queue, event);
        await unloadService.unloadData();
        const workerItems = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems.length).is.equal(0);

        const result = await getRoundsHistoryModels().original.findAll();
        expect(result[0].finishedAt.getTime()).to.be.approximately(Date.now(), 1000);
    });

    it("process duplicates while unloading sessions - not found", async () => {
        const unloadService = new UnloadSessionService(await SessionHistoryConsumer.create());
        const sessionQueue = new GameSessionQueue();
        const history1: SessionHistory = {
            sessionId: "1",
            gameId: "game_id",
            gameVersion: "0.1.0",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            browser: "Chrome",
            os: "Linux",
            platform: "Unix",
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            broken: false,
            browserVersion: "63.05.123145",
            ctrl: 100,
            referrer: "google.com",
            operatorSiteId: 1,
            extSessionId: "operator_session_id"
        };
        await saveEvent(sessionQueue, history1);
        await unloadService.unloadData();

        const history2: SessionHistory = {
            sessionId: "2",
            gameId: "game_id_2",
            gameVersion: "0.1.0",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            browser: "Chrome",
            os: "Linux",
            platform: "Unix",
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            broken: false,
            browserVersion: "63.05.123145",
            ctrl: 200,
            referrer: "google.com",
            operatorSiteId: 2,
            extSessionId: "operator_session_id"
        };

        const history3: SessionHistory = {
            sessionId: "3",
            gameId: "game_id_3",
            gameVersion: "0.1.0",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            browser: "Chrome",
            os: "Linux",
            platform: "Unix",
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            broken: false,
            browserVersion: "63.05.123145",
            ctrl: 300,
            referrer: "google.com",
            operatorSiteId: 3,
            extSessionId: "operator_session_id"
        };

        await saveEvent(sessionQueue, history2);
        await saveEvent(sessionQueue, history1);
        await saveEvent(sessionQueue, history3);

        await unloadService.unloadData();
        const sessions = await getSessionHistoryModels().original.findAll();
        sessions.sort((i1, i2) => parseInt(i1.sessionId, 10) - parseInt(i2.sessionId, 10));
        expect(sessions.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl"))).deep.equal([
            {
                sessionId: "1",
                brandId: 2,
                currency: "USD",
                country: null,
                language: null,
                deviceId: "device",
                gameCode: "sw_game",
                gameId: "game_id",
                gameVersion: "0.1.0",
                playerCode: "player001",
                screenSize: null,
                browser: "Chrome",
                os: "Linux",
                platform: "Unix",
                test: false,
                broken: false,
                ip: null,
                playedFromCountry: null,
                operatorCountry: null,
                operatorPlayerCountry: null,
                browserVersion: "63.05.123145",
                interruptionReason: null,
                referrer: "google.com",
                operatorSiteId: 1,
                extSessionId: "operator_session_id"
            },
            {
                sessionId: "2",
                gameId: "game_id_2",
                gameVersion: "0.1.0",
                brandId: 2,
                playerCode: "player001",
                deviceId: "device",
                gameCode: "sw_game",
                currency: "USD",
                browser: "Chrome",
                os: "Linux",
                platform: "Unix",
                test: false,
                broken: false,
                browserVersion: "63.05.123145",
                screenSize: null,
                country: null,
                language: null,
                ip: null,
                playedFromCountry: null,
                operatorCountry: null,
                operatorPlayerCountry: null,
                interruptionReason: null,
                referrer: "google.com",
                operatorSiteId: 2,
                extSessionId: "operator_session_id"
            },
            {
                sessionId: "3",
                gameId: "game_id_3",
                gameVersion: "0.1.0",
                brandId: 2,
                playerCode: "player001",
                deviceId: "device",
                gameCode: "sw_game",
                currency: "USD",
                browser: "Chrome",
                os: "Linux",
                platform: "Unix",
                test: false,
                broken: false,
                browserVersion: "63.05.123145",
                screenSize: null,
                country: null,
                language: null,
                ip: null,
                playedFromCountry: null,
                operatorCountry: null,
                operatorPlayerCountry: null,
                interruptionReason: null,
                referrer: "google.com",
                operatorSiteId: 3,
                extSessionId: "operator_session_id"
            }
        ]);
        const duplicates = await getSessionHistoryModels().duplicates.findAll();
        expect(duplicates).deep.equal([]);
    });

    it("process duplicates while unloading sessions - found", async () => {
        const unloadService = new UnloadSessionService(await SessionHistoryConsumer.create());
        const sessionQueue = new GameSessionQueue();
        const history1: SessionHistory = {
            sessionId: "1",
            gameId: "game_id",
            gameVersion: "0.1.0",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            browser: "Chrome",
            os: "Linux",
            platform: "Unix",
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            broken: false,
            browserVersion: "63.05.123145",
            ctrl: 100,
            operatorSiteId: 1,
            extSessionId: "operator_session_id"
        };
        await saveEvent(sessionQueue, history1);
        await unloadService.unloadData();

        const history2: SessionHistory = {
            sessionId: "2",
            gameId: "game_id_2",
            gameVersion: "0.1.0",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            browser: "Chrome",
            os: "Linux",
            platform: "Unix",
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            broken: false,
            browserVersion: "63.05.123145",
            ctrl: 200,
            operatorSiteId: 2,
            extSessionId: "operator_session_id"
        };

        const history3: SessionHistory = {
            sessionId: "3",
            gameId: "game_id_3",
            gameVersion: "0.1.0",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            browser: "Chrome",
            os: "Linux",
            platform: "Unix",
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            broken: false,
            browserVersion: "63.05.123145",
            ctrl: 300,
            operatorSiteId: 3,
            extSessionId: "operator_session_id"
        };

        await saveEvent(sessionQueue, history2);
        await saveEvent(sessionQueue, { ...history1, ctrl: 99 });
        await saveEvent(sessionQueue, history3);

        await unloadService.unloadData();
        const sessions = await getSessionHistoryModels().original.findAll();
        sessions.sort((i1, i2) => parseInt(i1.sessionId, 10) - parseInt(i2.sessionId, 10));
        expect(sessions.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl"))).deep.equal([
            {
                sessionId: "1",
                brandId: 2,
                currency: "USD",
                country: null,
                language: null,
                deviceId: "device",
                gameCode: "sw_game",
                gameId: "game_id",
                gameVersion: "0.1.0",
                playerCode: "player001",
                screenSize: null,
                browser: "Chrome",
                os: "Linux",
                platform: "Unix",
                test: false,
                broken: false,
                ip: null,
                playedFromCountry: null,
                operatorCountry: null,
                operatorPlayerCountry: null,
                browserVersion: "63.05.123145",
                interruptionReason: null,
                referrer: null,
                operatorSiteId: 1,
                extSessionId: "operator_session_id"
            },
            {
                sessionId: "2",
                gameId: "game_id_2",
                gameVersion: "0.1.0",
                brandId: 2,
                playerCode: "player001",
                deviceId: "device",
                gameCode: "sw_game",
                currency: "USD",
                browser: "Chrome",
                os: "Linux",
                platform: "Unix",
                test: false,
                broken: false,
                browserVersion: "63.05.123145",
                screenSize: null,
                country: null,
                language: null,
                ip: null,
                playedFromCountry: null,
                operatorCountry: null,
                operatorPlayerCountry: null,
                interruptionReason: null,
                referrer: null,
                operatorSiteId: 2,
                extSessionId: "operator_session_id"
            },
            {
                sessionId: "3",
                gameId: "game_id_3",
                gameVersion: "0.1.0",
                brandId: 2,
                playerCode: "player001",
                deviceId: "device",
                gameCode: "sw_game",
                currency: "USD",
                browser: "Chrome",
                os: "Linux",
                platform: "Unix",
                test: false,
                broken: false,
                browserVersion: "63.05.123145",
                screenSize: null,
                country: null,
                language: null,
                ip: null,
                playedFromCountry: null,
                operatorCountry: null,
                operatorPlayerCountry: null,
                interruptionReason: null,
                referrer: null,
                operatorSiteId: 3,
                extSessionId: "operator_session_id"
            }
        ]);
        const duplicates = await getSessionHistoryModels().duplicates.findAll();
        expect(
            duplicates.map(x => _.omit(x.get(), "insertedAt", "startedAt", "finishedAt", "id"))
        ).deep.equal([
            {
                sessionId: "1",
                brandId: 2,
                currency: "USD",
                country: null,
                language: null,
                deviceId: "device",
                gameCode: "sw_game",
                gameId: "game_id",
                gameVersion: "0.1.0",
                playerCode: "player001",
                screenSize: null,
                browser: "Chrome",
                os: "Linux",
                platform: "Unix",
                test: false,
                broken: false,
                ip: null,
                playedFromCountry: null,
                operatorCountry: null,
                operatorPlayerCountry: null,
                browserVersion: "63.05.123145",
                ctrl: 99,
                interruptionReason: null,
                referrer: null,
                operatorSiteId: 1,
                extSessionId: "operator_session_id"
            },
        ]);
    });

    it("process duplicates while unloading sessions, skip unfinished sessions", async () => {
        const unloadService = new UnloadSessionService(await SessionHistoryConsumer.create());
        const sessionQueue = new GameSessionQueue();
        const history1: SessionHistory = {
            sessionId: "1",
            gameId: "game_id",
            gameVersion: "0.1.0",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            browser: "Chrome",
            os: "Linux",
            platform: "Unix",
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            broken: false,
            browserVersion: "63.05.123145",
            ctrl: 100,
            extSessionId: "operator_session_id"
        };
        const history2: SessionHistory = {
            sessionId: "2",
            gameId: "game_id_2",
            gameVersion: "0.1.0",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            browser: "Chrome",
            os: "Linux",
            platform: "Unix",
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            broken: false,
            browserVersion: "63.05.123145",
            ctrl: 200,
            extSessionId: "operator_session_id"
        };
        const history3: SessionHistory = {
            sessionId: "3",
            gameId: "game_id_3",
            gameVersion: "0.1.0",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            browser: "Chrome",
            os: "Linux",
            platform: "Unix",
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            broken: false,
            browserVersion: "63.05.123145",
            ctrl: 300,
            extSessionId: "operator_session_id"
        };

        await saveEvent(sessionQueue, history1);
        await saveEvent(sessionQueue, history2);
        await saveEvent(sessionQueue, history3);
        await unloadService.unloadData();

        await saveEvent(sessionQueue, history2);
        await saveEvent(sessionQueue, { ...history1, finishedAt: null, ctrl: 99 });
        await saveEvent(sessionQueue, history3);

        await unloadService.unloadData();
        const sessions = await getSessionHistoryModels().original.findAll();
        sessions.sort((i1, i2) => parseInt(i1.sessionId, 10) - parseInt(i2.sessionId, 10));
        expect(sessions.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl"))).deep.equal([
            {
                sessionId: "1",
                brandId: 2,
                currency: "USD",
                country: null,
                language: null,
                deviceId: "device",
                gameCode: "sw_game",
                gameId: "game_id",
                gameVersion: "0.1.0",
                playerCode: "player001",
                screenSize: null,
                browser: "Chrome",
                os: "Linux",
                platform: "Unix",
                test: false,
                broken: false,
                ip: null,
                playedFromCountry: null,
                operatorCountry: null,
                operatorPlayerCountry: null,
                browserVersion: "63.05.123145",
                interruptionReason: null,
                referrer: null,
                operatorSiteId: null,
                extSessionId: "operator_session_id"
            },
            {
                sessionId: "2",
                gameId: "game_id_2",
                gameVersion: "0.1.0",
                brandId: 2,
                playerCode: "player001",
                deviceId: "device",
                gameCode: "sw_game",
                currency: "USD",
                browser: "Chrome",
                os: "Linux",
                platform: "Unix",
                test: false,
                broken: false,
                browserVersion: "63.05.123145",
                screenSize: null,
                country: null,
                language: null,
                ip: null,
                playedFromCountry: null,
                operatorCountry: null,
                operatorPlayerCountry: null,
                interruptionReason: null,
                referrer: null,
                operatorSiteId: null,
                extSessionId: "operator_session_id"
            },
            {
                sessionId: "3",
                gameId: "game_id_3",
                gameVersion: "0.1.0",
                brandId: 2,
                playerCode: "player001",
                deviceId: "device",
                gameCode: "sw_game",
                currency: "USD",
                browser: "Chrome",
                os: "Linux",
                platform: "Unix",
                test: false,
                broken: false,
                browserVersion: "63.05.123145",
                screenSize: null,
                country: null,
                language: null,
                ip: null,
                playedFromCountry: null,
                operatorCountry: null,
                operatorPlayerCountry: null,
                interruptionReason: null,
                referrer: null,
                operatorSiteId: null,
                extSessionId: "operator_session_id"
            }
        ]);
        const duplicates = await getSessionHistoryModels().duplicates.findAll();
        expect(duplicates.map(x => _.omit(x.get(), "insertedAt", "startedAt", "finishedAt", "id")))
            .deep
            .equal([]);
    });

    it("processes constraints while unloading game history", async () => {
        const unloadService = new UnloadEventHistoryService(await GameHistoryConsumer.create());
        const queue = new GameHistoryQueue();
        const event1: GameEventHistory = {
            sessionId: "1",
            roundId: "1",
            eventId: 1,
            type: "slot",
            gameVersion: "0.1.0",
            gameId: "game_id",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            walletTransactionId: "trx_id",
            bet: 100,
            win: 1000,
            balanceBefore: 200,
            balanceAfter: 1100,
            roundEnded: true,
            result: undefined,
            ts: new Date(),
            test: false,
            ctrl: 100
        };
        await saveEvent(queue, event1);
        await unloadService.unloadData();
        const workerItems = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems.length).is.equal(0);

        const result = await getGameHistoryModels().original.findAll();
        expect(result).deep.equals([]);

        const list = await getGameHistory(0, 10);
        expect(list.map(x => _.omit(x, "ctrl"))).deep.equal([
            {
                "sessionId": "1",
                "bet": 100,
                "brandId": 2,
                "currency": "USD",
                "deviceId": "device",
                "eventId": 1,
                "gameCode": "sw_game",
                "gameId": "game_id",
                "playerCode": "player001",
                "roundEnded": true,
                "roundId": "1",
                "test": false,
                "ts": list[0].ts,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "walletTransactionId": "trx_id",
                "win": 1000,
                "balanceAfter": 1100,
                "balanceBefore": 200
            }
        ]);

    });

    it("repairs workers while unloading game history", async () => {
        const unloadService = new UnloadEventHistoryService(await GameHistoryConsumer.create());
        const queue = new GameHistoryQueue();
        const event1: GameEventHistory = {
            sessionId: "1",
            roundId: "1",
            eventId: 1,
            type: "slot",
            gameVersion: "0.1.0",
            gameId: "game_id",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            walletTransactionId: "trx_id",
            bet: 100,
            win: 1000,
            balanceBefore: 200,
            balanceAfter: 1100,
            roundEnded: true,
            result: {
                spinResult: "spin_result",
            },
            ts: new Date(),
            test: false,
            ctrl: 100
        };
        await saveEvent(queue, event1);
        await queue.pop(1);

        let repairedList = await getRange(queue.workerList, 0, 0);
        expect(repairedList).deep.eq([
            {
                "sessionId": "1",
                "bet": 100,
                "brandId": 2,
                "ctrl": 100,
                "currency": "USD",
                "deviceId": "device",
                "eventId": 1,
                "gameCode": "sw_game",
                "gameId": "game_id",
                "playerCode": "player001",
                "result": {
                    "spinResult": "spin_result",
                },
                "roundEnded": true,
                "roundId": "1",
                "test": false,
                "ts": repairedList[0].ts,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "walletTransactionId": "trx_id",
                "win": 1000,
                "balanceAfter": 1100,
                "balanceBefore": 200
            }
        ]);

        await unloadService.repairOrphanWorkers(Date.now() + 1000);
        await unloadService.unloadData();
        const workerItems = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems.length).is.equal(0);

        const result = await getGameHistoryModels().original.findAll();
        result.sort((i1, i2) => i1.eventId - i2.eventId);
        expect(result.map((x) => _.omit(x.toJSON(), "insertedAt", "ctrl"))).deep.equal([
            {
                "sessionId": "1",
                "bet": "100",
                "brandId": 2,
                "currency": "USD",
                "deviceId": "device",
                "eventId": 1,
                "gameCode": "sw_game",
                "gameId": "game_id",
                "playerCode": "player001",
                "result": {
                    "spinResult": "spin_result",
                },
                "roundEnded": true,
                "roundId": "1",
                "test": false,
                "ts": result[0].ts,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "walletTransactionId": "trx_id",
                "win": "1000",
                "balanceAfter": "1100",
                "balanceBefore": "200",
                "totalJpContribution": null,
                "totalJpWin": null,
                "extraData": null,
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            }
        ]);
        repairedList = await getRange(queue.workerList, 0, 0);
        expect(repairedList).deep.eq([]);
    });

    it("repairs workers while unloading rounds (old)", async function() {
        this.timeout(40000);
        const unloadService = new UnloadRoundService(await RoundHistoryConsumer.createOld());
        const roundQueue = new GameRoundQueue();
        const round1: RoundHistory = {
            id: "1",
            sessionId: "1",
            gameId: "game_id",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 100,
            totalWin: 1000,
            totalEvents: 200,
            balanceBefore: 10,
            balanceAfter: 910,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 100
        };
        await saveEvent(roundQueue, round1);
        await roundQueue.pop(1);

        let repairedList = await getRange(roundQueue.workerList, 0, 0);
        expect(repairedList.map(x => _.omit(x, "insertedAt", "startedAt", "finishedAt", "ctrl", "extraData"))).deep.eq([
            {
                "sessionId": "1",
                "brandId": 2,
                "currency": "USD",
                "deviceId": "device",
                "gameCode": "sw_game",
                "gameId": "game_id",
                "id": "1",
                "playerCode": "player001",
                "test": false,
                "totalBet": 100,
                "totalEvents": 200,
                "totalWin": 1000,
                "balanceAfter": 910,
                "balanceBefore": 10
            }
        ]);

        await unloadService.repairOrphanWorkers(Date.now() + 1000);
        await unloadService.unloadData();
        const workerItems = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems.length).is.equal(0);

        const rounds = await getOldRoundHistoryModels().original.findAll();
        rounds.sort((i1, i2) => parseInt(i1.id, 10) - parseInt(i2.id, 10));
        expect(rounds.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl", "extraData")))
            .deep
            .equal([
                {
                    "sessionId": "1",
                    "brandId": 2,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "1",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "100",
                    "totalEvents": "200",
                    "totalWin": "1000",
                    "balanceAfter": "910",
                    "balanceBefore": "10",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                }
            ]);

        repairedList = await getRange(roundQueue.workerList, 0, 0);
        expect(repairedList).deep.eq([]);
    });

    it("repairs workers while unloading rounds", async function() {
        this.timeout(40000);
        const unloadService = new UnloadRoundService(await RoundHistoryConsumer.create());
        const roundQueue = new GameRoundQueue();
        const round1: RoundHistory = {
            id: "1",
            sessionId: "1",
            gameId: "game_id",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 100,
            totalWin: 1000,
            totalEvents: 200,
            balanceBefore: 10,
            balanceAfter: 910,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 100
        };
        await saveEvent(roundQueue, round1);
        await roundQueue.pop(1);

        let repairedList = await getRange(roundQueue.workerList, 0, 0);
        expect(repairedList.map(x => _.omit(x, "insertedAt", "startedAt", "finishedAt", "ctrl", "extraData"))).deep.eq([
            {
                "sessionId": "1",
                "brandId": 2,
                "currency": "USD",
                "deviceId": "device",
                "gameCode": "sw_game",
                "gameId": "game_id",
                "id": "1",
                "playerCode": "player001",
                "test": false,
                "totalBet": 100,
                "totalEvents": 200,
                "totalWin": 1000,
                "balanceAfter": 910,
                "balanceBefore": 10
            }
        ]);

        await unloadService.repairOrphanWorkers(Date.now() + 1000);
        await unloadService.unloadData();
        const workerItems = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems.length).is.equal(0);

        const rounds = await getRoundsHistoryModels().original.findAll();
        rounds.sort((i1, i2) => parseInt(i1.id, 10) - parseInt(i2.id, 10));
        expect(rounds.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl", "extraData")))
            .deep
            .equal([
                {
                    "sessionId": "1",
                    "brandId": 2,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "1",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "100",
                    "totalEvents": "200",
                    "totalWin": "1000",
                    "balanceAfter": "910",
                    "balanceBefore": "10",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                }
            ]);

        repairedList = await getRange(roundQueue.workerList, 0, 0);
        expect(repairedList).deep.eq([]);
        expect(await getRoundsHistoryModels().unfinished.findAll()).deep.equal([]);
    });

    it("repairs workers while unloading sessions", async () => {
        const unloadService = new UnloadSessionService(await SessionHistoryConsumer.create());
        const sessionQueue = new GameSessionQueue();
        const history: SessionHistory = {
            sessionId: "1",
            gameId: "game_id",
            gameVersion: "0.1.0",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            browser: "Chrome",
            os: "Linux",
            platform: "Unix",
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            broken: false,
            browserVersion: "63.05.123145",
            ctrl: 100,
            extSessionId: "operator_session_id"
        };
        await saveEvent(sessionQueue, history);
        await sessionQueue.pop(1);

        let repairedList = await getRange(sessionQueue.workerList, 0, 0);
        expect(repairedList.map(x => _.omit(x, "startedAt", "finishedAt", "ctrl"))).deep.eq([
            {
                "sessionId": "1",
                "brandId": 2,
                "currency": "USD",
                "deviceId": "device",
                "gameCode": "sw_game",
                "gameId": "game_id",
                "gameVersion": "0.1.0",
                "playerCode": "player001",
                "browser": "Chrome",
                "os": "Linux",
                "platform": "Unix",
                "test": false,
                "broken": false,
                "browserVersion": "63.05.123145",
                "extSessionId": "operator_session_id"
            }
        ]);

        await unloadService.repairOrphanWorkers(Date.now() + 1000);
        await unloadService.unloadData();
        const workerItems = await getRange(unloadService.workerList, 0, 1000);
        expect(workerItems.length).is.equal(0);

        const sessions = await getSessionHistoryModels().original.findAll();
        sessions.sort((i1, i2) => parseInt(i1.sessionId, 10) - parseInt(i2.sessionId, 10));
        expect(sessions.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl"))).deep.equal([
            {
                "sessionId": "1",
                "brandId": 2,
                "currency": "USD",
                "country": null,
                "language": null,
                "deviceId": "device",
                "gameCode": "sw_game",
                "gameId": "game_id",
                "gameVersion": "0.1.0",
                "playerCode": "player001",
                "screenSize": null,
                "browser": "Chrome",
                "os": "Linux",
                "platform": "Unix",
                "test": false,
                "broken": false,
                ip: null,
                playedFromCountry: null,
                operatorCountry: null,
                operatorPlayerCountry: null,
                browserVersion: "63.05.123145",
                interruptionReason: null,
                referrer: null,
                operatorSiteId: null,
                extSessionId: "operator_session_id"
            }
        ]);

        repairedList = await getRange(sessionQueue.workerList, 0, 0);
        expect(repairedList).deep.eq([]);
    });

    it("Stores aams participant code to history", async () => {
        await contextManager.findOrCreateGameContext(
            gameID, session, gameData, TEST_MODULE_NAME, requestContext);
        const context = await contextManager.findGameContextById(gameID);
        context.gameContext = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}
                }
            }
        };

        context.lastRequestId = 2;

        context.gameData.gameTokenData.regulatoryData = {
            aamsSessionCode: "aams-code-for-player-under-it-jrsdctn",
            participationStartDate: "2022-01-01"
        };

        const request = { request: "req", requestId: 1 };

        // round 0
        const history1 = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: { positions: [1, 2, 3] }
        };

        const newState1 = _.cloneDeep(context.gameContext);
        newState1["currentScene"] = "NEWSCENE1";
        const walletOperation1: PaymentOperation = {
            operation: "payment",
            transactionId: "1",
            bet: 1000,
            win: 200,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history1.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, newState1, request, history1);
        await context.commitPendingModification(
            { currency: "USD", main: 19800, previousValue: 20000 });

        // round 1
        const history2 = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: { positions: [1, 2, 3, 4] }
        };

        const newState2 = _.cloneDeep(newState1);
        newState2["currentScene"] = "NEWSCENE2";
        const walletOperation2: PaymentOperation = {
            operation: "payment",
            transactionId: "2",
            bet: 2000,
            win: 100,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history2.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation2, newState2, request, history2);
        await context.commitPendingModification({ currency: "USD", main: 18700, previousValue: 19800 });

        await context.remove(true);

        const gameHistoryConsumer = await GameHistoryConsumer.create();
        const unloadHistoryService = new UnloadEventHistoryService(gameHistoryConsumer);
        await unloadHistoryService.unloadData();
        const historyWorkerItems = await getRange(unloadHistoryService.workerList, 0, 1000);
        expect(historyWorkerItems.length).is.equal(0);

        const events = await getGameHistoryModels().original.findAll();
        expect(events.length).to.equal(2);
        events.sort((i1, i2) => parseInt(i1.walletTransactionId, 10) - parseInt(i2.walletTransactionId, 10));
        expect(events.map((x) => _.omit(x.toJSON(), "insertedAt", "ctrl"))).deep.equal([
            {
                "sessionId": "0",
                "bet": "1000",
                "brandId": 1,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "GM001",
                "gameId": "gameId",
                "playerCode": "playerId",
                "result": history1.data,
                "roundEnded": true,
                "roundId": "0",
                "eventId": 0,
                "ts": events[0].ts,
                "walletTransactionId": "1",
                "win": "200",
                "balanceAfter": "19800",
                "balanceBefore": "20000",
                "test": false,
                "totalJpContribution": "0",
                "totalJpWin": "0",
                "extraData": {
                    regulatoryData: {
                        aamsSessionCode: "aams-code-for-player-under-it-jrsdctn",
                        participationStartDate: "2022-01-01"
                    }
                },
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            },
            {
                "sessionId": "0",
                "bet": "2000",
                "brandId": 1,
                "type": "slot",
                "gameVersion": TEST_MODULE_NAME.version,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "GM001",
                "gameId": "gameId",
                "playerCode": "playerId",
                "result": history2.data,
                "roundEnded": true,
                "roundId": "1",
                "eventId": 0,
                "ts": events[1].ts,
                "walletTransactionId": "2",
                "win": "100",
                "balanceAfter": "18700",
                "balanceBefore": "19800",
                "test": false,
                "totalJpContribution": "0",
                "totalJpWin": "0",
                "extraData": {
                    regulatoryData: {
                        aamsSessionCode: "aams-code-for-player-under-it-jrsdctn",
                        participationStartDate: "2022-01-01"
                    }
                },
                "credit": null,
                "debit": null,
                "freeBetCoin": null,
                "lobbySessionId": null,
                "isHidden": null,
            }
        ]);

    });

    it("moves old spins to database - finished at should be fixed", async () => {
        const unloadService = new UnloadEventHistoryService(await GameHistoryConsumer.create());
        const queue = new GameHistoryQueue();

        const pastDate = new Date();
        pastDate.setDate(pastDate.getDate() - config.unloader.gameHistoryLatestTsPeriod - 1);
        const event1 = deepClone(predefinedGHEvents[0]);
        event1.ts = pastDate;
        const event3 = deepClone(predefinedGHEvents[2]);
        event3.ts = pastDate;
        await saveEvent(queue, event1);
        await saveEvent(queue, predefinedGHEvents[1]);
        await saveEvent(queue, event3);
        await unloadService.unloadData();

        const result = await getGameHistoryModels().original.findAll();
        result.sort((i1, i2) => i1.eventId - i2.eventId);
        expect(Math.abs(result[0].ts.getTime() - new Date().getTime())).lessThan(20000);
        expect(result[1].ts).deep.equal(predefinedGHEvents[1].ts);
        expect(Math.abs(result[2].ts.getTime() - new Date().getTime())).lessThan(20000);
    });

    it("moves old rounds to database - finished at should be fixed", async () => {
        const unloadService = new UnloadRoundService(await RoundHistoryConsumer.create());
        const roundQueue = new GameRoundQueue();
        const round1 = _.cloneDeep(predefinedRounds[1]);
        const pastDate = new Date();
        pastDate.setDate(pastDate.getDate() - config.unloader.gameHistoryLatestTsPeriod - 1);
        round1.finishedAt = pastDate;

        // unfinished
        await saveEvent(roundQueue, predefinedRounds[0]);
        await saveEvent(roundQueue, round1);
        await saveEvent(roundQueue, predefinedRounds[2]);
        await unloadService.unloadData();

        const unfinishedRounds = await getRoundsHistoryModels().unfinished.findAll();
        expect(unfinishedRounds[0].finishedAt).to.equal(undefined);

        const finishedRounds = await getRoundsHistoryModels().original.findAll();
        finishedRounds.sort((i1, i2) => parseInt(i1.id, 10) - parseInt(i2.id, 10));
        expect(Math.abs(finishedRounds[0].finishedAt.getTime() - new Date().getTime())).lessThan(20000);
        expect(finishedRounds[1].finishedAt).deep.equal(predefinedRounds[2].finishedAt);
    });

    it("splits by broken and finished_at", async () => {
        const unloadService = new UnloadRoundService(await RoundHistoryConsumer.create());
        const roundQueue = new GameRoundQueue();
        const rounds = _.cloneDeep(predefinedRounds);
        const r = _.cloneDeep(rounds[0]);
        r.id = "1";
        delete r.broken;
        rounds.push(r);

        for (const round of rounds) {
            await saveEvent(roundQueue, round);
        }
        await unloadService.unloadData();

        let dbItems = await getRoundsHistoryModels().original.findAll();
        expect(dbItems.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl", "extraData")))
            .deep
            .equal([
                {
                    "sessionId": "1",
                    "brandId": 3,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "2",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "40",
                    "totalEvents": "10",
                    "totalWin": "10",
                    "balanceAfter": "70",
                    "balanceBefore": "100",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                },
                {
                    "sessionId": "1",
                    "brandId": 4,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "3",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "90",
                    "totalEvents": "7",
                    "totalWin": "9",
                    "balanceAfter": "84",
                    "balanceBefore": "3",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                }
            ]);
        dbItems = await getRoundsHistoryModels().unfinished.findAll();
        expect(dbItems.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl", "extraData")))
            .deep
            .equal([
                {
                    "sessionId": "1",
                    "brandId": 4,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "0",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "90",
                    "totalEvents": "7",
                    "totalWin": "9",
                    "balanceAfter": "84",
                    "balanceBefore": "3",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                },
                {
                    "sessionId": "1",
                    "brandId": 4,
                    "currency": "USD",
                    "deviceId": "device",
                    "gameCode": "sw_game",
                    "gameId": "game_id",
                    "id": "1",
                    "playerCode": "player001",
                    "recoveryType": null,
                    "test": false,
                    "totalBet": "90",
                    "totalEvents": "7",
                    "totalWin": "9",
                    "balanceAfter": "84",
                    "balanceBefore": "3",
                    "totalJpContribution": null,
                    "totalJpWin": null,
                    "credit": null,
                    "debit": null,
                    "operatorSiteId": null
                },
            ]);
    });

    it("illegal UnloadRoundHistory state and repair", async function() {
        this.timeout(40000);
        const unloadService = new UnloadRoundService(await RoundHistoryConsumer.create());
        const roundQueue = new GameRoundQueue();
        const round1: RoundHistory = {
            id: "1",
            sessionId: "1",
            gameId: "game_id",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 100,
            totalWin: 1000,
            totalEvents: 200,
            balanceBefore: 10,
            balanceAfter: 910,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 100
        };
        await saveEvent(roundQueue, round1);
        const unloaderQueue: GameRoundQueue = unloadService["queue"] as GameRoundQueue;
        await unloaderQueue.pop();

        expect((await getRange(unloaderQueue.workerList, 0, 0))
            .map(x => _.omit(x, "insertedAt", "startedAt", "finishedAt", "ctrl", "extraData")))
            .deep.eq([
            {
                "sessionId": "1",
                "brandId": 2,
                "currency": "USD",
                "deviceId": "device",
                "gameCode": "sw_game",
                "gameId": "game_id",
                "id": "1",
                "playerCode": "player001",
                "test": false,
                "totalBet": 100,
                "totalEvents": 200,
                "totalWin": 1000,
                "balanceAfter": 910,
                "balanceBefore": 10
            }
        ]);

        expect(await unloadService.unloadData()).is.undefined;
        expect((await getRange(unloadService.workerList, 0, 1000)).length).is.equal(0);
        expect((await getRange(unloaderQueue.listName, 0, 0)).map(x => _.omit(x,
            "insertedAt",
            "startedAt",
            "finishedAt",
            "ctrl",
            "extraData"))).deep.eq([
            {
                "sessionId": "1",
                "brandId": 2,
                "currency": "USD",
                "deviceId": "device",
                "gameCode": "sw_game",
                "gameId": "game_id",
                "id": "1",
                "playerCode": "player001",
                "test": false,
                "totalBet": 100,
                "totalEvents": 200,
                "totalWin": 1000,
                "balanceAfter": 910,
                "balanceBefore": 10
            }
        ]);

        expect((await getRoundsHistoryModels().original.findAll())
            .map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl", "extraData")))
            .deep.equal([]);

        await unloadService.unloadData();
        expect((await getRoundsHistoryModels().original.findAll())
            .map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl", "extraData")))
            .deep.equal([
            {
                "sessionId": "1",
                "brandId": 2,
                "currency": "USD",
                "deviceId": "device",
                "gameCode": "sw_game",
                "gameId": "game_id",
                "id": "1",
                "playerCode": "player001",
                "recoveryType": null,
                "test": false,
                "totalBet": "100",
                "totalEvents": "200",
                "totalWin": "1000",
                "balanceAfter": "910",
                "balanceBefore": "10",
                "totalJpContribution": null,
                "totalJpWin": null,
                "credit": null,
                "debit": null,
                "operatorSiteId": null
            }
        ]);

        expect(await getRange(roundQueue.workerList, 0, 0)).deep.eq([]);
        expect(await getRoundsHistoryModels().unfinished.findAll()).deep.equal([]);
    });

    it("Stores external round id to round history", async () => {
        await contextManager.findOrCreateGameContext(
            gameID, session, gameData, TEST_MODULE_NAME, requestContext);
        const context = await contextManager.findGameContextById(gameID);
        context.gameContext = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}
                }
            }
        };

        context.lastRequestId = 2;

        const request = { request: "req", requestId: 1 };

        // round 0
        const history1 = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: { positions: [1, 2, 3] }
        };

        const newState1 = _.cloneDeep(context.gameContext);
        newState1["currentScene"] = "NEWSCENE1";
        const walletOperation1: PaymentOperation = {
            operation: "payment",
            transactionId: "1",
            bet: 1000,
            win: 200,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history1.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, newState1, request, history1);
        await context.commitPendingModification(
            { currency: "USD", main: 19800, previousValue: 20000, extraData: { extRoundId: "extRoundId4test" } });

        await context.remove(true);

        const roundHistoryConsumer = await RoundHistoryConsumer.create();
        const unloadRoundService = new UnloadRoundService(roundHistoryConsumer);
        await unloadRoundService.unloadData();
        const roundWorkerItems = await getRange(unloadRoundService.workerList, 0, 1000);
        expect(roundWorkerItems.length).is.equal(0);

        const rounds = await getRoundsHistoryModels().original.findAll();
        // rounds.sort((i1, i2) => parseInt(i1.id, 10) - parseInt(i2.id, 10));
        expect(rounds.map((x) => _.omit(x.toJSON(), "startedAt", "finishedAt", "insertedAt", "ctrl")))
            .deep
            .equal([
                {
                    "balanceAfter": "19800",
                    "balanceBefore": "20000",
                    "brandId": 1,
                    "credit": null,
                    "currency": "USD",
                    "debit": null,
                    "deviceId": "deviceId",
                    "gameCode": "GM001",
                    "gameId": "gameId",
                    "id": "0",
                    "playerCode": "playerId",
                    "recoveryType": null,
                    "sessionId": "0",
                    "test": false,
                    "totalBet": "1000",
                    "totalEvents": "1",
                    "totalJpContribution": "0",
                    "totalJpWin": "0",
                    "totalWin": "200",
                    "extraData": {
                        "extRoundId": "extRoundId4test"
                    },
                    "operatorSiteId": 1
                }
            ]);
    });

    describe("Divide and conquer flow for invalid messages", () => {
        let unloadService: UnloadService<any>;
        let mockConsumer: any;
        let mockRedisQueue: any;
        let mockDLQ: any;

        const round: RoundHistory = {
            id: "1",
            sessionId: "1",
            gameId: "game_id",
            brandId: 2,
            playerCode: "player001",
            deviceId: "device",
            gameCode: "sw_game",
            currency: "USD",
            totalBet: 100,
            totalWin: 1000,
            totalEvents: 200,
            balanceBefore: 10,
            balanceAfter: 910,
            startedAt: new Date(),
            finishedAt: new Date(),
            test: false,
            ctrl: 100
        };

        const generateRounds = (listLength: number): RoundHistory[] => {
            const bigListOfRounds: RoundHistory[] = [];
            for (let i = 0; i < listLength; ++i) {
                bigListOfRounds.push({ ...round, id: String(i) });
            }
            return bigListOfRounds;
        };

        beforeEach(() => {
            mockConsumer = { save: stub() };
            mockRedisQueue = { pop: stub(), commit: stub() };
            mockDLQ = { push: stub() };
            unloadService = new UnloadService(mockRedisQueue, mockConsumer, 10, mockDLQ);
        });

        it("should process 1000 valid items without errors", async () => {
            mockRedisQueue.pop.returns(generateRounds(1000));
            mockConsumer.save.resolves();

            await unloadService.unloadData();

            expect(mockConsumer.save.calledOnce).to.be.true;
            expect(mockDLQ.push.called).to.be.false;
        });

        it("should send a single invalid item to the DLQ", async () => {
            mockRedisQueue.pop.returns([round]);
            mockConsumer.save.rejects(new Error(UnloadService.KAFKA_INVALID_MESSAGE));
            await unloadService.unloadData();
            expect(mockDLQ.push.calledOnce).to.be.true;
        });

        it("should send two invalid items to the DLQ", async () => {
            mockRedisQueue.pop.returns(generateRounds(2));
            mockConsumer.save.rejects(new Error(UnloadService.KAFKA_INVALID_MESSAGE));
            await unloadService.unloadData();
            expect(mockDLQ.push.calledTwice).to.be.true;
        });

        it("should send an invalid item from two to the DLQ", async () => {
            mockRedisQueue.pop.returns(generateRounds(2));
            (unloadService as any).map = (item: RoundHistory) => item;
            mockConsumer.save = (items: RoundHistory[]) => {
                if (items.some(item => item.id === round.id)) {
                    return Promise.reject(new Error(UnloadService.KAFKA_INVALID_MESSAGE));
                }
                return Promise.resolve();
            };
            await unloadService.unloadData();
            expect(mockDLQ.push.calledOnce).to.be.true;
        });

        it("should send an invalid item from 1000 to the DLQ", async () => {
            mockRedisQueue.pop.returns(generateRounds(1000));
            (unloadService as any).map = (item: RoundHistory) => item;
            mockConsumer.save = (items: RoundHistory[]) => {
                if (items.some(item => item.id === round.id)) {
                    return Promise.reject(new Error(UnloadService.KAFKA_INVALID_MESSAGE));
                }
                return Promise.resolve();
            };
            await unloadService.unloadData();
            expect(mockDLQ.push.calledOnce).to.be.true;
        });
    });
});
