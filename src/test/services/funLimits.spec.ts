import { expect, should, use } from "chai";
import { getFunGameLimits, getFunGameMultiplier } from "../../skywind/utils/funGameLimits";

const limitsPrototype = require("../../../resources/funDefaultLimitsPrototype.json");
import * as exchanger from "../../skywind/services/currencyexchange";

use(require("chai-as-promised"));
import { SinonStub, stub } from "sinon";

should();

describe("Fun game default limits", () => {

    it("USD limits (x1)", async () => {
        const limits = getFunGameLimits("USD");
        expect(limits).deep.equal(limitsPrototype);
    });

    it("MYR limits (x5)", async () => {
        const limits = getFunGameLimits("MYR");
        expect(limits).deep.equal({
            "high": {
                "stakeAll": [0.2, 0.4, 0.6, 0.8, 1, 1.2, 1.6, 2, 4, 5, 6, 8, 10, 15, 20, 40, 100, 160],
                "stakeDef": 0.8,
                "stakeMax": 160,
                "stakeMin": 0.2
            },
            "low": {
                "stakeAll": [0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.4, 0.5, 1, 1.25, 1.5, 2, 2.5, 3.75, 5, 10, 25, 40],
                "stakeDef": 0.2,
                "stakeMax": 40,
                "stakeMin": 0.05,
            },
            "maxTotalStake": 2500,
            "mid": {
                "stakeAll": [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.8, 1, 2, 2.5, 3, 4, 5, 7.5, 10, 20, 50, 80],
                "stakeDef": 0.4,
                "stakeMax": 80,
                "stakeMin": 0.1,
            },
            "coinsRate": 0.05,
            "stakeAll": [0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.4, 0.5, 1, 1.25, 1.5, 2, 2.5, 3.75, 5, 10, 25, 40],
            "stakeDef": 0.2,
            "stakeMax": 40,
            "stakeMin": 0.05,
            "winMax": 7500000
        });
    });

    it("Multiplier rate USD (x1)", async () => {
        const limits = getFunGameMultiplier("USD");
        expect(limits).deep.equal(1);
    });

    it("Multiplier rate MYR (x5)", async () => {
        const limits = getFunGameMultiplier("MYR");
        expect(limits).deep.equal(5);
    });

    it("Multiplier rate by fake currency", async () => {
        const limits = getFunGameMultiplier("FAKE");
        expect(limits).deep.equal(1);
    });
});

describe("Calculated multipliers", () => {

    let getExchangerStub: SinonStub;

    beforeEach(() => {
        getExchangerStub = stub(exchanger, "getCurrencyExchange");
    });

    afterEach(() => {
        getExchangerStub.restore();
    });

    it("Multiplier rate by mock currency (>1)", async () => {
        getExchangerStub.returns({
            getExchangeRate: (baseCurrency: string, targetCurrency: string): number => {
                expect(baseCurrency).equal("USD");
                expect(targetCurrency).equal("FAKE");
                return 345;
            }
        });

        const limits = getFunGameMultiplier("FAKE");
        expect(limits).deep.equal(200);

    });

    it("Multiplier rate by mock currency (<1)", async () => {
        getExchangerStub.returns({
            getExchangeRate: (baseCurrency: string, targetCurrency: string): number => {
                expect(baseCurrency).equal("USD");
                expect(targetCurrency).equal("FAKE");
                return 0.0345;
            }
        });

        const limits = getFunGameMultiplier("FAKE");
        expect(limits).deep.equal(0.02);
    });
});
