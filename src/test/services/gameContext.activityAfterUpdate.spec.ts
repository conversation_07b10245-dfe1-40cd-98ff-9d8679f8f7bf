import { suite, test } from "mocha-typescript";
import { GameFlowContext } from "../../skywind/services/context/gamecontext";
import { Currency, GameMode } from "@skywind-group/sw-game-core";
import { GameData } from "../../skywind/services/auth";
import { GameContextID } from "../../skywind/services/contextIds";
import { GameTokenData, Settings } from "../../skywind/services/tokens";
import { activityList, createGameToken, flushAll, TEST_MODULE_NAME } from "../helper";
import { GameSession } from "../../skywind/services/gameSession";
import { expect } from "chai";
import { getGameFlowContextManager } from "../../skywind/services/contextmanager/contextManagerImpl";
import { SinonFakeTimers, useFakeTimers } from "sinon";
import { GameFlowInfoImpl } from "../../skywind/services/context/gameFlowInfo";
import config from "../../skywind/config";

@suite()
class GameContextActivityAfterUpdateSpec {

    private clock: SinonFakeTimers;

    public async before() {
        await flushAll();
        this.clock = useFakeTimers();
    }

    public async after() {
        this.clock.restore();
    }

    @test()
    public async testActivityMarkerAfterCreate() {
        this.clock.setSystemTime(777);
        const ctx: GameFlowContext = await this.createContext("player0001");

        expect(await activityList())
            .deep
            .equal([ctx.id.asString(), (777 + config.cleanUp.expireIn * 60000).toString()]);
    }

    @test()
    public async testActivityMarkerAfterCreate_WithMinPaymentRetryOption() {
        this.clock.setSystemTime(777);
        const ctx: GameFlowContext = await this.createContext("player0001", 1, "real", 5);

        expect(await activityList())
            .deep
            .equal([ctx.id.asString(), (777 + config.cleanUp.expireIn * 60000).toString()]);
    }

    @test()
    public async testActivityMarkerAfterUpdatePending() {
        this.clock.setSystemTime(777);
        const ctx: GameFlowContext = await this.createContext("player0001");
        const history1: any = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: {
                positions: [1, 2, 3],
            }
        };

        const walletOperation1: any = {
            operation: "payment",
            transactionId: "1",
            roundId: ctx.roundId,
            roundPID: new GameFlowInfoImpl(ctx).roundPID,
            roundEnded: history1.roundEnded,
            bet: 1900,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await ctx.updatePendingModification(walletOperation1, {}, {} as any, history1);

        expect(await activityList())
            .deep
            .equal([ctx.id.asString(), (777 + config.cleanUp.expireIn * 60000).toString()]);
        this.clock.setSystemTime(888);
        await ctx.commitPendingModification();
        expect(await activityList())
            .deep
            .equal([ctx.id.asString(), (888 + config.cleanUp.expireIn * 60000).toString()]);
    }

    @test()
    public async testActivityMarkerAfterUpdatePending_WithMinRetryTimeout() {
        this.clock.setSystemTime(777);
        const ctx: GameFlowContext = await this.createContext("player0001", 1, "real", 35);
        const history1: any = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: {
                positions: [1, 2, 3],
            }
        };

        const walletOperation1: any = {
            operation: "payment",
            transactionId: "1",
            roundId: ctx.roundId,
            roundPID: new GameFlowInfoImpl(ctx).roundPID,
            roundEnded: history1.roundEnded,
            bet: 1900,
            win: 200,
            currency: "USD",
            ts: new Date()
        };

        await ctx.updatePendingModification(walletOperation1, {}, {} as any, history1);
        expect(await activityList())
            .deep
            .equal([ctx.id.asString(), (777 + 35 * 1000).toString()]);

        this.clock.setSystemTime(888);
        await ctx.commitPendingModification();
        expect(await activityList())
            .deep
            .equal([ctx.id.asString(), (888 + config.cleanUp.expireIn * 60000).toString()]);
    }

    @test()
    public async testActivityMarkerAfterRollbackPending_WithMinRetryTimeout() {
        this.clock.setSystemTime(777);
        const ctx: GameFlowContext = await this.createContext("player0001", 1, "real", 35);
        const history1: any = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: {
                positions: [1, 2, 3],
            }
        };

        const walletOperation1: any = {
            operation: "payment",
            transactionId: "1",
            roundId: ctx.roundId,
            roundPID: new GameFlowInfoImpl(ctx).roundPID,
            roundEnded: history1.roundEnded,
            bet: 1900,
            win: 200,
            currency: "USD",
            ts: new Date()
        };

        await ctx.updatePendingModification(walletOperation1, {}, {} as any, history1);
        expect(await activityList())
            .deep
            .equal([ctx.id.asString(), (777 + 35 * 1000).toString()]);

        this.clock.setSystemTime(888);
        await ctx.rollbackPendingModification();
        expect(await activityList())
            .deep
            .equal([ctx.id.asString(), (888 + config.cleanUp.expireIn * 60000).toString()]);
    }

    private async createContext(playerCode: string, brandId: number = 1,
                                mode: GameMode = "real", minPaymentRetryTimeout: number = 0) {
        const currency: Currency = 0;
        const settings: Settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: currency,
            stakeAll: [1, 2, 3, 4],
            stakeDef: currency,
            stakeMax: currency,
            stakeMin: currency,
            winMax: currency,
            currencyMultiplier: 100,
            minPaymentRetryTimeout
        };

        const gameID: GameContextID = GameContextID.create("gameId", brandId, playerCode, "deviceId", mode);

        const gameData: GameData = {
            gameTokenData: undefined,
            limits: settings,
        };

        const gameTokenData: GameTokenData = {
            playerCode: gameID.playerCode,
            gameCode: "GM001",
            brandId: gameID.brandId,
            currency: "USD",
            playmode: mode
        };

        gameTokenData.token = await createGameToken(gameTokenData);
        gameData.gameTokenData = gameTokenData;
        const sessionId = await GameSession.generate(gameID, "real");

        return getGameFlowContextManager().findOrCreateGameContext(gameID, sessionId, gameData, TEST_MODULE_NAME);
    }
}
