import { suite, test } from "mocha-typescript";
import { GameData, LogoutType } from "../../skywind/services/auth";
import { createGameToken, flushAll, syncModels, TEST_MODULE_NAME } from "../helper";
import { GameTokenData } from "../../skywind/services/tokens";
import { GameSession } from "../../skywind/services/gameSession";
import { GameContextID, PlayerContextID } from "../../skywind/services/contextIds";
import { Currency } from "@skywind-group/sw-game-core";
import { expect, use } from "chai";
import { getGameFlowContextManager } from "../../skywind/services/contextmanager/contextManagerImpl";
import { getGameContextModel, getPlayerContextModel } from "../../skywind/services/offlinestorage/models";
import { GameFlowContextImpl } from "../../skywind/services/context/gameContextImpl";
import { PaymentOperation } from "../../skywind/services/wallet";
import { getUnfinishedGameHistoryService } from "../../skywind/services/playerUnfinishedGameHistory";
import { MerchantLogoutResult, SpecialState } from "../../skywind/services/context/gamecontext";
import { CleanupPlayerContextService } from "../../skywind/services/cleanup/cleanupPlayerContextService";
import { CleanupGameContextService } from "../../skywind/services/cleanup/cleanupGameContextService";

import "chai-exclude";
import { PlayerContextImpl } from "../../skywind/services/playercontext/playerContext";

use(require("chai-exclude"));

@suite()
class PlayerUnfinishedGameHistorySpec {
    private contextManager = getGameFlowContextManager();
    private currency: Currency = 0;
    private settings = {
        coins: [3, 4],
        defaultCoin: 4,
        maxTotalStake: this.currency,
        stakeAll: [1, 2, 3, 4],
        stakeDef: this.currency,
        stakeMax: this.currency,
        stakeMin: this.currency,
        winMax: this.currency,
        currencyMultiplier: 100,
    };

    private gameID: GameContextID = GameContextID.create("gameId", 1, "playerId", "web");

    private gameData: GameData = {
        gameTokenData: undefined,
        limits: this.settings
    };

    private gameTokenData: GameTokenData = {
        playerCode: this.gameID.playerCode,
        gameCode: this.gameID.gameCode,
        brandId: this.gameID.brandId,
        currency: "USD",
        playmode: "real"
    };

    private sessionId: GameSession;

    private ctx: GameFlowContextImpl;

    public async before() {
        this.gameTokenData.token = await createGameToken(this.gameTokenData);
        this.gameData.gameTokenData = this.gameTokenData;
        this.sessionId = await GameSession.generate(this.gameID, "real");

        await flushAll();
        await syncModels();
        await getPlayerContextModel().truncate();
        await getGameContextModel().truncate();

        this.ctx = await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME);
    }

    @test()
    public async testNoUnfinishedNoBroken() {
        const history = await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode
        });

        expect(history).deep.equals([]);

        expect(await getPlayerContextModel().findAll()).deep.equal([]);
        expect(await getGameContextModel().findAll()).deep.equal([]);
    }

    @test()
    public async testUnfinishedHistoryRequestFilters() {
        const ts = this.ctx.updatedAt;
        const result = [
            {
                balanceAfter: undefined,
                balanceBefore: undefined,
                bet: 0,
                brandId: "W4RkGRen",
                broken: true,
                currency: "USD",
                firstTs: ts,
                device: "web",
                finished: false,
                gameCode: "gameId",
                isTest: undefined,
                playerCode: "playerId",
                revenue: 0,
                roundId: "doRlLRrM",
                status: "requireLogout",
                totalEvents: 0,
                ts: undefined,
                win: 0,
                gameContextId: "games:context:1:playerId:gameId:web"
            }
        ];

        await this.ctx.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode
        })).deep.equals(result);

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: "unknown"
        })).deep.equals([]);

        // filter by gameCode

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            gameCode: this.gameID.gameCode
        })).deep.equals(result);

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            gameCode: "unknown"
        })).deep.equals([]);

        // filter by ts__gte

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            ts__gte: ts.getTime()
        })).deep.equals(result);

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            ts__gte: ts.getTime() + 1000
        })).deep.equals([]);

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            ts__gte: ts.getTime() - 1000
        })).deep.equals(result);

        // filter by ts__lte

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            ts__lte: ts.getTime()
        })).deep.equals(result);

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            ts__lte: ts.getTime() - 1000
        })).deep.equals([]);

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            ts__lte: ts.getTime() + 1000
        })).deep.equals(result);

        // filter by ts__gt

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            ts__gt: ts.getTime()
        })).deep.equals([]);

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            ts__gt: ts.getTime() - 1000
        })).deep.equals(result);

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            ts__gt: ts.getTime() + 1000
        })).deep.equals([]);

        // filter by ts__lt

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            ts__lt: ts.getTime()
        })).deep.equals([]);

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            ts__lt: ts.getTime() - 1000
        })).deep.equals([]);

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            ts__lt: ts.getTime() + 1000
        })).deep.equals(result);

        // filter by round id

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            roundId: 0
        })).deep.equals([]);

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            roundId: 99
        })).deep.equals([]);

        // filter by status

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            status: "requireLogout"
        })).deep.equals(result);

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            status: "unfinished"
        })).deep.equals([]);

        // No filtering

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId
        })).deep.equals(result);
    }

    @test()
    public async testBrokenGame() {
        const result: any = [
            {
                balanceAfter: undefined,
                balanceBefore: undefined,
                bet: 0,
                brandId: "W4RkGRen",
                broken: true,
                currency: "USD",
                device: "web",
                finished: false,
                gameCode: "gameId",
                isTest: undefined,
                playerCode: "playerId",
                revenue: 0,
                roundId: "doRlLRrM",
                status: "broken",
                totalEvents: 0,
                ts: undefined,
                win: 0,
                gameContextId: "games:context:1:playerId:gameId:web"
            }
        ];

        const resultWithBrokenSpin = [
            {
                balanceAfter: undefined,
                balanceBefore: undefined,
                bet: 0,
                brandId: "W4RkGRen",
                broken: true,
                currency: "USD",
                firstTs: undefined,
                device: "web",
                finished: false,
                gameCode: "gameId",
                isTest: undefined,
                playerCode: "playerId",
                revenue: 0,
                roundId: "doRlLRrM",
                status: "broken",
                totalEvents: 0,
                ts: undefined,
                win: 0,
                gameContextId: "games:context:1:playerId:gameId:web",
                pendingSpin: {
                    balanceAfter: undefined,
                    balanceBefore: undefined,
                    bet: 100,
                    endOfRound: true,
                    spinNumber: 0,
                    test: undefined,
                    ts: undefined,
                    type: "slot",
                    walletTransactionId: "1",
                    win: 20
                }
            }
        ];

        await this.ctx.updatePendingModification({
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD"
            } as PaymentOperation,
            {
                state: { currentScene: "sceneTest" },
            }, { request: "req", requestId: 1 }, {
                type: "slot",
                roundEnded: true,
                data: {}
            });
        result[0].firstTs = this.ctx.pendingModification.walletOperation.ts;

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode
        })).deep.equals(result);

        // include broken spin details
        const gameHistoryDetails = await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            includeBrokenSpin: true
        });
        resultWithBrokenSpin[0].firstTs = gameHistoryDetails[0].firstTs;
        expect(gameHistoryDetails).deep.equal(resultWithBrokenSpin);

        expect(await getPlayerContextModel().findAll()).deep.equal([]);
        expect(await getGameContextModel().findAll()).deep.equal([]);
    }

    @test()
    public async testUnfinishedGame_StartMiniGame() {
        await this.ctx.updatePendingModification({
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD",
                ts: new Date()
            } as PaymentOperation,
            {
                state: { currentScene: "sceneTest" },
            }, { request: "req", requestId: 1 }, {
                type: "slot",
                roundEnded: true,
                data: {}
            });

        await this.ctx.commitPendingModification({ main: 1000, previousValue: 1080, currency: "USD" }, undefined, {
            token: "xxx",
            jackpotsInfo: [
                {
                    id: "test", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, gameHistoryEnabled: true, paymentStatisticEnabled: true
                }
            ],
            contributionEnabled: true,
            jackpot: {
                transactionId: "1",
                result: [
                    {
                        jackpotId: "test",
                        jackpotType: undefined,
                        event: "start-mini-game",
                    }
                ]
            },
        });

        const result = [
            {
                balanceAfter: undefined,
                balanceBefore: undefined,
                bet: 0,
                brandId: "W4RkGRen",
                broken: true,
                currency: "USD",
                firstTs: undefined,
                device: "web",
                finished: false,
                gameCode: "gameId",
                isTest: undefined,
                playerCode: "playerId",
                revenue: 0,
                roundId: "W4RkGRen",
                status: "unfinished",
                totalEvents: 0,
                ts: undefined,
                win: 0,
                gameContextId: "games:context:1:playerId:gameId:web"
            }
        ];

        const gameHistoryDetails = await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode
        });
        result[0].ts = gameHistoryDetails[0].ts;
        result[0].firstTs = gameHistoryDetails[0].firstTs;
        expect(gameHistoryDetails).deep.equals(result);

        expect(await getPlayerContextModel().findAll()).deep.equal([]);
        expect(await getGameContextModel().findAll()).deep.equal([]);
    }

    @test()
    public async testRequireLogout_1() {
        const ts = this.ctx.updatedAt;
        const result = [
            {
                balanceAfter: undefined,
                balanceBefore: undefined,
                bet: 0,
                brandId: "W4RkGRen",
                broken: true,
                currency: "USD",
                firstTs: ts,
                device: "web",
                finished: false,
                gameCode: "gameId",
                isTest: undefined,
                playerCode: "playerId",
                revenue: 0,
                roundId: "doRlLRrM",
                status: "requireLogout",
                totalEvents: 0,
                ts: undefined,
                win: 0,
                gameContextId: "games:context:1:playerId:gameId:web"
            }
        ];

        await this.ctx.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode
        })).deep.equals(result);

        expect(await getPlayerContextModel().findAll()).deep.equal([]);
        expect(await getGameContextModel().findAll()).deep.equal([]);
    }

    @test()
    public async testRequireLogout_2() {
        this.ctx = await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            { ...this.gameData, ...{ logoutOptions: { type: LogoutType.ALL } } },
            TEST_MODULE_NAME);
        const ts = this.ctx.updatedAt;
        const result = [
            {
                balanceAfter: undefined,
                balanceBefore: undefined,
                bet: 0,
                brandId: "W4RkGRen",
                broken: true,
                currency: "USD",
                firstTs: ts,
                device: "web",
                finished: false,
                gameCode: "gameId",
                isTest: undefined,
                playerCode: "playerId",
                revenue: 0,
                roundId: "doRlLRrM",
                status: "requireLogout",
                totalEvents: 0,
                ts: undefined,
                win: 0,
                gameContextId: "games:context:1:playerId:gameId:web"
            }
        ];

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode
        })).deep.equals(result);

        expect(await getPlayerContextModel().findAll()).deep.equal([]);
        expect(await getGameContextModel().findAll()).deep.equal([]);
    }

    @test()
    public async testRequireLogout_3() {
        this.ctx = await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            { ...this.gameData, ...{ logoutOptions: { type: LogoutType.UNFINISHED } } },
            TEST_MODULE_NAME);
        await this.ctx.updatePendingModification({
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD"
            } as PaymentOperation,
            {
                state: { currentScene: "sceneTest" },
            }, { request: "req", requestId: 1 }, {
                type: "slot",
                roundEnded: false,
                data: {}
            });
        const ts = this.ctx.pendingModification.walletOperation.ts;
        await this.ctx.commitPendingModification({ main: 1000, previousValue: 1080, currency: "USD" });
        const result = [
            {
                balanceAfter: 1000,
                balanceBefore: 1080,
                bet: 100,
                brandId: "W4RkGRen",
                broken: true,
                currency: "USD",
                firstTs: ts,
                device: "web",
                finished: false,
                gameCode: "gameId",
                isTest: undefined,
                playerCode: "playerId",
                revenue: 80,
                roundId: "doRlLRrM",
                status: "requireLogout",
                totalEvents: 1,
                ts: undefined,
                win: 20,
                gameContextId: "games:context:1:playerId:gameId:web"
            }
        ];

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode
        })).deep.equals(result);

        expect(await getPlayerContextModel().findAll()).deep.equal([]);
        expect(await getGameContextModel().findAll()).deep.equal([]);
    }

    @test()
    public async testBrokenIntegration() {
        const ts = this.ctx.updatedAt;
        const result = [
            {
                balanceAfter: undefined,
                balanceBefore: undefined,
                bet: 0,
                brandId: "W4RkGRen",
                broken: true,
                currency: "USD",
                firstTs: ts,
                device: "web",
                finished: false,
                gameCode: "gameId",
                isTest: undefined,
                playerCode: "playerId",
                revenue: 0,
                roundId: "doRlLRrM",
                status: "brokenIntegration",
                totalEvents: 0,
                ts: undefined,
                win: 0,
                gameContextId: "games:context:1:playerId:gameId:web"
            }
        ];

        await this.ctx.setSpecialState(SpecialState.BROKEN_INTEGRATION);

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode
        })).deep.equals(result);

        expect(await getPlayerContextModel().findAll()).deep.equal([]);
        expect(await getGameContextModel().findAll()).deep.equal([]);
    }

    @test()
    public async testBrokenOfflineGame() {
        const ts = this.ctx.updatedAt;
        const result = [
            {
                balanceAfter: undefined,
                balanceBefore: undefined,
                bet: 0,
                brandId: "W4RkGRen",
                broken: true,
                currency: "USD",
                firstTs: ts,
                device: "web",
                finished: false,
                gameCode: "gameId",
                isTest: undefined,
                playerCode: "playerId",
                revenue: 0,
                roundId: "doRlLRrM",
                status: "broken",
                totalEvents: 0,
                ts: undefined,
                win: 0,
                gameContextId: "games:context:1:playerId:gameId:web"
            }
        ];

        const resultWithBrokenSpin = [
            {
                balanceAfter: undefined,
                balanceBefore: undefined,
                bet: 0,
                brandId: "W4RkGRen",
                broken: true,
                currency: "USD",
                firstTs: ts,
                device: "web",
                finished: false,
                gameCode: "gameId",
                isTest: undefined,
                playerCode: "playerId",
                revenue: 0,
                roundId: "doRlLRrM",
                status: "broken",
                totalEvents: 0,
                ts: undefined,
                win: 0,
                gameContextId: "games:context:1:playerId:gameId:web",
                pendingSpin: {
                    balanceAfter: undefined,
                    balanceBefore: undefined,
                    bet: 100,
                    endOfRound: true,
                    spinNumber: 0,
                    test: undefined,
                    ts: undefined,
                    type: "slot",
                    walletTransactionId: "1",
                    win: 20
                }
            }
        ];

        await this.ctx.updatePendingModification({
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD"
            } as PaymentOperation,
            {
                state: { currentScene: "sceneTest" },
            }, { request: "req", requestId: 1 }, {
                type: "slot",
                roundEnded: true,
                data: {}
            });

        await new CleanupGameContextService().cleanUp([this.ctx.id.asString()], true);

        expect(await this.contextManager.findGameContextById(this.ctx.id)).is.undefined;

        result[0].firstTs = this.ctx.pendingModification.walletOperation.ts;
        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode
        })).deep.equals(result);

        // include broken spin details
        const gameHistoryDetails = await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            includeBrokenSpin: true
        });
        resultWithBrokenSpin[0].firstTs = gameHistoryDetails[0].firstTs;
        expect(gameHistoryDetails).deep.equal(resultWithBrokenSpin);

        expect(await getPlayerContextModel().findAll()).deep.equal([]);
        expect((await getGameContextModel().findAll()).map(i => i.id)).deep.equal([this.ctx.id.asString()]);
    }

    @test()
    public async testBrokenOfflineGameAndPlayer() {
        const ts = this.ctx.updatedAt;
        const result = [
            {
                balanceAfter: undefined,
                balanceBefore: undefined,
                bet: 0,
                brandId: "W4RkGRen",
                broken: true,
                currency: "USD",
                firstTs: ts,
                device: "web",
                finished: false,
                gameCode: "gameId",
                isTest: undefined,
                playerCode: "playerId",
                revenue: 0,
                roundId: "doRlLRrM",
                status: "broken",
                totalEvents: 0,
                ts: undefined,
                win: 0,
                gameContextId: "games:context:1:playerId:gameId:web"
            }
        ];

        const resultWithBrokenSpin = [
            {
                balanceAfter: undefined,
                balanceBefore: undefined,
                bet: 0,
                brandId: "W4RkGRen",
                broken: true,
                currency: "USD",
                firstTs: ts,
                device: "web",
                finished: false,
                gameCode: "gameId",
                isTest: undefined,
                playerCode: "playerId",
                revenue: 0,
                roundId: "doRlLRrM",
                status: "broken",
                totalEvents: 0,
                ts: undefined,
                win: 0,
                gameContextId: "games:context:1:playerId:gameId:web",
                pendingSpin: {
                    balanceAfter: undefined,
                    balanceBefore: undefined,
                    bet: 100,
                    endOfRound: true,
                    spinNumber: 0,
                    test: undefined,
                    ts: undefined,
                    type: "slot",
                    walletTransactionId: "1",
                    win: 20
                }
            }
        ];

        await this.ctx.updatePendingModification({
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD"
            } as PaymentOperation,
            {
                state: { currentScene: "sceneTest" },
            }, { request: "req", requestId: 1 }, {
                type: "slot",
                roundEnded: true,
                data: {}
            });

        await new CleanupGameContextService().cleanUp([this.ctx.id.asString()], true);
        await new CleanupPlayerContextService().cleanUp([this.ctx.id.playerContextID.asString()], true);

        expect(await this.contextManager.findGameContextById(this.ctx.id)).is.undefined;
        result[0].firstTs = this.ctx.pendingModification.walletOperation.ts;

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode
        })).deep.equals(result);

        // include broken spin details
        const gameHistoryDetails = await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            includeBrokenSpin: true
        });
        resultWithBrokenSpin[0].firstTs = gameHistoryDetails[0].firstTs;
        expect(gameHistoryDetails).deep.equal(resultWithBrokenSpin);

        expect((await getPlayerContextModel().findAll()).map(i => i.id))
            .deep
            .equal([this.ctx.id.playerContextID.asString()]);
        expect((await getGameContextModel().findAll()).map(i => i.id)).deep.equal([this.ctx.id.asString()]);
    }

    @test()
    public async testRequireTransferOut() {
        this.ctx.gameData.settings = { transferEnabled: true };
        this.ctx.gameData.gameTokenData.transferEnabled = true;
        this.ctx.markForFullUpdate();

        await this.ctx.updatePendingModification({
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD"
            } as PaymentOperation,
            {
                state: { currentScene: "sceneTest" },
            }, { request: "req", requestId: 1 }, {
                type: "slot",
                roundEnded: false,
                data: {}
            });

        await this.ctx.commitPendingModification();

        await this.ctx.updatePendingModification({
                operation: "payment",
                transactionId: "2",
                bet: 100,
                win: 20,
                currency: "USD"
            } as PaymentOperation,
            {
                state: { currentScene: "sceneTest1" },
            }, { request: "req", requestId: 2 }, {
                type: "slot",
                roundEnded: true,
                data: {}
            });

        const result = [
            {
                balanceAfter: undefined,
                balanceBefore: undefined,
                bet: 100,
                brandId: "W4RkGRen",
                broken: true,
                currency: "USD",
                device: "web",
                finished: false,
                gameCode: "gameId",
                isTest: undefined,
                playerCode: "playerId",
                revenue: 80,
                roundId: "doRlLRrM",
                status: "requireTransferOut",
                totalEvents: 1,
                win: 20,
                gameContextId: "games:context:1:playerId:gameId:web"
            }
        ];

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode
        })).excludingEvery(["ts", "firstTs"]).deep.equals(result);
    }

    @test()
    public async findGameContexts() {
        const result = await getUnfinishedGameHistoryService()
            .getGameContexts({ gameCode: "gameId", brandId: 1, playerCode: "playerId" });
        expect(result.length).to.equal(1);
        expect(result[0].id.asString()).to.equal("games:context:1:playerId:gameId:web");
    }

    /**
     * This test emulates scenario of searching for unfinished games, case that we sometimes encountered
     * with some old rounds:
     *
     * In some cases playerContext was not present nor in redis nor in Postgres, whereas gameContext was still present
     * in Postgres (which basically means that round is broken because its old and it is in Postgres), which
     * made method getPlayerUnfinishedHistory() to not find such gameContext as it firstly looks for playerContext.
     *
     * And so this test checks the improvement of getPlayerUnfinishedHistory() method that searches gameContext straight
     * in redis and postgres, if PlayerContext does not exist and if gameCode param was passed as a parameter.
     */
    @test()
    public async testFindBrokenGameContexts() {
        const ts = this.ctx.updatedAt;
        const result = [
            {
                balanceAfter: undefined,
                balanceBefore: undefined,
                bet: 0,
                brandId: "W4RkGRen",
                broken: true,
                currency: "USD",
                firstTs: ts,
                device: "web",
                finished: false,
                gameCode: "gameId",
                isTest: undefined,
                playerCode: "playerId",
                revenue: 0,
                roundId: "doRlLRrM",
                status: "broken",
                totalEvents: 0,
                ts: undefined,
                win: 0,
                gameContextId: "games:context:1:playerId:gameId:web"
            }
        ];

        const resultWithBrokenSpin = [
            {
                balanceAfter: undefined,
                balanceBefore: undefined,
                bet: 0,
                brandId: "W4RkGRen",
                broken: true,
                currency: "USD",
                firstTs: ts,
                device: "web",
                finished: false,
                gameCode: "gameId",
                isTest: undefined,
                playerCode: "playerId",
                revenue: 0,
                roundId: "doRlLRrM",
                status: "broken",
                totalEvents: 0,
                ts: undefined,
                win: 0,
                gameContextId: "games:context:1:playerId:gameId:web",
                pendingSpin: {
                    balanceAfter: undefined,
                    balanceBefore: undefined,
                    bet: 100,
                    endOfRound: true,
                    spinNumber: 0,
                    test: undefined,
                    ts: undefined,
                    type: "slot",
                    walletTransactionId: "1",
                    win: 20
                }
            }
        ];

        await this.ctx.updatePendingModification({
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD"
            } as PaymentOperation,
            {
                state: { currentScene: "sceneTest" },
            }, { request: "req", requestId: 1 }, {
                type: "slot",
                roundEnded: true,
                data: {}
            });

        // await new CleanupGameContextService().cleanUp([this.ctx.id.asString()], true);
        // await new CleanupPlayerContextService().cleanUp([this.ctx.id.playerContextID.asString()], true);

        // game and player contexts must be in redis
        expect(await this.contextManager.findGameContextById(this.ctx.id)).to.exist;
        const playerContextID = PlayerContextID.create(this.gameID.brandId, this.gameID.playerCode);
        expect(await this.contextManager.findPlayerContextById(playerContextID)).to.exist;

        // move player's context to Postgres
        await new CleanupPlayerContextService().cleanUp([this.ctx.id.playerContextID.asString()], true);
        expect(await this.contextManager.findPlayerContextById(playerContextID)).to.not.exist;

        // remove player's context from Postgres
        await this.contextManager.removePlayerContextOffline([new PlayerContextImpl(playerContextID)]);

        expect((await getPlayerContextModel().findAll()).map(i => i.id))
            .deep
            .equal([]);

        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode
        })).deep.equals([]);
        result[0].firstTs = this.ctx.pendingModification.walletOperation.ts;
        expect(await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            gameCode: this.gameID.gameCode
        })).deep.equals(result);

        // include broken spin details
        const gameHistoryDetails = await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory({
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            gameCode: this.gameID.gameCode,
            includeBrokenSpin: true
        });
        resultWithBrokenSpin[0].firstTs = gameHistoryDetails[0].firstTs;
        expect(gameHistoryDetails).deep.equal(resultWithBrokenSpin);
    }
}
