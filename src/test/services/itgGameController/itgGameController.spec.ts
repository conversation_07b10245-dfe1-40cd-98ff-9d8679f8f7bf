import { GameNotMatch, ValidationError } from "../../../skywind/errors";
import { expect, should, use } from "chai";
import * as _ from "lodash";
import { GameInitResponse, GameStateInfoRequest } from "@skywind-group/sw-game-core";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { generateSessionToken, generateStartGameToken } from "../../../skywind/services/tokens";
import { GameContextID } from "../../../skywind/services/contextIds";
import config from "../../../skywind/config";
import { funDefaultLimit } from "../../funDefaultLimits";
import { GameSession } from "../../../skywind/services/gameSession";
import { suite, test } from "mocha-typescript";
import { BaseGameControllerSpec } from "../gameController/gameController.base.spec";
import { GameFlowContextImpl } from "../../../skywind/services/context/gameContextImpl";
import { getITGGameController, ITGDeferredUpdateRequest } from "../../../skywind/services/itgGameController";
import { GameData } from "../../../skywind/services/auth";
import { GameFinalizationType } from "@skywind-group/sw-wallet-adapter-core";

require("source-map-support").install();

const defaultLimitsByCurrency = funDefaultLimit.byCurrencies;

should();
use(require("chai-as-promised"));

@suite("ITG Game controller")
export class ITGGameControllerSpec extends BaseGameControllerSpec {

    @test("processes ITG game 'init' request")
    public async processItgGameInitFlow() {
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        const itgGameController = getITGGameController();

        const initResponse = await itgGameController.process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
            gameVersion: "1.2.0"
        });

        expect(initResponse).deep.equal({
            balance: {
                amount: 1000,
                bonus: {
                    amount: 0,
                },
                currency: "USD",
                real: {
                    amount: 1000,
                },
            },
            extraData: undefined,
            gameSession: initResponse.gameSession,
            settings: undefined,
            gameSettings: undefined,
            brandSettings: undefined,
            jrsdSettings: { a: 1 },
            jurisdictionCode: "GB",
            playedFromCountry: "GB",
            playerCode: "PL001",
            currencyReplacement: undefined,
            lastRequestId: 0,
            result: {
                request: "init",
                settings: {
                    coins: [
                        1
                    ],
                    currencyMultiplier: 100,
                    defaultCoin: 1,
                    maxTotalStake: 500,
                    stakeAll: [
                        0.1,
                        0.5,
                        1,
                        2,
                        3,
                        5
                    ],
                    stakeDef: 1,
                    stakeMax: 10,
                    stakeMin: 0.1,
                    winMax: 3000000,
                },
                totalBet: undefined,
                totalWin: undefined
            },
            roundEnded: true,
            roundTotalBet: undefined,
            roundTotalWin: undefined,
            limits: {
                coins: [1],
                defaultCoin: 1,
                maxTotalStake: 500,
                stakeAll: [0.1, 0.5, 1, 2, 3, 5],
                stakeDef: 1,
                stakeMax: 10,
                stakeMin: 0.1,
                winMax: 3000000,
                currencyMultiplier: 100,
            }
        });

        expect(BaseGameControllerSpec.startGame.args[0][0]).equal(BaseGameControllerSpec.gameData.gameId);
        expect(BaseGameControllerSpec.startGame.args[0][1]).equal(BaseGameControllerSpec.startToken);

        const context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            lastRequestId: 0,
            _prev_sessionId: undefined,
            _sessionId: GameSession.create(initResponse.gameSession),
            gameData: BaseGameControllerSpec.gameDataWithLimits,
            gameVersion: "1.2.0",
            id: GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            lockExclusively: false,
            reactive: true,
            gameSerialNumber: 0,
            totalEventId: 0,
            retryAttempts: undefined,
            logoutResult: undefined,
            logoutId: undefined,
            pendingModification: undefined,
            jackpotPending: undefined,
            roundEnded: true,
            persistencePolicy: 0,
            round: undefined,
            gameContext: undefined,
            jpContext: undefined,
            version: 1,
            roundId: "0",
            specialState: undefined,
            settings: {
                coins: [1],
                defaultCoin: 1,
                maxTotalStake: 500,
                stakeAll: [0.1, 0.5, 1, 2, 3, 5],
                stakeDef: 1,
                stakeMax: 10,
                stakeMin: 0.1,
                winMax: 3000000,
                currencyMultiplier: 100,
            }
        });
    }

    @test("processes ITG game 'init' request with initial state update")
    public async processItgGameInitFlowWithInitialStateUpdate() {
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        const itgGameController = getITGGameController();
        const initResponse = await itgGameController.process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId"
        });

        expect(initResponse).deep.equal({
            balance: {
                amount: 1000,
                bonus: {
                    amount: 0,
                },
                currency: "USD",
                real: {
                    amount: 1000,
                },
            },
            extraData: undefined,
            gameSession: initResponse.gameSession,
            settings: undefined,
            gameSettings: undefined,
            brandSettings: undefined,
            jrsdSettings: { a: 1 },
            jurisdictionCode: "GB",
            playedFromCountry: "GB",
            playerCode: "PL001",
            currencyReplacement: undefined,
            lastRequestId: 0,
            result: {
                request: "init",
                settings: {
                    coins: [
                        1
                    ],
                    currencyMultiplier: 100,
                    defaultCoin: 1,
                    maxTotalStake: 500,
                    stakeAll: [
                        0.1,
                        0.5,
                        1,
                        2,
                        3,
                        5
                    ],
                    stakeDef: 1,
                    stakeMax: 10,
                    stakeMin: 0.1,
                    winMax: 3000000,
                },
                totalBet: undefined,
                totalWin: undefined
            },
            roundEnded: true,
            roundTotalBet: undefined,
            roundTotalWin: undefined,
            limits: {
                coins: [1],
                defaultCoin: 1,
                maxTotalStake: 500,
                stakeAll: [0.1, 0.5, 1, 2, 3, 5],
                stakeDef: 1,
                stakeMax: 10,
                stakeMin: 0.1,
                winMax: 3000000,
                currencyMultiplier: 100,
            }
        });

        expect(BaseGameControllerSpec.startGame.args[0][0]).equal(BaseGameControllerSpec.gameData.gameId);
        expect(BaseGameControllerSpec.startGame.args[0][1]).equal(BaseGameControllerSpec.startToken);

        const deferredUpdateRequest = {
            request: "deferred-update",
            requestId: 0,
            gameSession: initResponse.gameSession,
            gameState: {
                prop: "anything",
                state: "string"
            },
            gameType: "normal",
            gameStatus: "settled"
        };

        await itgGameController.process(deferredUpdateRequest);

        const context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            lastRequestId: 0,
            _prev_sessionId: undefined,
            _sessionId: GameSession.create(initResponse.gameSession),
            gameData: BaseGameControllerSpec.gameDataWithLimits,
            gameVersion: "1.0.0",
            id: GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            lockExclusively: false,
            reactive: true,
            gameSerialNumber: 0,
            totalEventId: 0,
            retryAttempts: undefined,
            logoutResult: undefined,
            logoutId: undefined,
            pendingModification: undefined,
            jackpotPending: undefined,
            roundEnded: true,
            persistencePolicy: 0,
            round: undefined,
            gameContext: deferredUpdateRequest.gameState,
            jpContext: undefined,
            version: 2,
            roundId: "0",
            specialState: undefined,
            settings: {
                coins: [1],
                defaultCoin: 1,
                maxTotalStake: 500,
                stakeAll: [0.1, 0.5, 1, 2, 3, 5],
                stakeDef: 1,
                stakeMax: 10,
                stakeMin: 0.1,
                winMax: 3000000,
                currencyMultiplier: 100,
            }
        });
    }

    @test("processes ITG game 'init' request without gameId")
    public async processItgGameInitRequestWithoutGameId() {
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        this.stubGame();

        const initResponse = await getITGGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            gameId: undefined,
            deviceId: "deviceId",
        });

        expect(initResponse).deep.equal({
            balance: {
                amount: 1000,
                bonus: {
                    amount: 0,
                },
                currency: "USD",
                real: {
                    amount: 1000,
                },
            },
            extraData: undefined,
            gameSession: initResponse.gameSession,
            gameSettings: undefined,
            brandSettings: undefined,
            settings: undefined,
            jrsdSettings: { a: 1 },
            jurisdictionCode: "GB",
            playedFromCountry: "GB",
            playerCode: "PL001",
            currencyReplacement: undefined,
            lastRequestId: 0,
            result: {
                request: "init",
                settings: {
                    coins: [
                        1
                    ],
                    currencyMultiplier: 100,
                    defaultCoin: 1,
                    maxTotalStake: 500,
                    stakeAll: [
                        0.1,
                        0.5,
                        1,
                        2,
                        3,
                        5
                    ],
                    stakeDef: 1,
                    stakeMax: 10,
                    stakeMin: 0.1,
                    winMax: 3000000,
                },
                totalBet: undefined,
                totalWin: undefined
            },
            roundEnded: true,
            roundTotalBet: undefined,
            roundTotalWin: undefined,
            limits: {
                coins: [1],
                defaultCoin: 1,
                maxTotalStake: 500,
                stakeAll: [0.1, 0.5, 1, 2, 3, 5],
                stakeDef: 1,
                stakeMax: 10,
                stakeMin: 0.1,
                winMax: 3000000,
                currencyMultiplier: 100,
            }
        });

        expect(BaseGameControllerSpec.startGame.args[0][0]).equal(BaseGameControllerSpec.gameData.gameId);
        expect(BaseGameControllerSpec.startGame.args[0][1]).equal(BaseGameControllerSpec.startToken);
        const context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            lastRequestId: 0,
            _prev_sessionId: undefined,
            _sessionId: GameSession.create(initResponse.gameSession),
            gameData: BaseGameControllerSpec.gameDataWithLimits,
            gameContext: undefined,
            gameVersion: "1.0.0",
            id: GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            lockExclusively: false,
            reactive: true,
            gameSerialNumber: 0,
            totalEventId: 0,
            retryAttempts: undefined,
            logoutResult: undefined,
            logoutId: undefined,
            pendingModification: undefined,
            jackpotPending: undefined,
            roundEnded: true,
            persistencePolicy: 0,
            round: undefined,
            jpContext: undefined,
            version: 1,
            roundId: "0",
            settings: {
                coins: [1],
                defaultCoin: 1,
                maxTotalStake: 500,
                stakeAll: [0.1, 0.5, 1, 2, 3, 5],
                stakeDef: 1,
                stakeMax: 10,
                stakeMin: 0.1,
                winMax: 3000000,
                currencyMultiplier: 100,
            },
            specialState: undefined
        });
    }

    @test("processes ITG game 'init' request - game not matching")
    public async processesItgInit_GameNotMatching() {
        const startTokenDataMismatch = {
            playerCode: "PL001",
            gameCode: "test_slot",
            brandId: 1,
            providerGameCode: "other_slot"
        };
        const startTokenMismatch = await generateStartGameToken(startTokenDataMismatch);
        await expect(getITGGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: startTokenMismatch,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        })).to.be.rejectedWith(GameNotMatch);
    }

    @test("processes ITG game 'init' request - deviceId is missing")
    public async processesItgInit_DeviceIdIsMissing() {
        const startTokenData = {
            playerCode: "PL001",
            gameCode: "test_slot",
            brandId: 1,
            providerGameCode: "test_slot"
        };
        const startToken = await generateStartGameToken(startTokenData);
        await expect(getITGGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: startToken,
            name: "GAME!",
            gameId: "test_slot",
        })).to.be.rejectedWith(ValidationError).then(err => {
            expect(err.message).to.be.equal("DeviceId is missing");
        });
    }

    @test("processes ITG game 'init' request and load context form offline storage")
    public async processGameInit_And_LoadContextOffline() {
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        this.stubGame();

        await getITGGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId");
        const context: GameFlowContextImpl = await getGameFlowContextManager()
            .findGameContextById(gameID);
        context.session = await GameSession.generate(gameID, "real");
        context.lastRequestId = -1;
        context.gameData = BaseGameControllerSpec.gameData;
        context.settings = {};

        // move to offline storage
        await getGameFlowContextManager().saveGameContextOffline([context]);
        await context.remove();

        const initResponse = await getITGGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        const newContext = await getGameFlowContextManager()
            .findGameContextById(gameID);
        expect(_.omit(newContext, "createdAt", "updatedAt", "requestContext")).deep.equal({
            lastRequestId: 0,
            _prev_sessionId: undefined,
            _sessionId: GameSession.create(initResponse.gameSession),
            gameData: BaseGameControllerSpec.gameDataWithLimits,
            gameVersion: "1.0.0",
            id: GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            lockExclusively: false,
            reactive: true,
            gameSerialNumber: 0,
            totalEventId: 0,
            retryAttempts: undefined,
            logoutResult: undefined,
            logoutId: undefined,
            pendingModification: undefined,
            jackpotPending: undefined,
            roundEnded: true,
            persistencePolicy: 0,
            round: undefined,
            gameContext: undefined,
            jpContext: undefined,
            version: 3,
            roundId: "0",
            specialState: undefined,
            settings: {
                coins: [1],
                defaultCoin: 1,
                maxTotalStake: 500,
                stakeAll: [0.1, 0.5, 1, 2, 3, 5],
                stakeDef: 1,
                stakeMax: 10,
                stakeMin: 0.1,
                winMax: 3000000,
                currencyMultiplier: 100,
            },
        });

        const offlineContext = await getGameFlowContextManager().findOfflineGameContext(newContext.id);
        expect(offlineContext).is.undefined;
    }

    @test("processes ITG game 'init' request - fun mode")
    public async processGameInit_FunMode() {
        this.stubGame(this.createGame([
                {
                    gameId: "test_slot",
                    name: "GAME!",
                    request: "init",
                    settings: defaultLimitsByCurrency["USD"],
                    slot: {
                        stub: "FORTESTREASON",
                    },
                    previousResult: null,
                    stake: null,
                } as GameInitResponse
            ],
            [
                {
                    currentScene: "defaultScene",
                    nextScene: "nextScene",
                }
            ]));

        const expectedSettings = _.merge({}, defaultLimitsByCurrency["USD"],
            { "coins": [1], "currencyMultiplier": 100, "defaultCoin": 1 });

        BaseGameControllerSpec.funStartGame.returns(BaseGameControllerSpec.funStartGameResult);

        const initResponse = await getITGGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.funStartGameToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse).deep.equal({
            balance: {
                amount: config.funGame.startAmount,
                bonus: {
                    amount: 0,
                },
                currency: "USD",
                real: {
                    amount: config.funGame.startAmount,
                },
            },
            extraData: undefined,
            gameSession: initResponse.gameSession,
            playerCode: "PL001",
            currencyReplacement: undefined,
            lastRequestId: 0,
            result: {
                request: "init",
                settings: {
                    chipList: [
                        0.01,
                        0.02,
                        0.03,
                        0.04,
                        0.05,
                        0.06,
                        0.08,
                        0.1,
                        0.2,
                        0.25,
                        0.3,
                        0.4,
                        0.5,
                        0.75,
                        1,
                        2,
                        5,
                        8
                    ],
                    coins: [
                        1
                    ],
                    coinsRate: 0.01,
                    currencyMultiplier: 100,
                    defaultCoin: 1,
                    maxTotalStake: 500,
                    stakeAll: [
                        0.01,
                        0.02,
                        0.03,
                        0.04,
                        0.05,
                        0.06,
                        0.08,
                        0.1,
                        0.2,
                        0.25,
                        0.3,
                        0.4,
                        0.5,
                        0.75,
                        1,
                        2,
                        5,
                        8
                    ],
                    stakeDef: 0.04,
                    stakeMax: 8,
                    stakeMin: 0.01,
                    winMax: 1500000
                },
                totalBet: undefined,
                totalWin: undefined
            },
            roundEnded: true,
            roundTotalBet: undefined,
            roundTotalWin: undefined,
            settings: undefined,
            gameSettings: undefined,
            brandSettings: undefined,
            jrsdSettings: undefined,
            jurisdictionCode: undefined,
            playedFromCountry: "GB",
            limits: defaultLimitsByCurrency["USD"]
        });

        const gameID = GameContextID.create("for_unit_tests", 1, "PL001", "deviceId");
        const context = await getGameFlowContextManager("fun")
            .findGameContextById(gameID);
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            lastRequestId: 0,
            _prev_sessionId: undefined,
            _sessionId: GameSession.create(initResponse.gameSession),
            pendingModification: undefined,
            jackpotPending: undefined,
            gameData: context.gameData,
            gameVersion: "1.0.0",
            roundEnded: true,
            persistencePolicy: 0,
            round: undefined,
            roundId: "0",
            id: gameID,
            lockExclusively: false,
            reactive: true,
            gameSerialNumber: 0,
            totalEventId: 0,
            retryAttempts: undefined,
            logoutResult: undefined,
            logoutId: undefined,
            gameContext: undefined,
            jpContext: undefined,
            settings: expectedSettings,
            specialState: undefined,
            version: 1
        });
    }

    @test("process ITG state request without balance")
    public async processItgGetStateRequest_WithoutBalance() {
        // Ensure the game context exists
        const id: GameContextID = GameContextID.create(ITGGameControllerSpec.gameTokenData.gameCode,
            ITGGameControllerSpec.gameTokenData.brandId,
            ITGGameControllerSpec.gameTokenData.playerCode,
            "deviceId",
            ITGGameControllerSpec.gameTokenData.playmode);
        const session = await GameSession.generate(id, ITGGameControllerSpec.gameTokenData.playmode);
        const contextManager = getGameFlowContextManager(ITGGameControllerSpec.gameTokenData.playmode);
        const gameData = {
            gameTokenData: ITGGameControllerSpec.gameTokenData,
            gameId: "test_slot",
            limits: {
                stakeAll: [
                    0.01,
                    0.02,
                    0.03,
                    0.04,
                    0.05,
                    0.06,
                    0.07,
                    0.08,
                    0.09,
                    0.1,
                    0.12,
                    0.14,
                    0.16,
                    0.18,
                    0.2,
                    0.22,
                    0.25,
                    0.3,
                    0.35,
                    0.4,
                    0.45,
                    0.5,
                    0.6,
                    0.7,
                    0.8,
                    0.9,
                    1,
                    1.2,
                    1.4,
                    1.5,
                    1.6,
                    1.8,
                    2,
                    2.5,
                    3,
                    3.5,
                    4,
                    4.5,
                    5,
                    7.5,
                    10,
                    15
                ],
                stakeDef: 0.05,
                stakeMax: 15,
                stakeMin: 0.01,
                maxTotalStake: 300,
                winMax: 430729.5
            },
            settings: {
                prop: true
            },
            jrsdSettings: {
                autoPlay: false
            },
            gameSettings: {
                freebetsEnabled: true
            },
            brandSettings: {
                fastPlay: false,
                turbo: false,
                turboPlus: false
            }
        } as GameData;

        await contextManager.createOrUpdate(
            id,
            undefined,
            session,
            gameData,
            undefined
        );

        const sessionToken = await generateSessionToken({
            sessionId: "0",
            id: "games:context:1:PL001:test_slot:deviceId",
            gameMode: "real"
        });

        const itgGameController = getITGGameController();

        // Update the state once
        const deferredUpdateRequest: ITGDeferredUpdateRequest = {
            request: "deferred-update",
            requestId: 0,
            gameSession: sessionToken,
            gameState: {
                someState: "string"
            },
            gameType: "normal",
            gameStatus: "settled",
            bet: 10
        };
        await itgGameController.commitDeferredUpdate(deferredUpdateRequest);

        const itgStateRequest: GameStateInfoRequest = {
            request: "state",
            gameSession: sessionToken
        };

        const itgStateResponse = await itgGameController.getStateForItg(itgStateRequest);
        expect(itgStateResponse).to.deep.equal({
            playerCode: ITGGameControllerSpec.gameTokenData.playerCode,
            gameId: "test_slot",
            gameState: {
                someState: "string"
            },
            limits: gameData.limits,
            settings: gameData.settings,
            jrsdSettings: gameData.jrsdSettings,
            gameSettings: gameData.gameSettings,
            brandSettings: gameData.brandSettings,
            balance: undefined,
            lastRequestId: 0
        });
    }

    @test("process ITG state request with balance")
    public async processItgGetStateRequest_WithBalance() {
        // Ensure the game context exists
        const id: GameContextID = GameContextID.create(ITGGameControllerSpec.gameTokenData.gameCode,
            ITGGameControllerSpec.gameTokenData.brandId,
            ITGGameControllerSpec.gameTokenData.playerCode,
            "deviceId",
            ITGGameControllerSpec.gameTokenData.playmode);
        const session = await GameSession.generate(id, ITGGameControllerSpec.gameTokenData.playmode);
        const contextManager = getGameFlowContextManager(ITGGameControllerSpec.gameTokenData.playmode);
        const gameData = {
            gameTokenData: ITGGameControllerSpec.gameTokenData,
            gameId: "test_slot",
            limits: {
                stakeAll: [
                    0.01,
                    0.02,
                    0.03,
                    0.04,
                    0.05,
                    0.06,
                    0.07,
                    0.08,
                    0.09,
                    0.1,
                    0.12,
                    0.14,
                    0.16,
                    0.18,
                    0.2,
                    0.22,
                    0.25,
                    0.3,
                    0.35,
                    0.4,
                    0.45,
                    0.5,
                    0.6,
                    0.7,
                    0.8,
                    0.9,
                    1,
                    1.2,
                    1.4,
                    1.5,
                    1.6,
                    1.8,
                    2,
                    2.5,
                    3,
                    3.5,
                    4,
                    4.5,
                    5,
                    7.5,
                    10,
                    15
                ],
                stakeDef: 0.05,
                stakeMax: 15,
                stakeMin: 0.01,
                maxTotalStake: 300,
                winMax: 430729.5
            },
            settings: {
                prop: true
            },
            jrsdSettings: {
                autoPlay: false
            },
            gameSettings: {
                freebetsEnabled: true
            },
            brandSettings: {
                fastPlay: false,
                turbo: false,
                turboPlus: false
            }
        } as GameData;

        await contextManager.createOrUpdate(
            id,
            undefined,
            session,
            gameData,
            undefined
        );

        const sessionToken = await generateSessionToken({
            sessionId: "0",
            id: "games:context:1:PL001:test_slot:deviceId",
            gameMode: "real"
        });

        const itgGameController = getITGGameController();

        // Update the state once
        const deferredUpdateRequest: ITGDeferredUpdateRequest = {
            request: "deferred-update",
            requestId: 0,
            gameSession: sessionToken,
            gameState: {
                someState: "string"
            },
            gameType: "normal",
            gameStatus: "settled"
        };
        await itgGameController.commitDeferredUpdate(deferredUpdateRequest);

        const itgStateRequest: GameStateInfoRequest = {
            request: "state",
            gameSession: sessionToken,
            getBalance: true
        };
        const playerBalance = await itgGameController.getPlayerBalance(itgStateRequest);

        const itgStateResponse = await itgGameController.getStateForItg(itgStateRequest);
        expect(itgStateResponse).to.deep.equal({
            playerCode: ITGGameControllerSpec.gameTokenData.playerCode,
            gameId: "test_slot",
            gameState: {
                someState: "string"
            },
            limits: gameData.limits,
            settings: gameData.settings,
            jrsdSettings: gameData.jrsdSettings,
            gameSettings: gameData.gameSettings,
            brandSettings: gameData.brandSettings,
            balance: playerBalance.balance,
            lastRequestId: 0
        });
    }

    @test("process ITG commitDeferredUpdate")
    public async processItgCommitDeferredUpdate() {
        // Ensure the game context exists
        const id: GameContextID = GameContextID.create(ITGGameControllerSpec.gameTokenData.gameCode,
            ITGGameControllerSpec.gameTokenData.brandId,
            ITGGameControllerSpec.gameTokenData.playerCode,
            "deviceId",
            ITGGameControllerSpec.gameTokenData.playmode);
        const session = await GameSession.generate(id, ITGGameControllerSpec.gameTokenData.playmode);
        const contextManager = getGameFlowContextManager(ITGGameControllerSpec.gameTokenData.playmode);

        await contextManager.createOrUpdate(
            id,
            undefined,
            session,
            ITGGameControllerSpec.gameData,
            undefined
        );

        const sessionToken = await generateSessionToken({
            sessionId: "0",
            id: "games:context:1:PL001:test_slot:deviceId",
            gameMode: "real"
        });

        const itgGameController = getITGGameController();
        const deferredUpdateRequest: ITGDeferredUpdateRequest = {
            request: "deferred-update",
            requestId: 0,
            gameSession: sessionToken,
            gameState: {
                someState: "string"
            },
            gameType: "normal",
            gameStatus: "settled",
            bet: 1,
            win: 0.2,
            history: {
                type: "ITG",
                roundEnded: true,
                data: {}
            }
        };

        const previousPlayerBalance = await itgGameController.getPlayerBalance(deferredUpdateRequest);
        const deferredUpdateResponse = await itgGameController.commitDeferredUpdate(deferredUpdateRequest);
        const currentPlayerBalance = await itgGameController.getPlayerBalance(deferredUpdateRequest);
        const state = await itgGameController.getStateForItg({
            request: "state",
            gameSession: sessionToken
        });
        expect(deferredUpdateResponse).to.deep.equal({
            balance: currentPlayerBalance.balance,
            extraData: undefined,
            currencyReplacement: undefined,
            lastRequestId: 0,
            result: {
                request: "deferred-update",
                settings: {
                    coins: [
                        1
                    ],
                    currencyMultiplier: 100,
                    defaultCoin: 1
                },
                totalBet: 1,
                totalWin: 0.2,
            },
            roundEnded: true,
            roundTotalBet: 1,
            roundTotalWin: 0.2,
            settings: undefined
        });
        expect(currentPlayerBalance.balance.amount).to.equal(previousPlayerBalance.balance.amount - 0.8);
        expect(state.gameState).to.deep.equal({ someState: "string" });
    }

    @test("process ITG commitDeferredUpdate - fails when the game context does not exist")
    public async processItgCommitDeferredUpdateFailsWhenGameContextDoesNotExist() {
        const itgGameController = getITGGameController();
        const deferredUpdateRequest: ITGDeferredUpdateRequest = {
            request: "deferred-update",
            requestId: 0,
            gameSession: await generateSessionToken({
                sessionId: "0",
                id: "games:context:1:PL001:test_slot:deviceId",
                gameMode: "real"
            }),
            gameState: {
                someState: "string"
            },
            gameType: "normal",
            gameStatus: "settled",
            bet: 1,
            win: 0.2
        };
        expect(itgGameController.process(deferredUpdateRequest)).to.eventually.be.rejectedWith(Error);
    }

    @test("process ITG commitDeferredUpdate - finalization payment")
    public async processItgCommitDeferredUpdate_FinalizationPayment() {
        // Ensure the game context exists
        const id: GameContextID = GameContextID.create(ITGGameControllerSpec.gameTokenData.gameCode,
            ITGGameControllerSpec.gameTokenData.brandId,
            ITGGameControllerSpec.gameTokenData.playerCode,
            "deviceId",
            ITGGameControllerSpec.gameTokenData.playmode);
        const session = await GameSession.generate(id, ITGGameControllerSpec.gameTokenData.playmode);
        const contextManager = getGameFlowContextManager(ITGGameControllerSpec.gameTokenData.playmode);

        await contextManager.createOrUpdate(
            id,
            undefined,
            session,
            ITGGameControllerSpec.gameData,
            undefined
        );

        const sessionToken = await generateSessionToken({
            sessionId: "0",
            id: "games:context:1:PL001:test_slot:deviceId",
            gameMode: "real"
        });

        const itgGameController = getITGGameController();
        const deferredUpdateRequest: ITGDeferredUpdateRequest = {
            request: "deferred-update",
            requestId: 0,
            gameSession: sessionToken,
            gameState: {
                someState: "string"
            },
            gameType: "normal",
            gameStatus: "settled",
            bet: 0,
            win: 2,
            finalizationType: GameFinalizationType.ITG_FINALIZATION
        };

        const previousPlayerBalance = await itgGameController.getPlayerBalance(deferredUpdateRequest);
        const deferredUpdateResponse = await itgGameController.commitDeferredUpdate(deferredUpdateRequest);
        const currentPlayerBalance = await itgGameController.getPlayerBalance(deferredUpdateRequest);
        const state = await itgGameController.getStateForItg({
            request: "state",
            gameSession: sessionToken
        });
        expect(deferredUpdateResponse).to.deep.equal({
            balance: currentPlayerBalance.balance,
            extraData: undefined,
            lastRequestId: 0,
            result: {
                request: "deferred-update",
                settings: {
                    coins: [
                        1
                    ],
                    currencyMultiplier: 100,
                    defaultCoin: 1,
                },
                totalBet: 0,
                totalWin: 2,
            },
            roundEnded: false,
            roundTotalBet: 0,
            roundTotalWin: 2,
            settings: undefined,
            currencyReplacement: undefined
        });
        expect(currentPlayerBalance.balance.amount).to.equal(previousPlayerBalance.balance.amount + 2);
        expect(state.gameState).to.deep.equal({ someState: "string" });
    }
}
