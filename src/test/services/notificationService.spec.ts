import { suite, test } from "mocha-typescript";
import NotificationService from "../../skywind/services/notification";
import { stub } from "sinon";
import { expect, use } from "chai";
import { sleep } from "@skywind-group/sw-utils";

use(require("deep-equal-in-any-order"));

@suite()
class NotificationServiceSpec {

    @test("subscribes, notify")
    public async testSubscribeNotify() {
        const handler = stub();
        await NotificationService.subscribe("test", handler);
        await NotificationService.subscribe("test1", handler);
        await NotificationService.subscribe("test2", handler);

        await NotificationService.notify("test", { id: 1 });
        await NotificationService.notify("test1", { id: 2 });
        await NotificationService.notify("test", { id: 3 });

        await sleep(100);

        expect(handler.args).deep.equalInAnyOrder([
            ["test", { "id": 1 }],
            ["test1", { "id": 2 }],
            ["test", { "id": 3 }],
        ]);
    }

    @test("subscribes, notify with several handlers")
    public async testSubscribeNotifyWithSeveralHandlers() {
        const handler1 = stub();
        const handler2 = stub();
        await NotificationService.subscribe("test3", handler1);
        await NotificationService.subscribe("test3", handler2);
        await NotificationService.subscribe("test4", handler1);

        await NotificationService.notify("test3", { id: 1 });
        await NotificationService.notify("test4", { id: 2 });

        await sleep(100);

        expect(handler1.args).deep.eq([
            ["test3", { "id": 1 }],
            ["test4", { "id": 2 }],
        ]);

        expect(handler2.args).deep.eq([
            ["test3", { "id": 1 }],
        ]);
    }
}
