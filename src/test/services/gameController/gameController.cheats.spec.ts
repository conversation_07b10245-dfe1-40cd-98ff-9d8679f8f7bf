import { expect, should, use } from "chai";
import { GameInitResponse } from "@skywind-group/sw-game-core";
import { getSyncGameController } from "../../../skywind/services/synccontroller";
import { suite, test } from "mocha-typescript";
import { BaseGameControllerSpec } from "./gameController.base.spec";
import config from "../../../skywind/config";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { GameContextID } from "../../../skywind/services/contextIds";
import { GameSession } from "../../../skywind/services/gameSession";
import { TEST_MODULE_NAME } from "../../helper";
import * as _ from "lodash";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("GameController - Cheats")
class Cheats extends BaseGameControllerSpec {

    public static cheatingAllowed;

    public static async before() {
        await BaseGameControllerSpec.before();
        const cheatsConfig = require("../../../../resources/testRandomFactory/cheatsConfig.json");
        this.cheatingAllowed = cheatsConfig.allowSetPositionsByClient;
        cheatsConfig.allowSetPositionsByClient = true;
    }

    public static async after() {
        await BaseGameControllerSpec.after();
        const cheatsConfig = require("../../../../resources/testRandomFactory/cheatsConfig.json");
        cheatsConfig.allowSetPositionsByClient = this.cheatingAllowed;
    }

    @test("cheats when cheating allowed")
    public async cheatsWhenCheatingAllowed() {
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
            ]
        ));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": BaseGameControllerSpec.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
        });

        const spinResponse = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
            positions: [0]
        });

        expect(spinResponse).deep.equal({
            "balance": {
                "amount": 1004,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1004,
                }
            },
            "extraData": undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 1,
            "result": {
                "request": "spin",
                "roundEnded": false,
                "totalBet": 1,
                "totalWin": 5,
            },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId");
        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": BaseGameControllerSpec.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                currentBet: 1,
                betsCount: 1
            },
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 4,
        });
    }
}
