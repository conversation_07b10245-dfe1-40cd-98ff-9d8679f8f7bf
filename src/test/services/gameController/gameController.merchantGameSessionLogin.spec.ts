import { expect, should, use } from "chai";
import * as _ from "lodash";
import { TEST_MODULE_NAME } from "../../helper";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { GameContextID } from "../../../skywind/services/contextIds";
import { getSyncGameController } from "../../../skywind/services/synccontroller";
import { GameSession } from "../../../skywind/services/gameSession";
import { suite, test } from "mocha-typescript";
import { BaseGameControllerSpec } from "./gameController.base.spec";
import { MerchantLogoutResult } from "../../../skywind/services/context/gamecontext";
import { testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import { ManagementAPITransientError } from "../../../skywind/errors";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;
import status200 = testing.status200;
import status500 = testing.status500;

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("Game controller. Restore broken game with merchant game session login")
export class GameControllerMerchantGameSessionLoginSpec extends BaseGameControllerSpec {
    private request: RequestMock;

    public async before(): Promise<any> {
        this.request = requestMock(superagent);
        return super.before();
    }

    public async after(): Promise<any> {
        this.request.unmock(superagent);
        return super.after();
    }

    @test("processes game 'init' request - successful login")
    public async processGameInitRequest() {
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        this.stubGame();

        const initResponse1 = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse1).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1000,
                },
            },
            "gameSession": initResponse1.gameSession,
            "gameSettings": undefined,
            "brandSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": {
                "gameId": "test_slot",
                "name": "GAME!",
                "request": "init",
                "settings": {
                    "coins": [1],
                    "defaultCoin": 1,
                    "maxTotalStake": 500,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 10,
                    "stakeMin": 0.1,
                    "winMax": 3000000,
                    "currencyMultiplier": 100,
                },
                "slot": {
                    "stub": "FORTESTREASON",
                },
                "previousResult": null,
                "stake": null
            },
            "roundEnded": true,
            renderType: 0,
            brandInfo: undefined,
        });

        expect(BaseGameControllerSpec.startGame.args[0][0]).equal(BaseGameControllerSpec.gameData.gameId);
        expect(BaseGameControllerSpec.startGame.args[0][1]).equal(BaseGameControllerSpec.startToken);
        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse1.gameSession),
            "gameData": BaseGameControllerSpec.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "defaultScene",
                "nextScene": "nextScene",
            },
            "jpContext": undefined,
            "version": 2,
            "specialState": undefined,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
        });

        // mark the context as required login and init the game
        await context.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);
        await context.setLogoutResult(MerchantLogoutResult.REQUIRE_LOGIN);
        const logoutId = context.logoutId;
        this.request.post("http://api:3006//v2/play/game/login", status200({}));
        const initResponse2 = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse2).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1000,
                },
            },
            "gameSession": initResponse2.gameSession,
            "gameSettings": undefined,
            "brandSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": undefined,
            "roundEnded": true,
            renderType: 0,
            brandInfo: undefined,
        });

        expect(BaseGameControllerSpec.startGame.args[0][0]).equal(BaseGameControllerSpec.gameData.gameId);
        expect(BaseGameControllerSpec.startGame.args[0][1]).equal(BaseGameControllerSpec.startToken);
        context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse2.gameSession),
            "gameData": BaseGameControllerSpec.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": undefined,
            "jpContext": undefined,
            "specialState": undefined,
            "version": 7,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
        });
        expect(this.request.args[0].url).equal("http://api:3006//v2/play/game/login");
        expect(this.request.args[0].body).deep.equal({
            "gameContextId": context.id.asString(),
            "gameToken": context.gameData.gameTokenData.token,
            "logoutId": logoutId,
            "roundId": context.roundId,
            "roundPID": "doRlLRrM"
        });
    }

    @test("processes game 'init' request - skip login")
    public async processGameInitRequest_skiplogin() {
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        this.stubGame();

        const initResponse1 = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse1).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1000,
                },
            },
            "gameSession": initResponse1.gameSession,
            "gameSettings": undefined,
            "brandSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": {
                "gameId": "test_slot",
                "name": "GAME!",
                "request": "init",
                "settings": {
                    "coins": [1],
                    "defaultCoin": 1,
                    "maxTotalStake": 500,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 10,
                    "stakeMin": 0.1,
                    "winMax": 3000000,
                    "currencyMultiplier": 100,
                },
                "slot": {
                    "stub": "FORTESTREASON",
                },
                "previousResult": null,
                "stake": null
            },
            "roundEnded": true,
            renderType: 0,
            brandInfo: undefined,
        });

        expect(BaseGameControllerSpec.startGame.args[0][0]).equal(BaseGameControllerSpec.gameData.gameId);
        expect(BaseGameControllerSpec.startGame.args[0][1]).equal(BaseGameControllerSpec.startToken);
        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse1.gameSession),
            "gameData": BaseGameControllerSpec.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "defaultScene",
                "nextScene": "nextScene",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
        });

        // mark the context as required login and init the game
        await context.setLogoutResult(MerchantLogoutResult.SKIP_LOGIN);
        this.request.post("http://api:3006//v2/play/game/login", status200({}));
        const initResponse2 = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse2).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1000,
                },
            },
            "gameSession": initResponse2.gameSession,
            "gameSettings": undefined,
            "brandSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": undefined,
            "roundEnded": true,
            renderType: 0,
            brandInfo: undefined,
        });

        expect(BaseGameControllerSpec.startGame.args[0][0]).equal(BaseGameControllerSpec.gameData.gameId);
        expect(BaseGameControllerSpec.startGame.args[0][1]).equal(BaseGameControllerSpec.startToken);
        context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse2.gameSession),
            "gameData": BaseGameControllerSpec.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": undefined,
            "jpContext": undefined,
            "specialState": undefined,
            "version": 6,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
        });
        expect(this.request.args).is.undefined;
    }

    @test("processes game 'init' request - failed login")
    public async processGameInitRequest_failed() {
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        this.stubGame();

        const initResponse1 = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse1).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1000,
                },
            },
            "gameSession": initResponse1.gameSession,
            "gameSettings": undefined,
            "brandSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": {
                "gameId": "test_slot",
                "name": "GAME!",
                "request": "init",
                "settings": {
                    "coins": [1],
                    "defaultCoin": 1,
                    "maxTotalStake": 500,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 10,
                    "stakeMin": 0.1,
                    "winMax": 3000000,
                    "currencyMultiplier": 100,
                },
                "slot": {
                    "stub": "FORTESTREASON",
                },
                "previousResult": null,
                "stake": null
            },
            "roundEnded": true,
            renderType: 0,
            brandInfo: undefined,
        });

        expect(BaseGameControllerSpec.startGame.args[0][0]).equal(BaseGameControllerSpec.gameData.gameId);
        expect(BaseGameControllerSpec.startGame.args[0][1]).equal(BaseGameControllerSpec.startToken);
        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse1.gameSession),
            "gameData": BaseGameControllerSpec.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "defaultScene",
                "nextScene": "nextScene",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
        });

        // mark the context as required login and init the game
        await context.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);
        await context.setLogoutResult(MerchantLogoutResult.REQUIRE_LOGIN);
        this.request.post("http://api:3006//v2/play/game/login", status500({}));
        await expect(getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        })).to.be.rejectedWith(ManagementAPITransientError);

        expect(BaseGameControllerSpec.startGame.args[0][0]).equal(BaseGameControllerSpec.gameData.gameId);
        expect(BaseGameControllerSpec.startGame.args[0][1]).equal(BaseGameControllerSpec.startToken);
        context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(context.logoutId).is.not.undefined;
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext", "_sessionId", "logoutId")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "gameData": BaseGameControllerSpec.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": MerchantLogoutResult.REQUIRE_LOGIN,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "defaultScene",
                "nextScene": "nextScene"
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 5,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
        });
        expect(this.request.args[0].url).equal("http://api:3006//v2/play/game/login");
        expect(this.request.args[0].body).deep.equal({
            "gameContextId": context.id.asString(),
            "gameToken": context.gameData.gameTokenData.token,
            "logoutId": context.logoutId,
            "roundId": context.roundId,
            "roundPID": "doRlLRrM"
        });
    }

    @test("processes game 'init' request - finish logout first")
    public async processGameInti_relogin() {
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        this.stubGame();

        const initResponse1 = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse1).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1000,
                },
            },
            "gameSession": initResponse1.gameSession,
            "gameSettings": undefined,
            "brandSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": {
                "gameId": "test_slot",
                "name": "GAME!",
                "request": "init",
                "settings": {
                    "coins": [1],
                    "defaultCoin": 1,
                    "maxTotalStake": 500,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 10,
                    "stakeMin": 0.1,
                    "winMax": 3000000,
                    "currencyMultiplier": 100,
                },
                "slot": {
                    "stub": "FORTESTREASON",
                },
                "previousResult": null,
                "stake": null
            },
            "roundEnded": true,
            renderType: 0,
            brandInfo: undefined,
        });

        expect(BaseGameControllerSpec.startGame.args[0][0]).equal(BaseGameControllerSpec.gameData.gameId);
        expect(BaseGameControllerSpec.startGame.args[0][1]).equal(BaseGameControllerSpec.startToken);
        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse1.gameSession),
            "gameData": BaseGameControllerSpec.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "defaultScene",
                "nextScene": "nextScene",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
        });

        // mark the context as required login and init the game
        await context.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);
        const logoutId = context.logoutId;
        this.request.post("http://api:3006//v2/play/game/logout", status200({ requireLogin: true }));
        this.request.post("http://api:3006//v2/play/game/login", status200({}));
        const initResponse2 = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse2).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1000,
                },
            },
            "gameSession": initResponse2.gameSession,
            "gameSettings": undefined,
            "brandSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": undefined,
            "roundEnded": true,
            renderType: 0,
            brandInfo: undefined,
        });

        expect(BaseGameControllerSpec.startGame.args[0][0]).equal(BaseGameControllerSpec.gameData.gameId);
        expect(BaseGameControllerSpec.startGame.args[0][1]).equal(BaseGameControllerSpec.startToken);
        context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse2.gameSession),
            "gameData": BaseGameControllerSpec.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": 0,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": undefined,
            "jpContext": undefined,
            "specialState": undefined,
            "version": 10,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
        });
        expect(this.request.args[0].url).equal("http://api:3006//v2/play/game/logout");
        expect(this.request.args[0].body).deep.equal({
            "gameContextId": context.id.asString(),
            "gameToken": context.gameData.gameTokenData.token,
            "logoutId": logoutId,
            "roundId": context.roundId,
            "roundPID": "doRlLRrM",
            "state": "finished"
        });
        expect(this.request.args[1].url).equal("http://api:3006//v2/play/game/login");
        expect(this.request.args[1].body).deep.equal({
            "gameContextId": context.id.asString(),
            "gameToken": context.gameData.gameTokenData.token,
            "logoutId": logoutId,
            "roundId": context.roundId,
            "roundPID": "doRlLRrM",
        });
    }
}
