import { expect, should, use } from "chai";
import { getSyncGameController } from "../../../skywind/services/synccontroller";
import { suite, test } from "mocha-typescript";
import { BaseGameControllerSpec } from "./gameController.base.spec";
import { TestGame, TestGameWithMarkUpdate } from "../../testGames";
import {
    ClientResponse,
    GameHistory,
    GameInfo,
    GameInitResponse,
    GamePlayResponse,
    PaymentInfo,
    SomeGame
} from "@skywind-group/sw-game-core";
import { mock } from "sinon";
import { InternalEventIsNotSupported } from "../../../skywind/errors";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("Game controller: internalEvent")
export class GameControllerInternalEventSpec extends BaseGameControllerSpec {
    public extension: any = {
        beforeInit: mock(),
        afterInit: mock(),
        beforePlay: mock(),
        afterPlay: mock(),
    };
    public initResponse: ClientResponse;

    public async after() {
        this.extension.beforePlay.reset();
        this.extension.afterPlay.reset();
        return super.after();
    }

    @test("processes 'internalEvent' request")
    public async processSpinAsInternalEvent() {
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        ));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        this.extension.beforePlay.once();
        this.extension.afterPlay.once();

        const internalResponse = await getSyncGameController().internalEvent({
            request: "spin",
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        } as any);

        expect(internalResponse).deep.equal({
            "balance": {
                "amount": 1004,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1004,
                }
            },
            extraData: undefined,
            "gameSession": initResponse.gameSession,
            "requestId": undefined,
            "result": {
                "request": "spin",
                "roundEnded": false,
                "totalBet": 1,
                "totalWin": 5,
            },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        this.extension.beforePlay.verify();
        this.extension.afterPlay.verify();
    }

    @test("processes 'internalEvent' request - failed, method is missing")
    public async processInternalEventFailed() {
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        const game = this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        );
        delete game.internalEvent;
        this.stubGame(game);

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        this.extension.beforePlay.never();
        this.extension.afterPlay.never();

        await expect(getSyncGameController().internalEvent({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        } as any)).rejectedWith(InternalEventIsNotSupported);

        this.extension.beforePlay.verify();
        this.extension.afterPlay.verify();
    }

    @test("processes 'internalEvent' request with 'dontUseBalance' in response")
    public async processSpinAsInternalEventWithDontUseBalance() {
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                {
                    response: { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                    dontUseBalance: true,
                    dontUseJackpot: true,
                } as any,
            ]
        ));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        this.extension.beforePlay.once();
        this.extension.afterPlay.once();

        const internalResponse = await getSyncGameController().internalEvent({
            request: "spin",
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        } as any);

        expect(internalResponse).deep.equal({
            balance: undefined,
            extraData: undefined,
            "gameSession": initResponse.gameSession,
            "requestId": undefined,
            "result": {
                "request": "spin",
                "roundEnded": false,
                "totalBet": 1,
                "totalWin": 5,
            },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        this.extension.beforePlay.verify();
        this.extension.afterPlay.verify();
    }

    @test("processes 'internalEvent' request without 'dontUseBalance' in response")
    public async processSpinAsInternalEventWithoutDontUseBalance() {
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                {
                    response: { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                } as any,
            ]
        ));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        this.extension.beforePlay.once();
        this.extension.afterPlay.once();

        const internalResponse = await getSyncGameController().internalEvent({
            request: "spin",
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        } as any);

        expect(internalResponse).deep.equal({
            balance: {
                "amount": 1004,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1004,
                }
            },
            extraData: undefined,
            "gameSession": initResponse.gameSession,
            "requestId": undefined,
            "result": {
                "request": "spin",
                "roundEnded": false,
                "totalBet": 1,
                "totalWin": 5,
            },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        this.extension.beforePlay.verify();
        this.extension.afterPlay.verify();
    }

    protected createGame(initResponses: GameInitResponse[],
                         contexts: any[] = [],
                         payments: PaymentInfo[] = [],
                         histories: GameHistory[] = [],
                         playResponses: GamePlayResponse[] = [],
                         info?: () => GameInfo): SomeGame {
        return new TestGameWithMarkUpdate([this.extension],
            initResponses,
            contexts,
            payments,
            histories,
            playResponses,
            info);
    }
}
