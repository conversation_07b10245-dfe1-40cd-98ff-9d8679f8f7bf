import { expect, should, use } from "chai";
import { getSyncGameController } from "../../../skywind/services/synccontroller";
import { getService as getPlayerGameInfoService } from "../../../skywind/services/playerGameInfo";
import { suite, test } from "mocha-typescript";
import { BaseGameControllerSpec } from "./gameController.base.spec";
import { stub, SinonStub } from "sinon";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("Game controller: getGames")
export class GameControllerGetGamesSpec extends BaseGameControllerSpec {
    public getGamesStub: SinonStub;

    public async after() {
        this.getGamesStub.restore();
        return super.after();
    }

    public async before(): Promise<any> {
        this.getGamesStub = stub(getPlayerGameInfoService(), "getAvailableGames");
        return super.before();
    }

    @test("processes 'getGames' request")
    public async processGetGamesRequest() {
        const gamesList = {
            "games": ["test"]
        };
        this.getGamesStub.returns(gamesList);

        const result = await getSyncGameController().process({
            request: "get-games",
            startGameToken: BaseGameControllerSpec.startToken,
            requestId: 0
        });

        expect(result).to.deep.equal(gamesList);
    }
}
