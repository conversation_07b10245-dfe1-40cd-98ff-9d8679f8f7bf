import { expect, should, use } from "chai";
import { getSyncGameController } from "../../../skywind/services/synccontroller";
import { suite, test } from "mocha-typescript";
import { BaseGameControllerSpec } from "./gameController.base.spec";
import { TestGame, TestGameWithMarkUpdate } from "../../testGames";
import {
    ClientResponse,
    GameHistory,
    GameInfo,
    GameInitResponse,
    GamePlayResponse,
    PaymentInfo,
    SomeGame
} from "@skywind-group/sw-game-core";
import { mock } from "sinon";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("Game controller: stateInfo")
export class GameControllerStateInfoSpec extends BaseGameControllerSpec {
    public extension: any = {
        beforeInit: mock(),
        afterInit: mock(),
        beforeStateInfo: mock(),
        afterStateInfo: mock(),
    };
    public initResponse: ClientResponse;

    public async before() {
        await super.before();
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        this.stubGame();

        this.initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(this.initResponse).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1000,
                },
            },
            "gameSession": this.initResponse.gameSession,
            "gameSettings": undefined,
            "brandSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": {
                "gameId": "test_slot",
                "name": "GAME!",
                "request": "init",
                "settings": {
                    "coins": [1],
                    "defaultCoin": 1,
                    "maxTotalStake": 500,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 10,
                    "stakeMin": 0.1,
                    "winMax": 3000000,
                    "currencyMultiplier": 100,
                },
                "slot": {
                    "stub": "FORTESTREASON",
                },
                "previousResult": null,
                "stake": null
            },
            "roundEnded": true,
            renderType: 0,
            brandInfo: undefined,
        });

        expect(BaseGameControllerSpec.startGame.args[0][0]).equal(BaseGameControllerSpec.gameData.gameId);
        expect(BaseGameControllerSpec.startGame.args[0][1]).equal(BaseGameControllerSpec.startToken);
    }

    public async after() {
        this.extension.beforeStateInfo.reset();
        this.extension.afterStateInfo.reset();
        return super.after();
    }

    @test("processes 'stateInfo' request")
    public async processGameInitRequest() {
        this.extension.beforeStateInfo.once().returns(Promise.resolve());
        this.extension.afterStateInfo.once().returns(Promise.resolve());

        const stateResponse1 = await getSyncGameController().process({
            request: "state",
            requestId: 1,
            gameSession: this.initResponse.gameSession,
            getBalance: true
        });

        expect(stateResponse1).deep.equals({
            gameSession: this.initResponse.gameSession,
            requestId: 1,
            balance:
                {
                    currency: "USD",
                    amount: 1000,
                    real: { amount: 1000 },
                    bonus: { amount: 0 }
                },
            extraData: undefined,
            result: { request: "state", data: "SOME GAME INFO" },
            roundEnded: true
        });

        this.extension.beforeStateInfo.verify();
        this.extension.afterStateInfo.verify();

        expect(this.extension.afterStateInfo.args[0][2]).deep.equals(stateResponse1.result);
    }

    @test("processes 'stateInfo' request without balance")
    public async processGameInitRequestWithoutBalance() {
        this.extension.beforeStateInfo.once().returns(Promise.resolve());
        this.extension.afterStateInfo.once().returns(Promise.resolve());

        const stateResponse = await getSyncGameController().process({
            request: "state",
            requestId: 1,
            gameSession: this.initResponse.gameSession,
        });

        expect(stateResponse).deep.equals({
            gameSession: this.initResponse.gameSession,
            balance: undefined,
            requestId: 1,
            extraData: undefined,
            result: { request: "state", data: "SOME GAME INFO" },
            roundEnded: true
        });

        this.extension.beforeStateInfo.verify();
        this.extension.afterStateInfo.verify();

        expect(this.extension.afterStateInfo.args[0][2]).deep.equals(stateResponse.result);
    }

    protected createGame(initResponses: GameInitResponse[],
                         contexts: any[] = [],
                         payments: PaymentInfo[] = [],
                         histories: GameHistory[] = [],
                         playResponses: GamePlayResponse[] = [],
                         info?: () => GameInfo): SomeGame {
        return new TestGameWithMarkUpdate([this.extension],
            initResponses,
            contexts,
            payments,
            histories,
            playResponses,
            info);
    }
}

@suite("Game controller: stateInfo withoud game info")
export class GameControllerStateInfoWithoudGameInfoSpec extends GameControllerStateInfoSpec {

    @test("processes 'stateInfo' request")
    public async processGameInitRequestWithoutGameInfo() {
        this.extension.beforeStateInfo.never();
        this.extension.afterStateInfo.never();

        const stateResponse1 = await getSyncGameController().process({
            request: "state",
            requestId: 1,
            gameSession: this.initResponse.gameSession,
            getBalance: true
        });

        expect(stateResponse1).deep.equals({
            gameSession: this.initResponse.gameSession,
            requestId: 1,
            balance:
                {
                    currency: "USD",
                    amount: 1000,
                    real: { amount: 1000 },
                    bonus: { amount: 0 }
                },
            extraData: undefined,
            result: { request: "state" },
            roundEnded: true
        });

        this.extension.beforeStateInfo.verify();
        this.extension.afterStateInfo.verify();
    }

    @test("processes 'stateInfo' request without balance")
    public async processGameInitRequestWithoutBalanceAndGameInfo() {
        this.extension.beforeStateInfo.never();
        this.extension.afterStateInfo.never();

        const stateResponse = await getSyncGameController().process({
            request: "state",
            requestId: 1,
            gameSession: this.initResponse.gameSession,
        });

        expect(stateResponse).deep.equals({
            gameSession: this.initResponse.gameSession,
            balance: undefined,
            requestId: 1,
            result: { request: "state" },
            roundEnded: true,
            extraData: undefined,
        });

        this.extension.beforeStateInfo.verify();
        this.extension.afterStateInfo.verify();
    }

    protected createGame(initResponses: GameInitResponse[],
                         contexts: any[] = [],
                         payments: PaymentInfo[] = [],
                         histories: GameHistory[] = [],
                         playResponses: GamePlayResponse[] = [],
                         info?: () => GameInfo): SomeGame {
        return new TestGame(
            initResponses,
            contexts,
            payments,
            histories,
            playResponses,
            info);
    }

}
