import { expect, should, use } from "chai";
import * as _ from "lodash";
import {
    createGameToken,
    flushAll,
    getGameHistoryItem,
    getRoundHistory,
    syncModels,
    TEST_MODULE_NAME,
    TestLoadResult
} from "../../helper";
import {
    GameHistory,
    GameInfo,
    GameInitResponse,
    GamePlayResponse,
    PaymentInfo,
    SomeGame
} from "@skywind-group/sw-game-core";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { GameTokenData, generateStartGameToken, Limits } from "../../../skywind/services/tokens";
import { GameContextID } from "../../../skywind/services/contextIds";
import { getSyncGameController } from "../../../skywind/services/synccontroller";
import { GameSession } from "../../../skywind/services/gameSession";
import { suite, test } from "mocha-typescript";
import { GameData, getService as getAuthservice, StartGameResult } from "../../../skywind/services/auth";
import { getGameContextModel } from "../../../skywind/services/offlinestorage/models";
import config from "../../../skywind/config";
import { SinonStub, stub } from "sinon";
import { TestGame } from "../../testGames";
import { APIPlayerWalletManager } from "../../../skywind/services/wallet";
import * as GameService from "../../../skywind/services/game/game";
import { setRandomGeneratorFactory } from "../../..";
import { RandomGeneratorFactoryImpl } from "../../testRandomFactory";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("Game controller in 'play_money' mode")
export class GameControllerPlayMoneySpec {
    public static startGame: SinonStub;
    public static generateTransactionId: SinonStub;
    public static getBalance: SinonStub;
    public static commitOperation: SinonStub;
    public static loadGame: SinonStub;

    public static playMoneyStartTokenData = {
        playerCode: "PL001",
        gameCode: "test_slot",
        providerGameCode: "test_slot",
        brandId: 1,
        playmode: "play_money"
    };

    public static playMoneyGameTokenData: GameTokenData = {
        playerCode: "PL001",
        gameCode: "test_slot",
        brandId: 1,
        currency: "XXX",
        playmode: "play_money"
    };

    public static gameData: GameData = {
        gameTokenData: GameControllerPlayMoneySpec.playMoneyGameTokenData,
        jrsdSettings: { a: 1 },
        jurisdictionCode: "GB",
        playedFromCountry: "GB",
        limits: {
            coins: [1],
            defaultCoin: 1,
            maxTotalStake: 500,
            stakeAll: [0.1, 0.5, 1, 2, 3, 5],
            stakeDef: 1,
            stakeMax: 10,
            stakeMin: 0.1,
            winMax: 3000000,
            currencyMultiplier: 100,
        } as Limits,
        gameId: "test_slot",
    } as GameData;

    public static startGameResult = {
        gameData: GameControllerPlayMoneySpec.gameData,
        clientSettings: {}
    } as StartGameResult;

    public static startToken: string;
    public static walletThroughAPIPrevValue: boolean;

    public static async before() {
        await syncModels();
        GameControllerPlayMoneySpec.walletThroughAPIPrevValue = config.walletThroughAPI;
        config.walletThroughAPI = true;
        GameControllerPlayMoneySpec.startGame = stub(getAuthservice("play_money"), "startGame");
        GameControllerPlayMoneySpec.generateTransactionId = stub(APIPlayerWalletManager.prototype,
            "generateTransactionId");
        GameControllerPlayMoneySpec.getBalance = stub(APIPlayerWalletManager.prototype, "getBalance");
        GameControllerPlayMoneySpec.commitOperation = stub(APIPlayerWalletManager.prototype, "commitOperation");
        GameControllerPlayMoneySpec.loadGame = stub(GameService, "load");
        GameControllerPlayMoneySpec.startToken = await generateStartGameToken(
            GameControllerPlayMoneySpec.playMoneyStartTokenData);
        GameControllerPlayMoneySpec.playMoneyGameTokenData.token =
            await createGameToken(GameControllerPlayMoneySpec.playMoneyGameTokenData);
        setRandomGeneratorFactory(new RandomGeneratorFactoryImpl());
    }

    public static async after() {
        GameControllerPlayMoneySpec.loadGame.restore();
        GameControllerPlayMoneySpec.startGame.restore();
        GameControllerPlayMoneySpec.startGame.restore();
        GameControllerPlayMoneySpec.generateTransactionId.restore();
        GameControllerPlayMoneySpec.getBalance.restore();
        GameControllerPlayMoneySpec.commitOperation.restore();
        config.walletThroughAPI = GameControllerPlayMoneySpec.walletThroughAPIPrevValue;
    }

    public async before(): Promise<any> {
        await getGameContextModel().truncate();
        await flushAll();
    }

    public async after() {
        GameControllerPlayMoneySpec.startGame.reset();
        GameControllerPlayMoneySpec.startGame.reset();
        GameControllerPlayMoneySpec.generateTransactionId.reset();
        GameControllerPlayMoneySpec.getBalance.reset();
        GameControllerPlayMoneySpec.commitOperation.reset();
        GameControllerPlayMoneySpec.loadGame.reset();
    }

    @test("processes two sequential 'spin' request")
    public async processTwoSequelntialSpins() {
        GameControllerPlayMoneySpec.startGame.returns(Promise.resolve(GameControllerPlayMoneySpec.startGameResult));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "currencyMultiplier": 100,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        ));
        GameControllerPlayMoneySpec.getBalance.returns(Promise.resolve({
            currency: "XXX",
            main: 1000
        }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: GameControllerPlayMoneySpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId", "play_money"));

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": GameControllerPlayMoneySpec.gameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:play_money:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000
            },
        });

        /**
         * First spin
         */

        GameControllerPlayMoneySpec.generateTransactionId.returns(Promise.resolve("TEST_TRX_ID"));
        GameControllerPlayMoneySpec.commitOperation.returns(Promise.resolve({
            currency: "XXX",
            main: 1004,
            previousValue: 1000
        }));

        const spinResponse = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        });

        expect(spinResponse).deep.equal({
            "balance": {
                "amount": 1004,
                "bonus": {
                    "amount": 0,
                },
                "currency": "XXX",
                "real": {
                    "amount": 1004,
                },
            },
            "extraData": undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 1,
            "result": {
                "request": "spin",
                "roundEnded": false,
                "totalBet": 1,
                "totalWin": 5,
            },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        GameControllerPlayMoneySpec.generateTransactionId.resetBehavior();
        GameControllerPlayMoneySpec.commitOperation.resetBehavior();
        GameControllerPlayMoneySpec.generateTransactionId.returns(Promise.resolve("TEST_TRX_ID2"));
        GameControllerPlayMoneySpec.commitOperation.returns(Promise.resolve({
            currency: "XXX",
            main: 1000,
            previousValue: 1004
        }));

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId", "play_money");
        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:play_money:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "reactive": true,
            "logoutId": undefined,
            "gameData": GameControllerPlayMoneySpec.gameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                currentBet: 1,
                betsCount: 1
            },
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
            "specialState": undefined,
            "version": 4,
        });

        /**
         *  Second spin
         */

        const spinResponse2 = await getSyncGameController().process({
            request: "spin",
            requestId: 2,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 100,
            coin: 33,
        });

        expect(spinResponse2).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "XXX",
                "real": {
                    "amount": 1000,
                }
            },
            "extraData": undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 2,
            "result": {
                "request": "spin",
                "roundEnded": true,
                "totalBet": 5,
                "totalWin": 1,
            },
            "roundEnded": true,
            "roundTotalBet": 6,
            "roundTotalWin": 6
        });

        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 2,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:play_money:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "gameSerialNumber": 0,
            "totalEventId": 2,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "reactive": true,
            "logoutId": undefined,
            "gameData": GameControllerPlayMoneySpec.gameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "roundId": "1",
            "gameContext": {
                "currentScene": "scene3",
                "nextScene": "scene4",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
            "specialState": undefined,
            "version": 6,
        });

        const spin2 = await getGameHistoryItem();
        expect(spin2).is.undefined;

        const spin1 = await getGameHistoryItem();
        expect(spin1).is.undefined;

        const items = await getRoundHistory(0, 1000);

        expect(items).deep.equal([]);
    }

    protected stubGame(game?: SomeGame) {
        GameControllerPlayMoneySpec.loadGame.returns(Promise.resolve(new TestLoadResult(game || this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "defaultScene",
                    "nextScene": "nextScene",
                }
            ]))));
    }

    protected createGame(initResponses: GameInitResponse[],
                         contexts: any[] = [],
                         payments: PaymentInfo[] = [],
                         histories: GameHistory[] = [],
                         playResponses: GamePlayResponse[] = [],
                         info?: () => GameInfo): SomeGame {
        return new TestGame(initResponses, contexts, payments, histories, playResponses, info);
    }
}
