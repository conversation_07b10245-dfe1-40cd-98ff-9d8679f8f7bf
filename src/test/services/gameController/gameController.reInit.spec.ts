import { expect, should, use } from "chai";
import * as _ from "lodash";
import { TEST_MODULE_NAME } from "../../helper";
import {
    GameHistory,
    GameInfo,
    GameInitResponse,
    GamePlayResponse,
    PaymentInfo,
    PushService,
    SomeGame
} from "@skywind-group/sw-game-core";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { GameContextID } from "../../../skywind/services/contextIds";
import { GameSession } from "../../../skywind/services/gameSession";
import { suite, test } from "mocha-typescript";
import { BaseGameControllerSpec } from "./gameController.base.spec";
import { createAsyncGameController } from "../../../skywind/services/asynccontroller";
import { mock } from "sinon";
import { AsyncTestGame } from "../../testGames";
import { generateStartGameToken, verifySessionToken } from "../../../skywind/services/tokens";
import { GameReInitError, GameSessionClosed } from "../../../skywind/errors";
import { ReInitClientResponse } from "../../../skywind/services/requests";
import config from "../../../skywind/config";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

const defaultAmount = config.funGame.defaultFunGameStartAmount;

@suite("Game controller: reinit")
export class GameControllerReInitSpec extends BaseGameControllerSpec {

    private readonly pushService: PushService = {
        notifyPlayer: mock(),
        notifyError: mock(),
        scheduleInternalEvent: mock()
    };

    @test("processes two sequential 'spin' request, 'reinit' and  a new 'spin'")
    public async processReInit() {
        const controller = createAsyncGameController(this.pushService);
        BaseGameControllerSpec.startGame.returns(
            Promise.resolve(BaseGameControllerSpec.startGameResultWithLimitsAndPlayerInfo)
        );
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                },
                {
                    "currentScene": "scene4",
                    "nextScene": "scene5",
                },
                {
                    "currentScene": "scene5",
                    "nextScene": "scene6",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
                { request: "spin", totalBet: 3, totalWin: 4, roundEnded: true },
            ]
        ));

        const initResponse = await controller.process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });
        expect(initResponse.clientSettings.nickname).to.equal("testNickname");

        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": BaseGameControllerSpec.gameDataWithLimitsAndPlayerInfo,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2",
            },
            "jpContext": undefined,
            "version": 2,
            "specialState": undefined,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
        });

        /**
         * First spin
         */
        const spinResponse = await controller.process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        });

        expect(spinResponse).deep.equal({
            "balance": {
                "amount": 1004,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1004,
                }
            },
            extraData: undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 1,
            "result": {
                "request": "spin",
                "roundEnded": false,
                "totalBet": 1,
                "totalWin": 5,
            },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId");
        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": BaseGameControllerSpec.gameDataWithLimitsAndPlayerInfo,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                currentBet: 1,
                betsCount: 1
            },
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 4,
        });

        /**
         *  Second spin
         */

        const spinResponse2 = await controller.process({
            request: "spin",
            requestId: 2,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 100,
            coin: 33,
        });

        expect(spinResponse2).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1000,
                }
            },
            extraData: undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 2,
            "result": {
                "request": "spin",
                "roundEnded": true,
                "totalBet": 5,
                "totalWin": 1,
            },
            "roundEnded": true,
            "roundTotalBet": 6,
            "roundTotalWin": 6
        });

        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 2,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 2,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": BaseGameControllerSpec.gameDataWithLimitsAndPlayerInfo,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "roundId": "1",
            "gameContext": {
                "currentScene": "scene3",
                "nextScene": "scene4",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 6,
        });

        const reInitResponse: ReInitClientResponse = await controller.reInit({
            request: "reinit",
            deviceId: "deviceId",
            gameId: "test_slot",
            startGameToken: BaseGameControllerSpec.startToken,
            gameSession: initResponse.gameSession
        });
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 2,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 2,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": BaseGameControllerSpec.gameDataWithLimitsAndPlayerInfo,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "roundId": "1",
            "gameContext": {
                "currentScene": "scene3",
                "nextScene": "scene4",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 6,
        });
        expect(reInitResponse.lastRequestId).eq(2);

        const oldSession = await verifySessionToken(initResponse.gameSession);
        const newSession = await verifySessionToken(reInitResponse.gameSession);
        expect(oldSession.id).deep.equals(newSession.id);
        expect(oldSession.sessionId).deep.equals(newSession.sessionId);

        const spinResponse3 = await controller.process({
            request: "spin",
            requestId: 3,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 100,
            coin: 33,
        });

        expect(spinResponse3).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1000,
                }
            },
            extraData: undefined,
            "gameSession": reInitResponse.gameSession,
            "requestId": 3,
            "result": {
                "request": "spin",
                "roundEnded": true,
                "totalBet": 3,
                "totalWin": 4,
            },
            "roundEnded": true,
            "roundTotalBet": 3,
            "roundTotalWin": 4
        });

        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 3,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 2,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": BaseGameControllerSpec.gameDataWithLimitsAndPlayerInfo,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "roundId": "1",
            "gameContext": {
                "currentScene": "scene5",
                "nextScene": "scene6",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 8,
        });
    }

    @test("reInit failed - wrong gameId")
    public async processReInitBadGameId() {
        const controller = createAsyncGameController(this.pushService);
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                },
                {
                    "currentScene": "scene4",
                    "nextScene": "scene5",
                },
                {
                    "currentScene": "scene5",
                    "nextScene": "scene6",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
                { request: "spin", totalBet: 3, totalWin: 4, roundEnded: true },
            ]
        ));

        const initResponse = await controller.process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        await expect(controller.reInit({
            request: "reinit",
            deviceId: "deviceId",
            gameId: "test_slot_wrong",
            startGameToken: BaseGameControllerSpec.startToken,
            gameSession: initResponse.gameSession
        })).rejectedWith(GameReInitError);

        const context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": BaseGameControllerSpec.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2",
            },
            "jpContext": undefined,
            "version": 2,
            "specialState": undefined,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
        });
    }

    @test("reInit failed - session closed")
    public async processReInitSessionClosed() {
        const controller = createAsyncGameController(this.pushService);
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                },
                {
                    "currentScene": "scene4",
                    "nextScene": "scene5",
                },
                {
                    "currentScene": "scene5",
                    "nextScene": "scene6",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
                { request: "spin", totalBet: 3, totalWin: 4, roundEnded: true },
            ]
        ));

        const initResponse = await controller.process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });
        const context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        await context.remove(true);

        await expect(controller.reInit({
            request: "reinit",
            deviceId: "deviceId",
            gameId: "test_slot_wrong",
            startGameToken: BaseGameControllerSpec.startToken,
            gameSession: initResponse.gameSession
        })).rejectedWith(GameSessionClosed);
    }

    @test("reInit failed - wrong deviceId")
    public async processReInitBadDeviceId() {
        const controller = createAsyncGameController(this.pushService);
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                },
                {
                    "currentScene": "scene4",
                    "nextScene": "scene5",
                },
                {
                    "currentScene": "scene5",
                    "nextScene": "scene6",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
                { request: "spin", totalBet: 3, totalWin: 4, roundEnded: true },
            ]
        ));

        const initResponse = await controller.process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        await expect(controller.reInit({
            request: "reinit",
            deviceId: "deviceId_wrong",
            gameId: "test_slot",
            startGameToken: BaseGameControllerSpec.startToken,
            gameSession: initResponse.gameSession
        })).rejectedWith(GameReInitError);

        const context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": BaseGameControllerSpec.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2",
            },
            "jpContext": undefined,
            "version": 2,
            "specialState": undefined,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
        });
    }

    @test("reInit failed - wrong sessionId")
    public async processReInitBadSessionId() {
        const controller = createAsyncGameController(this.pushService);
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                },
                {
                    "currentScene": "scene4",
                    "nextScene": "scene5",
                },
                {
                    "currentScene": "scene5",
                    "nextScene": "scene6",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
                { request: "spin", totalBet: 3, totalWin: 4, roundEnded: true },
            ]
        ));

        const initResponse1 = await controller.process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });
        const initResponse2 = await controller.process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        await expect(controller.reInit({
            request: "reinit",
            deviceId: "deviceId",
            gameId: "test_slot",
            startGameToken: BaseGameControllerSpec.startToken,
            gameSession: initResponse1.gameSession
        })).rejectedWith(GameReInitError);

        const context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse2.gameSession),
            "gameData": BaseGameControllerSpec.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "version": 4,
            "specialState": undefined,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
        });
    }

    @test("reInit failed - wrong startGameToken")
    public async processReInitBadStartGameToken() {
        const controller = createAsyncGameController(this.pushService);
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                },
                {
                    "currentScene": "scene4",
                    "nextScene": "scene5",
                },
                {
                    "currentScene": "scene5",
                    "nextScene": "scene6",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
                { request: "spin", totalBet: 3, totalWin: 4, roundEnded: true },
            ]
        ));

        const initResponse = await controller.process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        await expect(controller.reInit({
            request: "reinit",
            deviceId: "deviceId",
            gameId: "test_slot",
            startGameToken: await generateStartGameToken({
                ...BaseGameControllerSpec.startTokenData,
                brandId: 777
            } as any),
            gameSession: initResponse.gameSession
        })).rejectedWith(GameReInitError);

        await expect(controller.reInit({
            request: "reinit",
            deviceId: "deviceId",
            gameId: "test_slot",
            startGameToken: await generateStartGameToken({
                ...BaseGameControllerSpec.startTokenData,
                playerCode: "wrong_player"
            } as any),
            gameSession: initResponse.gameSession
        })).rejectedWith(GameReInitError);

        await expect(controller.reInit({
            request: "reinit",
            deviceId: "deviceId",
            gameId: "test_slot",
            startGameToken: await generateStartGameToken({
                ...BaseGameControllerSpec.startTokenData,
                gameCode: "wrong_game_code"
            } as any),
            gameSession: initResponse.gameSession
        })).rejectedWith(GameReInitError);

        const context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": BaseGameControllerSpec.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2",
            },
            "jpContext": undefined,
            "version": 2,
            "specialState": undefined,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
        });
    }

    @test("fun mode - processes two sequential 'spin' request, 'reinit' and  a new 'spin'")
    public async processReInitForFun() {
        const controller = createAsyncGameController(this.pushService);
        BaseGameControllerSpec.startGame.returns(Promise.resolve(BaseGameControllerSpec.startGameResultWithLimits));
        const bet1 = 2;
        const win1 = 6;
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                },
                {
                    "currentScene": "scene4",
                    "nextScene": "scene5",
                },
                {
                    "currentScene": "scene5",
                    "nextScene": "scene6",
                }
            ], [
                { bet: bet1, win: win1 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
                { request: "spin", totalBet: 3, totalWin: 4, roundEnded: true },
            ]
        ));

        BaseGameControllerSpec.funStartGame.returns(BaseGameControllerSpec.funStartGameResult);
        const initResponse = await controller.process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.funStartGameToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        let context = await getGameFlowContextManager("fun")
            .findGameContextById(GameContextID.create("for_unit_tests", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext", "settings")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": BaseGameControllerSpec.funGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:for_unit_tests:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2",
            },
            "jpContext": undefined,
            "version": 2,
            "specialState": undefined,
            "roundId": "0"
        });

        /**
         * First spin
         */
        const spinResponse = await controller.process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        });

        expect(spinResponse).deep.equal({
            "balance": {
                "amount": defaultAmount + win1 - bet1,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": defaultAmount + win1 - bet1,
                }
            },
            extraData: undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 1,
            "result": {
                "request": "spin",
                "roundEnded": false,
                "totalBet": 1,
                "totalWin": 5,
            },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        const gameID = GameContextID.create("for_unit_tests", 1, "PL001", "deviceId");
        context = await getGameFlowContextManager("fun").findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext", "settings")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:context:1:PL001:for_unit_tests:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": BaseGameControllerSpec.funGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": defaultAmount,
                "balanceAfter": defaultAmount + win1 - bet1,
                "startedAt": context.round.startedAt,
                "totalBet": bet1,
                "totalEvents": 1,
                "totalWin": win1,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                currentBet: bet1,
                betsCount: 1
            },
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 4,
        });

        /**
         *  Second spin
         */

        const spinResponse2 = await controller.process({
            request: "spin",
            requestId: 2,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 100,
            coin: 33,
        });

        expect(spinResponse2).deep.equal({
            "balance": {
                "amount": defaultAmount,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": defaultAmount,
                }
            },
            extraData: undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 2,
            "result": {
                "request": "spin",
                "roundEnded": true,
                "totalBet": 5,
                "totalWin": 1,
            },
            "roundEnded": true,
            "roundTotalBet": 7,
            "roundTotalWin": 7
        });

        context = await getGameFlowContextManager("fun").findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext", "settings")).deep.equal({
            "lastRequestId": 2,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:context:1:PL001:for_unit_tests:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 2,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": BaseGameControllerSpec.funGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "roundId": "1",
            "gameContext": {
                "currentScene": "scene3",
                "nextScene": "scene4",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 6,
        });

        const reInitResponse: ReInitClientResponse = await controller.reInit({
            request: "reinit",
            deviceId: "deviceId",
            gameId: "test_slot",
            startGameToken: BaseGameControllerSpec.funStartGameToken,
            gameSession: initResponse.gameSession
        });
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext", "settings")).deep.equal({
            "lastRequestId": 2,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:context:1:PL001:for_unit_tests:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 2,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": BaseGameControllerSpec.funGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "roundId": "1",
            "gameContext": {
                "currentScene": "scene3",
                "nextScene": "scene4",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 6,
        });
        expect(reInitResponse.lastRequestId).eq(2);

        const oldSession = await verifySessionToken(initResponse.gameSession);
        const newSession = await verifySessionToken(reInitResponse.gameSession);
        expect(oldSession.id).deep.equals(newSession.id);
        expect(oldSession.sessionId).deep.equals(newSession.sessionId);

        const spinResponse3 = await controller.process({
            request: "spin",
            requestId: 3,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 100,
            coin: 33,
        });

        expect(spinResponse3).deep.equal({
            "balance": {
                "amount": defaultAmount,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": defaultAmount,
                }
            },
            extraData: undefined,
            "gameSession": reInitResponse.gameSession,
            "requestId": 3,
            "result": {
                "request": "spin",
                "roundEnded": true,
                "totalBet": 3,
                "totalWin": 4,
            },
            "roundEnded": true,
            "roundTotalBet": 3,
            "roundTotalWin": 4
        });

        context = await getGameFlowContextManager("fun").findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext", "settings")).deep.equal({
            "lastRequestId": 3,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:context:1:PL001:for_unit_tests:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 2,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": BaseGameControllerSpec.funGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "roundId": "1",
            "gameContext": {
                "currentScene": "scene5",
                "nextScene": "scene6",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 8,
        });
    }

    protected createGame(initResponses: GameInitResponse[],
                         contexts: any[] = [],
                         payments: PaymentInfo[] = [],
                         histories: GameHistory[] = [],
                         playResponses: GamePlayResponse[] = [],
                         info?: () => GameInfo): SomeGame {
        return new AsyncTestGame(initResponses, contexts, [], payments, histories, playResponses, 1);
    }
}
