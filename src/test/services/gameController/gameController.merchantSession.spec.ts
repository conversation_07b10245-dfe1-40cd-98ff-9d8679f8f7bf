import { expect, should, use } from "chai";
import * as _ from "lodash";
import { TEST_MODULE_NAME } from "../../helper";
import { GameInitResponse } from "@skywind-group/sw-game-core";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { GameContextID } from "../../../skywind/services/contextIds";
import { getSyncGameController } from "../../../skywind/services/synccontroller";
import { GameSession } from "../../../skywind/services/gameSession";
import { suite, test } from "mocha-typescript";
import { BaseGameControllerSpec } from "./gameController.base.spec";
import { generateStartGameToken } from "../../../skywind/services/tokens";
import { deepClone } from "../../../skywind/utils/cloner";
import { CannotStartNewMerchantPlayerSessionError } from "../../../skywind/errors";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("Game controller. Check merchant session")
export class GameControllerSpec extends BaseGameControllerSpec {

    @test()
    public async restoreStateOfBrokenGame() {
        const startGameTokenData = {
            ...BaseGameControllerSpec.startTokenData,
            merchantSessionId: "session_1",
            merchantSessionStateful: true
        };
        const startGameToken = await generateStartGameToken(startGameTokenData);
        const gameData = deepClone(BaseGameControllerSpec.gameDataWithLimits);
        const startGameResult = deepClone(BaseGameControllerSpec.startGameResultWithLimits);
        startGameResult.gameData.gameTokenData.merchantSessionId = "session_1";
        gameData.gameTokenData.merchantSessionId = "session_1";
        BaseGameControllerSpec.startGame.returns(startGameResult);
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                },
                {
                    "currentScene": "scene4",
                    "nextScene": "scene5",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        ));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: startGameToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        const spinResponse = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        });

        expect(spinResponse).deep.equal({
            "balance": {
                "amount": 1004,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1004,
                }
            },
            "extraData": undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 1,
            "result": { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId");
        let context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": gameID,
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                currentBet: 1,
                betsCount: 1
            },
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "gameData": gameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 4,
        });

        /**
         *  Restart game and make second spin
         */

        const initResponse2 = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse2.gameSession),
            "id": gameID,
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                betsCount: 1,
                currentBet: 1
            },
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene3",
                "nextScene": "scene4",
            },
            "jpContext": undefined,
            "gameData": gameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 6,
        });

        const spinResponse2 = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse2.gameSession,
            lines: 100,
            bet: 5,
        });

        expect(spinResponse2).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1000,
                }
            },
            "extraData": undefined,
            "gameSession": initResponse2.gameSession,
            "requestId": 1,
            "result": { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            "roundEnded": true,
            "roundTotalBet": 6,
            "roundTotalWin": 6
        });

        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, ["createdAt", "updatedAt", "requestContext"])).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse2.gameSession),
            "id": gameID,
            "lockExclusively": false,
            "reactive": true,
            "gameVersion": TEST_MODULE_NAME.version,
            "gameSerialNumber": 0,
            "totalEventId": 2,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundId": "1",
            "gameContext": {
                "currentScene": "scene4",
                "nextScene": "scene5",
            },
            "jpContext": undefined,
            "gameData": gameData,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 8,
        });
    }

    @test
    public async restoreStateOfBrokenGame_NewSession_Failed() {
        let startGameTokenData = {
            ...BaseGameControllerSpec.startTokenData,
            merchantSessionId: "session_1",
            merchantSessionStateful: true
        };
        let startGameToken = await generateStartGameToken(startGameTokenData);
        const gameData = deepClone(BaseGameControllerSpec.gameDataWithLimits);
        const startGameResult = deepClone(BaseGameControllerSpec.startGameResultWithLimits);
        startGameResult.gameData.gameTokenData.merchantSessionId = "session_1";
        gameData.gameTokenData.merchantSessionId = "session_1";
        BaseGameControllerSpec.startGame.returns(startGameResult);
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                },
                {
                    "currentScene": "scene4",
                    "nextScene": "scene5",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        ));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: startGameToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        const spinResponse = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        });

        expect(spinResponse).deep.equal({
            "balance": {
                "amount": 1004,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1004,
                }
            },
            "extraData": undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 1,
            "result": { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId");
        let context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": gameID,
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                currentBet: 1,
                betsCount: 1
            },
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "gameData": gameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 4,
        });

        /**
         *  Restart game and make second spin
         */

        startGameTokenData = {
            ...BaseGameControllerSpec.startTokenData,
            merchantSessionId: "session_2",
            merchantSessionStateful: true
        };
        startGameToken = await generateStartGameToken(startGameTokenData);

        await expect(getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: startGameToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        })).to.be.rejectedWith(CannotStartNewMerchantPlayerSessionError);

        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": gameID,
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                betsCount: 1,
                currentBet: 1
            },
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "gameData": gameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 4,
        });
    }

    @test
    public async restoreStateOfBrokenGame_NewSessionNotStateful() {
        let startGameTokenData = {
            ...BaseGameControllerSpec.startTokenData,
            merchantSessionId: "session_1",
            merchantSessionStateful: false
        };
        let startGameToken = await generateStartGameToken(startGameTokenData);
        const gameData = deepClone(BaseGameControllerSpec.gameDataWithLimits);
        const startGameResult = deepClone(BaseGameControllerSpec.startGameResultWithLimits);
        startGameResult.gameData.gameTokenData.merchantSessionId = "session_1";
        gameData.gameTokenData.merchantSessionId = "session_1";
        BaseGameControllerSpec.startGame.returns(startGameResult);
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                },
                {
                    "currentScene": "scene4",
                    "nextScene": "scene5",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        ));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: startGameToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        const spinResponse = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        });

        expect(spinResponse).deep.equal({
            "balance": {
                "amount": 1004,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1004,
                }
            },
            "extraData": undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 1,
            "result": { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId");
        let context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": gameID,
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                currentBet: 1,
                betsCount: 1
            },
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "gameData": gameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 4,
        });

        /**
         *  Restart game and make second spin
         */

        startGameTokenData = {
            ...BaseGameControllerSpec.startTokenData,
            merchantSessionId: "session_2",
            merchantSessionStateful: false
        };
        startGameToken = await generateStartGameToken(startGameTokenData);

        const initResponse2 = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: startGameToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse2.gameSession),
            "id": gameID,
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                betsCount: 1,
                currentBet: 1
            },
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene3",
                "nextScene": "scene4",
            },
            "jpContext": undefined,
            "gameData": gameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 6,
        });

        const spinResponse2 = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse2.gameSession,
            lines: 100,
            bet: 5,
        });

        expect(spinResponse2).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1000,
                }
            },
            "extraData": undefined,
            "gameSession": initResponse2.gameSession,
            "requestId": 1,
            "result": { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            "roundEnded": true,
            "roundTotalBet": 6,
            "roundTotalWin": 6
        });

        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, ["createdAt", "updatedAt", "requestContext"])).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse2.gameSession),
            "id": gameID,
            "lockExclusively": false,
            "reactive": true,
            "gameVersion": TEST_MODULE_NAME.version,
            "gameSerialNumber": 0,
            "totalEventId": 2,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundId": "1",
            "gameContext": {
                "currentScene": "scene4",
                "nextScene": "scene5",
            },
            "jpContext": undefined,
            "gameData": gameData,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 8,
        });
    }

}
