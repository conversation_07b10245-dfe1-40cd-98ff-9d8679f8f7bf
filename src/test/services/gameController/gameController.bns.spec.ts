import { expect, should, use } from "chai";
import * as _ from "lodash";
import {
    createGameToken,
    flushAll,
    getGameHistoryItem,
    getRoundHistory,
    syncModels,
    TEST_MODULE_NAME,
    TestLoadResult
} from "../../helper";
import {
    GameHistory,
    GameInfo,
    GameInitResponse,
    GamePlayResponse,
    PaymentInfo,
    SomeGame
} from "@skywind-group/sw-game-core";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { GameTokenData, generateStartGameToken, Limits } from "../../../skywind/services/tokens";
import { GameContextID } from "../../../skywind/services/contextIds";
import { getSyncGameController } from "../../../skywind/services/synccontroller";
import { GameSession } from "../../../skywind/services/gameSession";
import { suite, test } from "mocha-typescript";
import { BNSGameData, GameData, StartGameResult, getService as getAuthservice } from "../../../skywind/services/auth";
import { getGameContextModel } from "../../../skywind/services/offlinestorage/models";
import config from "../../../skywind/config";
import { SinonStub, stub } from "sinon";
import { TestGame } from "../../testGames";
import { APIPlayerWalletManager } from "../../../skywind/services/wallet";
import * as GameService from "../../../skywind/services/game/game";
import { deepClone } from "../../../skywind/utils/cloner";
import { ManagementAPIError, ManualRedeemBNSError, RedeemBNSError } from "../../../skywind/errors";
import { CleanupGameContextService } from "../../../skywind/services/cleanup/cleanupGameContextService";
import { setRandomGeneratorFactory } from "../../../index";
import { RandomGeneratorFactoryImpl } from "../../testRandomFactory";
import { publicId } from "@skywind-group/sw-utils";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("Game controller in BNS mode")
export class GameControllerBNSSpec {
    public static bnsStartGame: SinonStub;
    public static startGame: SinonStub;
    public static generateTransactionId: SinonStub;
    public static getBalance: SinonStub;
    public static commitOperation: SinonStub;
    public static loadGame: SinonStub;

    public static bnsStartTokenData = {
        playerCode: "PL001",
        gameCode: "test_slot",
        providerGameCode: "test_slot",
        brandId: 1,
        playmode: "bns"
    };

    public static startTokenData = {
        playerCode: "PL001",
        gameCode: "test_slot",
        providerGameCode: "test_slot",
        brandId: 1,
        playmode: "real"
    };

    public static bnsGameTokenData: GameTokenData = {
        playerCode: "PL001",
        gameCode: "test_slot",
        brandId: 1,
        currency: "BNS",
        playmode: "bns"
    };

    public static gameTokenData: GameTokenData = {
        playerCode: "PL001",
        gameCode: "test_slot",
        brandId: 1,
        currency: "BNS",
        playmode: "real"
    };

    public static bnsGameData: BNSGameData = {
        gameTokenData: GameControllerBNSSpec.bnsGameTokenData,
        jrsdSettings: { a: 1 },
        jurisdictionCode: "GB",
        playedFromCountry: "GB",
        bnsPromotion: {
            promoId: 1,
        },
        limits: {
            coins: [1],
            defaultCoin: 1,
            maxTotalStake: 500,
            stakeAll: [0.1, 0.5, 1, 2, 3, 5],
            stakeDef: 1,
            stakeMax: 10,
            stakeMin: 0.1,
            winMax: 3000000,
            currencyMultiplier: 100,
        } as Limits,
        gameId: "test_slot",
        renderType: 0,
    } as GameData;

    public static gameData: BNSGameData = {
        gameTokenData: GameControllerBNSSpec.gameTokenData,
        jrsdSettings: { a: 1 },
        jurisdictionCode: "GB",
        playedFromCountry: "GB",
        bnsPromotion: {
            promoId: 1,
        },
        limits: {
            coins: [1],
            defaultCoin: 1,
            maxTotalStake: 500,
            stakeAll: [0.1, 0.5, 1, 2, 3, 5],
            stakeDef: 1,
            stakeMax: 10,
            stakeMin: 0.1,
            winMax: 3000000,
            currencyMultiplier: 100,
        } as Limits,
        gameId: "test_slot",
    } as GameData;

    public static startGameResult = {
        gameData: GameControllerBNSSpec.gameData,
        clientSettings: {}
    } as StartGameResult;

    public static startGameResultBns = {
        gameData: GameControllerBNSSpec.bnsGameData,
        clientSettings: {}
    } as StartGameResult;

    public static bnsStartToken: string;
    public static startToken: string;
    public static walletThroughAPIPrevValue: boolean;

    public static async before() {
        await syncModels();
        GameControllerBNSSpec.walletThroughAPIPrevValue = config.walletThroughAPI;
        config.walletThroughAPI = true;
        GameControllerBNSSpec.bnsStartGame = stub(getAuthservice("bns"), "startGame");
        GameControllerBNSSpec.startGame = stub(getAuthservice("real"), "startGame");
        GameControllerBNSSpec.generateTransactionId = stub(APIPlayerWalletManager.prototype, "generateTransactionId");
        GameControllerBNSSpec.getBalance = stub(APIPlayerWalletManager.prototype, "getBalance");
        GameControllerBNSSpec.commitOperation = stub(APIPlayerWalletManager.prototype, "commitOperation");
        GameControllerBNSSpec.loadGame = stub(GameService, "load");
        GameControllerBNSSpec.bnsStartToken = await generateStartGameToken(GameControllerBNSSpec.bnsStartTokenData);
        GameControllerBNSSpec.bnsGameTokenData.token = await createGameToken(GameControllerBNSSpec.bnsGameTokenData);
        GameControllerBNSSpec.startGameResultBns.gameData.gameTokenData = GameControllerBNSSpec.bnsGameTokenData;
        GameControllerBNSSpec.startToken = await generateStartGameToken(GameControllerBNSSpec.startTokenData);
        GameControllerBNSSpec.gameTokenData.token = await createGameToken(GameControllerBNSSpec.gameTokenData);
        GameControllerBNSSpec.startGameResult.gameData.gameTokenData = GameControllerBNSSpec.gameTokenData;
        setRandomGeneratorFactory(new RandomGeneratorFactoryImpl());
    }

    public static async after() {
        GameControllerBNSSpec.loadGame.restore();
        GameControllerBNSSpec.bnsStartGame.restore();
        GameControllerBNSSpec.startGame.restore();
        GameControllerBNSSpec.generateTransactionId.restore();
        GameControllerBNSSpec.getBalance.restore();
        GameControllerBNSSpec.commitOperation.restore();
        config.walletThroughAPI = GameControllerBNSSpec.walletThroughAPIPrevValue;
    }

    public async before(): Promise<any> {
        await getGameContextModel().truncate();
        await flushAll();
    }

    public async after() {
        GameControllerBNSSpec.bnsStartGame.reset();
        GameControllerBNSSpec.startGame.reset();
        GameControllerBNSSpec.generateTransactionId.reset();
        GameControllerBNSSpec.getBalance.reset();
        GameControllerBNSSpec.commitOperation.reset();
        GameControllerBNSSpec.loadGame.reset();
    }

    @test("processes game 'init' request")
    public async processGameInitRequestBNS() {
        GameControllerBNSSpec.bnsStartGame.returns(Promise.resolve(GameControllerBNSSpec.startGameResultBns));
        this.stubGame();
        GameControllerBNSSpec.getBalance.returns(Promise.resolve({
            currency: "BNS",
            main: 1000,
            bonusCoins: {
                amount: 1000,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: GameControllerBNSSpec.bnsStartToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "BNS",
                "real": {
                    "amount": 1000,
                },
                "bonusCoins": {
                    "amount": 1000,
                    "expireAt": "0",
                    "expireCountdown": 0,
                    "promoId": "1",
                    "redeemMinAmount": 10000,
                    "redeemBalance": 10,
                    "redeemCurrency": "USD",
                    "rewardedAmount": 10,
                }
            },
            "gameSession": initResponse.gameSession,
            "brandSettings": undefined,
            "gameSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": {
                "gameId": "test_slot",
                "name": "GAME!",
                "request": "init",
                "settings": {
                    "coins": [1],
                    "defaultCoin": 1,
                    "currencyMultiplier": 100,
                    "maxTotalStake": 500,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 10,
                    "stakeMin": 0.1,
                    "winMax": 3000000,
                },
                "slot": {
                    "stub": "FORTESTREASON",
                },
                "previousResult": null,
                "stake": null
            },
            "roundEnded": true,
            renderType: 0,
            brandInfo: undefined,
        });

        expect(GameControllerBNSSpec.bnsStartGame.args[0][0]).equal(GameControllerBNSSpec.gameData.gameId);
        expect(GameControllerBNSSpec.bnsStartGame.args[0][1]).equal(GameControllerBNSSpec.bnsStartToken);
        const context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "defaultScene",
                "nextScene": "nextScene",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
        });
    }

    @test("processes game 'init' request, expired and no money to redeem")
    public async processGameInitRequestBNS_HideBonusBalance1() {
        GameControllerBNSSpec.bnsStartGame.returns(Promise.resolve(GameControllerBNSSpec.startGameResultBns));
        this.stubGame();
        GameControllerBNSSpec.getBalance.returns(Promise.resolve({
            currency: "BNS",
            main: 1000,
            bonusCoins: {
                amount: 1000,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                expireAt: "0",
                promoId: "1",
            }
        }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: GameControllerBNSSpec.bnsStartToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "BNS",
                "real": {
                    "amount": 1000,
                },
                "bonusCoins": {
                    "amount": 1000,
                    "expireAt": "0",
                    "expireCountdown": 0,
                    "promoId": "1",
                    "redeemMinAmount": 10000,
                    "rewardedAmount": 10
                }
            },
            "gameSession": initResponse.gameSession,
            "brandSettings": undefined,
            "gameSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": {
                "gameId": "test_slot",
                "name": "GAME!",
                "request": "init",
                "settings": {
                    "coins": [1],
                    "defaultCoin": 1,
                    "currencyMultiplier": 100,
                    "maxTotalStake": 500,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 10,
                    "stakeMin": 0.1,
                    "winMax": 3000000,
                },
                "slot": {
                    "stub": "FORTESTREASON",
                },
                "previousResult": null,
                "stake": null
            },
            "roundEnded": true,
            renderType: 0,
            brandInfo: undefined,
        });

        expect(GameControllerBNSSpec.bnsStartGame.args[0][0]).equal(GameControllerBNSSpec.gameData.gameId);
        expect(GameControllerBNSSpec.bnsStartGame.args[0][1]).equal(GameControllerBNSSpec.bnsStartToken);
        const context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "defaultScene",
                "nextScene": "nextScene",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
        });
    }

    @test("processes game 'init' request, zero amount and no money to redeem")
    public async processGameInitRequestBNS_HideBonusBalance2() {
        GameControllerBNSSpec.bnsStartGame.returns(Promise.resolve(GameControllerBNSSpec.startGameResultBns));
        this.stubGame();
        GameControllerBNSSpec.getBalance.returns(Promise.resolve({
            currency: "BNS",
            main: 1000,
            bonusCoins: {
                amount: 0,
                redeemMinAmount: 10000,
                expireAt: Number.MAX_SAFE_INTEGER,
                promoId: "1",
            }
        }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: GameControllerBNSSpec.bnsStartToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "bonusCoins": {
                    "amount": 0,
                    "expireAt": Number.MAX_SAFE_INTEGER,
                    "expireCountdown": 0,
                    "promoId": "1",
                    "redeemMinAmount": 10000
                },
                "currency": "BNS",
                "real": {
                    "amount": 1000,
                },
            },
            "gameSession": initResponse.gameSession,
            "brandSettings": undefined,
            "gameSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": {
                "gameId": "test_slot",
                "name": "GAME!",
                "request": "init",
                "settings": {
                    "coins": [1],
                    "defaultCoin": 1,
                    "currencyMultiplier": 100,
                    "maxTotalStake": 500,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 10,
                    "stakeMin": 0.1,
                    "winMax": 3000000,
                },
                "slot": {
                    "stub": "FORTESTREASON",
                },
                "previousResult": null,
                "stake": null
            },
            "roundEnded": true,
            renderType: 0,
            brandInfo: undefined,
        });

        expect(GameControllerBNSSpec.bnsStartGame.args[0][0]).equal(GameControllerBNSSpec.gameData.gameId);
        expect(GameControllerBNSSpec.bnsStartGame.args[0][1]).equal(GameControllerBNSSpec.bnsStartToken);
        const context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "defaultScene",
                "nextScene": "nextScene",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
        });
    }

    @test("process player's bns unfinished game")
    public async gameHaveGameINBNS() {
        GameControllerBNSSpec.bnsStartGame.returns(Promise.resolve(GameControllerBNSSpec.startGameResultBns));
        this.stubGame();
        GameControllerBNSSpec.getBalance.returns(Promise.resolve({
            currency: "BNS",
            main: 1000,
            bonusCoins: {
                amount: 1000,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                expireAt: "0",
                promoId: "1",
            }
        }));

        const initResponse1 = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: GameControllerBNSSpec.bnsStartToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse1).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "bonusCoins": {
                    "amount": 1000,
                    "expireAt": "0",
                    "expireCountdown": 0,
                    "promoId": "1",
                    "redeemMinAmount": 10000,
                    "rewardedAmount": 10
                },
                "currency": "BNS",
                "real": {
                    "amount": 1000,
                },
            },
            "gameSession": initResponse1.gameSession,
            "brandSettings": undefined,
            "gameSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": {
                "gameId": "test_slot",
                "name": "GAME!",
                "request": "init",
                "settings": {
                    "coins": [1],
                    "defaultCoin": 1,
                    "currencyMultiplier": 100,
                    "maxTotalStake": 500,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 10,
                    "stakeMin": 0.1,
                    "winMax": 3000000,
                },
                "slot": {
                    "stub": "FORTESTREASON",
                },
                "previousResult": null,
                "stake": null
            },
            "roundEnded": true,
            renderType: 0,
            brandInfo: undefined,
        });

        GameControllerBNSSpec.startGame.returns(Promise.resolve(GameControllerBNSSpec.startGameResult));
        GameControllerBNSSpec.loadGame.reset();
        this.stubGame();
        const initResponse2 = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: GameControllerBNSSpec.bnsStartToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse2).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "BNS",
                "real": {
                    "amount": 1000,
                },
                "bonusCoins": {
                    "amount": 1000,
                    "expireAt": "0",
                    "expireCountdown": 0,
                    "promoId": "1",
                    "redeemMinAmount": 10000,
                    "rewardedAmount": 10,
                }
            },
            "gameSession": initResponse2.gameSession,
            "brandSettings": undefined,
            "gameSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": {
                "gameId": "test_slot",
                "name": "GAME!",
                "request": "init",
                "settings": {
                    "coins": [1],
                    "defaultCoin": 1,
                    "currencyMultiplier": 100,
                    "maxTotalStake": 500,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 10,
                    "stakeMin": 0.1,
                    "winMax": 3000000,
                },
                "slot": {
                    "stub": "FORTESTREASON",
                },
                "previousResult": null,
                "stake": null
            },
            "roundEnded": true,
            renderType: 0,
            brandInfo: undefined,
        });
    }

    @test("processes two sequential 'spin' request")
    public async processTwoSequelntialSpins() {
        GameControllerBNSSpec.bnsStartGame.returns(Promise.resolve(GameControllerBNSSpec.startGameResultBns));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "currencyMultiplier": 100,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        ));
        GameControllerBNSSpec.getBalance.returns(Promise.resolve({
            currency: "BNS",
            main: 1000,
            bonusCoins: {
                amount: 1000,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: GameControllerBNSSpec.bnsStartToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns"));

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000
            },
        });

        /**
         * First spin
         */

        GameControllerBNSSpec.generateTransactionId.returns(Promise.resolve("TEST_TRX_ID"));
        GameControllerBNSSpec.commitOperation.returns(Promise.resolve({
            currency: "BNS",
            main: 1004,
            previousValue: 1000,
            bonusCoins: {
                amount: 1004,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        const spinResponse = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        });

        expect(spinResponse).deep.equal({
            "balance": {
                "amount": 1004,
                "bonus": {
                    "amount": 0,
                },
                "currency": "BNS",
                "real": {
                    "amount": 1004,
                },
                "bonusCoins": {
                    "amount": 1004,
                    "expireAt": "0",
                    "expireCountdown": 0,
                    "promoId": "1",
                    "redeemMinAmount": 10000,
                    "redeemBalance": 10,
                    "redeemCurrency": "USD",
                    "rewardedAmount": 10
                }
            },
            "extraData": undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 1,
            "result": {
                "request": "spin",
                "roundEnded": false,
                "totalBet": 1,
                "totalWin": 5,
            },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        GameControllerBNSSpec.generateTransactionId.resetBehavior();
        GameControllerBNSSpec.commitOperation.resetBehavior();
        GameControllerBNSSpec.generateTransactionId.returns(Promise.resolve("TEST_TRX_ID2"));
        GameControllerBNSSpec.commitOperation.returns(Promise.resolve({
            currency: "BNS",
            main: 1000,
            previousValue: 1004,
            bonusCoins: {
                amount: 1000,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns");
        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                currentBet: 1,
                betsCount: 1
            },
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
            "specialState": undefined,
            "version": 4,
        });

        /**
         *  Second spin
         */

        const spinResponse2 = await getSyncGameController().process({
            request: "spin",
            requestId: 2,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 100,
            coin: 33,
        });

        expect(spinResponse2).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "BNS",
                "real": {
                    "amount": 1000,
                },
                "bonusCoins": {
                    "amount": 1000,
                    "rewardedAmount": 10,
                    "redeemMinAmount": 10000,
                    "redeemBalance": 10,
                    "redeemCurrency": "USD",
                    "expireCountdown": 0,
                    "expireAt": "0",
                    "promoId": "1",
                }
            },
            "extraData": undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 2,
            "result": {
                "request": "spin",
                "roundEnded": true,
                "totalBet": 5,
                "totalWin": 1,
            },
            "roundEnded": true,
            "roundTotalBet": 6,
            "roundTotalWin": 6
        });

        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 2,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 2,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "roundId": "1",
            "gameContext": {
                "currentScene": "scene3",
                "nextScene": "scene4",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
            "specialState": undefined,
            "version": 6,
        });

        const spin2 = await getGameHistoryItem();
        expect(spin2).to.contain({
            "bet": 5,
            "roundEnded": true,
            "roundId": "0",
            "eventId": 1,
            "type": "slot",
            "currency": "BNS",
            "win": 1,
            "balanceAfter": 1000,
            "balanceBefore": 1004
        });

        const spin1 = await getGameHistoryItem();
        expect(spin1).to.contain({
            "bet": 1,
            "roundEnded": false,
            "roundId": "0",
            "eventId": 0,
            "type": "slot",
            "win": 5,
            "currency": "BNS",
            "balanceAfter": 1004,
            "balanceBefore": 1000
        });

        const items = await getRoundHistory(0, 1000);

        expect(items).deep.equals(
            [
                {
                    "balanceAfter": 1000,
                    "balanceBefore": 1000,
                    "brandId": 1,
                    "broken": false,
                    "currency": "BNS",
                    "deviceId": "deviceId",
                    "finishedAt": items[0].finishedAt,
                    "gameCode": "test_slot",
                    "gameId": "test_slot",
                    "id": "0",
                    "ctrl": items[0].ctrl,
                    "playerCode": "PL001",
                    "sessionId": "0",
                    "startedAt": items[0].startedAt,
                    "totalBet": 6,
                    "totalEvents": 2,
                    "totalWin": 6,
                    "totalJpContribution": 0,
                    "totalJpWin": 0,
                }
            ]);
    }

    @test("restores state of broken game: init new game session and make spins")
    public async restoreStateOfBrokenGame() {
        GameControllerBNSSpec.bnsStartGame.returns(Promise.resolve(GameControllerBNSSpec.startGameResultBns));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "currencyMultiplier": 100,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                },
                {
                    "currentScene": "scene4",
                    "nextScene": "scene5",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        ));
        GameControllerBNSSpec.getBalance.returns(Promise.resolve({
            currency: "BNS",
            main: 1000,
            bonusCoins: {
                amount: 1000,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: GameControllerBNSSpec.bnsStartToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000
            },
        });

        /**
         * First spin
         */

        GameControllerBNSSpec.generateTransactionId.returns(Promise.resolve("TEST_TRX_ID"));
        GameControllerBNSSpec.commitOperation.returns(Promise.resolve({
            currency: "BNS",
            main: 1004,
            previousValue: 1000,
            bonusCoins: {
                amount: 1004,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        const spinResponse = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        });

        expect(spinResponse).deep.equal({
            "balance": {
                "amount": 1004,
                "bonus": {
                    "amount": 0,
                },
                "currency": "BNS",
                "real": {
                    "amount": 1004,
                },
                "bonusCoins": {
                    "amount": 1004,
                    "expireAt": "0",
                    "expireCountdown": 0,
                    "promoId": "1",
                    "redeemMinAmount": 10000,
                    "redeemBalance": 10,
                    "redeemCurrency": "USD",
                    "rewardedAmount": 10
                }
            },
            "extraData": undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 1,
            "result": {
                "request": "spin",
                "roundEnded": false,
                "totalBet": 1,
                "totalWin": 5,
            },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        GameControllerBNSSpec.generateTransactionId.resetBehavior();
        GameControllerBNSSpec.commitOperation.resetBehavior();
        GameControllerBNSSpec.generateTransactionId.returns(Promise.resolve("TEST_TRX_ID2"));
        GameControllerBNSSpec.commitOperation.returns(Promise.resolve({
            currency: "BNS",
            main: 1000,
            previousValue: 1004,
            bonusCoins: {
                amount: 1000,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns");
        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                currentBet: 1,
                betsCount: 1
            },
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
            "specialState": undefined,
            "version": 4,
        });

        /**
         *  Restart game and make second spin
         */

        const cleanupService = new CleanupGameContextService();
        await cleanupService.forceCleanUp(gameID.brandId, 1000);

        let offlineItems = await getGameContextModel().findAll();
        expect(offlineItems.length).equal(1);

        const initResponse2 = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: GameControllerBNSSpec.bnsStartToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        offlineItems = await getGameContextModel().findAll();
        expect(offlineItems.length).equal(0);

        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse2.gameSession),
            "id": gameID,
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "broken": true,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                currentBet: 1,
                betsCount: 1
            },
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene3",
                "nextScene": "scene4",
            },
            "jpContext": undefined,
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,

            },
            "specialState": undefined,
            "version": 7,
        });

        const spinResponse2 = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse2.gameSession,
            lines: 100,
            bet: 100,
            coin: 33,
        });

        expect(spinResponse2).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "BNS",
                "real": {
                    "amount": 1000,
                },
                "bonusCoins": {
                    "amount": 1000,
                    "rewardedAmount": 10,
                    "redeemMinAmount": 10000,
                    "redeemBalance": 10,
                    "redeemCurrency": "USD",
                    "expireCountdown": 0,
                    "expireAt": "0",
                    "promoId": "1",
                }
            },
            "extraData": undefined,
            "gameSession": initResponse2.gameSession,
            "requestId": 1,
            "result": {
                "request": "spin",
                "roundEnded": true,
                "totalBet": 5,
                "totalWin": 1,
            },
            "roundEnded": true,
            "roundTotalBet": 6,
            "roundTotalWin": 6
        });

        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse2.gameSession),
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 2,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "roundId": "1",
            "gameContext": {
                "currentScene": "scene4",
                "nextScene": "scene5",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
            "specialState": undefined,
            "version": 9
        });

        const spin2 = await getGameHistoryItem();
        expect(spin2).to.contain({
            "bet": 5,
            "roundEnded": true,
            "roundId": "0",
            "eventId": 1,
            "currency": "BNS",
            "type": "slot",
            "win": 1,
            "balanceAfter": 1000,
            "balanceBefore": 1004
        });

        const items = await getRoundHistory(0, 1000);

        expect(items).deep.equals(
            [
                {
                    "balanceAfter": 1000,
                    "balanceBefore": 1000,
                    "brandId": 1,
                    "broken": true,
                    "currency": "BNS",
                    "deviceId": "deviceId",
                    "finishedAt": items[0].finishedAt,
                    "gameCode": "test_slot",
                    "gameId": "test_slot",
                    "id": "0",
                    "ctrl": items[0].ctrl,
                    "playerCode": "PL001",
                    "sessionId": "1",
                    "startedAt": items[1].startedAt,
                    "totalBet": 6,
                    "totalEvents": 2,
                    "totalWin": 6,
                    "totalJpContribution": 0,
                    "totalJpWin": 0,
                },
                {
                    "balanceAfter": 1004,
                    "balanceBefore": 1000,
                    "brandId": 1,
                    "broken": true,
                    "currency": "BNS",
                    "deviceId": "deviceId",
                    "gameCode": "test_slot",
                    "gameId": "test_slot",
                    "id": "0",
                    "ctrl": items[1].ctrl,
                    "playerCode": "PL001",
                    "sessionId": "0",
                    "startedAt": items[1].startedAt,
                    "totalBet": 1,
                    "totalEvents": 1,
                    "totalWin": 5,
                    "totalJpContribution": 0,
                    "totalJpWin": 0,
                }
            ]);
    }

    @test("remove game context and finalize round if promo has changed")
    public async updatePromoId() {
        GameControllerBNSSpec.bnsStartGame.returns(Promise.resolve(GameControllerBNSSpec.startGameResultBns));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        ));
        GameControllerBNSSpec.getBalance.returns(Promise.resolve({
            currency: "BNS",
            main: 1000,
            bonusCoins: {
                amount: 1000,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: GameControllerBNSSpec.bnsStartToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000
            },
        });

        /**
         * First spin
         */

        GameControllerBNSSpec.generateTransactionId.returns(Promise.resolve("TEST_TRX_ID"));
        GameControllerBNSSpec.commitOperation.returns(Promise.resolve({
            currency: "BNS",
            main: 1004,
            previousValue: 1000,
            bonusCoins: {
                amount: 1004,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        const spinResponse = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        });

        expect(spinResponse).deep.equal({
            "balance": {
                "amount": 1004,
                "bonus": {
                    "amount": 0,
                },
                "currency": "BNS",
                "real": {
                    "amount": 1004,
                },
                "bonusCoins": {
                    "amount": 1004,
                    "expireAt": "0",
                    "expireCountdown": 0,
                    "promoId": "1",
                    "redeemMinAmount": 10000,
                    "redeemBalance": 10,
                    "redeemCurrency": "USD",
                    "rewardedAmount": 10
                }
            },
            "extraData": undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 1,
            "result": {
                "request": "spin",
                "roundEnded": false,
                "totalBet": 1,
                "totalWin": 5,
            },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        GameControllerBNSSpec.generateTransactionId.resetBehavior();
        GameControllerBNSSpec.commitOperation.resetBehavior();
        GameControllerBNSSpec.generateTransactionId.returns(Promise.resolve("TEST_TRX_ID2"));
        GameControllerBNSSpec.commitOperation.returns(Promise.resolve({
            currency: "BNS",
            main: 1000,
            previousValue: 1004,
            bonusCoins: {
                amount: 1000,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns");
        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                currentBet: 1,
                betsCount: 1
            },
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
            "specialState": undefined,
            "version": 4,
        });

        /**
         *  Restart game and make second spin
         */

        const cleanupService = new CleanupGameContextService();
        await cleanupService.forceCleanUp(gameID.brandId, 1000);

        let offlineItems = await getGameContextModel().findAll();
        expect(offlineItems.length).equal(1);

        GameControllerBNSSpec.getBalance.returns(Promise.resolve({
            currency: "BNS",
            main: 1000,
            bonusCoins: {
                amount: 1000,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "2",
            }
        }));

        const result = deepClone(GameControllerBNSSpec.startGameResultBns);
        result.gameData.bnsPromotion.promoId = "2";
        GameControllerBNSSpec.bnsStartGame.returns(Promise.resolve(result));

        const initResponse2 = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: GameControllerBNSSpec.bnsStartToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        offlineItems = await getGameContextModel().findAll();
        expect(offlineItems.length).equal(0);

        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse2.gameSession),
            "id": gameID,
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundId": "1",
            "gameContext": undefined,
            "jpContext": undefined,
            "gameData": result.gameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,

            },
            "specialState": undefined,
            "version": 7,
        });

        const items = await getRoundHistory(0, 1000);

        expect(items).deep.equals(
            [
                {
                    "balanceAfter": 1004,
                    "balanceBefore": 1000,
                    "brandId": 1,
                    "broken": true,
                    "currency": "BNS",
                    "deviceId": "deviceId",
                    "gameCode": "test_slot",
                    "gameId": "test_slot",
                    "id": "0",
                    "ctrl": items[0].ctrl,
                    "playerCode": "PL001",
                    "sessionId": "0",
                    "startedAt": items[0].startedAt,
                    "finishedAt": items[0].finishedAt,
                    "totalBet": 1,
                    "totalEvents": 1,
                    "totalWin": 5,
                    "totalJpContribution": 0,
                    "totalJpWin": 0,
                    "recoveryType": "finalize"
                },
                {
                    "balanceAfter": 1004,
                    "balanceBefore": 1000,
                    "brandId": 1,
                    "broken": true,
                    "currency": "BNS",
                    "deviceId": "deviceId",
                    "gameCode": "test_slot",
                    "gameId": "test_slot",
                    "id": "0",
                    "ctrl": items[1].ctrl,
                    "playerCode": "PL001",
                    "sessionId": "0",
                    "startedAt": items[1].startedAt,
                    "totalBet": 1,
                    "totalEvents": 1,
                    "totalWin": 5,
                    "totalJpContribution": 0,
                    "totalJpWin": 0,
                }
            ]);
    }

    @test("processes game 'redeem-bns' request - failed")
    public async processRedeemFailed() {
        GameControllerBNSSpec.bnsStartGame.returns(Promise.resolve(GameControllerBNSSpec.startGameResultBns));
        this.stubGame();
        const ts = Date.now() + 60 * 1000 * 60;
        GameControllerBNSSpec.getBalance.returns(Promise.resolve({
            currency: "BNS",
            main: 1000,
            bonusCoins: {
                amount: 1000,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                expireAt: new Date(ts).toISOString(),
                promoId: "1",
            }
        }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: GameControllerBNSSpec.bnsStartToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "BNS",
                "real": {
                    "amount": 1000,
                },
                "bonusCoins": {
                    "amount": 1000,
                    "expireAt": new Date(ts).toISOString(),
                    "expireCountdown": initResponse.balance.bonusCoins.expireCountdown,
                    "promoId": "1",
                    "redeemMinAmount": 10000,
                    "rewardedAmount": 10,
                }
            },
            "gameSession": initResponse.gameSession,
            "brandSettings": undefined,
            "gameSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": {
                "gameId": "test_slot",
                "name": "GAME!",
                "request": "init",
                "settings": {
                    "coins": [1],
                    "defaultCoin": 1,
                    "currencyMultiplier": 100,
                    "maxTotalStake": 500,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 10,
                    "stakeMin": 0.1,
                    "winMax": 3000000,
                },
                "slot": {
                    "stub": "FORTESTREASON",
                },
                "previousResult": null,
                "stake": null
            },
            "roundEnded": true,
            renderType: 0,
            brandInfo: undefined,
        });

        expect(GameControllerBNSSpec.bnsStartGame.args[0][0]).equal(GameControllerBNSSpec.gameData.gameId);
        expect(GameControllerBNSSpec.bnsStartGame.args[0][1]).equal(GameControllerBNSSpec.bnsStartToken);
        const context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "defaultScene",
                "nextScene": "nextScene",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
        });

        return getSyncGameController().process({
            request: "redeem-bns",
            gameSession: initResponse.gameSession,
            requestId: 1
        }).should.be.rejectedWith(RedeemBNSError);
    }

    @test("processes game 'redeem-bns' request - invalid promoId")
    public async processRedeemIvalidPromo() {
        GameControllerBNSSpec.bnsStartGame.returns(Promise.resolve(GameControllerBNSSpec.startGameResultBns));
        this.stubGame();
        const ts = Date.now() + 60 * 1000 * 60;
        GameControllerBNSSpec.getBalance.returns(Promise.resolve({
            currency: "BNS",
            main: 1000,
            bonusCoins: {
                amount: 1000,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 500,
                redeemCurrency: "USD",
                expireAt: new Date(ts).toISOString(),
                promoId: "1",
            }
        }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: GameControllerBNSSpec.bnsStartToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "BNS",
                "real": {
                    "amount": 1000,
                },
                "bonusCoins": {
                    "amount": 1000,
                    "expireAt": new Date(ts).toISOString(),
                    "expireCountdown": initResponse.balance.bonusCoins.expireCountdown,
                    "promoId": "1",
                    "redeemMinAmount": 10000,
                    "rewardedAmount": 10,
                    "redeemBalance": 500,
                    "redeemCurrency": "USD"
                }
            },
            "gameSession": initResponse.gameSession,
            "brandSettings": undefined,
            "gameSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": {
                "gameId": "test_slot",
                "name": "GAME!",
                "request": "init",
                "settings": {
                    "coins": [1],
                    "defaultCoin": 1,
                    "currencyMultiplier": 100,
                    "maxTotalStake": 500,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 10,
                    "stakeMin": 0.1,
                    "winMax": 3000000,
                },
                "slot": {
                    "stub": "FORTESTREASON",
                },
                "previousResult": null,
                "stake": null
            },
            "roundEnded": true,
            renderType: 0,
            brandInfo: undefined,
        });

        expect(GameControllerBNSSpec.bnsStartGame.args[0][0]).equal(GameControllerBNSSpec.gameData.gameId);
        expect(GameControllerBNSSpec.bnsStartGame.args[0][1]).equal(GameControllerBNSSpec.bnsStartToken);
        const context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "defaultScene",
                "nextScene": "nextScene",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
        });

        return getSyncGameController().process({
            request: "redeem-bns",
            gameSession: initResponse.gameSession,
            requestId: 1,
            promoId: "2"
        }).should.be.rejectedWith(RedeemBNSError);
    }

    @test("processes game  manual 'redeem-bns - failed, incomplete round'")
    public async processRedeemFailed_IncompleteRound() {
        GameControllerBNSSpec.bnsStartGame.returns(Promise.resolve(GameControllerBNSSpec.startGameResultBns));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "currencyMultiplier": 100,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
            ], [
                { bet: 1, win: 5 },
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
            ]
        ));
        GameControllerBNSSpec.getBalance.returns(Promise.resolve({
            currency: "BNS",
            main: 10000,
            bonusCoins: {
                amount: 10000,
                rewardedAmount: 10000,
                redeemMinAmount: 10000,
                redeemBalance: 200,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: GameControllerBNSSpec.bnsStartToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse).deep.equal({
            "balance": {
                "amount": 10000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "BNS",
                "real": {
                    "amount": 10000,
                },
                "bonusCoins": {
                    "amount": 10000,
                    "expireAt": "0",
                    "expireCountdown": 0,
                    "promoId": "1",
                    "redeemMinAmount": 10000,
                    "redeemBalance": 200,
                    "redeemCurrency": "USD",
                    "rewardedAmount": 10000,
                }
            },
            "gameSession": initResponse.gameSession,
            "brandSettings": undefined,
            "gameSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": {
                "gameId": "test_slot",
                "name": "GAME!",
                "request": "init",
                "settings": {
                    "coins": [1],
                    "defaultCoin": 1,
                    "currencyMultiplier": 100,
                    "maxTotalStake": 500,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 10,
                    "stakeMin": 0.1,
                    "winMax": 3000000,
                },
                "slot": {
                    "stub": "FORTESTREASON",
                },
                "previousResult": null,
                "stake": null
            },
            "roundEnded": true,
            renderType: 0,
            brandInfo: undefined,
        });

        expect(GameControllerBNSSpec.bnsStartGame.args[0][0]).equal(GameControllerBNSSpec.gameData.gameId);
        expect(GameControllerBNSSpec.bnsStartGame.args[0][1]).equal(GameControllerBNSSpec.bnsStartToken);
        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2"
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
        });

        /**
         * Make spin and incomplete round
         */

        GameControllerBNSSpec.generateTransactionId.returns(Promise.resolve("TEST_TRX_ID"));
        GameControllerBNSSpec.commitOperation.returns(Promise.resolve({
            currency: "BNS",
            main: 1004,
            previousValue: 1000,
            bonusCoins: {
                amount: 1004,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        const spinResponse = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        });

        expect(spinResponse).deep.equal({
            "balance": {
                "amount": 1004,
                "bonus": {
                    "amount": 0,
                },
                "currency": "BNS",
                "real": {
                    "amount": 1004,
                },
                "bonusCoins": {
                    "amount": 1004,
                    "expireAt": "0",
                    "expireCountdown": 0,
                    "promoId": "1",
                    "redeemMinAmount": 10000,
                    "redeemBalance": 10,
                    "redeemCurrency": "USD",
                    "rewardedAmount": 10
                }
            },
            "extraData": undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 1,
            "result": {
                "request": "spin",
                "roundEnded": false,
                "totalBet": 1,
                "totalWin": 5,
            },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        GameControllerBNSSpec.generateTransactionId.resetBehavior();
        GameControllerBNSSpec.commitOperation.resetBehavior();
        GameControllerBNSSpec.generateTransactionId.returns(Promise.resolve("TEST_TRX_ID2"));
        GameControllerBNSSpec.commitOperation.returns(Promise.resolve({
            currency: "BNS",
            main: 1000,
            previousValue: 1004,
            bonusCoins: {
                amount: 1000,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        context = await getGameFlowContextManager().findGameContextById(
            GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns"));

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                currentBet: 1,
                betsCount: 1
            },
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
            "specialState": undefined,
            "version": 4,
        });

        GameControllerBNSSpec.generateTransactionId.resetBehavior();
        GameControllerBNSSpec.commitOperation.resetBehavior();

        return getSyncGameController().process({
            request: "redeem-bns",
            gameSession: initResponse.gameSession,
            manual: true,
            requestId: 1
        }).should.be.rejectedWith(ManualRedeemBNSError);
    }

    @test("processes game  'redeem-bns of incomplete round'")
    public async processRedeemIncompleteRound() {
        GameControllerBNSSpec.bnsStartGame.returns(Promise.resolve(GameControllerBNSSpec.startGameResultBns));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "currencyMultiplier": 100,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
            ], [
                { bet: 1, win: 5 },
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
            ]
        ));
        GameControllerBNSSpec.getBalance.returns(Promise.resolve({
            currency: "BNS",
            main: 10000,
            bonusCoins: {
                amount: 10000,
                rewardedAmount: 10000,
                redeemMinAmount: 10000,
                redeemBalance: 200,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: GameControllerBNSSpec.bnsStartToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse).deep.equal({
            "balance": {
                "amount": 10000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "BNS",
                "real": {
                    "amount": 10000,
                },
                "bonusCoins": {
                    "amount": 10000,
                    "expireAt": "0",
                    "expireCountdown": 0,
                    "promoId": "1",
                    "redeemMinAmount": 10000,
                    "redeemBalance": 200,
                    "redeemCurrency": "USD",
                    "rewardedAmount": 10000,
                }
            },
            "gameSession": initResponse.gameSession,
            "brandSettings": undefined,
            "gameSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": {
                "gameId": "test_slot",
                "name": "GAME!",
                "request": "init",
                "settings": {
                    "coins": [1],
                    "defaultCoin": 1,
                    "currencyMultiplier": 100,
                    "maxTotalStake": 500,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 10,
                    "stakeMin": 0.1,
                    "winMax": 3000000,
                },
                "slot": {
                    "stub": "FORTESTREASON",
                },
                "previousResult": null,
                "stake": null
            },
            "roundEnded": true,
            renderType: 0,
            brandInfo: undefined,
        });

        expect(GameControllerBNSSpec.bnsStartGame.args[0][0]).equal(GameControllerBNSSpec.gameData.gameId);
        expect(GameControllerBNSSpec.bnsStartGame.args[0][1]).equal(GameControllerBNSSpec.bnsStartToken);
        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2"
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
        });

        /**
         * Make spin and incomplete round
         */

        GameControllerBNSSpec.generateTransactionId.returns(Promise.resolve("TEST_TRX_ID"));
        GameControllerBNSSpec.commitOperation.returns(Promise.resolve({
            currency: "BNS",
            main: 1004,
            previousValue: 1000,
            bonusCoins: {
                amount: 1004,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        const spinResponse = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        });

        expect(spinResponse).deep.equal({
            "balance": {
                "amount": 1004,
                "bonus": {
                    "amount": 0,
                },
                "currency": "BNS",
                "real": {
                    "amount": 1004,
                },
                "bonusCoins": {
                    "amount": 1004,
                    "expireAt": "0",
                    "expireCountdown": 0,
                    "promoId": "1",
                    "redeemMinAmount": 10000,
                    "redeemBalance": 10,
                    "redeemCurrency": "USD",
                    "rewardedAmount": 10
                }
            },
            "extraData": undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 1,
            "result": {
                "request": "spin",
                "roundEnded": false,
                "totalBet": 1,
                "totalWin": 5,
            },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        GameControllerBNSSpec.generateTransactionId.resetBehavior();
        GameControllerBNSSpec.commitOperation.resetBehavior();
        GameControllerBNSSpec.generateTransactionId.returns(Promise.resolve("TEST_TRX_ID2"));
        GameControllerBNSSpec.commitOperation.returns(Promise.resolve({
            currency: "BNS",
            main: 1000,
            previousValue: 1004,
            bonusCoins: {
                amount: 1000,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        context = await getGameFlowContextManager().findGameContextById(
            GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns"));

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                currentBet: 1,
                betsCount: 1
            },
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
            "specialState": undefined,
            "version": 4,
        });

        GameControllerBNSSpec.generateTransactionId.reset();
        GameControllerBNSSpec.commitOperation.reset();
        GameControllerBNSSpec.generateTransactionId.returns(Promise.resolve("TEST_TRX_ID2"));
        GameControllerBNSSpec.commitOperation.returns(Promise.resolve({
            currency: "BNS",
            main: 0,
            previousValue: 10000,
            bonusCoins: {
                amount: 0,
                rewardedAmount: 0,
                redeemMinAmount: 0,
                expireAt: "0",
                promoId: "1",
            }
        }));

        const redeemResponse = await getSyncGameController().process({
            request: "redeem-bns",
            gameSession: initResponse.gameSession,
            requestId: 1
        });

        expect(redeemResponse).deep.equals({
            "balance": {
                "amount": 0,
                "bonus": {
                    "amount": 0,
                },
                "currency": "BNS",
                "real": {
                    "amount": 0,
                },
                "bonusCoins": {
                    "amount": 0,
                    "expireAt": "0",
                    "expireCountdown": 0,
                    "promoId": "1",
                    "redeemMinAmount": 0,
                    "rewardedAmount": 0,
                }
            },
            "extraData": undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 1,
            "result": {
                "request": "redeem-bns",
                "redeemBalance": 200,
                "redeemCurrency": "USD",
                "promoId": "1"
            },
            "roundEnded": false
        });

        expect(GameControllerBNSSpec.commitOperation.args[0][0]).deep.include(
            {
                "amount": 200,
                "currency": "BNS",
                "deviceId": "deviceId",
                "operation": "redeem-bns",
                "promoId": "1",
                "roundId": "1",
                "roundPID": publicId.instance.encode(1),
                "eventId": 1,
                "transactionId": "TEST_TRX_ID2"
            });
        expect(+GameControllerBNSSpec.commitOperation.args[0][0].ts).is.not.undefined;
    }

    @test("processes game 'redeem-bns'")
    public async processRedeem() {
        GameControllerBNSSpec.bnsStartGame.returns(Promise.resolve(GameControllerBNSSpec.startGameResultBns));
        this.stubGame();
        GameControllerBNSSpec.getBalance.returns(Promise.resolve({
            currency: "BNS",
            main: 10000,
            bonusCoins: {
                amount: 10000,
                rewardedAmount: 10000,
                redeemMinAmount: 10000,
                redeemBalance: 200,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: GameControllerBNSSpec.bnsStartToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse).deep.equal({
            "balance": {
                "amount": 10000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "BNS",
                "real": {
                    "amount": 10000,
                },
                "bonusCoins": {
                    "amount": 10000,
                    "expireAt": "0",
                    "expireCountdown": 0,
                    "promoId": "1",
                    "redeemMinAmount": 10000,
                    "redeemBalance": 200,
                    "redeemCurrency": "USD",
                    "rewardedAmount": 10000,
                }
            },
            "gameSession": initResponse.gameSession,
            "brandSettings": undefined,
            "gameSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": {
                "gameId": "test_slot",
                "name": "GAME!",
                "request": "init",
                "settings": {
                    "coins": [1],
                    "defaultCoin": 1,
                    "currencyMultiplier": 100,
                    "maxTotalStake": 500,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 10,
                    "stakeMin": 0.1,
                    "winMax": 3000000,
                },
                "slot": {
                    "stub": "FORTESTREASON",
                },
                "previousResult": null,
                "stake": null
            },
            "roundEnded": true,
            renderType: 0,
            brandInfo: undefined,
        });

        expect(GameControllerBNSSpec.bnsStartGame.args[0][0]).equal(GameControllerBNSSpec.gameData.gameId);
        expect(GameControllerBNSSpec.bnsStartGame.args[0][1]).equal(GameControllerBNSSpec.bnsStartToken);
        const context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "defaultScene",
                "nextScene": "nextScene",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
        });

        GameControllerBNSSpec.generateTransactionId.resetBehavior();
        GameControllerBNSSpec.commitOperation.resetBehavior();
        GameControllerBNSSpec.generateTransactionId.returns(Promise.resolve("TEST_TRX_ID2"));
        GameControllerBNSSpec.commitOperation.returns(Promise.resolve({
            currency: "BNS",
            main: 0,
            previousValue: 10000,
            bonusCoins: {
                amount: 0,
                rewardedAmount: 0,
                redeemMinAmount: 0,
                expireAt: "0",
                promoId: "1",
            }
        }));

        const redeemResponse = await getSyncGameController().process({
            request: "redeem-bns",
            gameSession: initResponse.gameSession,
            requestId: 1
        });

        expect(redeemResponse).deep.equals({
            "balance": {
                "amount": 0,
                "bonus": {
                    "amount": 0,
                },
                "currency": "BNS",
                "real": {
                    "amount": 0,
                },
                "bonusCoins": {
                    "amount": 0,
                    "expireAt": "0",
                    "expireCountdown": 0,
                    "promoId": "1",
                    "redeemMinAmount": 0,
                    "rewardedAmount": 0,
                }
            },
            "extraData": undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 1,
            "result": {
                "request": "redeem-bns",
                "redeemBalance": 200,
                "redeemCurrency": "USD",
                "promoId": "1"
            },
            "roundEnded": true
        });

        expect(GameControllerBNSSpec.commitOperation.args[0][0]).deep.include(
            {
                "amount": 200,
                "currency": "BNS",
                "deviceId": "deviceId",
                "operation": "redeem-bns",
                "promoId": "1",
                "roundId": "1",
                "roundPID": publicId.instance.encode(1),
                "eventId": 0,
                "transactionId": "TEST_TRX_ID2"
            });
        expect(GameControllerBNSSpec.commitOperation.args[0][0].ts).is.not.undefined;
    }

    @test("processes InsufficientBNSBalance, rollback and close round")
    public async processInsufficientBNSBalance() {
        GameControllerBNSSpec.bnsStartGame.returns(Promise.resolve(GameControllerBNSSpec.startGameResultBns));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "currencyMultiplier": 100,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: false, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        ));
        GameControllerBNSSpec.getBalance.returns(Promise.resolve({
            currency: "BNS",
            main: 1000,
            bonusCoins: {
                amount: 1000,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: GameControllerBNSSpec.bnsStartToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns"));

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000
            },
        });

        /**
         * First spin
         */

        GameControllerBNSSpec.generateTransactionId.returns(Promise.resolve("TEST_TRX_ID"));
        GameControllerBNSSpec.commitOperation.returns(Promise.resolve({
            currency: "BNS",
            main: 1004,
            previousValue: 1000,
            bonusCoins: {
                amount: 1004,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        const spinResponse = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        });

        expect(spinResponse).deep.equal({
            "balance": {
                "amount": 1004,
                "bonus": {
                    "amount": 0,
                },
                "currency": "BNS",
                "real": {
                    "amount": 1004,
                },
                "bonusCoins": {
                    "amount": 1004,
                    "expireAt": "0",
                    "expireCountdown": 0,
                    "promoId": "1",
                    "redeemMinAmount": 10000,
                    "redeemBalance": 10,
                    "redeemCurrency": "USD",
                    "rewardedAmount": 10
                }
            },
            "extraData": undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 1,
            "result": {
                "request": "spin",
                "roundEnded": false,
                "totalBet": 1,
                "totalWin": 5,
            },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });
        let roundHistory = await getRoundHistory(0, 1000);
        expect(roundHistory).deep.equal([]);

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns");
        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                currentBet: 1,
                betsCount: 1
            },
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
            "specialState": undefined,
            "version": 4,
        });

        /**
         *  Second spin
         */
        GameControllerBNSSpec.generateTransactionId.resetBehavior();
        GameControllerBNSSpec.commitOperation.resetBehavior();
        GameControllerBNSSpec.generateTransactionId.returns(Promise.resolve("TEST_TRX_ID2"));
        GameControllerBNSSpec.commitOperation.returns(
            Promise.reject(new ManagementAPIError(400, { responseStatus: 400, code: 691 } as any)));

        await expect(getSyncGameController().process({
            request: "spin",
            requestId: 2,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 100,
            coin: 33,
        })).to.be.rejectedWith(ManagementAPIError);

        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 2,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 2,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "finishedAt": context.round.finishedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                currentBet: 1,
                betsCount: 1
            },
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
            "specialState": undefined,
            "version": 6,
        });

        const spin1 = await getGameHistoryItem();
        expect(spin1).to.contain({
            "bet": 1,
            "roundEnded": false,
            "roundId": "0",
            "eventId": 0,
            "type": "slot",
            "win": 5,
            "currency": "BNS",
            "balanceAfter": 1004,
            "balanceBefore": 1000
        });

        const spin2 = await getGameHistoryItem();
        expect(spin2).is.undefined;

        roundHistory = await getRoundHistory(0, 1000);

        expect(roundHistory.map(item => _.omit(item, "finishedAt", "startedAt", "ctrl"))).deep.equals(
            [
                {
                    balanceAfter: 1004,
                    balanceBefore: 1000,
                    brandId: 1,
                    broken: false,
                    currency: "BNS",
                    deviceId: "deviceId",
                    gameCode: "test_slot",
                    gameId: "test_slot",
                    id: "0",
                    playerCode: "PL001",
                    sessionId: "0",
                    totalBet: 1,
                    totalEvents: 1,
                    totalWin: 5,
                    totalJpContribution: 0,
                    totalJpWin: 0,
                }
            ]);
    }

    @test("processes Error, rollback pending and don't close rounds")
    public async processErrorAndRollback() {
        GameControllerBNSSpec.bnsStartGame.returns(Promise.resolve(GameControllerBNSSpec.startGameResultBns));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "currencyMultiplier": 100,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: false, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        ));
        GameControllerBNSSpec.getBalance.returns(Promise.resolve({
            currency: "BNS",
            main: 1000,
            bonusCoins: {
                amount: 1000,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: GameControllerBNSSpec.bnsStartToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns"));

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2",
            },
            "jpContext": undefined,
            "specialState": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000
            },
        });

        /**
         * First spin
         */

        GameControllerBNSSpec.generateTransactionId.returns(Promise.resolve("TEST_TRX_ID"));
        GameControllerBNSSpec.commitOperation.returns(Promise.resolve({
            currency: "BNS",
            main: 1004,
            previousValue: 1000,
            bonusCoins: {
                amount: 1004,
                rewardedAmount: 10,
                redeemMinAmount: 10000,
                redeemBalance: 10,
                redeemCurrency: "USD",
                expireAt: "0",
                promoId: "1",
            }
        }));

        const spinResponse = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        });

        expect(spinResponse).deep.equal({
            "balance": {
                "amount": 1004,
                "bonus": {
                    "amount": 0,
                },
                "currency": "BNS",
                "real": {
                    "amount": 1004,
                },
                "bonusCoins": {
                    "amount": 1004,
                    "expireAt": "0",
                    "expireCountdown": 0,
                    "promoId": "1",
                    "redeemMinAmount": 10000,
                    "redeemBalance": 10,
                    "redeemCurrency": "USD",
                    "rewardedAmount": 10
                }
            },
            "extraData": undefined,
            "gameSession": initResponse.gameSession,
            "requestId": 1,
            "result": {
                "request": "spin",
                "roundEnded": false,
                "totalBet": 1,
                "totalWin": 5,
            },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });
        let roundHistory = await getRoundHistory(0, 1000);
        expect(roundHistory).deep.equal([]);

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId", "bns");
        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                currentBet: 1,
                betsCount: 1
            },
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
            "specialState": undefined,
            "version": 4,
        });

        /**
         *  Second spin
         */
        GameControllerBNSSpec.generateTransactionId.resetBehavior();
        GameControllerBNSSpec.commitOperation.resetBehavior();
        GameControllerBNSSpec.generateTransactionId.returns(Promise.resolve("TEST_TRX_ID2"));
        GameControllerBNSSpec.commitOperation.returns(
            Promise.reject(new ManagementAPIError(400, { responseStatus: 400, code: 100 } as any)));

        await expect(getSyncGameController().process({
            request: "spin",
            requestId: 2,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 100,
            coin: 33,
        })).to.be.rejectedWith(ManagementAPIError);

        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 2,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:bns:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 2,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": GameControllerBNSSpec.bnsGameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                currentBet: 1,
                betsCount: 1
            },
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "currencyMultiplier": 100,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
            },
            "specialState": undefined,
            "version": 6,
        });

        const spin1 = await getGameHistoryItem();
        expect(spin1).to.contain({
            "bet": 1,
            "roundEnded": false,
            "roundId": "0",
            "eventId": 0,
            "type": "slot",
            "win": 5,
            "currency": "BNS",
            "balanceAfter": 1004,
            "balanceBefore": 1000
        });

        const spin2 = await getGameHistoryItem();
        expect(spin2).is.undefined;

        roundHistory = await getRoundHistory(0, 1000);

        expect(roundHistory).deep.equal([]);
    }

    protected stubGame(game?: SomeGame) {
        GameControllerBNSSpec.loadGame.returns(Promise.resolve(new TestLoadResult(game || this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "defaultScene",
                    "nextScene": "nextScene",
                }
            ]))));
    }

    protected createGame(initResponses: GameInitResponse[],
                         contexts: any[] = [],
                         payments: PaymentInfo[] = [],
                         histories: GameHistory[] = [],
                         playResponses: GamePlayResponse[] = [],
                         info?: () => GameInfo): SomeGame {
        return new TestGame(initResponses, contexts, payments, histories, playResponses, info);
    }
}
