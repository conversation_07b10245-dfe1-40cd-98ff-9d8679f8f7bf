import { should, use } from "chai";
import { GameInitResponse } from "@skywind-group/sw-game-core";
import { suite, test } from "mocha-typescript";
import { GameControllerSpec } from "./gameController.spec";
import { mock } from "sinon";
import { TestGameWithMarkUpdate } from "../../testGames";
import {
    GameHistory,
    GameInfo,
    GamePlayResponse,
    PaymentInfo,
    SomeGame
} from "@skywind-group/sw-game-core";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("Game controller with defferedUpdate")
class GameControllerWithDeferredUpdateSpec extends GameControllerSpec {

    public extension: any = {
        beforeInit: mock(),
        afterInit: mock(),
        beforePlay: mock(),
        afterPlay: mock()
    };

    public async before(): Promise<any> {
        this.extension.beforeInit.reset();
        this.extension.afterInit.reset();
        this.extension.beforePlay.reset();
        this.extension.afterPlay.reset();

        this.extension.beforeInit.never();
        this.extension.afterInit.never();
        this.extension.beforePlay.never();
        this.extension.afterPlay.never();

        return super.before();
    }

    public async after(): Promise<void> {
        this.extension.beforeInit.verify();
        this.extension.afterInit.verify();
        this.extension.beforePlay.verify();
        this.extension.afterPlay.verify();
        return super.after();
    }

    @test("processes game 'init' request")
    public async processGameInitRequest() {
        this.extension.beforeInit.once();
        this.extension.afterInit.once();
        return super.processGameInitRequest();
    }

    @test("processes game 'init' request - game id not match")
    public async processesInit_GameIdNotMatch() {
        return super.processesInit_GameIdNotMatch();
    }

    @test("processes game 'init' request and load context form offline storage")
    public async processGameInit_And_LoadContextOffline() {
        this.extension.beforeInit.twice();
        this.extension.afterInit.twice();
        return super.processGameInit_And_LoadContextOffline();
    }

    @test("processes game 'init' request - fun mode")
    public async processGameInit_FunMode() {
        this.extension.beforeInit.once();
        this.extension.afterInit.once();
        return super.processGameInit_FunMode();
    }

    @test("processes game 'init' request custom limits")
    public async processGameInitCustomLimits() {
        this.extension.beforeInit.once();
        this.extension.afterInit.once();
        return super.processGameInitCustomLimits();
    }

    @test("processes two sequential 'spin' request")
    public async processTwoSequelntialSpins() {
        this.extension.beforeInit.once();
        this.extension.afterInit.once();
        this.extension.beforePlay.twice();
        this.extension.afterPlay.twice();
        return super.processTwoSequelntialSpins();

    }

    @test("processes two sequential 'spin' request - fun mode")
    public async processesTwoSequentialSpin_FunMode() {
        this.extension.beforeInit.once();
        this.extension.afterInit.once();
        this.extension.beforePlay.twice();
        this.extension.afterPlay.twice();
        return super.processesTwoSequentialSpin_FunMode();
    }

    @test("restores state of broken game: init new game session and make spins")
    public async restoreStateOfBrokenGame() {
        this.extension.beforeInit.twice();
        this.extension.afterInit.twice();
        this.extension.beforePlay.twice();
        this.extension.afterPlay.twice();
        return super.restoreStateOfBrokenGame();
    }

    protected createGame(initResponses: GameInitResponse[],
                         contexts: any[] = [],
                         payments: PaymentInfo[] = [],
                         histories: GameHistory[] = [],
                         playResponses: GamePlayResponse[] = [],
                         info?: () => GameInfo): SomeGame {
        return new TestGameWithMarkUpdate([this.extension],
            initResponses,
            contexts,
            payments,
            histories,
            playResponses,
            info);
    }
}
