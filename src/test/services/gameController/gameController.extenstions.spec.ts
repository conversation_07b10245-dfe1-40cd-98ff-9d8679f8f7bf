import { expect, should, use } from "chai";
import * as _ from "lodash";
import { GameData, StartGameResult } from "../../../skywind/services/auth";
import { TEST_MODULE_NAME } from "../../helper";
import { GameInitResponse } from "@skywind-group/sw-game-core";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { GameContextID } from "../../../skywind/services/contextIds";
import { getSyncGameController } from "../../../skywind/services/synccontroller";
import { GameSession } from "../../../skywind/services/gameSession";
import { suite, test } from "mocha-typescript";
import { BaseGameControllerSpec } from "./gameController.base.spec";
import { SinonStub, stub } from "sinon";
import { getJPNServer } from "../../../skywind/services/jpn/jpnserver";
import { init as initCurrencyExchange } from "../../../skywind/services/currencyexchange";
import { TestGameWithMarkUpdate, TestJackpotGameWithMarkUpdate } from "../../testGames";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("Game controller with global extensions")
export class GameControllerWithGlobalExtensionsSpec extends BaseGameControllerSpec {

    public static gameDataWithExtensions: GameData = {
        ...GameControllerWithGlobalExtensionsSpec.gameDataWithLimits,
        settings: {
            rtpConfigurator: {
                rtp: 95,
                rtpDeduction: 1
            },
        },
        jackpots: [{
            id: "mrkt1",
            isGameOwned: false,
            gameHistoryEnabled: false,
            paymentStatisticEnabled: false,
            contribution: 1
        }, {
            id: "mrkt2",
            isGameOwned: false,
            gameHistoryEnabled: false,
            paymentStatisticEnabled: false,
            contribution: 2
        }]
    };
    public static gameDataWithExtensionsAndJackpot: GameData = {
        ...GameControllerWithGlobalExtensionsSpec.gameDataWithLimits,
        settings: {
            jackpotId: "jp",
            rtpConfigurator: {
                rtp: 95,
                rtpDeduction: 1
            },
        },
        jackpots: [{
            id: "jp",
            isGameOwned: true,
            gameHistoryEnabled: true,
            paymentStatisticEnabled: true
        }, {
            id: "mrkt1",
            isGameOwned: false,
            gameHistoryEnabled: false,
            paymentStatisticEnabled: false,
            contribution: 1
        }, {
            id: "mrkt2",
            isGameOwned: false,
            gameHistoryEnabled: false,
            paymentStatisticEnabled: false,
            contribution: 2
        }, {
            id: "phantom",
            isGameOwned: false,
            gameHistoryEnabled: true,
            paymentStatisticEnabled: true
        }]
    };

    public static startGameResultWithExtensions = {
        gameData: GameControllerWithGlobalExtensionsSpec.gameDataWithExtensions,
        clientSettings: {}
    } as StartGameResult;

    public static startGameResultWithExtensionsAndJackpot = {
        gameData: GameControllerWithGlobalExtensionsSpec.gameDataWithExtensionsAndJackpot,
        clientSettings: {}
    } as StartGameResult;

    private jpnAuth: SinonStub;
    private jpnTickers: SinonStub;
    private jpnContribute: SinonStub;
    private jpnTrxId: SinonStub;

    public async before() {
        await super.before();
        await initCurrencyExchange();
        const jpnServer = getJPNServer();
        this.jpnAuth = stub(jpnServer, "auth");
        this.jpnTickers = stub(jpnServer, "getTickers");
        this.jpnContribute = stub(jpnServer, "contribute");
        this.jpnTrxId = stub(jpnServer, "generateTransactionId");
        GameControllerWithGlobalExtensionsSpec
            .startGameResultWithExtensions
            .gameData
            .gameTokenData = BaseGameControllerSpec.gameTokenData;

        GameControllerWithGlobalExtensionsSpec
            .startGameResultWithExtensionsAndJackpot
            .gameData
            .gameTokenData = BaseGameControllerSpec.gameTokenData;
    }

    public async after() {
        await super.after();
        this.jpnAuth.restore();
        this.jpnTickers.restore();
        this.jpnContribute.restore();
        this.jpnTrxId.restore();
    }

    @test("play game")
    public async playGame() {
        this.stubGame(new TestGameWithMarkUpdate([], [
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "defaultScene",
                    "nextScene": "nextScene",
                },
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
            ]
        ));

        BaseGameControllerSpec.startGame.returns(Promise.resolve(
            GameControllerWithGlobalExtensionsSpec.startGameResultWithExtensions));

        this.jpnAuth.returns({
            token: "jpn_auth_token",
            jackpots: [
                {
                    id: "mrkt1",
                    currency: "EUR",
                    jpGameId: "sw-marketing"
                },
                {
                    id: "mrkt2",
                    currency: "EUR",
                    jpGameId: "sw-marketing"
                }
            ]
        });

        this.jpnTickers.returns([
            {
                jackpotId: "mrkt1",
                pools: {},
            },
            {
                jackpotId: "mrkt2",
                pools: {},
            }
        ]);

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1000,
                },
            },
            "gameSession": initResponse.gameSession,
            "gameSettings": undefined,
            "brandSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": {
                "gameId": "test_slot",
                "name": "GAME!",
                "request": "init",
                "settings": {
                    "coins": [1],
                    "defaultCoin": 1,
                    "maxTotalStake": 500,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 10,
                    "stakeMin": 0.1,
                    "winMax": 3000000,
                    "currencyMultiplier": 100
                },
                "slot": {
                    "stub": "FORTESTREASON",
                },
                "previousResult": null,
                "stake": null
            },
            "roundEnded": true,
            renderType: 0,
            brandInfo: undefined,
        });

        expect(BaseGameControllerSpec.startGame.args[0][0]).equal(BaseGameControllerSpec.gameData.gameId);
        expect(BaseGameControllerSpec.startGame.args[0][1]).equal(BaseGameControllerSpec.startToken);
        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": GameControllerWithGlobalExtensionsSpec.gameDataWithExtensions,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "defaultScene",
                "nextScene": "nextScene",
            },
            "jpContext": {
                "contributionEnabled": true,
                "jackpotsInfo": [
                    {
                        "currency": "EUR",
                        "id": "mrkt1",
                        "jpGameId": "sw-marketing",
                        "isGameOwned": false,
                        "gameHistoryEnabled": false,
                        "paymentStatisticEnabled": false,
                        "contribution": 1
                    },
                    {
                        "currency": "EUR",
                        "id": "mrkt2",
                        "jpGameId": "sw-marketing",
                        "isGameOwned": false,
                        "gameHistoryEnabled": false,
                        "paymentStatisticEnabled": false,
                        "contribution": 2
                    }
                ],
                "token": "jpn_auth_token"
            },
            "version": 2,
            "roundId": "0",
            "specialState": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
                "jackpots": [],
                "rtpConfigurator": {
                    "rtp": 95,
                    "rtpDeduction": 1
                },
            },
        });

        this.jpnTrxId.returns({
            transactionId: "100",
        });

        this.jpnContribute.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "mrkt1",
                    jackpotType: "marketing",
                    pools: {
                        main: { amount: 0.01 }
                    },
                    totalContribution: 0.01,
                    contributions: [
                        { pool: "main", seed: 0.001, progressive: 0.009 },
                    ]
                }, {
                    type: "contribution",
                    jackpotId: "mrkt2",
                    jackpotType: "marketing",
                    pools: {
                        main: { amount: 0.02 }
                    },
                    totalContribution: 0.02,
                    contributions: [
                        { pool: "main", seed: 0.001, progressive: 0.009 },
                    ]
                }
            ],
        });

        const spinResponse = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        });

        expect(spinResponse).deep.equal({
            "balance": {
                "amount": 1004,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1004,
                }
            },
            "gameSession": initResponse.gameSession,
            "extraData": undefined,
            "requestId": 1,
            "result": {
                "request": "spin",
                "roundEnded": false,
                "totalBet": 1,
                "totalWin": 5,
            },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId");
        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": GameControllerWithGlobalExtensionsSpec.gameDataWithExtensions,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                "jpStatistic": {},
                currentBet: 1,
                betsCount: 1
            },
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2"
            },
            "jpContext": {
                "contributionEnabled": true,
                "jackpotsInfo": [
                    {
                        "currency": "EUR",
                        "id": "mrkt1",
                        "jpGameId": "sw-marketing",
                        "isGameOwned": false,
                        "gameHistoryEnabled": false,
                        "paymentStatisticEnabled": false,
                        "contribution": 1
                    },
                    {
                        "currency": "EUR",
                        "id": "mrkt2",
                        "jpGameId": "sw-marketing",
                        "isGameOwned": false,
                        "gameHistoryEnabled": false,
                        "paymentStatisticEnabled": false,
                        "contribution": 2
                    }
                ],
                "token": "jpn_auth_token"
            },
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
                "jackpots": [],
                "rtpConfigurator": {
                    "rtp": 95,
                    "rtpDeduction": 1
                },
            },
            "specialState": undefined,
            "version": 4,
        });

        delete this.jpnContribute.args[0][0].externalId;
        expect(this.jpnContribute.args[0][0]).to.deep.equal({
            "type": "contribution",
            "transactionId": "100",
            "roundId": "0",
            "amount": 1,
            "contributionPrecision": undefined,
            "deferredContribution": undefined,
            "exchangeRates": {
                "EUR": 0.87224
            },
            "jackpotIds": [
                "mrkt1",
                "mrkt2"
            ],
            "mrktContributionAmount_mrkt1": 0.01,
            "mrktContributionAmount_mrkt2": 0.02
        });
    }

    @test("play game with jackpots")
    public async playGameWithJackpot() {
        this.stubGame(new TestJackpotGameWithMarkUpdate([], [
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "defaultScene",
                    "nextScene": "nextScene",
                },
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
            ],
            [
                { type: "contribution", amount: 1 }
            ]
        ));

        BaseGameControllerSpec.startGame.returns(Promise.resolve(
            GameControllerWithGlobalExtensionsSpec.startGameResultWithExtensionsAndJackpot));

        this.jpnAuth.returns({
            token: "jpn_auth_token",
            jackpots: [
                {
                    id: "mrkt1",
                    currency: "EUR",
                    jpGameId: "sw-marketing"
                },
                {
                    id: "mrkt2",
                    currency: "EUR",
                    jpGameId: "sw-marketing"
                },
                {
                    id: "jp",
                    currency: "EUR",
                    jpGameId: "test"
                },
                {
                    id: "phantom",
                    currency: "CNY",
                    jpGameId: "test"
                }
            ]
        });

        this.jpnTickers.returns([
            {
                jackpotId: "mrkt1",
                jackpotType: "mrkt",
                jackpotBaseType: "baseMrkt",
                pools: {},
            },
            {
                jackpotId: "mrkt2",
                jackpotType: "mrkt",
                jackpotBaseType: "baseMrkt",
                pools: {},
            },
            {
                jackpotId: "jp",
                jackpotType: "test",
                jackpotBaseType: "base",
                pools: {},
            },
            {
                jackpotId: "phantom",
                jackpotType: "test",
                jackpotBaseType: "base",
                pools: {},
            }
        ]);

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            gameId: "test_slot",
            deviceId: "deviceId",
        });

        expect(initResponse).deep.equal({
            "balance": {
                "amount": 1000,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1000,
                },
            },
            "gameSession": initResponse.gameSession,
            "gameSettings": undefined,
            "brandSettings": undefined,
            "jrsdSettings": { a: 1 },
            "jurisdictionCode": "GB",
            "playedFromCountry": "GB",
            "result": {
                "gameId": "test_slot",
                "name": "GAME!",
                "request": "init",
                "settings": {
                    "coins": [1],
                    "defaultCoin": 1,
                    "maxTotalStake": 500,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 10,
                    "stakeMin": 0.1,
                    "winMax": 3000000,
                    "currencyMultiplier": 100
                },
                "slot": {
                    "stub": "FORTESTREASON",
                },
                "previousResult": null,
                "stake": null
            },
            "roundEnded": true,
            "ticker": {
                "jackpotId": "jp",
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {}
            },
            "tickers": [
                {
                    "jackpotId": "jp",
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pools": {}
                }
            ],
            renderType: 0,
            brandInfo: undefined,
        });

        expect(BaseGameControllerSpec.startGame.args[0][0]).equal(BaseGameControllerSpec.gameData.gameId);
        expect(BaseGameControllerSpec.startGame.args[0][1]).equal(BaseGameControllerSpec.startToken);
        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": GameControllerWithGlobalExtensionsSpec.gameDataWithExtensionsAndJackpot,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "defaultScene",
                "nextScene": "nextScene",
            },
            "jpContext": {
                "contributionEnabled": true,
                "jackpotsInfo": [
                    {
                        "id": "mrkt1",
                        "currency": "EUR",
                        "jpGameId": "sw-marketing",
                        "isGameOwned": false,
                        "gameHistoryEnabled": false,
                        "paymentStatisticEnabled": false,
                        "contribution": 1
                    },
                    {
                        "currency": "EUR",
                        "id": "mrkt2",
                        "jpGameId": "sw-marketing",
                        "isGameOwned": false,
                        "gameHistoryEnabled": false,
                        "paymentStatisticEnabled": false,
                        "contribution": 2
                    },
                    {
                        "currency": "EUR",
                        "id": "jp",
                        "jpGameId": "test",
                        "isGameOwned": true,
                        "gameHistoryEnabled": true,
                        "paymentStatisticEnabled": true
                    },
                    {
                        "currency": "CNY",
                        "gameHistoryEnabled": true,
                        "id": "phantom",
                        "isGameOwned": false,
                        "jpGameId": "test",
                        "paymentStatisticEnabled": true
                    }
                ],
                "token": "jpn_auth_token"
            },
            "version": 2,
            "roundId": "0",
            "specialState": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
                "jackpotId": "jp",
                "jackpots": [
                    {
                        "id": "jp",
                        "currency": "EUR",
                        "jpGameId": "test"
                    }
                ],
                "rtpConfigurator": {
                    "rtp": 95,
                    "rtpDeduction": 1
                },
            },
        });

        this.jpnTrxId.returns({
            transactionId: "100",
        });

        this.jpnContribute.returns({
            events: [
                {
                    type: "contribution",
                    jackpotId: "mrkt1",
                    jackpotType: "marketing",
                    jackpotBaseType: "baseMrkt",
                    pools: {
                        main: { amount: 0.01 }
                    },
                    totalContribution: 0.01,
                    contributions: [
                        { pool: "main", seed: 0.001, progressive: 0.009 },
                    ]
                }, {
                    type: "contribution",
                    jackpotId: "mrkt2",
                    jackpotType: "marketing",
                    jackpotBaseType: "baseMrkt",
                    pools: {
                        main: { amount: 0.02 }
                    },
                    totalContribution: 0.02,
                    contributions: [
                        { pool: "main", seed: 0.001, progressive: 0.009 },
                    ]
                }, {
                    type: "contribution",
                    jackpotId: "jp",
                    jackpotType: "test",
                    jackpotBaseType: "base",
                    pools: {
                        main: { amount: 10 }
                    },
                    totalContribution: 10,
                    contributions: [
                        { pool: "main", seed: 1, progressive: 9 },
                    ]
                }
            ],
        });

        const spinResponse = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        });

        expect(spinResponse).deep.equal({
            "balance": {
                "amount": 1004,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 1004,
                }
            },
            "gameSession": initResponse.gameSession,
            "extraData": undefined,
            "requestId": 1,
            "result": {
                "request": "spin",
                "roundEnded": false,
                "totalBet": 1,
                "totalWin": 5,
            },
            "roundEnded": false,
            "ticker": {
                "jackpotId": "jp",
                "jackpotType": "test",
                "jackpotBaseType": "base",
                "pools": {
                    "main": {
                        "amount": 10
                    }
                }
            },
            "tickers": [
                {
                    "jackpotId": "jp",
                    "jackpotType": "test",
                    "jackpotBaseType": "base",
                    "pools": {
                        "main": {
                            "amount": 10
                        }
                    }
                }
            ],
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId");
        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": GameControllerWithGlobalExtensionsSpec.gameDataWithExtensionsAndJackpot,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 1000,
                "balanceAfter": 1004,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 10,
                "totalJpWin": 0,
                currentBet: 1,
                betsCount: 1,
                "jpStatistic": {
                    "jp": {
                        "main": {
                            "contribution": {
                                "progressive": 9,
                                "seed": 1
                            },
                            "win": 0
                        }
                    }
                }
            },
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2"
            },
            "jpContext": {
                "contributionEnabled": true,
                "jackpotsInfo": [
                    {
                        "id": "mrkt1",
                        "currency": "EUR",
                        "jpGameId": "sw-marketing",
                        "isGameOwned": false,
                        "gameHistoryEnabled": false,
                        "paymentStatisticEnabled": false,
                        "contribution": 1
                    },
                    {
                        "currency": "EUR",
                        "id": "mrkt2",
                        "jpGameId": "sw-marketing",
                        "isGameOwned": false,
                        "gameHistoryEnabled": false,
                        "paymentStatisticEnabled": false,
                        "contribution": 2
                    },
                    {
                        "currency": "EUR",
                        "id": "jp",
                        "jpGameId": "test",
                        "isGameOwned": true,
                        "gameHistoryEnabled": true,
                        "paymentStatisticEnabled": true
                    },
                    {
                        "currency": "CNY",
                        "gameHistoryEnabled": true,
                        "id": "phantom",
                        "isGameOwned": false,
                        "jpGameId": "test",
                        "paymentStatisticEnabled": true
                    }
                ],
                "token": "jpn_auth_token"
            },
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
                "jackpotId": "jp",
                "jackpots": [
                    {
                        "currency": "EUR",
                        "id": "jp",
                        "jpGameId": "test"
                    }
                ],
                "rtpConfigurator": {
                    "rtp": 95,
                    "rtpDeduction": 1
                },
            },
            "specialState": undefined,
            "version": 4,
        });

        delete this.jpnContribute.args[0][0].externalId;
        expect(this.jpnContribute.args[0][0]).to.deep.equal({
            "type": "contribution",
            "transactionId": "100",
            "roundId": "0",
            "amount": 1,
            "contributionPrecision": undefined,
            "deferredContribution": undefined,
            "exchangeRates": {
                "CNY": 6.9475,
                "EUR": 0.87224
            },
            "jackpotIds": [
                "mrkt1",
                "mrkt2",
                "jp",
                "phantom"
            ],
            "mrktContributionAmount_mrkt1": 0.01,
            "mrktContributionAmount_mrkt2": 0.02
        });
    }
}
