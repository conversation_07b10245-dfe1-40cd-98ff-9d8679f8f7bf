import { expect, should, use } from "chai";
import { getSyncGameController } from "../../../skywind/services/synccontroller";
import { suite, test } from "mocha-typescript";
import { BaseGameControllerSpec } from "./gameController.base.spec";
import { GameInitResponse } from "@skywind-group/sw-game-core";
import { LogoutType } from "../../../skywind/services/auth";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { GameContextID } from "../../../skywind/services/contextIds";
import { MerchantLogoutResult } from "../../../skywind/services/context/gamecontext";
import { testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;
import status200 = testing.status200;

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("Game controller: login")
export class GameControllerLoginSpec extends BaseGameControllerSpec {

    private request: RequestMock;

    public async before() {
        this.request = requestMock(superagent);
        return super.before();
    }

    public after() {
        this.request.unmock(superagent);
        return super.after();
    }

    @test()
    public async loginOnInit() {
        this.request.post("http://api:3006//v2/play/game/login", status200({}));
        BaseGameControllerSpec.startGame.returns(
            Promise.resolve({
                gameData: {
                    ...BaseGameControllerSpec.gameDataWithLimits,
                    ...{ logoutOptions: { type: LogoutType.UNFINISHED } }
                },
                clientSettings: {}
            }));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        ));

        await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        await context.setLogoutResult(MerchantLogoutResult.REQUIRE_LOGIN);

        await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/game/login");
        expect(this.request.args.length).equals(1);

        context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));

        expect(context.logoutResult).is.undefined;
    }

    @test()
    public async skipLoginOnInit() {
        this.request.post("http://api:3006//v2/play/game/login", status200({}));
        BaseGameControllerSpec.startGame.returns(
            Promise.resolve({
                gameData: {
                    ...BaseGameControllerSpec.gameDataWithLimits,
                    ...{ logoutOptions: { type: LogoutType.UNFINISHED } }
                },
                clientSettings: {}
            }));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        ));

        await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        await context.setLogoutResult(MerchantLogoutResult.SKIP_LOGIN);

        await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        expect(this.request.args).is.undefined;

        context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));

        expect(context.logoutResult).is.undefined;
    }

    @test()
    public async failedtoInit_LogoutIsPending() {
        this.request.post("http://api:3006//v2/play/game/logout", status200({ requireLogin: true }));
        this.request.post("http://api:3006//v2/play/game/login", status200({}));
        BaseGameControllerSpec.startGame.returns(
            Promise.resolve({
                gameData: {
                    ...BaseGameControllerSpec.gameDataWithLimits,
                    ...{ logoutOptions: { type: LogoutType.UNFINISHED } }
                },
                clientSettings: {}
            }));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        ));

        await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        await context.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);

        await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        expect(this.request.args[0].url).is.equal("http://api:3006//v2/play/game/logout");
        expect(this.request.args[1].url).is.equal("http://api:3006//v2/play/game/login");

        context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));

        expect(context.logoutResult).is.undefined;
    }

    @test()
    public async logoutAndLoginOnInit() {
        this.request.post("http://api:3006//v2/play/game/logout", status200({
            requireLogin: true
        }));
        this.request.post("http://api:3006//v2/play/game/login", status200({}));
        BaseGameControllerSpec.startGame.returns(
            Promise.resolve({
                gameData: {
                    ...BaseGameControllerSpec.gameDataWithLimits,
                    ...{ logoutOptions: { type: LogoutType.ALL } }
                },
                clientSettings: {}
            }));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        ));

        await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/game/logout");
        expect(this.request.args[1].url).equals("http://api:3006//v2/play/game/login");
        expect(this.request.args.length).equals(2);

        const context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));

        expect(context.logoutResult).is.undefined;
    }

    @test()
    public async logoutWithoutLogin() {
        this.request.post("http://api:3006//v2/play/game/logout", status200({
            requireLogin: false
        }));
        BaseGameControllerSpec.startGame.returns(
            Promise.resolve({
                gameData: {
                    ...BaseGameControllerSpec.gameDataWithLimits,
                    ...{ logoutOptions: { type: LogoutType.ALL } }
                },
                clientSettings: {}
            }));
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        ));

        await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/game/logout");
        expect(this.request.args.length).equals(1);

        const context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));

        expect(context.logoutResult).is.undefined;
    }
}
