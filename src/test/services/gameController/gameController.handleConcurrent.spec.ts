import { ConcurrentAccessToGameSession } from "../../../skywind/errors";
import { should, use } from "chai";
import * as sinon from "sinon";
import { mock } from "sinon";
import {
    GameHistory,
    GameInfo,
    GameInitResponse,
    GamePlayResponse,
    PaymentInfo,
    SomeGame
} from "@skywind-group/sw-game-core";
import { getSyncGameController } from "../../../skywind/services/synccontroller";
import { suite, test } from "mocha-typescript";
import { BaseGameControllerSpec } from "./gameController.base.spec";
import { TestGameWithMarkUpdate } from "../../testGames";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("GameControllers - handles concurrent game accees")
class HandleConcurrentGameAccess extends BaseGameControllerSpec {

    @test("when we receive wrong requestID")
    public async receiveWrongRequestId() {
        BaseGameControllerSpec.startGame.returns(BaseGameControllerSpec.startGameResultWithLimits);
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
            ]
        ));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        /**
         * First spin
         */

        return getSyncGameController().process({
            request: "spin",
            requestId: 2,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 666,
            coin: 33,
        }).should.eventually.rejectedWith(ConcurrentAccessToGameSession);
    }

    @test("when player renew game session")
    public async renewSession() {
        BaseGameControllerSpec.startGame.returns(BaseGameControllerSpec.startGameResultWithLimits);
        const gameInitResponse = {
            "gameId": "test_slot",
            "name": "GAME!",
            "request": "init",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "slot": {
                "stub": "FORTESTREASON",
            },
            "previousResult": null,
            "stake": null,
        } as GameInitResponse;
        this.stubGame(this.createGame([
                gameInitResponse,
                gameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                }
            ]
        ));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        const clock = sinon.useFakeTimers();
        clock.tick(3600);

        // someone init new game session
        const initResponse2 = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: BaseGameControllerSpec.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });
        clock.restore();

        await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 666,
            coin: 33,
        }).should.eventually.rejectedWith(ConcurrentAccessToGameSession);
    }
}

@suite("GameControllers - handles concurrent game accees (game with markUpdate")
class HandleConcurrentGameAccessWithDefferedUpdate extends HandleConcurrentGameAccess {

    public extension: any = {
        beforeInit: mock(),
        afterInit: mock(),
        beforePlay: mock(),
        afterPlay: mock()
    };

    public async before(): Promise<any> {
        this.extension.beforeInit.reset();
        this.extension.afterInit.reset();
        this.extension.beforePlay.reset();
        this.extension.afterPlay.reset();

        this.extension.beforeInit.never();
        this.extension.afterInit.never();
        this.extension.beforePlay.never();
        this.extension.afterPlay.never();
        return super.before();
    }

    public async after(): Promise<void> {
        this.extension.beforeInit.verify();
        this.extension.afterInit.verify();
        this.extension.beforePlay.verify();
        this.extension.afterPlay.verify();
        return super.after();
    }

    @test("when we receive wrong requestID")
    public async receiveWrongRequestId() {
        this.extension.beforeInit.once();
        this.extension.afterInit.once();
        return super.receiveWrongRequestId();
    }

    @test("when player renew game session")
    public async renewSession() {
        this.extension.beforeInit.twice();
        this.extension.afterInit.twice();
        return super.renewSession();
    }

    protected createGame(initResponses: GameInitResponse[],
                         contexts: any[] = [],
                         payments: PaymentInfo[] = [],
                         histories: GameHistory[] = [],
                         playResponses: GamePlayResponse[] = [],
                         info?: () => GameInfo): SomeGame {
        return new TestGameWithMarkUpdate([this.extension],
            initResponses,
            contexts,
            payments,
            histories,
            playResponses,
            info);
    }
}
