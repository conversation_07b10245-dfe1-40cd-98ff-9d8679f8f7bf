import { expect, should, use } from "chai";
import * as _ from "lodash";
import { GameData, getService as getAuthservice, StartGameResult } from "../../../skywind/services/auth";
import {
    createGameToken,
    flushAll,
    getGameHistory,
    getRoundHistory,
    syncModels,
    TEST_MODULE_NAME,
    TestLoadResult
} from "../../helper";
import {
    GameHistory,
    GameInfo,
    GameInitResponse,
    GamePlayResponse,
    PaymentInfo,
    SomeGame
} from "@skywind-group/sw-game-core";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { GameTokenData, generateStartGameToken, Limits } from "../../../skywind/services/tokens";
import { GameContextID } from "../../../skywind/services/contextIds";
import { getSyncGameController } from "../../../skywind/services/synccontroller";
import { GameSession } from "../../../skywind/services/gameSession";
import { suite, test } from "mocha-typescript";
import { SinonStub, stub } from "sinon";
import * as GameService from "../../../skywind/services/game/game";
import { setRandomGeneratorFactory } from "../../..";
import { RandomGeneratorFactoryImpl } from "../../testRandomFactory";
import { getGameContextModel } from "../../../skywind/services/offlinestorage/models";
import { TestGame } from "../../testGames";
import { testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import { deepClone } from "../../../skywind/utils/cloner";
import { init } from "../../../skywind/services/currencyexchange";
import { ManagementAPITransientError } from "../../../skywind/errors";
import status200 = testing.status200;
import status400 = testing.status400;
import status500 = testing.status500;
import { PaymentOperation } from "../../../skywind/services/wallet";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("Game controller. DeferredPayments")
export class GameControllerWithDeferredPaymentsSpec {
    public startGame: SinonStub;
    public funStartGame: SinonStub;
    public loadGame: SinonStub;
    public startTokenData = {
        playerCode: "PL001",
        gameCode: "test_slot",
        providerGameCode: "test_slot",
        brandId: 1,
    };
    public gameData: GameData = {
        gameId: "test_slot",
        gameTokenData: undefined,
    } as GameData;
    public gameTokenData: GameTokenData = {
        playerCode: "PL001",
        gameCode: "test_slot",
        brandId: 1,
        currency: "USD",
        playmode: "real"
    };
    public gameDataWithLimits: GameData = {
        gameTokenData: undefined,
        gameMode: "real",
        playerCode: "PL001",
        gameCode: "test_slot",
        brandId: 1,
        currency: "USD",
        jrsdSettings: { a: 1 },
        jurisdictionCode: "GB",
        playedFromCountry: "GB",
        gameId: "test_slot",
        limits: {
            coins: [1],
            defaultCoin: 1,
            maxTotalStake: 500,
            stakeAll: [0.1, 0.5, 1, 2, 3, 5],
            stakeDef: 1,
            stakeMax: 10,
            stakeMin: 0.1,
            winMax: 3000000,
            currencyMultiplier: 100,
        } as Limits
    } as GameData;
    public startGameResultWithLimits = {
        gameData: this.gameDataWithLimits,
        clientSettings: {}
    } as StartGameResult;
    public startToken: string;
    public requestMock: testing.RequestMock;

    public async before(): Promise<any> {
        await syncModels();
        await init();

        this.startGame = stub(getAuthservice("real"), "startGame");
        this.funStartGame = stub(getAuthservice("fun"), "startGame");
        this.loadGame = stub(GameService, "load");
        this.startToken = await generateStartGameToken(this.startTokenData);
        this.gameTokenData.token = await createGameToken(this.gameTokenData);
        this.gameData.gameTokenData = this.gameTokenData;
        this.gameDataWithLimits.gameTokenData = this.gameTokenData;
        setRandomGeneratorFactory(new RandomGeneratorFactoryImpl());
        this.requestMock = testing.requestMock(superagent);
        await getGameContextModel().truncate();
        await flushAll();
    }

    public async after() {
        this.startGame.restore();
        this.funStartGame.restore();
        this.loadGame.restore();
        this.requestMock.unmock(superagent);
    }

    @test("processes a deferred payment")
    public async processDeferredPayment() {
        this.startGame.returns(this.startGameResultWithLimits);
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        ));

        this.requestMock.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
            deferredPayments: [
                {
                    id: 1,
                    amount: 1000,
                    currencyCode: "USD"
                }
            ]
        }));

        this.requestMock.put("http://api:3006//v2/play/payment/deferred", status200({
            currency: "USD",
            main: 200
        }));

        this.requestMock.post("http://api:3006//v2/play/payment/transactionId",
            status200({
                transactionId: "TRX_ID_1",
            }));

        this.requestMock.get("http://api:3006//v2/play/balance", status200({
            "currency": "USD",
            "main": 1000,
        }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: this.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": this.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2",
            },
            "jpContext": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined
        });

        const spinResponse = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        });

        expect(spinResponse).deep.equal({
            "balance": {
                "amount": 200,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 200,
                }
            },
            "gameSession": initResponse.gameSession,
            "extraData": undefined,
            "requestId": 1,
            "result": {
                "request": "spin",
                "roundEnded": false,
                "totalBet": 1,
                "totalWin": 5,
            },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId");
        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": this.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 100,
                "balanceAfter": 100,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                currentBet: 1,
                betsCount: 1
            },
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 6,
        });

        const rounds = await getRoundHistory(0, 100);
        expect(rounds.length).eq(1);
        expect(_.omit(rounds[0], "startedAt", "finishedAt", "ctrl")).deep.equals(
            {
                balanceAfter: 200,
                balanceBefore: 200,
                brandId: 1,
                broken: false,
                credit: 1000,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "test_slot",
                gameId: "test_slot",
                id: "1",
                playerCode: "PL001",
                sessionId: "0",
                totalBet: 0,
                totalEvents: 1,
                totalWin: 0,
            }
        );

        const items = await getGameHistory(0, 100);
        expect(items.length).eq(2);
        expect(_.omit(items[0], "ts", "ctrl")).deep.equals({
            sessionId: "0",
            gameId: "test_slot",
            gameVersion: "0.1.0",
            gameCode: "test_slot",
            brandId: 1,
            type: "deferred-payment",
            playerCode: "PL001",
            deviceId: "deviceId",
            roundEnded: true,
            walletTransactionId: "TRX_ID_1",
            eventId: 0,
            currency: "USD",
            win: 0,
            bet: 0,
            balanceBefore: 200,
            balanceAfter: 200,
            roundId: "1",
            result: {},
            totalJpWin: 0,
            totalJpContribution: 0,
            credit: 1000,
            totalRoundBet: 0,
            totalRoundWin: 0
        });
        expect(_.omit(items[1], "ts", "ctrl")).deep.equals({
            sessionId: "0",
            gameId: "test_slot",
            gameVersion: "0.1.0",
            gameCode: "test_slot",
            brandId: 1,
            type: "slot",
            playerCode: "PL001",
            deviceId: "deviceId",
            roundEnded: false,
            walletTransactionId: "TRX_ID_1",
            eventId: 0,
            currency: "USD",
            win: 5,
            bet: 1,
            balanceBefore: 100,
            balanceAfter: 100,
            roundId: "0",
            result: { positions: [1, 2, 3] },
            totalJpWin: 0,
            totalJpContribution: 0,
            totalRoundBet: 1,
            totalRoundWin: 5
        });

        expect(this.requestMock.args.map(c => c.url)).deep.equal(
            [
                "http://api:3006//v2/play/balance",
                "http://api:3006//v2/play/payment/transactionId",
                "http://api:3006//v2/play/payment",
                "http://api:3006//v2/play/payment/transactionId",
                "http://api:3006//v2/play/payment/deferred",
            ]
        );

        expect(this.requestMock.args[4].body.actions).deep.equal([
            {
                action: "credit",
                amount: 1000,
                attribute: "balance",
                changeType: "deferred-payment"
            }
        ]);

        expect(this.requestMock.args[4].body.deferredPayment).deep.equal({
            id: 1,
            amount: 1000,
            currencyCode: "USD"
        });

        this.requestMock.clearRoutes();

        this.requestMock.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 300,
        }));

        this.requestMock.post("http://api:3006//v2/play/payment/transactionId",
            status200({
                transactionId: "TRX_ID_2",
            }));

        const spinResponse2 = await getSyncGameController().process({
            request: "spin",
            requestId: 2,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 100,
            coin: 33,
        });

        expect(spinResponse2).deep.equal({
            "balance": {
                "amount": 300,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 300,
                }
            },
            "gameSession": initResponse.gameSession,
            "extraData": undefined,
            "requestId": 2,
            "result": {
                "request": "spin",
                "roundEnded": true,
                "totalBet": 5,
                "totalWin": 1,
            },
            "roundEnded": true,
            "roundTotalBet": 6,
            "roundTotalWin": 6
        });

        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 2,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 2,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": this.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "roundId": "2",
            "gameContext": {
                "currentScene": "scene3",
                "nextScene": "scene4",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 8,
        });

        const rounds1 = await getRoundHistory(0, 100);
        expect(rounds1.length).eq(2);
        expect(_.omit(rounds1[0], "startedAt", "finishedAt", "ctrl")).deep.equals(
            {
                balanceAfter: 300,
                balanceBefore: 100,
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "test_slot",
                gameId: "test_slot",
                id: "0",
                playerCode: "PL001",
                sessionId: "0",
                totalBet: 6,
                totalEvents: 2,
                totalJpContribution: 0,
                totalJpWin: 0,
                totalWin: 6
            }
        );

        const items1 = await getGameHistory(0, 100);
        expect(items1.length).eq(3);
        expect(_.omit(items1[0], "ts", "ctrl")).deep.equals({
            balanceAfter: 300,
            balanceBefore: 300,
            bet: 5,
            brandId: 1,
            currency: "USD",
            deviceId: "deviceId",
            eventId: 1,
            gameCode: "test_slot",
            gameId: "test_slot",
            gameVersion: "0.1.0",
            playerCode: "PL001",
            result: {
                positions: [4, 5, 6]
            },
            roundEnded: true,
            roundId: "0",
            sessionId: "0",
            totalJpContribution: 0,
            totalJpWin: 0,
            type: "slot",
            walletTransactionId: "TRX_ID_2",
            win: 1,
            totalRoundBet: 6,
            totalRoundWin: 6
        });
    }

    @test("processes multiple deferred payments, payment rolled back")
    public async processDeferredPayments_rollback() {
        this.startGame.returns(
            this.startGameResultWithLimits);
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        ));

        this.requestMock.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
            deferredPayments: [
                {
                    id: 1,
                    amount: 1000,
                    currencyCode: "USD"
                },
                {
                    id: 2,
                    amount: 2000,
                    currencyCode: "EUR"
                }
            ]
        }));

        this.requestMock.put("http://api:3006//v2/play/payment/deferred", status400({}));

        this.requestMock.post("http://api:3006//v2/play/payment/transactionId",
            status200({
                transactionId: "TRX_ID_1",
            }));

        this.requestMock.get("http://api:3006//v2/play/balance", status200({
            "currency": "USD",
            "main": 1000,
        }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: this.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": this.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2",
            },
            "jpContext": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined
        });

        const spinResponse = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        });

        expect(spinResponse).deep.equal({
            "balance": {
                "amount": 100,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 100,
                }
            },
            "gameSession": initResponse.gameSession,
            "extraData": undefined,
            "requestId": 1,
            "result": {
                "request": "spin",
                "roundEnded": false,
                "totalBet": 1,
                "totalWin": 5,
            },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId");
        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 3,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": this.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": false,
            "persistencePolicy": 0,
            "round": {
                "balanceBefore": 100,
                "balanceAfter": 100,
                "startedAt": context.round.startedAt,
                "totalBet": 1,
                "totalEvents": 1,
                "totalWin": 5,
                "totalJpContribution": 0,
                "totalJpWin": 0,
                currentBet: 1,
                betsCount: 1
            },
            "roundId": "0",
            "gameContext": {
                "currentScene": "scene2",
                "nextScene": "scene3",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 8,
        });

        const rounds = await getRoundHistory(0, 100);
        expect(rounds.length).eq(0);

        const items = await getGameHistory(0, 100);
        expect(items.length).eq(1);
        expect(_.omit(items[0], "ts", "ctrl")).deep.equals({
            sessionId: "0",
            gameId: "test_slot",
            gameVersion: "0.1.0",
            gameCode: "test_slot",
            brandId: 1,
            type: "slot",
            playerCode: "PL001",
            deviceId: "deviceId",
            roundEnded: false,
            walletTransactionId: "TRX_ID_1",
            eventId: 0,
            currency: "USD",
            win: 5,
            bet: 1,
            balanceBefore: 100,
            balanceAfter: 100,
            roundId: "0",
            result: { positions: [1, 2, 3] },
            totalJpWin: 0,
            totalJpContribution: 0,
            totalRoundBet: 1,
            totalRoundWin: 5
        });

        expect(this.requestMock.args.map(c => c.url)).deep.equal(
            [
                "http://api:3006//v2/play/balance",
                "http://api:3006//v2/play/payment/transactionId",
                "http://api:3006//v2/play/payment",
                "http://api:3006//v2/play/payment/transactionId",
                "http://api:3006//v2/play/payment/deferred",
                "http://api:3006//v2/play/payment/transactionId",
                "http://api:3006//v2/play/payment/deferred",
            ]
        );

        expect(this.requestMock.args[4].body.actions).deep.equal([
            {
                action: "credit",
                amount: 1000,
                attribute: "balance",
                changeType: "deferred-payment"
            }
        ]);

        expect(this.requestMock.args[4].body.deferredPayment).deep.equal({
            id: 1,
            amount: 1000,
            currencyCode: "USD"
        });

        expect(this.requestMock.args[6].body.actions).deep.equal([
            {
                action: "credit",
                amount: 2292.94,
                attribute: "balance",
                changeType: "deferred-payment"
            }
        ]);

        expect(this.requestMock.args[6].body.deferredPayment).deep.equal({
            id: 2,
            amount: 2000,
            currencyCode: "EUR"
        });

        this.requestMock.clearRoutes();

        this.requestMock.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 300,
        }));

        this.requestMock.post("http://api:3006//v2/play/payment/transactionId",
            status200({
                transactionId: "TRX_ID_2",
            }));

        const spinResponse2 = await getSyncGameController().process({
            request: "spin",
            requestId: 2,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 100,
            coin: 33,
        });

        expect(spinResponse2).deep.equal({
            "balance": {
                "amount": 300,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 300,
                }
            },
            "gameSession": initResponse.gameSession,
            "extraData": undefined,
            "requestId": 2,
            "result": {
                "request": "spin",
                "roundEnded": true,
                "totalBet": 5,
                "totalWin": 1,
            },
            "roundEnded": true,
            "roundTotalBet": 6,
            "roundTotalWin": 6
        });

        context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 2,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 4,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameData": this.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "roundId": "3",
            "gameContext": {
                "currentScene": "scene3",
                "nextScene": "scene4",
            },
            "jpContext": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 10,
        });

        const rounds1 = await getRoundHistory(0, 100);
        expect(rounds1.length).eq(1);
        expect(_.omit(rounds1[0], "startedAt", "finishedAt", "ctrl")).deep.equals(
            {
                balanceAfter: 300,
                balanceBefore: 100,
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "test_slot",
                gameId: "test_slot",
                id: "0",
                playerCode: "PL001",
                sessionId: "0",
                totalBet: 6,
                totalEvents: 2,
                totalJpContribution: 0,
                totalJpWin: 0,
                totalWin: 6,
            }
        );

        const items1 = await getGameHistory(0, 100);
        expect(items1.length).eq(2);
        expect(_.omit(items1[0], "ts", "ctrl")).deep.equals({
            balanceAfter: 300,
            balanceBefore: 300,
            bet: 5,
            brandId: 1,
            currency: "USD",
            deviceId: "deviceId",
            eventId: 1,
            gameCode: "test_slot",
            gameId: "test_slot",
            gameVersion: "0.1.0",
            playerCode: "PL001",
            result: {
                positions: [4, 5, 6]
            },
            roundEnded: true,
            roundId: "0",
            sessionId: "0",
            totalJpContribution: 0,
            totalJpWin: 0,
            type: "slot",
            walletTransactionId: "TRX_ID_2",
            win: 1,
            totalRoundBet: 6,
            totalRoundWin: 6
        });
    }

    @test("processes multiple deferred payments, payment rolled back")
    public async processDeferredPayments_broken_payment() {
        this.startGame.returns(
            this.startGameResultWithLimits);
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        ));

        this.requestMock.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
            deferredPayments: [
                {
                    id: 1,
                    amount: 1000,
                    currencyCode: "USD"
                },
                {
                    id: 2,
                    amount: 2000,
                    currencyCode: "EUR"
                }
            ]
        }));

        this.requestMock.put("http://api:3006//v2/play/payment/deferred", status500({}));

        this.requestMock.post("http://api:3006//v2/play/payment/transactionId",
            status200({
                transactionId: "TRX_ID_1",
            }));

        this.requestMock.get("http://api:3006//v2/play/balance", status200({
            "currency": "USD",
            "main": 1000,
        }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: this.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));
        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "gameData": this.gameDataWithLimits,
            "gameVersion": TEST_MODULE_NAME.version,
            "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2",
            },
            "jpContext": undefined,
            "version": 2,
            "roundId": "0",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined
        });

        await expect(getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        })).to.be.rejectedWith(ManagementAPITransientError);

        const rounds = await getRoundHistory(0, 100);
        expect(rounds.length).eq(0);

        const items = await getGameHistory(0, 100);
        expect(items.length).eq(1);
        expect(_.omit(items[0], "ts", "ctrl")).deep.equals({
            sessionId: "0",
            gameId: "test_slot",
            gameVersion: "0.1.0",
            gameCode: "test_slot",
            brandId: 1,
            type: "slot",
            playerCode: "PL001",
            deviceId: "deviceId",
            roundEnded: false,
            walletTransactionId: "TRX_ID_1",
            eventId: 0,
            currency: "USD",
            win: 5,
            bet: 1,
            balanceBefore: 100,
            balanceAfter: 100,
            roundId: "0",
            result: { positions: [1, 2, 3] },
            totalJpWin: 0,
            totalJpContribution: 0,
            totalRoundBet: 1,
            totalRoundWin: 5
        });

        context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));

        expect(_.omit(context,
            "createdAt",
            "updatedAt",
            "requestContext",
            "pendingModification.walletOperation.ts",
            "round.startedAt"))
            .deep
            .equal({
                "lastRequestId": 1,
                "_prev_sessionId": undefined,
                "_sessionId": GameSession.create(initResponse.gameSession),
                "id": GameContextID.createFromString("games:context:1:PL001:test_slot:deviceId"),
                "lockExclusively": false,
                "reactive": true,
                "gameSerialNumber": 1,
                "totalEventId": 1,
                "retryAttempts": undefined,
                "logoutResult": undefined,
                "logoutId": undefined,
                "gameData": this.gameDataWithLimits,
                "gameVersion": TEST_MODULE_NAME.version,
                "pendingModification": {
                    "history": {
                        "credit": 1000,
                        "data": {},
                        "roundEnded": false,
                        "separateRound": true,
                        "type": "deferred-payment"
                    },
                    "newState": {
                        "currentScene": "scene2",
                        "nextScene": "scene3",
                    },
                    "separateRoundId": "1",
                    "walletOperation": {
                        "actions": [
                            {
                                "action": "credit",
                                "amount": 1000,
                                "attribute": "balance",
                                "changeType": "deferred-payment"
                            }
                        ],
                        "amount": 1000,
                        "currency": "USD",
                        "deferredPayment": {
                            "amount": 1000,
                            "currencyCode": "USD",
                            "id": 1
                        },
                        "deviceId": "deviceId",
                        "eventId": 1,
                        "operation": "deferred-payment",
                        "retry": 1,
                        "roundEnded": true,
                        "roundId": "1",
                        "roundPID": "W4RkGRen",
                        "gameSessionId": "0",
                        "totalBet": 1,
                        "totalEventId": 1,
                        "totalWin": 5,
                        "transactionId": "TRX_ID_1",
                    }
                },
                "jackpotPending": undefined,
                "persistencePolicy": 0,
                "round": {
                    "balanceAfter": 100,
                    "balanceBefore": 100,
                    "totalBet": 1,
                    "totalEvents": 1,
                    "totalJpContribution": 0,
                    "totalJpWin": 0,
                    "totalWin": 5,
                    currentBet: 1,
                    betsCount: 1
                },
                "roundEnded": false,
                "roundId": "0",
                "gameContext": {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                "jpContext": undefined,
                "settings": {
                    "coins": [1],
                    "defaultCoin": 1,
                    "maxTotalStake": 500,
                    "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                    "stakeDef": 1,
                    "stakeMax": 10,
                    "stakeMin": 0.1,
                    "winMax": 3000000,
                    "currencyMultiplier": 100,
                },
                "specialState": undefined,
                "version": 6,
            });
    }

    @test("processes a deferred payment with split payment")
    public async processDeferredPaymentWithSplitPayment() {
        const splitPaymentStartData = deepClone(this.startGameResultWithLimits);
        splitPaymentStartData.gameData.settings = { splitPayment: true };
        this.startGame.returns(splitPaymentStartData);
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                { bet: 1, win: 5 },
                { bet: 5, win: 1 }
            ], [
                { type: "slot", roundEnded: false, data: { positions: [1, 2, 3] } },
                { type: "slot", roundEnded: true, data: { positions: [4, 5, 6] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5, roundEnded: false },
                { request: "spin", totalBet: 5, totalWin: 1, roundEnded: true },
            ]
        ));

        this.requestMock.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
            deferredPayments: [
                {
                    id: 1,
                    amount: 1000,
                    currencyCode: "USD"
                }
            ]
        }));

        this.requestMock.put("http://api:3006//v2/play/payment/deferred", status200({
            currency: "USD",
            main: 200
        }));

        this.requestMock.post("http://api:3006//v2/play/payment/transactionId",
            status200({
                transactionId: "TRX_ID_1",
            }));

        this.requestMock.get("http://api:3006//v2/play/balance", status200({
            "currency": "USD",
            "main": 1000,
        }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: this.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        let context = await getGameFlowContextManager()
            .findGameContextById(GameContextID.create("test_slot", 1, "PL001", "deviceId"));

        const spinResponse = await getSyncGameController().process({
            request: "spin",
            requestId: 1,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 1,
        });

        expect(spinResponse).deep.equal({
            "balance": {
                "amount": 200,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 200,
                }
            },
            "gameSession": initResponse.gameSession,
            "extraData": undefined,
            "requestId": 1,
            "result": {
                "request": "spin",
                "roundEnded": false,
                "totalBet": 1,
                "totalWin": 5,
            },
            "roundEnded": false,
            "roundTotalBet": 1,
            "roundTotalWin": 5
        });

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId");
        context = await getGameFlowContextManager().findGameContextById(gameID);

        const rounds = await getRoundHistory(0, 100);
        expect(rounds.length).eq(1);
        expect(_.omit(rounds[0], "startedAt", "finishedAt", "ctrl")).deep.equals(
            {
                balanceAfter: 200,
                balanceBefore: 200,
                brandId: 1,
                broken: false,
                credit: 1000,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "test_slot",
                gameId: "test_slot",
                id: "1",
                playerCode: "PL001",
                sessionId: "0",
                totalBet: 0,
                totalEvents: 1,
                totalWin: 0,
            }
        );

        const items = await getGameHistory(0, 100);
        expect(items.length).eq(2);
        expect(_.omit(items[0], "ts", "ctrl")).deep.equals({
            sessionId: "0",
            gameId: "test_slot",
            gameVersion: "0.1.0",
            gameCode: "test_slot",
            brandId: 1,
            type: "deferred-payment",
            playerCode: "PL001",
            deviceId: "deviceId",
            roundEnded: true,
            walletTransactionId: "TRX_ID_1",
            eventId: 0,
            currency: "USD",
            win: 0,
            bet: 0,
            balanceBefore: 200,
            balanceAfter: 200,
            roundId: "1",
            result: {},
            totalJpWin: 0,
            totalJpContribution: 0,
            credit: 1000,
            totalRoundBet: 0,
            totalRoundWin: 0
        });
        expect(_.omit(items[1], "ts", "ctrl")).deep.equals({
            sessionId: "0",
            gameId: "test_slot",
            gameVersion: "0.1.0",
            gameCode: "test_slot",
            brandId: 1,
            type: "slot",
            playerCode: "PL001",
            deviceId: "deviceId",
            roundEnded: false,
            walletTransactionId: "TRX_ID_1",
            eventId: 0,
            currency: "USD",
            win: 5,
            bet: 1,
            balanceBefore: 100,
            balanceAfter: 100,
            roundId: "0",
            result: { positions: [1, 2, 3] },
            totalJpWin: 0,
            totalJpContribution: 0,
            totalRoundBet: 1,
            totalRoundWin: 5
        });

        expect(this.requestMock.args.map(c => c.url)).deep.equal(
            [
                "http://api:3006//v2/play/balance",
                "http://api:3006//v2/play/payment/transactionId",
                "http://api:3006//v2/play/payment",
                "http://api:3006//v2/play/payment",
                "http://api:3006//v2/play/payment/transactionId",
                "http://api:3006//v2/play/payment/deferred",
            ]
        );

        expect(this.requestMock.args[5].body.actions).deep.equal([
            {
                action: "credit",
                amount: 1000,
                attribute: "balance",
                changeType: "deferred-payment"
            }
        ]);

        expect(this.requestMock.args[5].body.deferredPayment).deep.equal({
            id: 1,
            amount: 1000,
            currencyCode: "USD"
        });

        this.requestMock.clearRoutes();

        this.requestMock.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 300,
        }));

        this.requestMock.post("http://api:3006//v2/play/payment/transactionId",
            status200({
                transactionId: "TRX_ID_2",
            }));

        const spinResponse2 = await getSyncGameController().process({
            request: "spin",
            requestId: 2,
            gameSession: initResponse.gameSession,
            lines: 100,
            bet: 100,
            coin: 33,
        });

        expect(spinResponse2).deep.equal({
            "balance": {
                "amount": 300,
                "bonus": {
                    "amount": 0,
                },
                "currency": "USD",
                "real": {
                    "amount": 300,
                }
            },
            "gameSession": initResponse.gameSession,
            "extraData": undefined,
            "requestId": 2,
            "result": {
                "request": "spin",
                "roundEnded": true,
                "totalBet": 5,
                "totalWin": 1,
            },
            "roundEnded": true,
            "roundTotalBet": 6,
            "roundTotalWin": 6
        });

        context = await getGameFlowContextManager().findGameContextById(gameID);

        const rounds1 = await getRoundHistory(0, 100);
        expect(rounds1.length).eq(2);
        expect(_.omit(rounds1[0], "startedAt", "finishedAt", "ctrl")).deep.equals(
            {
                balanceAfter: 300,
                balanceBefore: 100,
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "test_slot",
                gameId: "test_slot",
                id: "0",
                playerCode: "PL001",
                sessionId: "0",
                totalBet: 6,
                totalEvents: 2,
                totalJpContribution: 0,
                totalJpWin: 0,
                totalWin: 6
            }
        );

        const items1 = await getGameHistory(0, 100);
        expect(items1.length).eq(3);
        expect(_.omit(items1[0], "ts", "ctrl")).deep.equals({
            balanceAfter: 300,
            balanceBefore: 300,
            bet: 5,
            brandId: 1,
            currency: "USD",
            deviceId: "deviceId",
            eventId: 1,
            gameCode: "test_slot",
            gameId: "test_slot",
            gameVersion: "0.1.0",
            playerCode: "PL001",
            result: {
                positions: [4, 5, 6]
            },
            roundEnded: true,
            roundId: "0",
            sessionId: "0",
            totalJpContribution: 0,
            totalJpWin: 0,
            type: "slot",
            walletTransactionId: "TRX_ID_2",
            win: 1,
            totalRoundBet: 6,
            totalRoundWin: 6
        });
    }

    @test("processes a deferred payment on init")
    public async processDeferredPaymentOnInit() {
        const startGameResultWithDeferredPayment = _.cloneDeep(this.startGameResultWithLimits);
        startGameResultWithDeferredPayment.gameData.balance = {
            currency: "USD",
            main: 100,
            deferredPayments: [
                {
                    id: 1,
                    amount: 1000,
                    currencyCode: "USD"
                }
            ] as any
        };
        this.startGame.returns(startGameResultWithDeferredPayment);
        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ], [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2"
                }
            ], [], [], []
        ));

        this.requestMock.put("http://api:3006//v2/play/payment/deferred", status200({
            currency: "USD",
            main: 1100,
            previousValue: 100
        }));

        this.requestMock.post("http://api:3006//v2/play/payment/transactionId",
            status200({
                transactionId: "TRX_ID_1",
            }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: this.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        expect(initResponse.balance.amount).to.equal(1100);

        const rounds = await getRoundHistory(0, 100);
        expect(rounds.length).eq(1);
        expect(_.omit(rounds[0], "startedAt", "finishedAt", "ctrl")).deep.equals(
            {
                balanceAfter: 1100,
                balanceBefore: 100,
                brandId: 1,
                broken: false,
                credit: 1000,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "test_slot",
                gameId: "test_slot",
                id: "1",
                playerCode: "PL001",
                sessionId: "0",
                totalBet: 0,
                totalEvents: 1,
                totalWin: 0,
            }
        );

        const items = await getGameHistory(0, 100);
        expect(items.length).eq(1);
        expect(_.omit(items[0], "ts", "ctrl")).deep.equals({
            sessionId: "0",
            gameId: "test_slot",
            gameVersion: "0.1.0",
            gameCode: "test_slot",
            brandId: 1,
            type: "deferred-payment",
            playerCode: "PL001",
            deviceId: "deviceId",
            roundEnded: true,
            walletTransactionId: "TRX_ID_1",
            eventId: 0,
            currency: "USD",
            win: 0,
            bet: 0,
            balanceBefore: 100,
            balanceAfter: 1100,
            roundId: "1",
            result: {},
            totalJpWin: 0,
            totalJpContribution: 0,
            credit: 1000,
            totalRoundBet: 0,
            totalRoundWin: 0
        });

        expect(this.requestMock.args.map(c => c.url)).deep.equal(
            [
                "http://api:3006//v2/play/payment/transactionId",
                "http://api:3006//v2/play/payment/deferred",
            ]
        );

        expect(this.requestMock.args[1].body.actions).deep.equal([
            {
                action: "credit",
                amount: 1000,
                attribute: "balance",
                changeType: "deferred-payment"
            }
        ]);

        expect(this.requestMock.args[1].body.deferredPayment).deep.equal({
            id: 1,
            amount: 1000,
            currencyCode: "USD"
        });
    }

    @test("processes a deferred payment on init with pending")
    public async processDeferredPaymentOnInitWithPending() {
        const startGameResultWithDeferredPayment = _.cloneDeep(this.startGameResultWithLimits);
        startGameResultWithDeferredPayment.gameData.balance = {
            currency: "USD",
            main: 100,
            deferredPayments: [
                {
                    id: 1,
                    amount: 1000,
                    currencyCode: "USD"
                }
            ] as any
        };
        this.startGame.returns(startGameResultWithDeferredPayment);
        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId");
        const sessionId = await GameSession.generate(gameID, "real");
        const context = await getGameFlowContextManager()
            .findOrCreateGameContext(gameID, sessionId, { gameTokenData: this.gameTokenData } as any, {} as any);
        await context.updatePendingModification({
            operation: "payment",
            transactionId: "1",
            bet: 10,
            win: 3000,
            currency: "USD",
            roundId: "0"
        } as PaymentOperation, {} as any, {} as any, { type: "spin1", roundEnded: true } as any);

        this.stubGame(this.createGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ], [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2"
                }
            ], [], [], []
        ));

        this.requestMock.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 3000,
            previousValue: 10,
            deferredPayments: [
                {
                    id: 1,
                    amount: 1000,
                    currencyCode: "USD"
                }
            ]
        }));

        this.requestMock.put("http://api:3006//v2/play/payment/deferred", status200({
            currency: "USD",
            main: 4000,
            previousValue: 3000
        }));

        this.requestMock.post("http://api:3006//v2/play/payment/transactionId",
            status200({
                transactionId: "TRX_ID_1",
            }));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: this.startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        expect(initResponse.balance.amount).to.equal(4000);

        const rounds = await getRoundHistory(0, 100);
        expect(rounds.length).eq(2);
        expect(_.omit(rounds[1], "startedAt", "finishedAt", "ctrl")).deep.equals(
            {
                balanceAfter: 3000,
                balanceBefore: 10,
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "test_slot",
                gameId: "test_slot",
                id: "0",
                playerCode: "PL001",
                sessionId: "1",
                totalBet: 10,
                totalEvents: 1,
                totalWin: 3000,
                totalJpContribution: 0,
                totalJpWin: 0
            }
        );
        expect(_.omit(rounds[0], "startedAt", "finishedAt", "ctrl")).deep.equals(
            {
                balanceAfter: 4000,
                balanceBefore: 3000,
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "test_slot",
                gameId: "test_slot",
                id: "2",
                playerCode: "PL001",
                credit: 1000,
                sessionId: "1",
                totalBet: 0,
                totalWin: 0,
                totalEvents: 1
            }
        );

        const items = await getGameHistory(0, 100);
        expect(items.length).eq(2);

        expect(this.requestMock.args.map(c => c.url)).deep.equal(
            [
                "http://api:3006//v2/play/payment",
                "http://api:3006//v2/play/payment/transactionId",
                "http://api:3006//v2/play/payment/deferred",
            ]
        );
    }

    protected stubGame(game?: SomeGame) {
        this.loadGame
            .returns(Promise.resolve(new TestLoadResult(game || this.createGame(
                [
                    {
                        "gameId": "test_slot",
                        "name": "GAME!",
                        "request": "init",
                        "settings": {
                            "coins": [1],
                            "defaultCoin": 1,
                            "maxTotalStake": 500,
                            "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                            "stakeDef": 1,
                            "stakeMax": 10,
                            "stakeMin": 0.1,
                            "winMax": 3000000,
                            "currencyMultiplier": 100,
                        },
                        "slot": {
                            "stub": "FORTESTREASON",
                        },
                        "previousResult": null,
                        "stake": null,
                    } as GameInitResponse
                ],
                [
                    {
                        "currentScene": "defaultScene",
                        "nextScene": "nextScene",
                    }
                ]))));
    }

    protected createGame(initResponses: GameInitResponse[],
                         contexts: any[] = [],
                         payments: PaymentInfo[] = [],
                         histories: GameHistory[] = [],
                         playResponses: GamePlayResponse[] = [],
                         info?: () => GameInfo): SomeGame {
        return new TestGame(initResponses, contexts, payments, histories, playResponses, info);
    }
}
