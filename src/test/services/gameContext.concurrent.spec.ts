import { expect, should, use } from "chai";
import * as Errors from "../../skywind/errors";
import { ConcurrentAccessToGameSession } from "../../skywind/errors";
import { GameFlowContext } from "../../skywind/services/context/gamecontext";
import { getGameHistoryItem, TEST_MODULE_NAME } from "../helper";
import { PaymentOperation } from "../../skywind/services/wallet";
import { BaseGameContextSpec } from "./gameContext.spec";
import { GameSession } from "../../skywind/services/gameSession";
import { suite, test } from "mocha-typescript";
import { GameFlowInfoImpl } from "../../skywind/services/context/gameFlowInfo";

const chaiAsPromise = require("chai-as-promised");

should();
use(chaiAsPromise);

@suite("GameContext.ConcurrentAccessToGameSession")
class GameContextConcurrentAccessSpec extends BaseGameContextSpec {
    public async before() {
        await super.before();
        await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME);
    }

    @test("when requestId is stale")
    public async testRequestIdIsStale() {
        const originGameContext: GameFlowContext = await this.contextManager.findGameContextById(this.gameID);

        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };

        expect(originGameContext).contain(expected);
        expect(originGameContext.gameData).deep.equal(this.gameData);
        const gameContext = await this.contextManager.findGameContextById(this.gameID);
        gameContext.lastRequestId = gameContext.lastRequestId + 1;
        await gameContext.update();

        await expect(originGameContext.update())
            .to.be.rejectedWith(Errors.ConcurrentAccessToGameSession);
        expect(originGameContext.id).is.undefined;
        expect(originGameContext.corrupted).to.be.true;

        await expect(originGameContext.update())
            .to.be.rejectedWith(Errors.GameContextStateIsBroken);
    }

    @test("wrong sessionId is stale")
    public async testWrongSessionIdIsStale() {
        const originGameContext: GameFlowContext = await this.contextManager.findGameContextById(this.gameID);

        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };
        expect(originGameContext).contain(expected);
        expect(originGameContext.gameData).deep.equal(this.gameData);

        const gameContext = await this.contextManager.findGameContextById(this.gameID);
        gameContext.session = await GameSession.generate(this.gameID, "real");
        await gameContext.update();

        await expect(originGameContext.update())
            .to.be.rejectedWith(Errors.ConcurrentAccessToGameSession);
        expect(originGameContext.id).is.undefined;
        expect(originGameContext.corrupted).to.be.true;

        await expect(originGameContext.update())
            .to.be.rejectedWith(Errors.GameContextStateIsBroken);

    }

    @test("when walletTransactionID is stale")
    public async testWalletTransactionIdIsStale() {
        const originGameContext: GameFlowContext = await this.contextManager.findGameContextById(this.gameID);

        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };
        expect(originGameContext).contain(expected);
        expect(originGameContext.gameData).deep.equal(this.gameData);

        const gameContext = await this.contextManager.findGameContextById(this.gameID);

        const newState = {
            nextScene: "SOMESCENE3"
        };

        await gameContext.updatePendingModification(
            { operation: "payment", transactionId: "1", bet: 100, win: 20, currency: "USD" } as PaymentOperation,
            newState, { request: "req", requestId: 1 });
        await expect(originGameContext.update())
            .to.be.rejectedWith(Errors.ConcurrentAccessToGameSession);
        expect(originGameContext.id).is.undefined;
        expect(originGameContext.corrupted).to.be.true;

        await expect(originGameContext.update())
            .to.be.rejectedWith(Errors.GameContextStateIsBroken);
    }

    @test("when we made rollback modifications")
    public async testRollback() {
        const originGameContext: GameFlowContext = await this.contextManager.findGameContextById(this.gameID);

        const expected = {
            id: this.gameID,
            lastRequestId: 0,

        };
        expect(originGameContext).contain(expected);
        expect(originGameContext.gameData).deep.equal(this.gameData);

        const gameContext = await this.contextManager.findGameContextById(this.gameID);

        const newState = {
            nextScene: "SOMESCENE3"
        };

        await gameContext.updatePendingModification(
            { operation: "payment", transactionId: "1", bet: 100, win: 20, currency: "USD" } as PaymentOperation,
            newState, { request: "req", requestId: 1 });

        await gameContext.rollbackPendingModification();

        await expect(originGameContext.update(originGameContext.gameContext))
            .to.be.rejectedWith(Errors.ConcurrentAccessToGameSession);
        expect(originGameContext.id).is.undefined;
        expect(originGameContext.corrupted).to.be.true;

        await expect(originGameContext.update())
            .to.be.rejectedWith(Errors.GameContextStateIsBroken);
    }

    @test("when it was deleted")
    public async testWhenRemoved() {
        const originGameContext: GameFlowContext = await this.contextManager.findGameContextById(this.gameID);

        await originGameContext.remove();

        await expect(originGameContext.update())
            .to.be.rejectedWith(Errors.ConcurrentAccessToGameSession);
        expect(originGameContext.id).is.undefined;
        expect(originGameContext.corrupted).to.be.true;

        await expect(originGameContext.update())
            .to.be.rejectedWith(Errors.GameContextStateIsBroken);
    }

    @test("and history item isn't saved")
    public async testNotSaveHistory() {
        const originGameContext: GameFlowContext = await this.contextManager.findGameContextById(this.gameID);

        const request = { request: "req", requestId: 1 };

        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };
        expect(originGameContext).contain(expected);
        expect(originGameContext.gameData).deep.equal(this.gameData);

        const history1: any = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: {
                positions: [1, 2, 3, 4],
            }
        };
        const newState = {
            nextScene: "SOMESCENE3"
        };

        const walletOperation: any = {
            operation: "payment",
            transactionId: "1",
            bet: 100,
            win: 20,
            roundId: originGameContext.roundId,
            roundPID: new GameFlowInfoImpl(originGameContext).roundPID,
            roundEnded: history1.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        await originGameContext.updatePendingModification(
            walletOperation,
            newState,
            request,
            history1);
        walletOperation.ts = walletOperation.ts.toISOString();
        const gameContext = await this.contextManager.findGameContextById(this.gameID);
        expect(gameContext).contain(expected);
        expect(gameContext.gameData).deep.equal(this.gameData);
        expect(gameContext.pendingModification).deep.equal({
            "history": history1,
            "newState": newState,
            "walletOperation": walletOperation
        });

        gameContext.lastRequestId = gameContext.lastRequestId + 1;
        await gameContext.update();
        try {
            await originGameContext.commitPendingModification();

            expect.fail(undefined, ConcurrentAccessToGameSession);
        } catch (err) {
            if (!(err instanceof ConcurrentAccessToGameSession)) {
                expect.fail(err, ConcurrentAccessToGameSession);
            }
        }

        const savedHistoryItem = await getGameHistoryItem();
        expect(savedHistoryItem).is.undefined;
    }

    @test("isn't removed because was concurrently updated")
    public async testNotRemoved() {
        const originGameContext: GameFlowContext = await this.contextManager.findGameContextById(this.gameID);

        const gameContext = await this.contextManager.findGameContextById(this.gameID);
        const newState = {
            nextScene: "SOMESCENE3"
        };

        await gameContext.updatePendingModification(
            { operation: "payment", transactionId: "1", bet: 100, win: 20, currency: "USD" } as PaymentOperation,
            newState, { request: "req", requestId: 1 });

        await expect(originGameContext.remove())
            .to.be.rejectedWith(Errors.ConcurrentAccessToGameSession);
        expect(originGameContext.id).is.undefined;
        expect(originGameContext.corrupted).to.be.true;

        await expect(originGameContext.update())
            .to.be.rejectedWith(Errors.GameContextStateIsBroken);
    }

    @test("on manager.createOrUpdate, when context was updated before")
    public async testСreateOrUpdate_1() {
        const originGameContext: GameFlowContext = await this.contextManager.findGameContextById(this.gameID);
        const ctx: GameFlowContext = await this.contextManager.findGameContextById(this.gameID);
        await originGameContext.update({ type: "new_context" });

        await expect(this.contextManager.createOrUpdate(originGameContext.id,
            ctx,
            await GameSession.generate(this.gameID, this.gameData.gameTokenData.playmode),
            this.gameData,
            TEST_MODULE_NAME)).to.be.rejectedWith(Errors.ConcurrentAccessToGameSession);
    }

    @test("on manager.createOrUpdate, when context was created before")
    public async testСreateOrUpdate_2() {
        const originGameContext: GameFlowContext = await this.contextManager.findGameContextById(this.gameID);
        await expect(this.contextManager.createOrUpdate(originGameContext.id, undefined,
            await GameSession.generate(this.gameID, this.gameData.gameTokenData.playmode),
            this.gameData,
            TEST_MODULE_NAME)).to.be.rejectedWith(Errors.ConcurrentAccessToGameSession);
    }

    @test("on manager.createOrUpdate, when context was deleted before")
    public async testСreateOrUpdate_3() {
        const originGameContext: GameFlowContext = await this.contextManager.findGameContextById(this.gameID);
        await this.contextManager.findGameContextById(this.gameID).then(ctx => ctx.remove());
        await expect(this.contextManager.createOrUpdate(originGameContext.id, originGameContext,
            await GameSession.generate(this.gameID, this.gameData.gameTokenData.playmode),
            this.gameData,
            TEST_MODULE_NAME)).to.be.rejectedWith(Errors.ConcurrentAccessToGameSession);
    }

}
