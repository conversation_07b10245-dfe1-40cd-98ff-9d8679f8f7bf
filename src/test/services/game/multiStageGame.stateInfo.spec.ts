import { expect, should, use } from "chai";

import { MultiStageGame } from "../../../skywind/services/game/multiStageGame";
import { BaseMultiStageGameSpec } from "./mockedFlow";

import { suite, test } from "mocha-typescript";
import { mock } from "sinon";
import { setRandomGeneratorFactory } from "../../..";
import { RandomGeneratorFactoryImpl } from "../../testRandomFactory";

use(require("chai-as-promised"));
use(should);

@suite("MultiStageGame return stateInfo")
class MultiStageGamePlaySpec extends BaseMultiStageGameSpec {

    public before() {
        setRandomGeneratorFactory(new RandomGeneratorFactoryImpl());
    }

    public extension: any = {
        beforeInit: mock(),
        afterInit: mock(),
        beforeStateInfo: mock(),
        afterStateInfo: mock(),
    };

    @test("returns stateInfo for game and stages")
    public async getStateInfo() {
        const game: any = {
            getExtensionModules: () => {
                return [this.extension];
            },
            requests: [],
            stateInfo: async function() {
                return {
                    gameState: {}
                };
            }
        };
        const stage1 = {
            stateInfo: async function() {
                return {
                    stage1Response: {
                        winChallenge: true
                    }
                };
            },

            finalize: mock()

        };
        const stage2 = {
            stateInfo: function() {
                return {
                    stage2Response: {
                        winTournament: true
                    }
                };
            },
            finalize: mock()
        };

        const stage3 = {
            stateInfo: function() {
                return undefined;
            },
            finalize: mock()
        };

        this.flow.states.push({
            stagePolicies: [0, 0],
            stagesContexts: [{}, {}],
            gameContext: {},
        });
        const multiStageGame = new MultiStageGame(game, [stage1 as any, stage2 as any, stage3 as any]);

        this.extension.beforeStateInfo.once().returns(Promise.resolve(undefined));
        this.extension.afterStateInfo.once().returns(Promise.resolve(undefined));
        stage1.finalize.once();
        stage2.finalize.once();
        stage3.finalize.once();

        const result = await multiStageGame.stateInfo(this.flow as any);
        expect(result).deep.equals({
            "gameState": {},
            "stage1Response": {
                "winChallenge": true,
            },
            "stage2Response": {
                "winTournament": true,
            }
        });

        this.extension.beforeStateInfo.verify();
        this.extension.afterStateInfo.verify();
        stage1.finalize.verify();
        stage2.finalize.verify();
        stage3.finalize.verify();
        expect(this.extension.afterStateInfo.args[0][2]).deep.equals(await game.stateInfo());
    }

    @test("returns stateInfo without game state info")
    public async getStateInfoWithoutGameInfo() {
        const game: any = {
            getExtensionModules: () => {
                return [this.extension];
            },
            requests: []
        };
        const stage1 = {
            stateInfo: async function() {
                return {
                    stage1Response: {
                        winChallenge: true
                    }
                };
            },
            finalize: mock()

        };
        const stage2 = {
            stateInfo: function() {
                return {
                    stage2Response: {
                        winTournament: true
                    }
                };
            },
            finalize: mock()
        };

        const stage3 = {
            stateInfo: function() {
                return undefined;
            },
            finalize: mock()
        };

        this.flow.states.push({
            stagePolicies: [0, 0],
            stagesContexts: [{}, {}],
            gameContext: {},
        });
        const multiStageGame = new MultiStageGame(game, [stage1 as any, stage2 as any, stage3 as any]);

        this.extension.beforeStateInfo.never();
        this.extension.afterStateInfo.never();
        stage1.finalize.once();
        stage2.finalize.once();
        stage3.finalize.once();
        const result = await multiStageGame.stateInfo(this.flow as any);
        expect(result).deep.equals({
            "stage1Response": {
                "winChallenge": true,
            },
            "stage2Response": {
                "winTournament": true,
            }
        });

        this.extension.beforeStateInfo.verify();
        this.extension.afterStateInfo.verify();
        stage1.finalize.verify();
        stage2.finalize.verify();
        stage3.finalize.verify();
    }
}
