import { expect, should, use } from "chai";
import { mock } from "sinon";
import { LogLevel, PaymentInfo, SomeGameFlow, SomeStageFlow } from "@skywind-group/sw-game-core";

import { MultiStageGame } from "../../../skywind/services/game/multiStageGame";
import { BaseMultiStageGameSpec } from "./mockedFlow";

import { suite, test } from "mocha-typescript";

use(require("chai-as-promised"));
use(should);

@suite("MultiStageGame.play")
class MultiStageGamePlaySpec extends BaseMultiStageGameSpec {
    @test("simple")
    public async simpleScenario() {
        const game: any = {
            requests: [],
            play: async function(gameFlow: SomeGameFlow<any>) {
                this.requests.push(gameFlow.request());
                await gameFlow.update({
                        newState: {
                            value: "someValue"
                        }
                    },
                    {
                        actions: [
                            {
                                action: "debit",
                                attribute: "balance",
                                amount: 100,
                            }
                        ]
                    },
                    {
                        type: "slot",
                        roundEnded: true,
                        data: {}
                    });
                return {
                    gameResponse: {
                        played: true
                    }
                };
            }
        };
        const stage1 = {
            play: mock(),
            finalize: mock()
        };
        const stage2 = {
            play: mock(),
            finalize: mock()
        };

        this.flow.states.push({
            stagesPolicy: [0, 0],
            stagesContexts: [{}, {}],
            gameContext: {},
        });
        const multiStageGame = new MultiStageGame(game, [stage1 as any, stage2 as any]);

        this.flow.request.twice().returns({
            request: "play"
        });

        stage1.play.once().returns(Promise.resolve({
            stage1Response: {
                played: true
            }
        }));

        stage2.play.once().returns(Promise.resolve({
            stage2Response: {
                played: true
            }
        }));

        stage1.finalize.once().returns(Promise.resolve(undefined));
        stage2.finalize.once().returns(Promise.resolve(undefined));

        const result = await multiStageGame.play(this.flow as any);
        expect(result).deep.equals({
            "gameResponse": {
                "played": true,
            },
            "stage1Response": {
                "played": true,
            },
            "stage2Response": {
                "played": true,
            }
        });

        const flowId = this.flow.states[1].flowId;

        expect(this.flow.updates).deep.equals([
            {
                "context": {
                    "flowId": flowId,
                    "flowType": 2,
                    "step": undefined,
                    "gameContext": {
                        "newState": {
                            "value": "someValue"
                        }
                    },
                    "policy": 0,
                    "request": {
                        "request": "play"
                    },
                    "stagePolicies": [0, 0],
                    "stagesContexts": [{}, {}],
                    "stagesPolicy": [0, 0],
                    "totalBet": 100,
                    "winAmount": 0
                },
                "jackpotAction": undefined,
                "history": {
                    "roundEnded": true,
                    "type": "slot",
                    "data": {}
                },
                "payment": {
                    "actions": [
                        {
                            "action": "debit",
                            "amount": 100,
                            "attribute": "balance",
                        }
                    ]
                }
            },
            {
                "context": {
                    "flowId": undefined,
                    "flowType": undefined,
                    "step": undefined,
                    "gameContext": {
                        "newState": {
                            "value": "someValue"
                        }
                    },
                    "policy": undefined,
                    "request": undefined,
                    "stagePolicies": [0, 0],
                    "stagesContexts": [{}, {}],
                    "stagesPolicy": [0, 0],
                    "winAmount": undefined,
                    "totalBet": undefined,
                }
            }
        ]);
        stage1.play.verify();
        stage2.play.verify();
        stage1.finalize.verify();
        stage2.finalize.verify();
    }

    @test("with omit one stage")
    public async withOmitOneStage() {
        const game: any = {
            requests: [],
            play: async function(gameFlow: SomeGameFlow<any>) {
                this.requests.push(gameFlow.request());
                await gameFlow.update({
                        newState: {
                            value: "someValue"
                        }
                    },
                    {
                        actions: [
                            {
                                action: "debit",
                                attribute: "balance",
                                amount: 100,
                            }
                        ]
                    },
                    {
                        type: "slot",
                        roundEnded: true,
                        data: {}
                    });
                return {
                    gameResponse: {
                        played: true
                    }
                };
            }
        };
        const stage1 = {
            play: mock(),
            finalize: mock()
        };
        const stage2 = {
            play: mock(),
            finalize: mock()
        };

        this.flow.states.push({
            stagesPolicy: [0, 0],
            stagesContexts: [{}, {}],
            gameContext: {},
        });
        const multiStageGame = new MultiStageGame(game, [stage1 as any, stage2 as any]);

        this.flow.request.twice().returns({
            request: "play"
        });

        stage1.play.once().returns(undefined);

        stage2.play.once().returns(Promise.resolve({
            stage2Response: {
                played: true
            }
        }));

        stage1.finalize.once().returns(Promise.resolve(undefined));
        stage2.finalize.once().returns(Promise.resolve(undefined));

        const result = await multiStageGame.play(this.flow as any);
        expect(result).deep.equals({
            "gameResponse": {
                "played": true,
            },
            "stage2Response": {
                "played": true,
            }
        });

        const flowId = this.flow.states[1].flowId;

        expect(this.flow.updates).deep.equals([
            {
                context:
                    {
                        stagesPolicy: [0, 0],
                        stagesContexts: [{}, {}],
                        gameContext: { newState: { value: "someValue" } },
                        flowId: flowId,
                        flowType: 2,
                        step: undefined,
                        stagePolicies: [0, 0],
                        request: { request: "play" },
                        totalBet: 100,
                        winAmount: 0,
                        policy: 0
                    },
                history: { type: "slot", roundEnded: true, data: {} },
                payment: { actions: [{ action: "debit", attribute: "balance", amount: 100 }] },
                jackpotAction: undefined
            },
            {
                context:
                    {
                        stagesPolicy: [0, 0],
                        stagesContexts: [{}, {}],
                        gameContext: { newState: { value: "someValue" } },
                        flowId: undefined,
                        step: undefined,
                        flowType: undefined,
                        stagePolicies: [0, 0],
                        request: undefined,
                        policy: undefined,
                        totalBet: undefined,
                        winAmount: undefined
                    }
            }
        ]);
        stage1.play.verify();
        stage2.play.verify();
        stage1.finalize.verify();
        stage2.finalize.verify();
    }

    @test("with changing storage policy, setup stage context")
    public async changePolicy() {
        const game: any = {
            requests: [],
            play: async function(gameFlow: SomeGameFlow<any>) {
                this.requests.push(gameFlow.request());
                // test  method delegation
                gameFlow.pushService();
                await gameFlow.getBalance();
                gameFlow.log(LogLevel.Debug, "TestMessage");
                gameFlow.rng();
                gameFlow.settings();

                const currentContext = await gameFlow.gameContext();
                expect(currentContext).is.not.undefined;
                expect(gameFlow.info()).is.not.undefined;
                gameFlow.exchange(10, "USD");
                gameFlow.setPersistencePolicy(2);
                expect(gameFlow.persistencePolicy()).is.equals(2);

                await gameFlow.update({
                        newState: {
                            value: "someValue"
                        }
                    },
                    {
                        actions: [
                            {
                                action: "debit",
                                attribute: "balance",
                                amount: 100,
                            }
                        ]
                    },
                    {
                        type: "slot",
                        roundEnded: true,
                        data: {}
                    });
                return {
                    gameResponse: {
                        played: true
                    }
                };
            }
        };
        const stage1 = {
            play: async function(stageFlow: SomeStageFlow) {
                const context = { processed: true };
                const payment: PaymentInfo = { actions: [{ action: "debit", attribute: "balance", amount: 2000 }] };
                const history = { type: "challenge", roundEnded: false, data: { chanllenge: true } };
                stageFlow.persistencePolicy = 2;

                // test delegation methods
                stageFlow.pushService;
                await stageFlow.balance;
                stageFlow.log(LogLevel.Info, "TestMessage1");
                stageFlow.rng;
                stageFlow.settings;

                const currentContext = await stageFlow.gameContext;
                expect(currentContext).is.not.undefined;
                expect(stageFlow.info).is.not.undefined;
                expect(stageFlow.request).is.not.undefined;
                expect(stageFlow.flowId).is.not.undefined;
                stageFlow.exchange(20, "EUR");
                stageFlow.deferredUpdate({ context, payment, history });
                return {
                    stage1Response: {
                        winChallenge: true
                    }
                };
            },

            finalize: mock()
        };
        const stage2 = {
            play: function(stageFlow: SomeStageFlow) {
                const context = { processed: true };
                const payment: PaymentInfo = { actions: [{ action: "debit", attribute: "balance", amount: 3000 }] };
                const history = { type: "tournament", roundEnded: false, data: { tournament: true } };
                stageFlow.persistencePolicy = 3;
                stageFlow.deferredUpdate({ context, payment, history });
                return {
                    stage2Response: {
                        winTournament: true
                    }
                };
            },
            finalize: mock()
        };

        this.flow.states.push({
            stagePolicies: [0, 0],
            stagesContexts: [{}, {}],
            gameContext: {},
        });
        const multiStageGame = new MultiStageGame(game, [stage1 as any, stage2 as any]);

        this.flow.request.thrice().returns({
            request: "play"
        });

        stage1.finalize.once().returns(Promise.resolve(undefined));
        stage2.finalize.once().returns(Promise.resolve(undefined));
        this.flow.rng.twice();
        this.flow.settings.twice();
        this.flow.getBalance.twice();
        this.flow.pushService.twice();
        this.flow.log.twice();
        this.flow.exchange.twice();

        const result = await multiStageGame.play(this.flow as any);
        expect(result).deep.equals({
            "gameResponse": {
                "played": true,
            },
            "stage1Response": {
                "winChallenge": true,
            },
            "stage2Response": {
                "winTournament": true,
            }
        });

        const flowId = this.flow.states[1].flowId;
        expect(this.flow.updates).deep.equals([
            {
                context:
                    {
                        stagePolicies: [0, 0],
                        stagesContexts: [{}, {}],
                        gameContext: { newState: { value: "someValue" } },
                        flowId: flowId,
                        flowType: 2,
                        step: undefined,
                        request: { request: "play" },
                        winAmount: 0,
                        totalBet: 100,
                        policy: 2
                    },
                history: { type: "slot", roundEnded: true, data: {} },
                payment: { actions: [{ action: "debit", attribute: "balance", amount: 100 }] },
                jackpotAction: undefined
            },
            {
                context:
                    {
                        stagePolicies: [2, 0],
                        stagesContexts: [{ processed: true }, {}],
                        gameContext: { newState: { value: "someValue" } },
                        flowId: flowId,
                        flowType: 2,
                        step: 0,
                        request: { request: "play" },
                        totalBet: 100,
                        winAmount: 0,
                        policy: 2
                    },
                history:
                    {
                        type: "challenge",
                        roundEnded: false,
                        data: { chanllenge: true }
                    },
                payment: { actions: [{ action: "debit", attribute: "balance", amount: 2000 }] },
                jackpotAction: undefined
            },
            {
                context:
                    {
                        stagePolicies: [2, 3],
                        stagesContexts: [{ processed: true }, { processed: true }],
                        gameContext: { newState: { value: "someValue" } },
                        flowId: flowId,
                        flowType: 2,
                        step: 1,
                        request: { request: "play" },
                        totalBet: 100,
                        winAmount: 0,
                        policy: 2
                    },
                history:
                    {
                        type: "tournament",
                        roundEnded: false,
                        data: { tournament: true }
                    },
                payment: { actions: [{ action: "debit", attribute: "balance", amount: 3000 }] },
                jackpotAction: undefined
            },
            {
                context:
                    {
                        stagePolicies: [2, 3],
                        stagesContexts: [{ processed: true }, { processed: true }],
                        gameContext: { newState: { value: "someValue" } },
                        flowId: undefined,
                        flowType: undefined,
                        step: undefined,
                        request: undefined,
                        policy: undefined,
                        totalBet: undefined,
                        winAmount: undefined
                    }
            }
        ]);
        stage1.finalize.verify();
        stage2.finalize.verify();
        this.flow.rng.verify();
        this.flow.settings.verify();
        this.flow.getBalance.verify();
        this.flow.pushService.verify();
        this.flow.exchange.verify();
        this.flow.log.verify();
    }

    @test("needs to restart")
    public async needsToRestart() {
        const game: any = {
            requests: [],
            play: mock(),
        };
        const stage1 = {
            play: mock(),
            finalize: mock()
        };
        const stage2 = {
            play: mock(),
            finalize: mock()
        };

        this.flow.states.push({
            flowId: "UUID",
            stagesPolicy: [0, 0],
            stagesContexts: [{}, {}],
            gameContext: {},
        });
        const multiStageGame = new MultiStageGame(game, [stage1 as any, stage2 as any]);

        this.flow.request.returns({
            request: "play"
        });

        game.play.never();
        stage1.play.never();
        stage1.finalize.never();
        stage2.play.never();
        stage2.finalize.once().never();

        await multiStageGame.play(this.flow as any).should.eventually.rejectedWith(Error);

        stage1.play.verify();
        stage2.play.verify();
        stage1.finalize.verify();
        stage2.finalize.verify();
    }

    @test("with winJackpot")
    public async winJackpot() {
        const game: any = {
            requests: [],
            play: async function(gameFlow: SomeGameFlow<any>) {
                this.requests.push(gameFlow.request());
                await gameFlow.winJackpot({
                        newState: {
                            value: "someValue"
                        }
                    },
                    {
                        type: "win-jackpot",
                        jackpotId: "testJP"
                    },
                    {
                        type: "slot",
                        roundEnded: true,
                        data: {}
                    });
                return {
                    gameResponse: {
                        played: true
                    }
                };
            }
        };
        const stage1 = {
            play: function(stageFlow: SomeStageFlow) {
                const context = { processed: true };
                const jackpotAction = {
                    type: "win-jackpot",
                    jackpotId: "challengeJP"
                };
                const history = { type: "challenge", roundEnded: false, data: { chanllenge: true } };
                stageFlow.persistencePolicy = 2;
                stageFlow.deferredUpdate({ context, history, jackpotAction });
                return {
                    stage1Response: {
                        winChallenge: true
                    }
                };
            },

            finalize: mock()
        };
        const stage2 = {
            play: function(stageFlow: SomeStageFlow) {
                const context = { processed: true };
                const jackpotAction = {
                    type: "win-jackpot",
                    jackpotId: "tournamentJP"
                };
                const history = { type: "tournament", roundEnded: false, data: { tournament: true } };
                stageFlow.persistencePolicy = 3;
                stageFlow.deferredUpdate({ context, history, jackpotAction });
                return {
                    stage2Response: {
                        winTournament: true
                    }
                };
            },
            finalize: mock()
        };

        this.flow.states.push({
            stagePolicies: [0, 0],
            stagesContexts: [{}, {}],
            gameContext: {},
        });
        const multiStageGame = new MultiStageGame(game, [stage1 as any, stage2 as any]);

        this.flow.request.twice().returns({
            request: "play"
        });

        stage1.finalize.once().returns(Promise.resolve(undefined));
        stage2.finalize.once().returns(Promise.resolve(undefined));

        const result = await multiStageGame.play(this.flow as any);
        expect(result).deep.equals({
            "gameResponse": {
                "played": true,
            },
            "stage1Response": {
                "winChallenge": true,
            },
            "stage2Response": {
                "winTournament": true,
            }
        });

        const flowId = this.flow.states[1].flowId;
        expect(this.flow.updates).deep.equals([
            {
                context:
                    {
                        stagePolicies: [0, 0],
                        stagesContexts: [{}, {}],
                        gameContext: { newState: { value: "someValue" } },
                        flowId: flowId,
                        flowType: 2,
                        step: undefined,
                        request: { request: "play" },
                        policy: 0
                    },
                history: { type: "slot", roundEnded: true, data: {} },
                payment: undefined,
                jackpotAction: { type: "win-jackpot", jackpotId: "testJP" }
            },
            {
                context:
                    {
                        stagePolicies: [2, 0],
                        stagesContexts: [{ processed: true }, {}],
                        gameContext: { newState: { value: "someValue" } },
                        flowId: flowId,
                        flowType: 2,
                        step: 0,
                        request: { request: "play" },
                        policy: 0
                    },
                history:
                    {
                        type: "challenge",
                        roundEnded: false,
                        data: { chanllenge: true }
                    },
                payment: undefined,
                jackpotAction: { type: "win-jackpot", jackpotId: "challengeJP" }
            },
            {
                context:
                    {
                        stagePolicies: [2, 3],
                        stagesContexts: [{ processed: true }, { processed: true }],
                        gameContext: { newState: { value: "someValue" } },
                        flowId: flowId,
                        flowType: 2,
                        step: 1,
                        request: { request: "play" },
                        policy: 0
                    },
                history:
                    {
                        type: "tournament",
                        roundEnded: false,
                        data: { tournament: true }
                    },
                payment: undefined,
                jackpotAction: { type: "win-jackpot", jackpotId: "tournamentJP" }
            },
            {
                context:
                    {
                        stagePolicies: [2, 3],
                        stagesContexts: [{ processed: true }, { processed: true }],
                        gameContext: { newState: { value: "someValue" } },
                        flowId: undefined,
                        step: undefined,
                        flowType: undefined,
                        request: undefined,
                        policy: undefined,
                        totalBet: undefined,
                        winAmount: undefined
                    }
            }
        ]);
        stage1.finalize.verify();
        stage2.finalize.verify();
    }

    @test("with updateMiniGame")
    public async updateMiniGame() {
        const game: any = {
            requests: [],
            play: async function(gameFlow: SomeGameFlow<any>) {
                this.requests.push(gameFlow.request());
                await gameFlow.updateMiniGame({
                    newState: {
                        value: "someValue"
                    }
                }, { type: "mini-game", miniGame: true });

                return {
                    gameResponse: {
                        played: true
                    }
                };
            }
        };
        const stage1 = {
            play: mock(),
            finalize: mock()
        };
        const stage2 = {
            play: mock(),
            finalize: mock()
        };

        this.flow.states.push({
            stagesPolicy: [0, 0],
            stagesContexts: [{}, {}],
            gameContext: {},
        });
        const multiStageGame = new MultiStageGame(game, [stage1 as any, stage2 as any]);

        this.flow.request.twice().returns({
            request: "play"
        });

        stage1.play.once().returns(Promise.resolve({
            stage1Response: {
                played: true
            }
        }));

        stage2.play.once().returns(Promise.resolve({
            stage2Response: {
                played: true
            }
        }));

        stage1.finalize.once().returns(Promise.resolve(undefined));
        stage2.finalize.once().returns(Promise.resolve(undefined));

        const result = await multiStageGame.play(this.flow as any);
        expect(result).deep.equals({
            "gameResponse": {
                "played": true,
            },
            "stage1Response": {
                "played": true,
            },
            "stage2Response": {
                "played": true,
            }
        });

        const flowId = this.flow.states[1].flowId;
        expect(this.flow.updates).deep.equals([
            {
                context: {
                    flowId: flowId,
                    flowType: 2,
                    step: undefined,
                    gameContext: {
                        newState: {
                            value: "someValue"
                        }
                    },
                    policy: 0,
                    request: {
                        request: "play"
                    },
                    stagePolicies: [0, 0],
                    stagesContexts: [{}, {}],
                    stagesPolicy: [0, 0]
                },
                jackpotAction: {
                    type: "mini-game",
                    miniGame: true
                },
                payment: undefined,
                history: undefined
            },
            {
                context: {
                    flowId: undefined,
                    step: undefined,
                    flowType: undefined,
                    gameContext: {
                        newState: {
                            value: "someValue"
                        }
                    },
                    policy: undefined,
                    request: undefined,
                    stagePolicies: [0, 0],
                    stagesContexts: [{}, {}],
                    stagesPolicy: [0, 0],
                    totalBet: undefined,
                    winAmount: undefined
                }
            }
        ]);
        stage1.play.verify();
        stage2.play.verify();
        stage1.finalize.verify();
        stage2.finalize.verify();
    }

    @test("with payment")
    public async payment() {
        const game: any = {
            requests: [],
            play: async function(gameFlow: SomeGameFlow<any>) {
                this.requests.push(gameFlow.request());
                await gameFlow.payment({ actions: [{ action: "debit", attribute: "balance", amount: 987 }] });

                return {
                    gameResponse: {
                        played: true
                    }
                };
            }
        };
        const stage1 = {
            play: mock(),
            finalize: mock()
        };
        const stage2 = {
            play: mock(),
            finalize: mock()
        };

        this.flow.states.push({
            stagesPolicy: [0, 0],
            stagesContexts: [{}, {}],
            gameContext: {},
        });
        const multiStageGame = new MultiStageGame(game, [stage1 as any, stage2 as any]);

        this.flow.request.twice().returns({
            request: "play"
        });

        stage1.play.once().returns(Promise.resolve({
            stage1Response: {
                played: true
            }
        }));

        stage2.play.once().returns(Promise.resolve({
            stage2Response: {
                played: true
            }
        }));

        stage1.finalize.once().returns(Promise.resolve(undefined));
        stage2.finalize.once().returns(Promise.resolve(undefined));

        const result = await multiStageGame.play(this.flow as any);
        expect(result).deep.equals({
            "gameResponse": {
                "played": true,
            },
            "stage1Response": {
                "played": true,
            },
            "stage2Response": {
                "played": true,
            }
        });

        const flowId = this.flow.states[1].flowId;
        expect(this.flow.updates).deep.equals([
            {
                context:
                    {
                        stagesPolicy: [0, 0],
                        stagesContexts: [{}, {}],
                        gameContext: {},
                        flowId: flowId,
                        flowType: 2,
                        step: undefined,
                        stagePolicies: [0, 0],
                        request: { request: "play" },
                        totalBet: 987,
                        winAmount: 0,
                        policy: 0
                    },
                history: undefined,
                payment: { actions: [{ action: "debit", attribute: "balance", amount: 987 }] },
                jackpotAction: undefined
            },
            {
                context:
                    {
                        stagesPolicy: [0, 0],
                        stagesContexts: [{}, {}],
                        gameContext: {},
                        flowId: undefined,
                        flowType: undefined,
                        step: undefined,
                        stagePolicies: [0, 0],
                        request: undefined,
                        policy: undefined,
                        totalBet: undefined,
                        winAmount: undefined,
                    }
            }
        ]);
        stage1.play.verify();
        stage2.play.verify();
        stage1.finalize.verify();
        stage2.finalize.verify();
    }

    @test("with storeHistory")
    public async storeHistory() {
        const game: any = {
            requests: [],
            play: async function(gameFlow: SomeGameFlow<any>) {
                this.requests.push(gameFlow.request());
                await gameFlow.storeHistory({
                    type: "gameHistory",
                    roundEnded: false,
                    data: {}
                });

                return {
                    gameResponse: {
                        played: true
                    }
                };
            }
        };
        const stage1 = {
            play: mock(),
            finalize: mock()
        };
        const stage2 = {
            play: mock(),
            finalize: mock()
        };

        this.flow.states.push({
            stagesPolicy: [0, 0],
            stagesContexts: [{}, {}],
            gameContext: {},
        });
        const multiStageGame = new MultiStageGame(game, [stage1 as any, stage2 as any]);

        this.flow.request.twice().returns({
            request: "play"
        });

        stage1.play.once().returns(Promise.resolve({
            stage1Response: {
                played: true
            }
        }));

        stage2.play.once().returns(Promise.resolve({
            stage2Response: {
                played: true
            }
        }));

        stage1.finalize.once().returns(Promise.resolve(undefined));
        stage2.finalize.once().returns(Promise.resolve(undefined));

        const result = await multiStageGame.play(this.flow as any);
        expect(result).deep.equals({
            "gameResponse": {
                "played": true,
            },
            "stage1Response": {
                "played": true,
            },
            "stage2Response": {
                "played": true,
            }
        });

        const flowId = this.flow.states[1].flowId;
        expect(this.flow.updates).deep.equals([
            {
                context: {
                    flowId: flowId,
                    flowType: 2,
                    step: undefined,
                    gameContext: {},
                    policy: 0,
                    request: {
                        request: "play"
                    },
                    stagePolicies: [0, 0],
                    stagesContexts: [{}, {}],
                    stagesPolicy: [0, 0]
                },
                history: {
                    type: "gameHistory",
                    roundEnded: false,
                    data: {}
                },
                payment: undefined,
                jackpotAction: undefined
            },
            {
                context: {
                    flowId: undefined,
                    flowType: undefined,
                    step: undefined,
                    gameContext: {},
                    policy: undefined,
                    request: undefined,
                    stagePolicies: [0, 0],
                    stagesContexts: [{}, {}],
                    stagesPolicy: [0, 0],
                    totalBet: undefined,
                    winAmount: undefined,
                }
            }
        ]);
        stage1.play.verify();
        stage2.play.verify();
        stage1.finalize.verify();
        stage2.finalize.verify();
    }

    @test("main game context changes from stage context")
    public async changeMainContextFromStageContext() {
        const game: any = {
            requests: [],
            play: async function(gameFlow: SomeGameFlow<any>) {
                this.requests.push(gameFlow.request());
                // test  method delegation
                gameFlow.pushService();
                await gameFlow.getBalance();
                gameFlow.log(LogLevel.Debug, "TestMessage");
                gameFlow.rng();
                gameFlow.settings();

                const currentContext = await gameFlow.gameContext();
                expect(currentContext).is.not.undefined;
                expect(gameFlow.info()).is.not.undefined;
                gameFlow.exchange(10, "USD");
                gameFlow.setPersistencePolicy(2);
                expect(gameFlow.persistencePolicy()).is.equals(2);

                await gameFlow.update({
                        newState: {
                            value: "someValue"
                        },
                        totalBet: 10
                    },
                    {
                        actions: [
                            {
                                action: "debit",
                                attribute: "balance",
                                amount: 100,
                            }
                        ]
                    },
                    {
                        type: "slot",
                        roundEnded: true,
                        data: {}
                    });
                return {
                    gameResponse: {
                        played: true
                    }
                };
            }
        };
        const stage1 = {
            play: async function(stageFlow: SomeStageFlow) {
                const gameContext = { processed: true, stage1: "test", totalBet: 20 };
                const payment: PaymentInfo = { actions: [{ action: "debit", attribute: "balance", amount: 2000 }] };
                const history = { type: "challenge", roundEnded: false, data: { chanllenge: true } };
                stageFlow.persistencePolicy = 2;

                // test delegation methods
                stageFlow.pushService;
                await stageFlow.balance;
                stageFlow.log(LogLevel.Info, "TestMessage1");
                stageFlow.rng;
                stageFlow.settings;

                const currentContext = await stageFlow.gameContext;
                expect(currentContext).is.not.undefined;
                expect(stageFlow.info).is.not.undefined;
                expect(stageFlow.request).is.not.undefined;
                expect(stageFlow.flowId).is.not.undefined;
                stageFlow.exchange(20, "EUR");
                stageFlow.deferredUpdate({ payment, history, gameContext });
                return {
                    stage1Response: {
                        winChallenge: true
                    }
                };
            },

            finalize: mock()
        };
        const stage2 = {
            play: function(stageFlow: SomeStageFlow) {
                const context = { processed: true, stage2: "test" };
                const payment: PaymentInfo = { actions: [{ action: "debit", attribute: "balance", amount: 3000 }] };
                const history = { type: "tournament", roundEnded: false, data: { tournament: true } };
                stageFlow.persistencePolicy = 3;
                stageFlow.deferredUpdate({ context, payment, history });
                return {
                    stage2Response: {
                        winTournament: true
                    }
                };
            },
            finalize: mock()
        };

        this.flow.states.push({
            stagePolicies: [0, 0],
            stagesContexts: [{}, {}],
            gameContext: {},
        });
        const multiStageGame = new MultiStageGame(game, [stage1 as any, stage2 as any]);

        this.flow.request.thrice().returns({
            request: "play"
        });

        stage1.finalize.once().returns(Promise.resolve(undefined));
        stage2.finalize.once().returns(Promise.resolve(undefined));
        this.flow.rng.twice();
        this.flow.settings.twice();
        this.flow.getBalance.twice();
        this.flow.pushService.twice();
        this.flow.log.twice();
        this.flow.exchange.twice();

        const result = await multiStageGame.play(this.flow as any);
        expect(result).deep.equals({
            "gameResponse": {
                "played": true,
            },
            "stage1Response": {
                "winChallenge": true,
            },
            "stage2Response": {
                "winTournament": true,
            }
        });

        const flowId = this.flow.states[1].flowId;
        expect(this.flow.updates).deep.equals([
            {
                context:
                    {
                        stagePolicies: [0, 0],
                        stagesContexts: [{}, {}],
                        gameContext: { newState: { value: "someValue" }, totalBet: 10 },
                        flowId: flowId,
                        flowType: 2,
                        step: undefined,
                        request: { request: "play" },
                        winAmount: 0,
                        totalBet: 100,
                        policy: 2
                    },
                history: { type: "slot", roundEnded: true, data: {} },
                payment: { actions: [{ action: "debit", attribute: "balance", amount: 100 }] },
                jackpotAction: undefined
            },
            {
                context:
                    {
                        stagePolicies: [2, 0],
                        stagesContexts: [{}, {}],
                        gameContext: {
                            newState: { value: "someValue" },
                            totalBet: 20,
                            processed: true,
                            stage1: "test"
                        },
                        flowId: flowId,
                        flowType: 2,
                        step: 0,
                        request: { request: "play" },
                        totalBet: 100,
                        winAmount: 0,
                        policy: 2
                    },
                history:
                    {
                        type: "challenge",
                        roundEnded: false,
                        data: { chanllenge: true }
                    },
                payment: { actions: [{ action: "debit", attribute: "balance", amount: 2000 }] },
                jackpotAction: undefined
            },
            {
                context:
                    {
                        stagePolicies: [2, 3],
                        stagesContexts: [
                            {},
                            { processed: true, stage2: "test" }
                        ],
                        gameContext: {
                            newState: { value: "someValue" },
                            totalBet: 20,
                            processed: true,
                            stage1: "test"
                        },
                        flowId: flowId,
                        flowType: 2,
                        step: 1,
                        request: { request: "play" },
                        totalBet: 100,
                        winAmount: 0,
                        policy: 2
                    },
                history:
                    {
                        type: "tournament",
                        roundEnded: false,
                        data: { tournament: true }
                    },
                payment: { actions: [{ action: "debit", attribute: "balance", amount: 3000 }] },
                jackpotAction: undefined
            },
            {
                context:
                    {
                        stagePolicies: [2, 3],
                        stagesContexts: [
                            {},
                            { processed: true, stage2: "test" }
                        ],
                        gameContext: {
                            newState: { value: "someValue" },
                            totalBet: 20,
                            processed: true,
                            stage1: "test"
                        },
                        flowId: undefined,
                        flowType: undefined,
                        step: undefined,
                        request: undefined,
                        policy: undefined,
                        totalBet: undefined,
                        winAmount: undefined
                    }
            }
        ]);
        stage1.finalize.verify();
        stage2.finalize.verify();
        this.flow.rng.verify();
        this.flow.settings.verify();
        this.flow.getBalance.verify();
        this.flow.pushService.verify();
        this.flow.exchange.verify();
        this.flow.log.verify();
    }

    @test("with omit one stage for live game")
    public async withOmitOneStageForLiveGame() {
        const game: any = {
            requests: [],
            play: async function(gameFlow: SomeGameFlow<any>) {
                this.requests.push(gameFlow.request());
                await gameFlow.update({
                        newState: {
                            value: "someValue"
                        }
                    },
                    {
                        actions: [
                            {
                                action: "debit",
                                attribute: "balance",
                                amount: 100,
                            }
                        ]
                    },
                    {
                        type: "slot",
                        roundEnded: true,
                        data: {}
                    });
                return {
                    response: {
                        gameResponse: {
                            played: true
                        }
                    },
                    dontUseBalance: true
                };
            }
        };
        const stage1 = {
            play: mock(),
            finalize: mock()
        };
        const stage2 = {
            play: mock(),
            finalize: mock()
        };

        this.flow.states.push({
            stagesPolicy: [0, 0],
            stagesContexts: [{}, {}],
            gameContext: {},
        });
        const multiStageGame = new MultiStageGame(game, [stage1 as any, stage2 as any]);

        this.flow.request.twice().returns({
            request: "play"
        });

        stage1.play.once().returns(undefined);

        stage2.play.once().returns(Promise.resolve({
            stage2Response: {
                played: true
            }
        }));

        stage1.finalize.once().returns(Promise.resolve(undefined));
        stage2.finalize.once().returns(Promise.resolve(undefined));

        const result = await multiStageGame.play(this.flow as any);
        expect(result).deep.equals({
            response: {
                "gameResponse": {
                    "played": true,
                },
                "stage2Response": {
                    "played": true,
                }
            },
            dontUseBalance: true,
        });

        const flowId = this.flow.states[1].flowId;

        expect(this.flow.updates).deep.equals([
            {
                context:
                    {
                        stagesPolicy: [0, 0],
                        stagesContexts: [{}, {}],
                        gameContext: { newState: { value: "someValue" } },
                        flowId: flowId,
                        flowType: 2,
                        step: undefined,
                        stagePolicies: [0, 0],
                        request: { request: "play" },
                        totalBet: 100,
                        winAmount: 0,
                        policy: 0
                    },
                history: { type: "slot", roundEnded: true, data: {} },
                payment: { actions: [{ action: "debit", attribute: "balance", amount: 100 }] },
                jackpotAction: undefined
            },
            {
                context:
                    {
                        stagesPolicy: [0, 0],
                        stagesContexts: [{}, {}],
                        gameContext: { newState: { value: "someValue" } },
                        flowId: undefined,
                        step: undefined,
                        flowType: undefined,
                        stagePolicies: [0, 0],
                        request: undefined,
                        policy: undefined,
                        totalBet: undefined,
                        winAmount: undefined
                    }
            }
        ]);
        stage1.play.verify();
        stage2.play.verify();
        stage1.finalize.verify();
        stage2.finalize.verify();
    }
}
