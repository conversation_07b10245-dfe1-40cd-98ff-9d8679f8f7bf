import { expect, should, use } from "chai";
import { mock } from "sinon";
import { LogLevel, PaymentInfo, SomeGameFlow, SomeStageFlow } from "@skywind-group/sw-game-core";

import { MultiStageGame } from "../../../skywind/services/game/multiStageGame";
import { BaseMultiStageGameSpec } from "./mockedFlow";

import { suite, test } from "mocha-typescript";

use(require("chai-as-promised"));
use(should);

@suite("MultiStageGame use winAmount in stage")
class MultiStageGamePlaySpec extends BaseMultiStageGameSpec {

    @test("with changing storage policy, setup stage context")
    public async changePolicy() {
        const game: any = {
            requests: [],
            play: async function(gameFlow: SomeGameFlow<any>) {
                this.requests.push(gameFlow.request());
                // test  method delegation
                gameFlow.pushService();
                await gameFlow.getBalance();
                gameFlow.log(LogLevel.Debug, "TestMessage");
                gameFlow.rng();
                gameFlow.settings();

                const currentContext = await gameFlow.gameContext();
                expect(currentContext).is.not.undefined;
                expect(gameFlow.info()).is.not.undefined;
                gameFlow.exchange(10, "USD");

                await gameFlow.update({
                        newState: {
                            value: "someValue"
                        }
                    },
                    {
                        actions: [
                            {
                                action: "debit",
                                attribute: "balance",
                                amount: 100,
                            },
                            {
                                action: "credit",
                                attribute: "balance",
                                amount: 777,
                            }
                        ]
                    },
                    {
                        type: "slot",
                        roundEnded: true,
                        data: {}
                    });
                return {
                    gameResponse: {
                        played: true
                    }
                };
            }
        };
        const stage1 = {
            play: async function(stageFlow: SomeStageFlow) {
                const context = { processed: true };
                const payment: PaymentInfo = { actions: [{ action: "debit", attribute: "balance", amount: 2000 }] };
                const history = { type: "challenge", roundEnded: false, data: { chanllenge: true } };

                // test delegation methods
                stageFlow.pushService;
                await stageFlow.balance;
                stageFlow.log(LogLevel.Info, "TestMessage1");
                stageFlow.rng;
                stageFlow.settings;

                expect(stageFlow.winAmount).equal(777);
                const currentContext = await stageFlow.gameContext;
                expect(currentContext).is.not.undefined;
                expect(stageFlow.info).is.not.undefined;
                expect(stageFlow.request).is.not.undefined;
                expect(stageFlow.flowId).is.not.undefined;
                stageFlow.exchange(20, "EUR");
                stageFlow.deferredUpdate({ context, payment, history });
                return {
                    stage1Response: {
                        winChallenge: true
                    }
                };
            },

            finalize: mock()
        };
        const stage2 = {
            play: function(stageFlow: SomeStageFlow) {
                const context = { processed: true };
                const payment: PaymentInfo = { actions: [{ action: "debit", attribute: "balance", amount: 3000 }] };
                const history = { type: "tournament", roundEnded: false, data: { tournament: true } };
                expect(stageFlow.winAmount).equal(777);
                stageFlow.deferredUpdate({ context, payment, history });
                return {
                    stage2Response: {
                        winTournament: true
                    }
                };
            },
            finalize: mock()
        };

        this.flow.states.push({
            stagePolicies: [0, 0],
            stagesContexts: [{}, {}],
            gameContext: {},
        });
        const multiStageGame = new MultiStageGame(game, [stage1 as any, stage2 as any]);

        this.flow.request.thrice().returns({
            request: "play"
        });

        stage1.finalize.once().returns(Promise.resolve(undefined));
        stage2.finalize.once().returns(Promise.resolve(undefined));
        this.flow.rng.twice();
        this.flow.settings.twice();
        this.flow.getBalance.twice();
        this.flow.pushService.twice();
        this.flow.log.twice();
        this.flow.exchange.twice();

        const result = await multiStageGame.play(this.flow as any);
        expect(result).deep.equals({
            "gameResponse": {
                "played": true,
            },
            "stage1Response": {
                "winChallenge": true,
            },
            "stage2Response": {
                "winTournament": true,
            }
        });

        const flowId = this.flow.states[1].flowId;
        expect(this.flow.updates).deep.equals([
            {
                context:
                    {
                        stagePolicies: [0, 0],
                        stagesContexts: [{}, {}],
                        gameContext: { newState: { value: "someValue" } },
                        flowId: flowId,
                        flowType: 2,
                        step: undefined,
                        request: { request: "play" },
                        totalBet: 100,
                        winAmount: 777,
                        policy: 0
                    },
                history: { type: "slot", roundEnded: true, data: {} },
                payment: {
                    actions: [
                        {
                            action: "debit",
                            amount: 100,
                            attribute: "balance",
                        },
                        {
                            action: "credit",
                            amount: 777,
                            attribute: "balance",
                        }
                    ]
                },

                jackpotAction: undefined
            },
            {
                context:
                    {
                        stagePolicies: [0, 0],
                        stagesContexts: [{ processed: true }, {}],
                        gameContext: { newState: { value: "someValue" } },
                        flowId: flowId,
                        step: 0,
                        flowType: 2,
                        request: { request: "play" },
                        totalBet: 100,
                        winAmount: 777,
                        policy: 0
                    },
                history:
                    {
                        type: "challenge",
                        roundEnded: false,
                        data: { chanllenge: true }
                    },
                payment: { actions: [{ action: "debit", attribute: "balance", amount: 2000 }] },
                jackpotAction: undefined
            },
            {
                context:
                    {
                        stagePolicies: [0, 0],
                        stagesContexts: [{ processed: true }, { processed: true }],
                        gameContext: { newState: { value: "someValue" } },
                        flowId: flowId,
                        flowType: 2,
                        step: 1,
                        request: { request: "play" },
                        totalBet: 100,
                        winAmount: 777,
                        policy: 0
                    },
                history:
                    {
                        type: "tournament",
                        roundEnded: false,
                        data: { tournament: true }
                    },
                payment: { actions: [{ action: "debit", attribute: "balance", amount: 3000 }] },
                jackpotAction: undefined
            },
            {
                context:
                    {
                        stagePolicies: [0, 0],
                        stagesContexts: [{ processed: true }, { processed: true }],
                        gameContext: { newState: { value: "someValue" } },
                        flowId: undefined,
                        flowType: undefined,
                        step: undefined,
                        request: undefined,
                        policy: undefined,
                        totalBet: undefined,
                        winAmount: undefined
                    }
            }
        ]);
        stage1.finalize.verify();
        stage2.finalize.verify();
        this.flow.rng.verify();
        this.flow.settings.verify();
        this.flow.getBalance.verify();
        this.flow.pushService.verify();
        this.flow.exchange.verify();
        this.flow.log.verify();
    }
}
