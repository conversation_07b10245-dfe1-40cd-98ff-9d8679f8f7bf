import { expect, should, use } from "chai";
import { getGameHistoryItem, getRoundHistory, TEST_MODULE_NAME } from "../helper";
import { GameFlowContextImpl } from "../../skywind/services/context/gameContextImpl";
import { suite, test } from "mocha-typescript";
import { BaseGameContextSpec } from "./gameContext.spec";
import { GameFlowInfoImpl } from "../../skywind/services/context/gameFlowInfo";
import { RecoveryType } from "../../skywind/services/offlinestorage/offlineCommands";

const chaiAsPromise = require("chai-as-promised");
import _ = require("lodash");

should();
use(chaiAsPromise);

@suite("GameContext.Close round")
class GameContextCloseRoundSpec extends BaseGameContextSpec {

    @test("is updated & rollbacked pending modification + closeRound")
    public async testUpdateAndRollbackPendingAndCloseRound() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        let context = await this.contextManager.findGameContextById(this.gameID);
        const request = { request: "req", requestId: 1 };
        const state1 = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };

        const history1: any = {
            type: "slot",
            roundEnded: false,
            data: {
                positions: [1, 2, 3],
            }
        };

        const walletOperation1: any = {
            operation: "payment",
            transactionId: "1",
            bet: 1900,
            win: 200,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history1.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, state1, request, history1);

        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };

        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        walletOperation1.ts = walletOperation1.ts.toISOString();
        expect(context.pendingModification).deep.equal({
            "history": history1,
            "newState": state1,
            "walletOperation": walletOperation1,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state1);
        expect(context.pendingModification).is.undefined;

        let result: any = await getGameHistoryItem();
        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 0,
            roundId: "0",
            ctrl: result.ctrl,
            sessionId: this.sessionId.sessionId,
            result: history1.data,
            type: history1.type,
            gameVersion: TEST_MODULE_NAME.version,
            walletTransactionId: "1",
            currency: "USD",
            win: walletOperation1.win,
            bet: walletOperation1.bet,
            roundEnded: history1.roundEnded,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 1900,
            totalRoundWin: 200

        });

        const history2: any = {
            type: "slot",
            roundEnded: false,
            data: {
                positions: [1, 2],
            }
        };

        const state2 = _.cloneDeep(state1);
        state2["nextScene"] = "SOMESCENE2";

        const walletOperation2: any = {
            operation: "payment",
            transactionId: "2",
            bet: 2000,
            win: 100,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history2.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation2, state2, request, history2);

        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state1);
        walletOperation2.ts = walletOperation2.ts.toISOString();
        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history2,
            "newState": state2,
            "walletOperation": walletOperation2,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state2);
        expect(context.pendingModification).is.undefined;

        result = await getGameHistoryItem();

        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 1,
            ctrl: result.ctrl,
            gameVersion: TEST_MODULE_NAME.version,
            result: history2.data,
            type: history2.type,
            roundEnded: history2.roundEnded,
            roundId: "0",
            sessionId: this.sessionId.sessionId,
            walletTransactionId: "2",
            currency: "USD",
            win: walletOperation2.win,
            bet: walletOperation2.bet,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 3900,
            totalRoundWin: 300
        });
        let rounds = await getRoundHistory(0, 1000);
        expect(rounds).deep.equal([]);

        const history3: any = {
            type: "slot",
            roundEnded: false,
            data: {
                positions: [1, 2, 3, 4],
            }
        };

        const state3 = _.cloneDeep(state2);
        state3["nextScene"] = "SOMESCENE3";
        const walletOperation3: any = {
            operation: "payment",
            transactionId: "3",
            bet: 100,
            win: 200,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history3.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation3, state3, request, history3);

        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state2);

        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history3,
            "newState": state3,
            "walletOperation": walletOperation3,
        });

        await context.rollbackPendingModificationAndCloseRound();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state2);
        expect(context.pendingModification).undefined;

        result = await getGameHistoryItem();
        expect(result).is.undefined;

        rounds = await getRoundHistory(0, 1000);
        expect(rounds.map((item) => _.omit(item, "finishedAt", "startedAt", "ctrl"))).deep.equals([
            {
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "gameId",
                gameId: "gameId",
                id: "0",
                playerCode: "playerId",
                sessionId: this.sessionId.sessionId,
                totalBet: 3900,
                totalEvents: 2,
                totalWin: 300,
                totalJpContribution: 0,
                totalJpWin: 0
            }
        ]);
    }

    @test("is updated & rollbacked pending modification + closeRound skipped")
    public async testUpdateAndRollbackAndCloseRoundSkipped() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        let context = await this.contextManager.findGameContextById(this.gameID);
        const request = { request: "req", requestId: 1 };
        const state1 = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };

        const history1: any = {
            type: "slot",
            roundEnded: false,
            data: {
                positions: [1, 2, 3],
            }
        };

        const walletOperation1: any = {
            operation: "payment",
            transactionId: "1",
            bet: 1900,
            win: 200,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history1.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, state1, request, history1);

        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };
        walletOperation1.ts = walletOperation1.ts.toISOString();
        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        expect(context.pendingModification).deep.equal({
            "history": history1,
            "newState": state1,
            "walletOperation": walletOperation1,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state1);
        expect(context.pendingModification).is.undefined;

        let result: any = await getGameHistoryItem();
        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 0,
            roundId: "0",
            ctrl: result.ctrl,
            sessionId: this.sessionId.sessionId,
            result: history1.data,
            type: history1.type,
            gameVersion: TEST_MODULE_NAME.version,
            walletTransactionId: "1",
            currency: "USD",
            win: walletOperation1.win,
            bet: walletOperation1.bet,
            roundEnded: history1.roundEnded,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 1900,
            totalRoundWin: 200
        });

        const history2: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2],
            }
        };

        const state2 = _.cloneDeep(state1);
        state2["nextScene"] = "SOMESCENE2";

        const walletOperation2: any = {
            operation: "payment",
            transactionId: "2",
            bet: 2000,
            win: 100,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history2.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation2, state2, request, history2);
        walletOperation2.ts = walletOperation2.ts.toISOString();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state1);
        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history2,
            "newState": state2,
            "walletOperation": walletOperation2,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state2);
        expect(context.pendingModification).is.undefined;

        result = await getGameHistoryItem();

        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 1,
            gameVersion: TEST_MODULE_NAME.version,
            result: history2.data,
            type: history2.type,
            roundEnded: history2.roundEnded,
            roundId: "0",
            ctrl: result.ctrl,
            sessionId: this.sessionId.sessionId,
            walletTransactionId: "2",
            currency: "USD",
            win: walletOperation2.win,
            bet: walletOperation2.bet,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 3900,
            totalRoundWin: 300
        });

        let rounds = await getRoundHistory(0, 1000);
        expect(rounds.map((item) => _.omit(item, "finishedAt", "startedAt", "ctrl"))).deep.equals([
            {
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "gameId",
                gameId: "gameId",
                id: "0",
                playerCode: "playerId",
                sessionId: this.sessionId.sessionId,
                totalBet: 3900,
                totalEvents: 2,
                totalWin: 300,
                totalJpContribution: 0,
                totalJpWin: 0
            }
        ]);

        const history3: any = {
            type: "slot",
            roundEnded: false,
            data: {
                positions: [1, 2, 3, 4],
            }
        };

        const state3 = _.cloneDeep(state2);
        state3["nextScene"] = "SOMESCENE3";
        const walletOperation3: any = {
            operation: "payment",
            transactionId: "3",
            bet: 100,
            win: 200,
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history3.roundEnded,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation3, state3, request, history3);
        walletOperation3.ts = walletOperation3.ts.toISOString();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state2);

        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history3,
            "newState": state3,
            "walletOperation": walletOperation3,
        });

        await context.rollbackPendingModificationAndCloseRound();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state2);
        expect(context.pendingModification).undefined;

        result = await getGameHistoryItem();
        expect(result).is.undefined;

        rounds = await getRoundHistory(1, 1000);
        expect(rounds).deep.equal([]);
    }

    @test("close round")
    public async testCloseRound() {
        let context: GameFlowContextImpl = await this.contextManager
            .findOrCreateGameContext(this.gameID,
                this.sessionId,
                this.gameData,
                TEST_MODULE_NAME);

        context.roundEnded = false;
        const ts = new Date();
        await context.update();
        context = await this.contextManager.findGameContextById(this.gameID);

        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };
        expect(context).contain(expected);
        expect(context.session.id).equal(this.sessionId.id);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.settings).deep.equal(this.settings);
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);

        context.round = {
            totalBet: 100,
            totalWin: 20,
            totalEvents: 3,
            balanceBefore: 200,
            balanceAfter: 120,
            broken: true,
            startedAt: ts,
        };
        await context.forceCloseRound();

        const [ctx, playerCtx] = await this.contextManager.findContexts(this.gameID);
        expect(ctx).is.undefined;
        expect(playerCtx).is.not.undefined;
        expect(playerCtx.activeGames).deep.equal([]);
        expect(playerCtx.brokenGames).deep.equal([]);

        const rounds = await getRoundHistory(0, 100);
        expect(rounds).deep.equal([
            {
                "sessionId": this.sessionId.sessionId,
                "balanceAfter": 120,
                "balanceBefore": 200,
                "brandId": 1,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "gameId",
                "gameId": "gameId",
                // if round wasn't finished - try to remove the old version,
                // because the round could've been broken twice
                "broken": true,
                "id": "0",
                ctrl: rounds[0].ctrl,
                "playerCode": "playerId",
                "startedAt": rounds[0].startedAt,
                "finishedAt": rounds[0].finishedAt,
                "recoveryType": "force-finish",
                "totalBet": 100,
                "totalEvents": 3,
                "totalWin": 20,
            }
        ]);
    }

    @test("close round and newState")
    public async testCloseRound_WithNewState() {
        let context: GameFlowContextImpl = await this.contextManager
            .findOrCreateGameContext(this.gameID,
                this.sessionId,
                this.gameData,
                TEST_MODULE_NAME);

        context.roundEnded = false;
        const ts = new Date();
        await context.update();
        context = await this.contextManager.findGameContextById(this.gameID);

        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };
        expect(context).contain(expected);
        expect(context.session.id).equal(this.sessionId.id);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.settings).deep.equal(this.settings);
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);

        context.round = {
            totalBet: 100,
            totalWin: 20,
            totalEvents: 3,
            balanceBefore: 200,
            balanceAfter: 120,
            broken: true,
            startedAt: ts,
        };
        const newState = {
            type: "state_after_close_round"
        };
        await context.forceCloseRound(RecoveryType.FINALIZE, newState);

        const [ctx, playerCtx] = await this.contextManager.findContexts(this.gameID);
        expect(ctx).is.not.undefined;
        expect(ctx.round).is.undefined;
        expect(ctx.roundEnded).is.true;
        expect(ctx.specialState).is.undefined;
        expect(ctx.gameContext).deep.equal(newState);
        expect(playerCtx).is.not.undefined;
        expect(playerCtx.activeGames.map(g => g.id.asString()))
            .deep
            .equal(["games:context:1:playerId:gameId:deviceId"]);
        expect(playerCtx.brokenGames).deep.equal([]);

        const rounds = await getRoundHistory(0, 100);
        expect(rounds).deep.equal([
            {
                "sessionId": this.sessionId.sessionId,
                "balanceAfter": 120,
                "balanceBefore": 200,
                "brandId": 1,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "gameId",
                "gameId": "gameId",
                // if round wasn't finished - try to remove the old version,
                // because the round could've been broken twice
                "broken": true,
                "id": "0",
                ctrl: rounds[0].ctrl,
                "playerCode": "playerId",
                "startedAt": rounds[0].startedAt,
                "finishedAt": rounds[0].finishedAt,
                "recoveryType": "finalize",
                "totalBet": 100,
                "totalEvents": 3,
                "totalWin": 20,
            }
        ]);
    }
}
