import { <PERSON><PERSON><PERSON><PERSON>r, ManagementAPITransient<PERSON>rror, NeedToRestartTheGame } from "../../skywind/errors";
import { expect, should, use } from "chai";
import { SinonStub, stub } from "sinon";
import { GameData, getService as getAuthservice, StartGameResult } from "../../skywind/services/auth";
import { createGameToken, flushAll, TEST_MODULE_NAME, TestLoadResult } from "../helper";
import * as GameService from "../../skywind/services/game/game";
import { getGameFlowContextManager } from "../../skywind/services/contextmanager/contextManagerImpl";
import { getSyncGameController } from "../../skywind/services/synccontroller";
import config from "../../skywind/config";
import { GameContextID } from "../../skywind/services/contextIds";
import { GameInitResponse } from "@skywind-group/sw-game-core";
import * as _ from "lodash";
import { GameSession } from "../../skywind/services/gameSession";
import { TestGame } from "../testGames";
import { publicId, testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;
import status200 = testing.status200;
import status500 = testing.status500;
import status400 = testing.status400;
import { generateStartGameToken } from "../../skywind/services/tokens";
import { setRandomGeneratorFactory } from "../..";
import { RandomGeneratorFactoryImpl } from "../testRandomFactory";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

describe("Game controller", () => {
    let startGame: SinonStub;
    let loadGame: SinonStub;

    const startTokenData = {
        playerCode: "PL001",
        gameCode: "test_slot",
        providerGameCode: "test_slot",
        brandId: 1,
        module: "test_slot@latest",
    };

    const gameTokenData: any = {
        playerCode: "PL001",
        gameCode: "test_slot",
        brandId: 1,
        currency: "USD"
    };

    const gameData: GameData = {
        gameTokenData: gameTokenData,
        gameMode: gameTokenData.playmode,
        limits: {
            maxTotalStake: 500,
            stakeAll: [0.1, 0.5, 1, 2, 3, 5],
            stakeDef: 1,
            stakeMax: 10,
            stakeMin: 0.1,
            winMax: 3000000,
        },
        module: "test_slot@latest",
    } as GameData;

    const startGameResult = {
        gameData: gameData,
        clientSettings: {}
    } as StartGameResult;

    let startToken: string;
    let prevValue;
    let request: RequestMock;

    before(async () => {
        request = requestMock(superagent);
        startGame = stub(getAuthservice("real"), "startGame");
        loadGame = stub(GameService, "load");
        startToken = await generateStartGameToken(startTokenData);
        gameTokenData.token = await createGameToken(gameTokenData);
        gameData.gameTokenData = gameTokenData;
        prevValue = config.walletThroughAPI;
        config.walletThroughAPI = true;
        startGame.returns(startGameResult);
        setRandomGeneratorFactory(new RandomGeneratorFactoryImpl());
    });

    after(() => {
        request.unmock(superagent);
        startGame.restore();
        loadGame.restore();
        config.walletThroughAPI = prevValue;
    });

    beforeEach(async () => {
        request.post("http://api:3006//v2/play/payment/transactionId", status200({
            "transactionId": "SOMETRANSACTIONID",
        }));
        request.get("http://api:3006//v2/play/balance", status200({
            "currency": "USD",
            "main": 1000,
        }));
        return flushAll();
    });

    afterEach(() => {
        loadGame.resetBehavior();
        request.clearRoutes();
    });

    it("repeats payment when we restart game", async () => {
        request.put("http://api:3006//v2/play/payment", status500());
        loadGame.returns(Promise.resolve(new TestLoadResult(new TestGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse,
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                },
                {
                    "currentScene": "scene3",
                    "nextScene": "scene4",
                }
            ], [
                {
                    bet: 1, win: 5,
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                }
            ], [
                { type: "slot", roundEnded: true, data: { positions: [1, 2, 3] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5 },
            ]
        ))));

        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        try {
            await getSyncGameController().process({
                request: "spin",
                requestId: 1,
                gameSession: initResponse.gameSession,
                lines: 10,
                bet: 1,
            });
            expect.fail("Expected ManagementAPITransientError");
        } catch (err) {
            if (!(err instanceof ManagementAPITransientError)) {
                throw err;
            }
        }

        // Check that the payment request has been sent;
        expect(request.args[2].url).equal("http://api:3006//v2/play/payment");
        expect(request.args[2].body).deep.include({
            "operation": "payment",
            "deviceId": "deviceId",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "ts": request.args[2].body.ts,
            "currentScene": "scene2",
            "nextScene": "scene3",
            "currency": "USD",
            "gameToken": gameData.gameTokenData.token,
            "transactionId": "SOMETRANSACTIONID",
            "bet": undefined,
            "win": undefined,
            "eventId": 0,
            "actions": [
                {
                    "action": "debit",
                    "amount": 1,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 5,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ]
        });
        expect(request.args[2].body.ts).is.not.undefined;
        request.clearRoute("PUT", "http://api:3006//v2/play/payment");

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId");

        // check that we store last wallet info in context;
        let context = await getGameFlowContextManager()
            .findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": gameID,
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "roundId": "0",
            "gameData": context.gameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2",
            },
            "jpContext": undefined,
            "pendingModification": context.pendingModification,
            "jackpotPending": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 4
        });

        expect(context.pendingModification.walletOperation).deep.equal({
            "operation": "payment",
            "bet": 1,
            "currency": "USD",
            "currentScene": "scene2",
            "deviceId": "deviceId",
            "nextScene": "scene3",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "transactionId": "SOMETRANSACTIONID",
            "ts": context.pendingModification.walletOperation.ts,
            "win": 5,
            "eventId": 0,
            "totalBet": 1,
            "totalWin": 5,
            "totalEventId": 0,
            "retry": 1,
            "actions": [
                {
                    "action": "debit",
                    "amount": 1,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 5,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ]
        });
        /**
         *  Restart game and make second spin
         */
        request.put("http://api:3006//v2/play/payment", status200({
            "currency": "USD",
            "main": 2000,
        }));

        const initResponse2 = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            startGameToken: startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        context = await getGameFlowContextManager().findGameContextById(gameID);

        // Check that the payment requet has been sent;
        expect(request.args[2].url).equal("http://api:3006//v2/play/payment");
        expect(request.args[2].body).deep.include({
            "operation": "payment",
            "deviceId": "deviceId",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "bet": undefined,
            "win": undefined,
            "eventId": 0,
            "currentScene": "scene2",
            "nextScene": "scene3",
            "currency": "USD",
            "gameToken": gameData.gameTokenData.token,
            "transactionId": "SOMETRANSACTIONID",
            "actions": [
                {
                    "action": "debit",
                    "amount": 1,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 5,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ]
        });
        expect(request.args[2].body.ts).is.not.undefined;

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse2.gameSession),
            "id": gameID,
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "pendingModification": undefined,
            "jackpotPending": undefined,
            "gameContext": {
                "currentScene": "scene3",
                "nextScene": "scene4"
            },
            "jpContext": undefined,
            "gameData": context.gameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "roundEnded": true,
            "persistencePolicy": 0,
            "round": undefined,
            "roundId": "1",
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 7,
        });
    });

    it("rollbacks payment if we got unrecoverable exception", async () => {
        request.put("http://api:3006//v2/play/payment", status400({
            code: 400
        }));
        loadGame.returns(Promise.resolve(new TestLoadResult(new TestGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse,
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                }
            ], [
                {
                    bet: 1, win: 5,
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                }
            ], [
                { type: "slot", roundEnded: true, data: { positions: [1, 2, 3] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5 },
            ]
        ))));
        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        try {
            await getSyncGameController().process({
                request: "spin",
                requestId: 1,
                gameSession: initResponse.gameSession,
                lines: 10,
                bet: 1,
                coin: 33,
            });
            expect.fail("Expected ManagementAPIError");
        } catch (err) {
            if (!(err instanceof ManagementAPIError)) {
                throw err;
            }
        }

        // Check that the payment requet has been sent;
        expect(request.args[2].url).equal("http://api:3006//v2/play/payment");
        expect(request.args[2].body).deep.include({
            "operation": "payment",
            "currentScene": "scene2",
            "nextScene": "scene3",
            "deviceId": "deviceId",
            "currency": "USD",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "bet": undefined,
            "win": undefined,
            "gameToken": gameData.gameTokenData.token,
            "transactionId": "SOMETRANSACTIONID",
            "eventId": 0,
            "actions": [
                {
                    "action": "debit",
                    "amount": 1,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 5,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ]
        });
        expect(request.args[2].body.ts).is.not.undefined;

        request.clearRoute("PUT", "http://api:3006//v2/play/payment");

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId");

        // check that we store last wallet info in context;
        const context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": gameID,
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 1,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "jackpotPending": undefined,
            "round": undefined,
            "roundId": "1",
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2",
            },
            "jpContext": undefined,
            "gameData": context.gameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "pendingModification": undefined,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "specialState": undefined,
            "version": 4,
        });
    });

    it("is needed to restart game  when we make  spin after fail", async () => {
        request.put("http://api:3006//v2/play/payment", status500());
        loadGame.returns(Promise.resolve(new TestLoadResult(new TestGame([
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse,
                {
                    "gameId": "test_slot",
                    "name": "GAME!",
                    "request": "init",
                    "settings": {
                        "coins": [1],
                        "defaultCoin": 1,
                        "maxTotalStake": 500,
                        "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                        "stakeDef": 1,
                        "stakeMax": 10,
                        "stakeMin": 0.1,
                        "winMax": 3000000,
                        "currencyMultiplier": 100,
                    },
                    "slot": {
                        "stub": "FORTESTREASON",
                    },
                    "previousResult": null,
                    "stake": null,
                } as GameInitResponse
            ],
            [
                {
                    "currentScene": "scene1",
                    "nextScene": "scene2",
                },
                {
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                }
            ], [
                {
                    bet: 1, win: 5,
                    "currentScene": "scene2",
                    "nextScene": "scene3",
                }
            ], [
                { type: "slot", roundEnded: true, data: { positions: [1, 2, 3] } },
            ],
            [
                { request: "spin", totalBet: 1, totalWin: 5 },
            ]
        ))));
        const initResponse = await getSyncGameController().process({
            request: "init",
            requestId: 0,
            gameSession: "GAMESESSSION1",
            startGameToken: startToken,
            name: "GAME!",
            deviceId: "deviceId",
            gameId: "test_slot",
        });

        try {
            await getSyncGameController().process({
                request: "spin",
                requestId: 1,
                gameSession: initResponse.gameSession,
                lines: 10,
                bet: 1,
                coin: 33,
            });
            expect.fail("Expected ManagementAPITransientError");
        } catch (err) {
            if (!(err instanceof ManagementAPITransientError)) {
                throw err;
            }
        }

        // Check that the payment requet has been sent;
        expect(request.args[2].url).equal("http://api:3006//v2/play/payment");
        expect(request.args[2].body).deep.include({
            "operation": "payment",
            "deviceId": "deviceId",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "bet": undefined,
            "win": undefined,
            "currentScene": "scene2",
            "nextScene": "scene3",
            "currency": "USD",
            "eventId": 0,
            "gameToken": gameData.gameTokenData.token,
            "transactionId": "SOMETRANSACTIONID",
            "actions": [
                {
                    "action": "debit",
                    "amount": 1,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 5,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ]
        });
        expect(request.args[2].body.ts).is.not.undefined;
        request.clearRoute("PUT", "http://api:3006//v2/play/payment");

        const gameID = GameContextID.create("test_slot", 1, "PL001", "deviceId");

        // check that we store last wallet info in context;
        const context = await getGameFlowContextManager().findGameContextById(gameID);

        expect(_.omit(context, "createdAt", "updatedAt", "requestContext")).deep.equal({
            "lastRequestId": 1,
            "_prev_sessionId": undefined,
            "_sessionId": GameSession.create(initResponse.gameSession),
            "id": gameID,
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 0,
            "totalEventId": 0,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "roundEnded": true,
            "persistencePolicy": 0,
            "jackpotPending": undefined,
            "round": undefined,
            "roundId": "0",
            "gameData": context.gameData,
            "gameVersion": TEST_MODULE_NAME.version,
            "gameContext": {
                "currentScene": "scene1",
                "nextScene": "scene2",
            },
            "jpContext": undefined,
            "pendingModification": context.pendingModification,
            "settings": {
                "coins": [1],
                "defaultCoin": 1,
                "maxTotalStake": 500,
                "stakeAll": [0.1, 0.5, 1, 2, 3, 5],
                "stakeDef": 1,
                "stakeMax": 10,
                "stakeMin": 0.1,
                "winMax": 3000000,
                "currencyMultiplier": 100,
            },
            "version": 4,
            "specialState": undefined
        });

        expect(context.pendingModification.walletOperation).deep.equal({
            "operation": "payment",
            "bet": 1,
            "currency": "USD",
            "currentScene": "scene2",
            "deviceId": "deviceId",
            "nextScene": "scene3",
            "roundEnded": true,
            "roundId": "0",
            "roundPID": publicId.instance.encode(0),
            "gameSessionId": context.session.sessionId,
            "ts": context.pendingModification.walletOperation.ts,
            "transactionId": "SOMETRANSACTIONID",
            "win": 5,
            "totalBet": 1,
            "totalWin": 5,
            "eventId": 0,
            "totalEventId": 0,
            "retry": 1,
            "actions": [
                {
                    "action": "debit",
                    "amount": 1,
                    "attribute": "balance",
                    "changeType": "bet",
                },
                {
                    "action": "credit",
                    "amount": 5,
                    "attribute": "balance",
                    "changeType": "win",
                }
            ]
        });
        /**
         *  Make next spin
         */
        request.put("http://api:3006//v2/play/payment", status200({
            "currency": "USD",
            "main": 2000,
        }));

        return getSyncGameController().process({
            request: "spin",
            requestId: 2,
            gameSession: initResponse.gameSession,
            lines: 10,
            bet: 1,
            coin: 33,
        }).should.eventually.rejectedWith(NeedToRestartTheGame);
    });
});
