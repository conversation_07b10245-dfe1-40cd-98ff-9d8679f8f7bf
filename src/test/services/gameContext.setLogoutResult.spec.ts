import { createGameToken, flushAll, TEST_MODULE_NAME } from "../helper";
import { suite, test } from "mocha-typescript";
import { GameFlowContext, MerchantLogoutResult } from "../../skywind/services/context/gamecontext";
import { expect } from "chai";
import { getGameFlowContextManager } from "../../skywind/services/contextmanager/contextManagerImpl";
import { Currency, GameMode } from "@skywind-group/sw-game-core";
import { GameContextID } from "../../skywind/services/contextIds";
import { GameData } from "../../skywind/services/auth";
import { GameTokenData } from "../../skywind/services/tokens";
import { GameSession } from "../../skywind/services/gameSession";

@suite()
class GameContextSetLogoutResultSpec {
    public async before() {
        await flushAll();
    }

    public async after() {
        await flushAll();
    }

    @test()
    public async setLogoutResultAndGenerateLogoutId() {
        let ctx: GameFlowContext = await this.createContext("player0001");
        expect(ctx.logoutId).is.undefined;
        expect(ctx.logoutResult).is.undefined;
        await ctx.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);
        ctx = await getGameFlowContextManager().findGameContextById(ctx.id);
        expect(ctx.logoutId).is.not.undefined;
        expect(ctx.logoutResult).equal(MerchantLogoutResult.PENDING_LOGOUT);
        const lastLogoutId = ctx.logoutId;
        await ctx.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);
        expect(ctx.logoutId).equal(lastLogoutId);
        expect(ctx.logoutResult).equal(MerchantLogoutResult.PENDING_LOGOUT);
    }

    @test()
    public async cleanUpLogoutId_1() {
        let ctx: GameFlowContext = await this.createContext("player0001");
        expect(ctx.logoutId).is.undefined;
        expect(ctx.logoutResult).is.undefined;
        await ctx.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);
        ctx = await getGameFlowContextManager().findGameContextById(ctx.id);
        expect(ctx.logoutId).is.not.undefined;
        expect(ctx.logoutResult).equal(MerchantLogoutResult.PENDING_LOGOUT);
        await ctx.setLogoutResult(MerchantLogoutResult.SKIP_LOGIN);
        expect(ctx.logoutId).is.null;
        expect(ctx.logoutResult).equal(MerchantLogoutResult.SKIP_LOGIN);
    }

    @test()
    public async cleanUpLogoutId_2() {
        let ctx: GameFlowContext = await this.createContext("player0001");
        expect(ctx.logoutId).is.undefined;
        expect(ctx.logoutResult).is.undefined;
        await ctx.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);
        ctx = await getGameFlowContextManager().findGameContextById(ctx.id);
        expect(ctx.logoutId).is.not.undefined;
        expect(ctx.logoutResult).equal(MerchantLogoutResult.PENDING_LOGOUT);
        await ctx.setLogoutResult(null);
        expect(ctx.logoutId).is.null;
        expect(ctx.logoutResult).is.null;
    }

    @test()
    public async keepLogoutIdOnRequireLogin() {
        let ctx: GameFlowContext = await this.createContext("player0001");
        expect(ctx.logoutId).is.undefined;
        expect(ctx.logoutResult).is.undefined;
        await ctx.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);
        ctx = await getGameFlowContextManager().findGameContextById(ctx.id);
        expect(ctx.logoutId).is.not.undefined;
        expect(ctx.logoutResult).equal(MerchantLogoutResult.PENDING_LOGOUT);
        const lastLogoutId = ctx.logoutId;
        await ctx.setLogoutResult(MerchantLogoutResult.REQUIRE_LOGIN);
        expect(ctx.logoutId).equal(lastLogoutId);
        expect(ctx.logoutResult).equal(MerchantLogoutResult.REQUIRE_LOGIN);
    }

    private async createContext(playerCode: string, brandId: number = 1,
                                mode: GameMode = "real") {
        const currency: Currency = 0;
        const settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: currency,
            stakeAll: [1, 2, 3, 4],
            stakeDef: currency,
            stakeMax: currency,
            stakeMin: currency,
            winMax: currency,
            currencyMultiplier: 100,
        };

        const gameID: GameContextID = GameContextID.create("gameId", brandId, playerCode, "deviceId", mode);

        const gameData: GameData = {
            gameTokenData: undefined,
            limits: settings,
        };

        const gameTokenData: GameTokenData = {
            playerCode: gameID.playerCode,
            gameCode: "GM001",
            brandId: gameID.brandId,
            currency: "USD",
            playmode: mode
        };

        gameTokenData.token = await createGameToken(gameTokenData);
        gameData.gameTokenData = gameTokenData;
        const sessionId = await GameSession.generate(gameID, "real");

        return getGameFlowContextManager().findOrCreateGameContext(gameID, sessionId, gameData, TEST_MODULE_NAME);
    }
}
