import { expect, should, use } from "chai";
import { GameTokenData } from "../../skywind/services/tokens";
import { GameContextID } from "../../skywind/services/contextIds";
import { GameFlowContext, SpecialState } from "../../skywind/services/context/gamecontext";
import {
    createGameToken,
    flushAll,
    getGameHistoryItem,
    getRoundHistory,
    getSessionHistory,
    syncModels,
    TEST_MODULE_NAME
} from "../helper";
import { PaymentOperation } from "../../skywind/services/wallet";
import { Currency, GameHistory } from "@skywind-group/sw-game-core";
import config from "../../skywind/config";
import { GameData } from "../../skywind/services/auth";
import { GameSession } from "../../skywind/services/gameSession";
import { getGameContextEncoder } from "../../skywind/services/encoder/contextEncoding";
import { GameFlowContextImpl } from "../../skywind/services/context/gameContextImpl";
import { getGameFlowContextManager } from "../../skywind/services/contextmanager/contextManagerImpl";
import { suite, test } from "mocha-typescript";
import { GameFlowInfoImpl } from "../../skywind/services/context/gameFlowInfo";
import { SinonFakeTimers, useFakeTimers } from "sinon";
import { deepClone } from "../../skywind/utils/cloner";

const chaiAsPromise = require("chai-as-promised");
import _ = require("lodash");

should();
use(chaiAsPromise);

export class BaseGameContextSpec {
    public static clock: SinonFakeTimers;
    public readonly contextManager = getGameFlowContextManager();
    public readonly currency: Currency = 0;
    public readonly settings = {
        coins: [3, 4],
        defaultCoin: 4,
        maxTotalStake: this.currency,
        stakeAll: [1, 2, 3, 4],
        stakeDef: this.currency,
        stakeMax: this.currency,
        stakeMin: this.currency,
        winMax: this.currency,
        currencyMultiplier: 100,
    };
    public gameID: GameContextID = GameContextID.create("gameId", 1, "playerId", "deviceId");
    public readonly gameData: GameData = {
        gameTokenData: undefined,
        limits: this.settings,
        gameId: "gameId",
    };
    public readonly gameTokenData: GameTokenData = {
        playerCode: this.gameID.playerCode,
        gameCode: "gameId",
        brandId: this.gameID.brandId,
        currency: "USD",
        playmode: "real"
    };
    public sessionId: GameSession;
    public newSessionId: GameSession;

    public static before() {
        BaseGameContextSpec.clock = useFakeTimers(0);
    }

    public static after() {
        BaseGameContextSpec.clock.restore();
    }

    public async before() {
        this.gameTokenData.token = await createGameToken(this.gameTokenData);
        this.gameData.gameTokenData = this.gameTokenData;
        await flushAll();
        await syncModels();
        this.sessionId = await GameSession.generate(this.gameID, "real");
        this.newSessionId = await GameSession.generate(this.gameID, "real");
    }

}

@suite("GameContext")
class GameContextSpec extends BaseGameContextSpec {

    @test("is created")
    public async testCreated() {
        let context: GameFlowContext = await this.contextManager
            .findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        const expected = {
            currentScene: "scene",
        };

        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);
        expect(context.playerContext.brokenGames).deep.equal([]);

        await context.update(expected);
        context = await this.contextManager.findGameContextById(context.id);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(expected);
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);

        const sessions = await getSessionHistory(0, 100);
        expect(sessions.length).equal(1);
        expect(sessions[0].sessionId).to.equal(context.session.sessionId);
        expect(sessions[0].startedAt).to.exist;
        expect(sessions[0].finishedAt).to.not.exist;
    }

    @test("is created from copy")
    public async testCreatedFromCopy() {
        const id = GameContextID.createFromString(config.namespaces.contextPrefix + ":1:playerId:gameId:deviceId");
        const context: GameFlowContextImpl = new GameFlowContextImpl(id);
        context.gameVersion = "1";
        context.gameContext = {
            scenesState: {},
            currentScene: "scene",
            nextScene: "next",
            history: [],
            stake: null,
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };
        context.session = this.sessionId;
        context.lastRequestId = 1;
        context.gameSerialNumber = 1;
        context.totalEventId = 20;
        context.settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: 0,
            stakeAll: [1, 2, 3, 4],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 100,
            transferEnabled: false,
        };
        context.version = 3;
        context.gameData = this.gameData;
        context.pendingModification = {
            history: {
                type: "slot",
                roundEnded: true,
                data: { positions: [1, 2, 3] }
            },
            walletOperation: {
                operation: "payment",
                transactionId: "trxId",
                currency: "USD",
                bet: 100,
                win: 1000,
            } as PaymentOperation,
            newState: {},
        };

        context.roundEnded = false;
        context.roundId = "1";
        context.createdAt = new Date();
        context.updatedAt = new Date();
        context.round = {
            totalBet: 1,
            totalWin: 2,
            totalEvents: 3,
            balanceBefore: 4,
            balanceAfter: 5
        };

        const newSettings = {
            coins: [5, 6],
            defaultCoin: 5,
            maxTotalStake: 0,
            stakeAll: [5, 6, 7, 8],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 1000,
            transferEnabled: false,
        };

        const copy = await this.contextManager.createContextFrom(id,
            context,
            this.newSessionId,
            this.gameData,
            TEST_MODULE_NAME,
            newSettings);

        const expected = {
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": this.newSessionId,
            "gameData": this.gameData,
            "id": context.id,
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 20,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "gameVersion": "0.1.0",
            "roundEnded": false,
            "roundId": "1",
            "pendingModification": context.pendingModification,
            "gameContext": context.gameContext,
            "settings": newSettings,
            "specialState": undefined,
            "version": 4,
            "jpContext": undefined,
            "persistencePolicy": 0,
            "round": context.round,
        };

        expect(_.omit(copy, "createdAt", "updatedAt", "requestContext")).deep.equal(expected);

        const sessions = await getSessionHistory(0, 100);
        expect(sessions.length).equal(2);
        expect(sessions[0].sessionId).to.equal(this.sessionId.sessionId);
        expect(sessions[0].startedAt).to.exist;
        expect(sessions[0].finishedAt).to.exist;
        expect(sessions[1].sessionId).to.equal(this.newSessionId.sessionId);
        expect(sessions[1].startedAt).to.exist;
        expect(sessions[1].finishedAt).to.not.exist;
    }

    @test("is created from old copy")
    public async testCreatedFromOldCopy() {
        const id = GameContextID.createFromString(config.namespaces.contextPrefix + ":1:playerId:gameId:deviceId");
        const context: GameFlowContextImpl = new GameFlowContextImpl(id);
        context.gameVersion = undefined;
        context.gameContext = {
            scenesState: {},
            currentScene: "scene",
            nextScene: "next",
            history: [],
            stake: null,
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };
        context.session = this.sessionId;
        context.lastRequestId = 1;
        context.gameSerialNumber = 1;
        context.totalEventId = 1;
        context.settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: 0,
            stakeAll: [1, 2, 3, 4],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 100,
            transferEnabled: false,
        };
        context.version = 3;
        context.gameData = this.gameData;
        context.pendingModification = {
            history: {
                type: "slot",
                roundEnded: true,
                data: { positions: [1, 2, 3] }
            },
            walletOperation: {
                actions: [
                    { action: "debit", amount: 100, attribute: "balance" },
                    { action: "credit", amount: 1000, attribute: "balance" }
                ]
            } as any,
            newState: {},
        };

        context.roundEnded = false;
        context.roundId = "1";
        context.createdAt = new Date();
        context.updatedAt = new Date();

        const newSettings = {
            coins: [5, 6],
            defaultCoin: 5,
            maxTotalStake: 0,
            stakeAll: [5, 6, 7, 8],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 1000,
            transferEnabled: false,
        };

        const copy = await this.contextManager.createContextFrom(id,
            await getGameContextEncoder().encode(context),
            this.newSessionId,
            this.gameData,
            TEST_MODULE_NAME,
            newSettings);

        const expected = {
            "lastRequestId": 0,
            "_prev_sessionId": undefined,
            "_sessionId": this.newSessionId,
            "gameData": this.gameData,
            "id": context.id,
            "lockExclusively": false,
            "reactive": true,
            "gameSerialNumber": 1,
            "totalEventId": 1,
            "gameVersion": "0.1.0",
            "roundEnded": false,
            "roundId": "1",
            "pendingModification": {
                "history": {
                    "type": "slot",
                    "roundEnded": true,
                    "data": {
                        "positions": [
                            1,
                            2,
                            3
                        ]
                    }
                },
                "walletOperation": {
                    "actions": [
                        {
                            "action": "debit",
                            "attribute": "balance",
                            "amount": 100
                        },
                        {
                            "action": "credit",
                            "attribute": "balance",
                            "amount": 1000
                        }
                    ]
                },
                "newState": {}
            },
            "jackpotPending": undefined,
            "round": undefined,
            "retryAttempts": undefined,
            "logoutResult": undefined,
            "logoutId": undefined,
            "persistencePolicy": 0,
            "jpContext": undefined,
            "gameContext": context.gameContext,
            "settings": newSettings,
            "specialState": undefined,
            "version": 4,
        };
        expect(_.omit(copy, "createdAt", "updatedAt", "requestContext")).deep.equal(expected);

        const sessions = await getSessionHistory(0, 100);
        expect(sessions.length).equal(1);
        expect(sessions[0].sessionId).to.equal(this.newSessionId.sessionId);
        expect(sessions[0].startedAt).to.exist;
        expect(sessions[0].finishedAt).to.not.exist;
    }

    @test("migrate jp context from 2 version to 3 version")
    public async testMigrateJPContextFrom2To3Version() {
        const id = GameContextID.createFromString(config.namespaces.contextPrefix + ":1:playerId:gameId:deviceId");
        const context: GameFlowContextImpl = new GameFlowContextImpl(id);
        context.gameVersion = undefined;
        context.gameContext = {
            scenesState: {},
            currentScene: "scene",
            nextScene: "next",
            history: [],
            stake: null,
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };
        context.session = this.sessionId;
        context.lastRequestId = 1;
        context.gameSerialNumber = 1;
        context.totalEventId = 1;
        context.settings = {
            coins: [3, 4],
            defaultCoin: 4,
            maxTotalStake: 0,
            stakeAll: [1, 2, 3, 4],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 100,
            transferEnabled: false,
        };
        context.version = 3;
        context.gameData = this.gameData;
        context.pendingModification = {
            history: {
                type: "slot",
                roundEnded: true,
                data: { positions: [1, 2, 3] }
            },
            walletOperation: {
                actions: [
                    { action: "debit", amount: 100, attribute: "balance" },
                    { action: "credit", amount: 1000, attribute: "balance" }
                ]
            } as any,
            newState: {},
        };

        context.roundEnded = false;
        context.roundId = "1";
        context.createdAt = new Date();
        context.updatedAt = new Date();

        const newSettings = {
            coins: [5, 6],
            defaultCoin: 5,
            maxTotalStake: 0,
            stakeAll: [5, 6, 7, 8],
            stakeDef: 0,
            stakeMax: 0,
            stakeMin: 0,
            winMax: 0,
            currencyMultiplier: 1000,
            transferEnabled: false,
        };
        const oldJPContext = {
            token: "jp-token",
            jackpotsInfo: undefined,
            contributionEnabled: true
        };

        const gameContext = await this.contextManager.createContextFrom(id,
            await getGameContextEncoder().encode(context),
            this.newSessionId,
            this.gameData,
            TEST_MODULE_NAME,
            newSettings, undefined, oldJPContext);

        const newJPContext = {
            token: "jp-token",
            jackpotsInfo: [
                {
                    id: "test", jpGameId: "test", currency: "USD", type: "test", baseType: "base",
                    isGameOwned: true, gameHistoryEnabled: true, paymentStatisticEnabled: true
                }
            ],
            contributionEnabled: true
        };

        const gameContextWithNewJPContext = await this.contextManager.createContextFrom(id,
            gameContext,
            this.newSessionId,
            this.gameData,
            TEST_MODULE_NAME,
            newSettings, undefined, newJPContext);
        expect(gameContextWithNewJPContext.jpContext).deep.equals(newJPContext);
    }

    @test("is updated successfully")
    public async testUpdateSuccessFull() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        const context: GameFlowContextImpl = (await this.contextManager.findGameContextById(this.gameID));
        const state = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };
        context.lastRequestId = 2;

        await context.update(state);

        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state);
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);
    }

    @test("is updated and updated from game")
    public async testUpdateAndModifyFromGame() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        const context: GameFlowContextImpl = (await this.contextManager.findGameContextById(this.gameID));
        const state = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };
        context.lastRequestId = 2;

        await context.update(state);

        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state);
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);

        const initialState = { ...state };
        state.currentScene = "modifiedSceneTest";

        expect(context.gameContext).not.deep.equal(state);
        expect(context.gameContext).deep.equal(initialState);
    }

    @test("is updated & committed pending modification")
    public async testUpdateAdnCommitPending() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        let context = await this.contextManager.findGameContextById(this.gameID);
        const request = { request: "req", requestId: 1 };
        const state1 = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };

        const history1: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2, 3],
            }
        };

        const walletOperation1: any = {
            operation: "payment",
            transactionId: "1",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history1.roundEnded,
            bet: 1900,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, state1, request, history1);

        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };

        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        expect(context.gameSerialNumber).equal(0);
        expect(context.totalEventId).equal(0);
        walletOperation1.ts = walletOperation1.ts.toISOString();
        expect(context.pendingModification).deep.equal({
            "history": history1,
            "newState": state1,
            "walletOperation": walletOperation1,
        });
        expect(walletOperation1.totalBet).eq(1900);
        expect(walletOperation1.totalWin).eq(200);

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state1);
        expect(context.gameSerialNumber).equal(0);
        expect(context.totalEventId).equal(1);
        expect(context.pendingModification).is.undefined;

        let result: any = await getGameHistoryItem();
        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 0,
            roundId: "0",
            ctrl: result.ctrl,
            sessionId: this.sessionId.sessionId,
            result: history1.data,
            type: history1.type,
            gameVersion: TEST_MODULE_NAME.version,
            walletTransactionId: "1",
            currency: "USD",
            win: walletOperation1.win,
            bet: walletOperation1.bet,
            roundEnded: true,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 1900,
            totalRoundWin: 200
    });

        const history2: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2],
            }
        };

        const state2 = _.cloneDeep(state1);
        state2["nextScene"] = "SOMESCENE2";

        const walletOperation2: PaymentOperation = {
            operation: "payment",
            transactionId: "2",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.id,
            roundEnded: history2.roundEnded,
            bet: 2000,
            win: 100,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation2, state2, request, history2);

        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state1);
        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history2,
            "newState": state2,
            "walletOperation": walletOperation2,
        });
        expect(context.gameSerialNumber).equal(0);
        expect(context.totalEventId).equal(1);

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state2);
        expect(context.pendingModification).is.undefined;
        expect(context.gameSerialNumber).equal(0);
        expect(context.totalEventId).equal(2);
        expect(walletOperation2.totalBet).eq(2000);
        expect(walletOperation2.totalWin).eq(100);

        result = await getGameHistoryItem();

        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 0,
            gameVersion: TEST_MODULE_NAME.version,
            result: history2.data,
            type: history2.type,
            roundEnded: true,
            ctrl: result.ctrl,
            roundId: "1",
            sessionId: this.sessionId.sessionId,
            walletTransactionId: "2",
            currency: "USD",
            win: walletOperation2.win,
            bet: walletOperation2.bet,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 2000,
            totalRoundWin: 100
        });
        const history3: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2, 3, 4],
            }
        };

        const state3 = _.cloneDeep(state2);
        state3["nextScene"] = "SOMESCENE3";
        const walletOperation3: PaymentOperation = {
            operation: "payment",
            transactionId: "3",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.id,
            roundEnded: history3.roundEnded,
            bet: 100,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation3, state3, request, history3);

        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state2);
        expect(context.gameSerialNumber).equal(0);
        expect(context.totalEventId).equal(2);

        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history3,
            "newState": state3,
            "walletOperation": walletOperation3,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state3);
        expect(context.pendingModification).undefined;
        expect(context.gameSerialNumber).equal(0);
        expect(context.totalEventId).equal(3);
        expect(walletOperation3.totalBet).eq(100);
        expect(walletOperation3.totalWin).eq(200);

        result = await getGameHistoryItem();
        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameTokenData.gameCode,
            eventId: 0,
            result: history3.data,
            type: history3.type,
            gameVersion: TEST_MODULE_NAME.version,
            roundEnded: true,
            ctrl: result.ctrl,
            roundId: "2",
            sessionId: this.sessionId.sessionId,
            walletTransactionId: "3",
            currency: "USD",
            win: walletOperation3.win,
            bet: walletOperation3.bet,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 100,
            totalRoundWin: 200
        });

        const rounds = await getRoundHistory(0, 1000);
        expect(rounds.map((item) => _.omit(item, "finishedAt", "startedAt", "ctrl"))).deep.equals([
            {
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "gameId",
                gameId: "gameId",
                id: "2",
                playerCode: "playerId",
                sessionId: this.sessionId.sessionId,
                totalBet: 100,
                totalEvents: 1,
                totalWin: 200,
                totalJpContribution: 0,
                totalJpWin: 0,
            },
            {
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "gameId",
                gameId: "gameId",
                id: "1",
                playerCode: "playerId",
                sessionId: this.sessionId.sessionId,
                totalBet: 2000,
                totalEvents: 1,
                totalWin: 100,
                totalJpContribution: 0,
                totalJpWin: 0,
            },
            {
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "gameId",
                gameId: "gameId",
                id: "0",
                playerCode: "playerId",
                sessionId: this.sessionId.sessionId,
                totalBet: 1900,
                totalEvents: 1,
                totalWin: 200,
                totalJpContribution: 0,
                totalJpWin: 0,
            }
        ]);
    }

    @test("is updated & committed pending modification with free spins")
    public async testUpdateAdnCommitPending_freeSpins() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        let context = await this.contextManager.findGameContextById(this.gameID);
        const request = { request: "req", requestId: 1 };
        const state1 = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };

        const history1: any = {
            type: "slot",
            roundEnded: false,
            data: {
                positions: [1, 2, 3],
            }
        };

        const walletOperation1: any = {
            operation: "payment",
            transactionId: "1",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history1.roundEnded,
            bet: 1900,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, state1, request, history1);

        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };

        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        expect(context.gameSerialNumber).equal(0);
        expect(context.totalEventId).equal(0);
        walletOperation1.ts = walletOperation1.ts.toISOString();
        expect(context.pendingModification).deep.equal({
            "history": history1,
            "newState": state1,
            "walletOperation": walletOperation1,
        });
        expect(walletOperation1.totalBet).is.undefined;
        expect(walletOperation1.totalWin).is.undefined;

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state1);
        expect(context.gameSerialNumber).equal(1);
        expect(context.totalEventId).equal(1);
        expect(context.pendingModification).is.undefined;

        let result: any = await getGameHistoryItem();
        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 0,
            ctrl: result.ctrl,
            roundId: "0",
            sessionId: this.sessionId.sessionId,
            result: history1.data,
            type: history1.type,
            gameVersion: TEST_MODULE_NAME.version,
            walletTransactionId: "1",
            currency: "USD",
            win: walletOperation1.win,
            bet: walletOperation1.bet,
            roundEnded: history1.roundEnded,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 1900,
            totalRoundWin: 200
        });

        const history2: any = {
            type: "slot",
            roundEnded: false,
            data: {
                positions: [1, 2],
            }
        };

        const state2 = _.cloneDeep(state1);
        state2["nextScene"] = "SOMESCENE2";

        const walletOperation2: PaymentOperation = {
            operation: "payment",
            transactionId: "2",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.id,
            roundEnded: history2.roundEnded,
            bet: 2000,
            win: 100,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation2, state2, request, history2);

        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state1);
        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history2,
            "newState": state2,
            "walletOperation": walletOperation2,
        });
        expect(context.gameSerialNumber).equal(1);
        expect(context.totalEventId).equal(1);

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state2);
        expect(context.pendingModification).is.undefined;
        expect(context.gameSerialNumber).equal(2);
        expect(context.totalEventId).equal(2);
        expect(walletOperation2.totalBet).is.undefined;
        expect(walletOperation2.totalWin).is.undefined;

        result = await getGameHistoryItem();

        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 1,
            ctrl: result.ctrl,
            gameVersion: TEST_MODULE_NAME.version,
            result: history2.data,
            type: history2.type,
            roundEnded: history2.roundEnded,
            roundId: "0",
            sessionId: this.sessionId.sessionId,
            walletTransactionId: "2",
            currency: "USD",
            win: walletOperation2.win,
            bet: walletOperation2.bet,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 3900,
            totalRoundWin: 300
        });
        const history3: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2, 3, 4],
            }
        };

        const state3 = _.cloneDeep(state2);
        state3["nextScene"] = "SOMESCENE3";
        const walletOperation3: PaymentOperation = {
            operation: "payment",
            transactionId: "3",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: this.sessionId.sessionId,
            roundEnded: history3.roundEnded,
            bet: 100,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation3, state3, request, history3);

        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state2);
        expect(context.gameSerialNumber).equal(2);
        expect(context.totalEventId).equal(2);

        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history3,
            "newState": state3,
            "walletOperation": walletOperation3,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state3);
        expect(context.pendingModification).undefined;
        expect(context.gameSerialNumber).equal(0);
        expect(context.totalEventId).equal(3);
        expect(walletOperation3.totalBet).eq(4000);
        expect(walletOperation3.totalWin).eq(500);

        result = await getGameHistoryItem();
        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameTokenData.gameCode,
            eventId: 2,
            ctrl: result.ctrl,
            result: history3.data,
            type: history3.type,
            gameVersion: TEST_MODULE_NAME.version,
            roundEnded: true,
            roundId: "0",
            sessionId: this.sessionId.sessionId,
            walletTransactionId: "3",
            currency: "USD",
            win: walletOperation3.win,
            bet: walletOperation3.bet,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 4000,
            totalRoundWin: 500
        });

        const rounds = await getRoundHistory(0, 1000);
        expect(rounds.map((item) => _.omit(item, "finishedAt", "startedAt", "ctrl"))).deep.equals([
            {
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "gameId",
                gameId: "gameId",
                id: "0",
                playerCode: "playerId",
                sessionId: this.sessionId.sessionId,
                totalBet: 4000,
                totalEvents: 3,
                totalWin: 500,
                totalJpContribution: 0,
                totalJpWin: 0,
            }
        ]);
    }

    @test("is updated & committed pending modification with free spins and jpPayment")
    public async testUpdateAdnCommitPending_freeSpinsAndJPWin() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        let context = await this.contextManager.findGameContextById(this.gameID);
        const request = { request: "req", requestId: 1 };
        const state1 = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };

        const history1: any = {
            type: "slot",
            roundEnded: false,
            data: {
                positions: [1, 2, 3],
            }
        };

        const walletOperation1: any = {
            operation: "payment",
            transactionId: "1",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history1.roundEnded,
            bet: 1900,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, state1, request, history1);

        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };

        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        expect(context.gameSerialNumber).equal(0);
        expect(context.totalEventId).equal(0);
        walletOperation1.ts = walletOperation1.ts.toISOString();
        expect(context.pendingModification).deep.equal({
            "history": history1,
            "newState": state1,
            "walletOperation": walletOperation1,
        });
        expect(walletOperation1.totalBet).is.undefined;
        expect(walletOperation1.totalWin).is.undefined;

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state1);
        expect(context.gameSerialNumber).equal(1);
        expect(context.totalEventId).equal(1);
        expect(context.pendingModification).is.undefined;

        let result: any = await getGameHistoryItem();
        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 0,
            roundId: "0",
            ctrl: result.ctrl,
            sessionId: this.sessionId.sessionId,
            result: history1.data,
            type: history1.type,
            gameVersion: TEST_MODULE_NAME.version,
            walletTransactionId: "1",
            currency: "USD",
            win: walletOperation1.win,
            bet: walletOperation1.bet,
            roundEnded: history1.roundEnded,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 1900,
            totalRoundWin: 200
        });

        const history2: any = {
            type: "slot",
            roundEnded: false,
            data: {
                positions: [1, 2],
            }
        };

        const state2 = _.cloneDeep(state1);
        state2["nextScene"] = "SOMESCENE2";

        const walletOperation2: PaymentOperation = {
            operation: "payment",
            transactionId: "2",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history2.roundEnded,
            bet: 2000,
            win: 100,
            currency: "USD",
            ts: new Date()
        };
        const jpWin: PaymentOperation = {
            operation: "payment",
            transactionId: "3",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: context.session.sessionId,
            roundEnded: history2.roundEnded,
            win: 777,
            bet: 0,
            currency: "USD",
            ts: new Date()
        };

        const jpHistory: GameHistory = {
            type: "jackpot-win", roundEnded: false,
            data: {
                jackpotId: "testJackpot",
                pool: "testPool"
            }
        };

        await context.updatePendingModification(walletOperation2, state2, request, history2, {
            walletOperation: jpWin,
            history: jpHistory
        } as any);

        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state1);
        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history2,
            "newState": state2,
            "walletOperation": walletOperation2,
        });
        expect(context.jackpotPending).deep.equal({
            "history": jpHistory,
            "walletOperation": jpWin,
        });
        expect(context.gameSerialNumber).equal(1);
        expect(context.totalEventId).equal(1);

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state2);
        expect(context.pendingModification).is.undefined;
        expect(context.gameSerialNumber).equal(3);
        expect(context.totalEventId).equal(3);
        expect(walletOperation2.totalBet).is.undefined;
        expect(walletOperation2.totalWin).is.undefined;
        expect(jpWin.totalBet).is.undefined;
        expect(jpWin.totalWin).is.undefined;

        result = await getGameHistoryItem();

        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 2,
            ctrl: result.ctrl,
            gameVersion: TEST_MODULE_NAME.version,
            result: jpHistory.data,
            type: jpHistory.type,
            roundEnded: history2.roundEnded,
            roundId: "0",
            sessionId: this.sessionId.sessionId,
            walletTransactionId: "3",
            currency: "USD",
            win: jpWin.win,
            bet: jpWin.bet,
            ts: result.ts,
            totalJpContribution: 0,
            totalRoundBet: 3900,
            totalRoundWin: 1077
        });

        result = await getGameHistoryItem();

        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 1,
            ctrl: result.ctrl,
            gameVersion: TEST_MODULE_NAME.version,
            result: history2.data,
            type: history2.type,
            roundEnded: history2.roundEnded,
            roundId: "0",
            sessionId: this.sessionId.sessionId,
            walletTransactionId: "2",
            currency: "USD",
            win: walletOperation2.win,
            bet: walletOperation2.bet,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 3900,
            totalRoundWin: 300
        });
        const history3: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2, 3, 4],
            }
        };

        const state3 = _.cloneDeep(state2);
        state3["nextScene"] = "SOMESCENE3";
        const walletOperation3: PaymentOperation = {
            operation: "payment",
            transactionId: "4",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: this.sessionId.sessionId,
            roundEnded: history3.roundEnded,
            bet: 100,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation3, state3, request, history3);

        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state2);
        expect(context.gameSerialNumber).equal(3);
        expect(context.totalEventId).equal(3);

        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history3,
            "newState": state3,
            "walletOperation": walletOperation3,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state3);
        expect(context.pendingModification).undefined;
        expect(context.gameSerialNumber).equal(0);
        expect(context.totalEventId).equal(4);
        expect(walletOperation3.totalBet).eq(4000);
        expect(walletOperation3.totalWin).eq(1277);

        result = await getGameHistoryItem();
        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameTokenData.gameCode,
            eventId: 3,
            ctrl: result.ctrl,
            result: history3.data,
            type: history3.type,
            gameVersion: TEST_MODULE_NAME.version,
            roundEnded: true,
            roundId: "0",
            sessionId: this.sessionId.sessionId,
            walletTransactionId: "4",
            currency: "USD",
            win: walletOperation3.win,
            bet: walletOperation3.bet,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 4000,
            totalRoundWin: 1277
        });

        const rounds = await getRoundHistory(0, 1000);
        expect(rounds.map((item) => _.omit(item, "finishedAt", "startedAt", "ctrl"))).deep.equals([
            {
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "gameId",
                gameId: "gameId",
                id: "0",
                playerCode: "playerId",
                sessionId: this.sessionId.sessionId,
                totalBet: 4000,
                totalEvents: 4,
                totalWin: 1277,
                totalJpContribution: 0,
                totalJpWin: 0
            }
        ]);
    }

    @test("is updated & rollbacked pending modification")
    public async testUpdateAndRollbackPendingModification() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        const context = await this.contextManager.findGameContextById(this.gameID);

        const state1 = {
            currentScene: "sceneTest",
            state: {
                multiplier: 1,
                behaviorsState: {}
            }
        };
        context.lastRequestId = 2;

        const request = { request: "req", requestId: 1 };

        const history1: any = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: {
                positions: [1, 2, 3, 4],
            }
        };

        const walletOperation1: PaymentOperation = {
            operation: "payment",
            transactionId: "1",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: this.sessionId.sessionId,
            roundEnded: history1.roundEnded,
            bet: 1900,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, state1, request, history1, {
            jackpotOperation: {
                type: "contribution",
                payload: {
                    transactionId: "jpn_trx_id",
                    amount: 1000,
                    roundId: "1"
                }
            }
        });

        expect(context).contain({
            id: this.gameID,
            lastRequestId: 2,
        });
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history1,
            "newState": state1,
            "walletOperation": walletOperation1,
        });

        expect(context.jackpotPending).deep.equal({
            jackpotOperation: {
                type: "contribution",
                payload: {
                    transactionId: "jpn_trx_id",
                    amount: 1000,
                    roundId: "1"
                }
            }
        });

        expect(context.roundId).equals("0");
        await context.setSpecialState(SpecialState.FINALIZING);
        await context.rollbackPendingModification();
        expect(context).contain({
            id: this.gameID,
            lastRequestId: 2,
        });
        expect(context.roundId).equals("1");
        expect(context.specialState).is.null;
        expect(context.session).to.not.be.empty;
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        expect(context.pendingModification).is.undefined;
        expect(context.jackpotPending).is.undefined;
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);

        const result: any = await getGameHistoryItem();
        expect(result).is.undefined;
    }

    @test("is updated & rollbacked pending modification without starting new round")
    public async testUpdateAndRollbackPendingModificationWithoutStartingNewRound() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        const context = await this.contextManager.findGameContextById(this.gameID);

        const state1 = {
            currentScene: "sceneTest",
            state: {
                multiplier: 1,
                behaviorsState: {}
            }
        };
        context.lastRequestId = 2;

        const request = { request: "req", requestId: 1 };

        const history1: any = {
            type: "slot",
            version: 1,
            roundEnded: true,
            data: {
                positions: [1, 2, 3, 4],
            }
        };

        const walletOperation1: PaymentOperation = {
            operation: "payment",
            transactionId: "1",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: this.sessionId.sessionId,
            roundEnded: history1.roundEnded,
            bet: 1900,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, state1, request, history1, {
            jackpotOperation: {
                type: "contribution",
                payload: {
                    transactionId: "jpn_trx_id",
                    amount: 1000,
                    roundId: "1"
                }
            }
        });

        expect(context).contain({
            id: this.gameID,
            lastRequestId: 2,
        });
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history1,
            "newState": state1,
            "walletOperation": walletOperation1,
        });

        expect(context.jackpotPending).deep.equal({
            jackpotOperation: {
                type: "contribution",
                payload: {
                    transactionId: "jpn_trx_id",
                    amount: 1000,
                    roundId: "1"
                }
            }
        });

        expect(context.roundId).equals("0");
        await context.setSpecialState(SpecialState.FINALIZING);
        await context.rollbackPendingModification(true);
        expect(context).contain({
            id: this.gameID,
            lastRequestId: 2,
        });
        expect(context.roundId).equals("0");
        expect(context.specialState).is.null;
        expect(context.session).to.not.be.empty;
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        expect(context.pendingModification).is.undefined;
        expect(context.jackpotPending).is.undefined;
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);

        const result: any = await getGameHistoryItem();
        expect(result).is.undefined;
    }

    @test("game data is updated successfully")
    public async testUpdateGameData() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        let context: GameFlowContextImpl = (await this.contextManager.findGameContextById(this.gameID));
        context.gameData.gameTokenData.regulatoryData = { aamsSessionCode: "1", participationStartDate: "2021"};

        await context.updateGameData();
        context = (await this.contextManager.findGameContextById(this.gameID));

        expect(context.settings).deep.equal(this.settings);
        expect(context.playerContext).is.not.undefined;
        const testGameData = _.cloneDeep(this.gameData);
        testGameData.gameTokenData.regulatoryData = { aamsSessionCode: "1", participationStartDate: "2021"};
        expect(context.gameData).deep.equal(testGameData);
        expect(context.gameContext).is.undefined;
    }

    @test("is found by playerCode,gameID, deviceId and brandID")
    public async testFoundByPlayerCodeGameIdDeviceIdAndBrandId() {
        const context1: GameFlowContext = await this.contextManager
            .findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        context1.lastRequestId = 2;
        const state = {
            multiplier: 1,
            behaviorsState: {},
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true,
            }
        };

        await context1.update(state);
        const context2: GameFlowContext = await this.contextManager.findGameContextById(this.gameID);

        const expected = {
            id: this.gameID,
            lastRequestId: 2,
        };

        expect(context2).contain(expected);
        expect(context2.session.id).equals(this.sessionId.id);
        expect(context2.gameData).deep.equal(this.gameData);
        expect(context2.settings).deep.equal(this.settings);
        expect(context2.gameContext).deep.equal(state);
        expect(context2.playerContext).is.not.undefined;
        expect(context2.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);
    }

    @test("is deleted")
    public async testDelete() {
        const context: GameFlowContextImpl = await this.contextManager.findOrCreateGameContext(this.gameID,
            this.sessionId,
            this.gameData,
            TEST_MODULE_NAME);
        const expected = {
            id: this.gameID,
            session: this.sessionId,
            lastRequestId: 0,
        };
        expect(context).contain(expected);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.settings).deep.equal(this.settings);
        expect(context.playerContext).is.not.undefined;
        expect(context.playerContext.activeGames).deep.equal([
            {
                id: {
                    brandId: 1,
                    deviceId: "deviceId",
                    gameCode: "gameId",
                    idValue: "games:context:1:playerId:gameId:deviceId",
                    playerCode: "playerId",
                },
                mode: "real"
            }
        ]);
        await context.remove();
        expect(await this.contextManager.findGameContextById(this.gameID)).is.undefined;

        const [__, playerCtx] = await this.contextManager.findContexts(this.gameID);
        expect(playerCtx).is.not.undefined;
        expect(playerCtx.activeGames).deep.equal([]);
        expect(playerCtx.brokenGames).deep.equal([]);
    }

    @test("is deleted and session history is stored")
    public async testDeleteAndStoreSessionHistory() {
        const context: GameFlowContext = await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId,
            this.gameData, TEST_MODULE_NAME);
        const expected = {
            id: this.gameID,
            session: this.sessionId,
            lastRequestId: 0,
        };
        expect(context).contain(expected);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.settings).deep.equal(this.settings);
        await context.remove();

        const [ctx, playerCtx] = await this.contextManager.findContexts(this.gameID);
        expect(ctx).is.undefined;
        expect(playerCtx).is.not.undefined;
        expect(playerCtx.activeGames).deep.equal([]);
        expect(playerCtx.brokenGames).deep.equal([]);

        const sessions = await getSessionHistory(0, 100);
        expect(sessions.length).equal(2);
        expect(sessions[0].sessionId).to.equal(this.sessionId.sessionId);
        expect(sessions[0].startedAt).to.exist;
        expect(sessions[0].finishedAt).to.exist;
        expect(sessions[1].sessionId).to.equal(this.sessionId.sessionId);
        expect(sessions[1].startedAt).to.exist;
        expect(sessions[1].finishedAt).to.not.exist;
    }

    @test("is deleted and round isn't stored")
    public async testDeleteAndNotStoreRound() {
        let context: GameFlowContextImpl = await this.contextManager
            .findOrCreateGameContext(this.gameID,
                this.sessionId,
                this.gameData,
                TEST_MODULE_NAME);
        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };

        context.roundEnded = true;
        await context.update();
        context = (await this.contextManager.findGameContextById(this.gameID)) as GameFlowContextImpl;
        expect(context).contain(expected);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.settings).deep.equal(this.settings);

        await context.remove();

        const [ctx, playerCtx] = await this.contextManager.findContexts(this.gameID);
        expect(ctx).is.undefined;
        expect(playerCtx).is.not.undefined;
        expect(playerCtx.activeGames).deep.equal([]);
        expect(playerCtx.brokenGames).deep.equal([]);

        const rounds = await getRoundHistory(0, 100);
        expect(rounds.length).equal(0);
    }

    @test("is deleted and round isn't stored because there is no round statistic in context")
    public async testDeleteAndNotStoreRoundWithoutStat() {
        let context: GameFlowContextImpl = await this.contextManager
            .findOrCreateGameContext(this.gameID,
                this.sessionId,
                this.gameData,
                TEST_MODULE_NAME);
        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };

        context.roundEnded = false;
        await context.update();
        context = (await this.contextManager.findGameContextById(this.gameID)) as GameFlowContextImpl;
        expect(context).contain(expected);
        expect(context.session.id).equals(this.sessionId.id);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.settings).deep.equal(this.settings);

        await context.remove();

        const rounds = await getRoundHistory(0, 100);
        expect(rounds.length).equal(0);

        expect(await this.contextManager.findGameContextById(this.gameID)).is.undefined;
    }

    @test("is deleted and round is stored")
    public async testDeleteAndStoreRound() {
        let context: GameFlowContextImpl = await this.contextManager
            .findOrCreateGameContext(this.gameID,
                this.sessionId,
                this.gameData,
                TEST_MODULE_NAME);

        context.roundEnded = false;
        const ts = new Date();
        await context.update();
        context = await this.contextManager.findGameContextById(this.gameID);
        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };
        expect(context).contain(expected);
        expect(context.session.id).equal(this.sessionId.id);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.settings).deep.equal(this.settings);

        context.round = {
            totalBet: 100,
            totalWin: 20,
            totalEvents: 3,
            balanceBefore: 200,
            balanceAfter: 120,
            broken: true,
            startedAt: ts,
        };
        await context.remove(true);

        const [ctx, playerCtx] = await this.contextManager.findContexts(this.gameID);
        expect(ctx).is.undefined;
        expect(playerCtx).is.not.undefined;
        expect(playerCtx.activeGames).deep.equal([]);
        expect(playerCtx.brokenGames).deep.equal([]);

        const rounds = await getRoundHistory(0, 100);
        expect(rounds).deep.equal([
            {
                "sessionId": this.sessionId.sessionId,
                "balanceAfter": 120,
                "balanceBefore": 200,
                "brandId": 1,
                "currency": "USD",
                "deviceId": "deviceId",
                "gameCode": "gameId",
                "gameId": "gameId",
                ctrl: rounds[0].ctrl,
                // if round wasn't finished - try to remove the old version,
                // because the round could've been broken twice
                "broken": true,
                "id": "0",
                "playerCode": "playerId",
                "startedAt": ts.toISOString(),
                "totalBet": 100,
                "totalEvents": 3,
                "totalWin": 20,
            }
        ]);
    }

    @test("isn't found because not exists")
    public async testNotFound() {
        expect(await this.contextManager.findGameContextById(GameContextID.create("gameId",
            1,
            "playerId",
            "deviceId"))).is.undefined;
    }

    @test()
    public async testNewSessionsCreated() {
        await this.contextManager.findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        let context = await this.contextManager.findGameContextById(this.gameID);
        const request = { request: "req", requestId: 1 };
        const state1 = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };

        const history1: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2, 3],
            }
        };

        const walletOperation1: any = {
            operation: "payment",
            transactionId: "1",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history1.roundEnded,
            bet: 1900,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, state1, request, history1);

        const expected = {
            id: this.gameID,
            lastRequestId: 0,
        };

        context = await this.contextManager.findGameContextById(this.gameID);
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).is.undefined;
        expect(context.gameSerialNumber).equal(0);
        expect(context.totalEventId).equal(0);
        walletOperation1.ts = walletOperation1.ts.toISOString();
        expect(context.pendingModification).deep.equal({
            "history": history1,
            "newState": state1,
            "walletOperation": walletOperation1,
        });
        expect(walletOperation1.totalBet).eq(1900);
        expect(walletOperation1.totalWin).eq(200);

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state1);
        expect(context.gameSerialNumber).equal(0);
        expect(context.totalEventId).equal(1);
        expect(context.pendingModification).is.undefined;

        let result: any = await getGameHistoryItem();
        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 0,
            roundId: "0",
            ctrl: result.ctrl,
            sessionId: this.sessionId.sessionId,
            result: history1.data,
            type: history1.type,
            gameVersion: TEST_MODULE_NAME.version,
            walletTransactionId: "1",
            currency: "USD",
            win: walletOperation1.win,
            bet: walletOperation1.bet,
            roundEnded: true,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 1900,
            totalRoundWin: 200
        });

        const sessionId1 = await GameSession.generate(this.gameID, "real");
        context = await this.contextManager.findOrCreateGameContext(this.gameID,
            sessionId1,
            this.gameData,
            TEST_MODULE_NAME);

        const history2: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2],
            }
        };

        const state2 = _.cloneDeep(state1);
        state2["nextScene"] = "SOMESCENE2";

        const walletOperation2: PaymentOperation = {
            operation: "payment",
            transactionId: "2",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: this.sessionId.sessionId,
            roundEnded: history2.roundEnded,
            bet: 2000,
            win: 100,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation2, state2, request, history2);

        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state1);
        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history2,
            "newState": state2,
            "walletOperation": walletOperation2,
        });
        expect(context.gameSerialNumber).equal(0);
        expect(context.totalEventId).equal(1);

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state2);
        expect(context.pendingModification).is.undefined;
        expect(context.gameSerialNumber).equal(0);
        expect(context.totalEventId).equal(2);
        expect(walletOperation2.totalBet).eq(2000);
        expect(walletOperation2.totalWin).eq(100);

        result = await getGameHistoryItem();
        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameID.gameCode,
            eventId: 0,
            ctrl: result.ctrl,
            gameVersion: TEST_MODULE_NAME.version,
            result: history2.data,
            type: history2.type,
            roundEnded: true,
            roundId: "1",
            sessionId: sessionId1.sessionId,
            walletTransactionId: "2",
            currency: "USD",
            win: walletOperation2.win,
            bet: walletOperation2.bet,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 2000,
            totalRoundWin: 100
        });

        const sessionId2 = await GameSession.generate(this.gameID, "real");
        context = await this.contextManager.findOrCreateGameContext(this.gameID,
            sessionId2,
            this.gameData,
            TEST_MODULE_NAME);
        const history3: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2, 3, 4],
            }
        };

        const state3 = _.cloneDeep(state2);
        state3["nextScene"] = "SOMESCENE3";
        const walletOperation3: PaymentOperation = {
            operation: "payment",
            transactionId: "3",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: this.sessionId.sessionId,
            roundEnded: history3.roundEnded,
            bet: 100,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation3, state3, request, history3);

        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameContext).deep.equal(state2);
        expect(context.gameSerialNumber).equal(0);
        expect(context.totalEventId).equal(2);

        expect(context.pendingModification).deep.equal({
            "analytics": undefined,
            "history": history3,
            "newState": state3,
            "walletOperation": walletOperation3,
        });

        await context.commitPendingModification();
        expect(context).contain(expected);
        expect(context.settings).deep.equal(this.settings);
        expect(context.gameData).deep.equal(this.gameData);
        expect(context.gameContext).deep.equal(state3);
        expect(context.pendingModification).undefined;
        expect(context.gameSerialNumber).equal(0);
        expect(context.totalEventId).equal(3);
        expect(walletOperation3.totalBet).eq(100);
        expect(walletOperation3.totalWin).eq(200);

        result = await getGameHistoryItem();
        expect(result).deep.equal({
            gameId: this.gameID.gameCode,
            brandId: this.gameID.brandId,
            playerCode: this.gameID.playerCode,
            deviceId: this.gameID.deviceId,
            gameCode: this.gameTokenData.gameCode,
            eventId: 0,
            ctrl: result.ctrl,
            result: history3.data,
            type: history3.type,
            gameVersion: TEST_MODULE_NAME.version,
            roundEnded: true,
            roundId: "2",
            sessionId: sessionId2.sessionId,
            walletTransactionId: "3",
            currency: "USD",
            win: walletOperation3.win,
            bet: walletOperation3.bet,
            ts: result.ts,
            totalJpContribution: 0,
            totalJpWin: 0,
            totalRoundBet: 100,
            totalRoundWin: 200
        });

        const sessionId3 = await GameSession.generate(this.gameID, "real");
        context = await this.contextManager.findOrCreateGameContext(this.gameID,
            sessionId3,
            this.gameData,
            TEST_MODULE_NAME);

        await context.remove();

        const rounds = await getRoundHistory(0, 1000);
        expect(rounds.map((item) => _.omit(item, "finishedAt", "startedAt", "ctrl"))).deep.equals([
            {
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "gameId",
                gameId: "gameId",
                id: "2",
                playerCode: "playerId",
                sessionId: sessionId2.sessionId,
                totalBet: 100,
                totalEvents: 1,
                totalWin: 200,
                totalJpContribution: 0,
                totalJpWin: 0,
            },
            {
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "gameId",
                gameId: "gameId",
                id: "1",
                playerCode: "playerId",
                sessionId: sessionId1.sessionId,
                totalBet: 2000,
                totalEvents: 1,
                totalWin: 100,
                totalJpContribution: 0,
                totalJpWin: 0,
            },
            {
                brandId: 1,
                broken: false,
                currency: "USD",
                deviceId: "deviceId",
                gameCode: "gameId",
                gameId: "gameId",
                id: "0",
                playerCode: "playerId",
                sessionId: this.sessionId.sessionId,
                totalBet: 1900,
                totalEvents: 1,
                totalWin: 200,
                totalJpContribution: 0,
                totalJpWin: 0,
            }
        ]);
        const sessionsList = await getSessionHistory(0, 100);
        expect(sessionsList.length).equal(8);
        const sessions = new Set((sessionsList).map((item) => item.sessionId));
        expect(sessions.has(this.sessionId.sessionId)).is.true;
        expect(sessions.has(sessionId1.sessionId)).is.true;
        expect(sessions.has(sessionId2.sessionId)).is.true;
        expect(sessions.has(sessionId3.sessionId)).is.true;
    }

    @test("requires transfer out")
    public async testRequireTransferOut() {
        let context: GameFlowContext = await this.contextManager
            .findOrCreateGameContext(this.gameID, this.sessionId, this.gameData, TEST_MODULE_NAME);
        expect(context.requireTransferOut).is.false;

        const data = deepClone(this.gameData);
        data.gameTokenData.transferEnabled = true;
        context = await this.contextManager
            .findOrCreateGameContext(this.gameID, this.sessionId, data, TEST_MODULE_NAME);
        expect(context.requireTransferOut).is.false;
        const request = { request: "req", requestId: 1 };
        const state1 = {
            currentScene: "sceneTest",
            sceneStates: {
                sceneTest: {
                    multiplier: 1,
                    behaviorsState: {}

                }
            },
            previousProcessSceneResult: {
                state: { currentScene: "sceneTest" },
                request: "spin",
                totalBet: 100,
                totalWin: 200,
                roundEnded: true
            }
        };

        const history1: any = {
            type: "slot",
            roundEnded: false,
            data: {
                positions: [1, 2, 3],
            }
        };

        const walletOperation1: any = {
            operation: "payment",
            transactionId: "1",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            roundEnded: history1.roundEnded,
            bet: 1900,
            win: 200,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation1, state1, request, history1);

        expect(context.requireTransferOut).is.false;

        await context.commitPendingModification();

        expect(context.requireTransferOut).is.true;

        const history2: any = {
            type: "slot",
            roundEnded: true,
            data: {
                positions: [1, 2],
            }
        };

        const state2 = _.cloneDeep(state1);
        state2["nextScene"] = "SOMESCENE2";

        const walletOperation2: PaymentOperation = {
            operation: "payment",
            transactionId: "2",
            roundId: context.roundId,
            roundPID: new GameFlowInfoImpl(context).roundPID,
            gameSessionId: this.sessionId.sessionId,
            roundEnded: history2.roundEnded,
            bet: 2000,
            win: 100,
            currency: "USD",
            ts: new Date()
        };
        await context.updatePendingModification(walletOperation2, state2, request, history2);
        expect(context.requireTransferOut).is.true;

        await context.commitPendingModification();
        expect(context.requireTransferOut).is.false;
    }
}
