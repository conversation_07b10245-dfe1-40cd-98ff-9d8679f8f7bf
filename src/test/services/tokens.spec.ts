import { expect } from "chai";
import { GameTokenData, parseGameToken, parseStartToken, StartGameTokenData } from "../../skywind/services/tokens";
import * as jwt from "jsonwebtoken";

require("source-map-support").install();

describe("Tokens", () => {
    describe("Parsing", () => {
        it("Parsing start game token", async () => {
            const tokenData = {
                playerCode: "PL001",
                gameCode: "G001",
                providerCode: "P001",
                providerGameCode: "PG001",
                brandId: 1
            };

            const tokenValue = await jwt.sign(tokenData, "SOMESECRETKEY");
            const data: StartGameTokenData = parseStartToken(tokenValue);

            expect(data).contain(tokenData);
            expect(data.token).equal(tokenValue);
        });

        it("Parsing game token", async () => {
            const tokenData = {
                playerCode: "PL001",
                gameCode: "G001",
                providerCode: "P001",
                brandId: 1,
                currency: "USD",
            };

            const tokenValue = await jwt.sign(tokenData, "SOMESECRETKEY");
            const data: GameTokenData = parseGameToken(tokenValue);

            expect(data).contain(tokenData);
            expect(data.token).equal(tokenValue);
        });

        it("Parsing game token - fun mode", async () => {
            const tokenData = {
                playerCode: "PL001",
                gameCode: "G001",
                providerCode: "P001",
                brandId: 1,
                currency: "USD"
            };

            const tokenValue = await jwt.sign(tokenData, "SOMESECRETKEY");
            const data: GameTokenData = parseGameToken(tokenValue);

            expect(data).contain(tokenData);
            expect(data.token).equal(tokenValue);

        });

        it("Parsing start game token - failed", async () => {
            const tokenValue = "!!!!!!!!!!!";
            const data: StartGameTokenData = parseStartToken(tokenValue);
            expect(data).is.null;
        });

        it("Parsing  game token - failed", async () => {
            const tokenValue = "!!!!!!!!!!!";
            const data: GameTokenData = parseGameToken(tokenValue);
            expect(data).is.null;
        });
    });
});
