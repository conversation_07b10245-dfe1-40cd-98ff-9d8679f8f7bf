import { suite, test } from "mocha-typescript";
import { GameContextID } from "../../../skywind/services/contextIds";
import { GameSession } from "../../../skywind/services/gameSession";
import { activityList, syncModels, TEST_MODULE_NAME } from "../../helper";
import { expect } from "chai";
import { getGameContextModel } from "../../../skywind/services/offlinestorage/models";
import { BaseCleanupGameServiceSpec } from "./cleanupGameContextService.spec";
import { LogoutType } from "../../../skywind/services/auth";
import { testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import { SinonStub, stub } from "sinon";
import * as GameService from "../../../skywind/services/game/game";
import { DumyGame } from "../../testGames";
import { MerchantLogoutResult, SpecialState } from "../../../skywind/services/context/gamecontext";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;
import status200 = testing.status200;
import status500 = testing.status500;

@suite("CleanupGameContextService - completion")
class CleanupGameContextServiceCompletionSpec extends BaseCleanupGameServiceSpec {
    private request: RequestMock;
    private loadGame: SinonStub;

    public static async before() {
        await super.before();
        return syncModels();
    }

    public async before() {
        this.request = requestMock(superagent);
        this.loadGame = stub(GameService, "load");
        this.loadGame.returns(Promise.resolve({ name: "gameId", game: new DumyGame() }));
        return super.before();
    }

    public after() {
        this.loadGame.restore();
        this.request.unmock(superagent);
        return super.after();
    }

    @test()
    public async cleanUpSaveAndRemoveAndCompleteLogout() {
        this.request.post("http://api:3006//v2/play/game/logout", status200({}));
        this.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 1, "PLAYER1", "deviceId");
        const id3 = GameContextID.create("GM0003", 1, "PLAYER1", "deviceId");
        const id4 = GameContextID.create("GM0004", 1, "PLAYER1", "deviceId");

        await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, BaseCleanupGameServiceSpec.gameTokenData.playmode),
            BaseCleanupGameServiceSpec.startGame, TEST_MODULE_NAME);

        const ctx2 = await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, BaseCleanupGameServiceSpec.gameTokenData.playmode),
            BaseCleanupGameServiceSpec.startGame, TEST_MODULE_NAME);

        await ctx2.updatePendingModification({ operation: "payment" } as any,
            { type: "newContext" },
            {} as any,
            { roundEnded: false } as any);

        const ctx3 = await this.contextManager.findOrCreateGameContext(
            id3, await GameSession.generate(id2, BaseCleanupGameServiceSpec.gameTokenData.playmode),
            {
                ...BaseCleanupGameServiceSpec.startGame, ...{ logoutOptions: { type: LogoutType.UNFINISHED } }
            },
            TEST_MODULE_NAME);

        await ctx3.updatePendingModification({ operation: "payment" } as any,
            { type: "newContext" },
            {} as any,
            { type: "slot", roundEnded: false, data: {} });
        await ctx3.commitPendingModification();

        const ctx4 = await this.contextManager.findOrCreateGameContext(
            id4, await GameSession.generate(id4, BaseCleanupGameServiceSpec.gameTokenData.playmode),
            {
                ...BaseCleanupGameServiceSpec.startGame, ...{ logoutOptions: { type: LogoutType.ALL } }
            },
            TEST_MODULE_NAME);

        await ctx4.updatePendingModification({ operation: "payment" } as any,
            { type: "newContext" },
            {} as any,
            { type: "slot", roundEnded: true, data: {} });
        await ctx4.commitPendingModification();

        this.currentClock.restore();
        await this.contextManager.removeGameContextActivity(id1.asString(), 0);
        await this.contextManager.removeGameContextActivity(id2.asString(), 0);
        await this.contextManager.removeGameContextActivity(id3.asString(), 0);
        await this.contextManager.removeGameContextActivity(id4.asString(), 0);
        const result = await this.service.cleanUp([id1.asString(), id2.asString(), id3.asString(), id4.asString()],
            false);
        expect(result).deep.equal([2, 1]);

        expect(await this.contextManager.findGameContextById(id1)).is.undefined;
        expect(await this.contextManager.findGameContextById(id2)).is.undefined;
        expect(await this.contextManager.findGameContextById(id3)).is.undefined;
        expect(await this.contextManager.findGameContextById(id4)).is.not.undefined;

        const dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(2);
        expect(dbItems[0].id).equal(ctx2.id.asString());
        expect(dbItems[1].id).equal(ctx3.id.asString());
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/game/logout");
        expect(this.request.args[1].url).equals("http://api:3006//v2/play/game/logout");
        expect(this.request.args.length).equals(2);

        expect(await activityList(false)).deep.equal([id4.asString()]);
        const playerContext = await getGameFlowContextManager().findPlayerContextById(id1.playerContextID);
        expect(playerContext.activeGames.map(id => id.id.asString())).deep.equal([
            id4.asString()
        ]);
        expect(playerContext.brokenGames.map(id => id.id.asString()).sort()).deep.equal([
            id2.asString(), id3.asString()
        ]);
    }

    @test()
    public async cleanUpSaveAndRemoveAndCompleteLogoutFailed() {
        this.request.post("http://api:3006//v2/play/game/logout", status500({}));
        this.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 1, "PLAYER1", "deviceId");
        const id3 = GameContextID.create("GM0003", 1, "PLAYER1", "deviceId");

        await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, BaseCleanupGameServiceSpec.gameTokenData.playmode),
            BaseCleanupGameServiceSpec.startGame, TEST_MODULE_NAME);

        const ctx2 = await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, BaseCleanupGameServiceSpec.gameTokenData.playmode),
            BaseCleanupGameServiceSpec.startGame, TEST_MODULE_NAME);

        await ctx2.updatePendingModification({ operation: "payment" } as any, { type: "newContext" }, {} as any);

        const ctx3 = await this.contextManager.findOrCreateGameContext(
            id3, await GameSession.generate(id2, BaseCleanupGameServiceSpec.gameTokenData.playmode),
            {
                ...BaseCleanupGameServiceSpec.startGame, ...{
                    logoutOptions: {
                        type: LogoutType.UNFINISHED,
                        maxRetryAttempts: 2
                    }
                }
            },
            TEST_MODULE_NAME);

        await ctx3.updatePendingModification({ operation: "payment" } as any,
            { type: "newContext" },
            {} as any,
            { type: "slot", roundEnded: false, data: {} });
        await ctx3.commitPendingModification();

        this.currentClock.restore();
        await this.contextManager.removeGameContextActivity(id1.asString(), 0);
        await this.contextManager.removeGameContextActivity(id2.asString(), 0);
        await this.contextManager.removeGameContextActivity(id3.asString(), 0);
        let result = await this.service.cleanUp([id1.asString(), id2.asString(), id3.asString()], false);
        expect(result).deep.equal([1, 1]);

        expect(await this.contextManager.findGameContextById(id1)).is.undefined;
        expect(await this.contextManager.findGameContextById(id2)).is.undefined;
        expect(await this.contextManager.findGameContextById(id3)).is.not.undefined;
        let playerContext = await getGameFlowContextManager().findPlayerContextById(id1.playerContextID);
        expect(playerContext.activeGames.map(id => id.id.asString())).deep.equal([id3.asString()]);
        expect(playerContext.brokenGames.map(id => id.id.asString())).deep.equal([
            id2.asString()
        ]);

        const dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(1);
        expect(dbItems[0].id).equal(ctx2.id.asString());
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/game/logout");
        expect(this.request.args.length).equals(1);

        expect((await activityList())[0]).deep.equal(ctx3.id.asString());

        this.request.clearRoutes();
        this.request.post("http://api:3006//v2/play/game/logout", status200({}));
        await this.contextManager.removeGameContextActivity(id3.asString(), 0);
        result = await this.service.cleanUp([id3.asString()], false);
        expect(result).deep.equal([1, 0]);
        const dbItem = await getGameContextModel().findByPk(id3.asString());
        expect(dbItem.requireLogout).is.false;
        expect(dbItem.broken).is.true;
        expect(dbItem.brokenPayment).is.false;
        expect(dbItem.retryAttempts).is.null;
        expect(dbItem.specialState).is.null;
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/game/logout");
        expect(this.request.args.length).equals(1);
        expect((await activityList())).deep.equal([]);
        playerContext = await getGameFlowContextManager().findPlayerContextById(id1.playerContextID);
        expect(playerContext.activeGames.map(id => id.id.asString())).deep.equal([]);
        expect(playerContext.brokenGames.map(id => id.id.asString()).sort()).deep.equal([
            id2.asString(), id3.asString()
        ]);
    }

    @test()
    public async cleanUpSaveAndRemoveAndPaymentLogout() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/logout", status200({}));
        this.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 1, "PLAYER1", "deviceId");
        const id3 = GameContextID.create("GM0003", 1, "PLAYER1", "deviceId");

        await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, BaseCleanupGameServiceSpec.gameTokenData.playmode),
            BaseCleanupGameServiceSpec.startGame, TEST_MODULE_NAME);

        const ctx2 = await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, BaseCleanupGameServiceSpec.gameTokenData.playmode),
            BaseCleanupGameServiceSpec.startGame, TEST_MODULE_NAME);

        await ctx2.updatePendingModification({ operation: "payment" } as any, { type: "newContext" }, {} as any);

        const ctx3 = await this.contextManager.findOrCreateGameContext(
            id3, await GameSession.generate(id2, BaseCleanupGameServiceSpec.gameTokenData.playmode),
            {
                ...BaseCleanupGameServiceSpec.startGame,
                ...{
                    logoutOptions: { type: LogoutType.UNFINISHED, maxRetryAttempts: 2 },
                    settings: { maxPaymentRetryAttempts: 2 }
                }
            },
            TEST_MODULE_NAME);

        await ctx3.updatePendingModification({ operation: "payment" } as any,
            { type: "newContext" },
            {} as any,
            { type: "slot", roundEnded: false, data: {} });

        this.currentClock.restore();
        await this.contextManager.removeGameContextActivity(id1.asString(), 0);
        await this.contextManager.removeGameContextActivity(id2.asString(), 0);
        await this.contextManager.removeGameContextActivity(id3.asString(), 0);
        const result = await this.service.cleanUp([id1.asString(), id2.asString(), id3.asString()], false);
        expect(result).deep.equal([2, 1]);

        expect(await this.contextManager.findGameContextById(id1)).is.undefined;
        expect(await this.contextManager.findGameContextById(id2)).is.undefined;

        const dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(2);
        expect(dbItems[0].id).equal(ctx2.id.asString());
        expect(dbItems[0].requireLogout).is.false;
        expect(dbItems[0].broken).is.true;
        expect(dbItems[0].brokenPayment).is.true;
        expect(dbItems[0].retryAttempts).is.null;
        expect(dbItems[0].specialState).is.null;
        expect(dbItems[1].id).equal(ctx3.id.asString());
        expect(dbItems[1].requireLogout).is.false;
        expect(dbItems[1].broken).is.true;
        expect(dbItems[1].brokenPayment).is.false;
        expect(dbItems[1].retryAttempts).is.null;
        expect(dbItems[1].specialState).is.null;
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[1].url).equals("http://api:3006//v2/play/game/logout");
        expect(this.request.args.length).equals(2);

        expect(await activityList()).deep.equal([]);
        const playerContext = await getGameFlowContextManager().findPlayerContextById(id1.playerContextID);
        expect(playerContext.activeGames.map(id => id.id.asString())).deep.equal([]);
        expect(playerContext.brokenGames.map(id => id.id.asString()).sort()).deep.equal([
            id2.asString(), id3.asString()
        ]);
    }

    @test()
    public async cleanUpLogoutControlForBrokenPayment() {
        this.request.put("http://api:3006//v2/play/payment", status500({
            code: 806
        }));
        this.request.post("http://api:3006//v2/play/game/logout", status200({}));
        this.currentClock.setSystemTime(9);
        const id3 = GameContextID.create("GM0003", 1, "PLAYER34", "deviceId");
        const ctx3 = await this.contextManager.findOrCreateGameContext(
            id3, await GameSession.generate(id3, BaseCleanupGameServiceSpec.gameTokenData.playmode),
            {
                ...BaseCleanupGameServiceSpec.startGame,
                ...{
                    logoutOptions: { type: LogoutType.ALL, maxRetryAttempts: 2 },

                    settings: {
                        maxPaymentRetryAttempts: 2,
                        logoutControl: {
                            ignorePayments: {
                                offlineRetry: true,
                                gameClosure: true,
                                gameRelaunch: true
                            }
                        },
                    }
                }
            },
            TEST_MODULE_NAME);

        await ctx3.updatePendingModification({ operation: "payment" } as any,
            { type: "newContext" },
            {} as any,
            { type: "slot", roundEnded: false, data: {} });

        this.currentClock.restore();
        await this.contextManager.removeGameContextActivity(id3.asString(), 0);
        expect(await this.service.cleanUp([id3.asString()], false)).to.be.deep.equal([1, 0]);

        const dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(1);
        expect(dbItems[0].requireLogout).is.false;
        expect(dbItems[0].broken).is.true;
        expect(dbItems[0].brokenPayment).is.true;
        expect(dbItems[0].retryAttempts).is.null;
        expect(dbItems[0].specialState).is.to.be.equal(3);
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args[1].url).equals("http://api:3006//v2/play/game/logout");
        expect(this.request.args.length).equals(2);

        expect(await activityList()).deep.equal([]);
        const playerContext = await getGameFlowContextManager().findPlayerContextById(id3.playerContextID);
        expect(playerContext.activeGames.map(id => id.id.asString())).deep.equal([]);
        expect(playerContext.brokenGames.map(id => id.id.asString()).sort()).deep.equal([id3.asString()]);
    }

    @test()
    public async cleanUpSaveAndRemoveAndPaymentLogout_AndReactivate() {
        this.request.put("http://api:3006//v2/play/payment", status200({
            currency: "USD",
            main: 100,
        }));
        this.request.post("http://api:3006//v2/play/game/logout", status200({}));
        this.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 1, "PLAYER1", "deviceId");
        const id3 = GameContextID.create("GM0003", 1, "PLAYER1", "deviceId");

        await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, BaseCleanupGameServiceSpec.gameTokenData.playmode),
            BaseCleanupGameServiceSpec.startGame, TEST_MODULE_NAME);

        const ctx2 = await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, BaseCleanupGameServiceSpec.gameTokenData.playmode),
            BaseCleanupGameServiceSpec.startGame, TEST_MODULE_NAME);

        await ctx2.updatePendingModification({ operation: "payment" } as any,
            { type: "newContext" },
            {} as any,
            { roundEnded: false } as any);

        const ctx3 = await this.contextManager.findOrCreateGameContext(
            id3, await GameSession.generate(id3, BaseCleanupGameServiceSpec.gameTokenData.playmode),
            {
                ...BaseCleanupGameServiceSpec.startGame, ...{
                    logoutOptions: { type: LogoutType.UNFINISHED, maxRetryAttempts: 2 },
                    settings: { maxPaymentRetryAttempts: 2 }
                }
            },
            TEST_MODULE_NAME);

        await ctx3.updatePendingModification({ operation: "payment" } as any,
            { type: "newContext" },
            {} as any,
            { type: "slot", roundEnded: true, data: {} });

        this.currentClock.restore();
        await this.contextManager.removeGameContextActivity(id1.asString(), 0);
        await this.contextManager.removeGameContextActivity(id2.asString(), 0);
        await this.contextManager.removeGameContextActivity(id3.asString(), 0);
        const result = await this.service.cleanUp([id1.asString(), id2.asString(), id3.asString()], false);
        expect(result).deep.equal([1, 1]);

        expect(await this.contextManager.findGameContextById(id1)).is.undefined;
        expect(await this.contextManager.findGameContextById(id2)).is.undefined;
        expect(await this.contextManager.findGameContextById(id3)).is.not.undefined;

        const dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(1);
        expect(dbItems[0].id).equal(ctx2.id.asString());
        expect(dbItems[0].requireLogout).is.false;
        expect(dbItems[0].broken).is.true;
        expect(dbItems[0].brokenPayment).is.true;
        expect(dbItems[0].retryAttempts).is.null;
        expect(dbItems[0].specialState).is.null;
        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args.length).equals(1);

        expect(await activityList(false)).deep.equal([id3.asString()]);
        const playerContext = await getGameFlowContextManager().findPlayerContextById(id1.playerContextID);
        expect(playerContext.activeGames.map(id => id.id.asString())).deep.equal([id3.asString()]);
        expect(playerContext.brokenGames.map(id => id.id.asString())).deep.equal([
            id2.asString()
        ]);
    }

    @test()
    public async cleanupSaveAfterSeveralFailedAttempts() {
        this.request.post("http://api:3006//v2/play/game/logout", status500({}));
        this.currentClock.setSystemTime(9);
        const id = GameContextID.create("GM0003", 1, "PLAYER1", "deviceId");

        let ctx = await this.contextManager.findOrCreateGameContext(
            id, await GameSession.generate(id, BaseCleanupGameServiceSpec.gameTokenData.playmode),
            {
                ...BaseCleanupGameServiceSpec.startGame, ...{
                    logoutOptions: {
                        type: LogoutType.UNFINISHED,
                        maxRetryAttempts: 3
                    }
                }
            },
            TEST_MODULE_NAME);

        await ctx.updatePendingModification({ operation: "payment" } as any,
            { type: "newContext" },
            {} as any,
            { type: "slot", roundEnded: false, data: {} });
        await ctx.commitPendingModification();

        this.currentClock.restore();
        expect(await this.service.cleanUp([id.asString()], false)).deep.equal([0, 0]);
        ctx = await this.contextManager.findGameContextById(id);
        expect(ctx.retryAttempts).equal(1);
        expect(ctx.logoutResult).equal(MerchantLogoutResult.PENDING_LOGOUT);
        let dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(0);

        expect(await this.service.cleanUp([id.asString()], false)).deep.equal([0, 0]);
        ctx = await this.contextManager.findGameContextById(id);
        expect(ctx.retryAttempts).equal(2);
        expect(ctx.logoutResult).equal(MerchantLogoutResult.PENDING_LOGOUT);
        dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(0);
        expect(await this.service.cleanUp([id.asString()], false)).deep.equal([1, 0]);
        ctx = await this.contextManager.findGameContextById(id);
        expect(ctx.retryAttempts).equal(3);
        expect(ctx.logoutResult).equal(MerchantLogoutResult.PENDING_LOGOUT);
        dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(1);
        expect(dbItems[0].requireLogout).equal(true);
        expect(dbItems[0].broken).equal(true);
        expect(dbItems[0].specialState).is.null;
        expect(dbItems[0].retryAttempts).equal(3);

        const playerContext = await getGameFlowContextManager().findPlayerContextById(id.playerContextID);
        expect(playerContext.activeGames.map(info => info.id.asString())).deep.equal([]);
        expect(playerContext.brokenGames.map(info => info.id.asString())).deep.equal([id.asString()]);
    }

    @test()
    public async cleanupCannotCompletePayment() {
        this.request.put("http://api:3006//v2/play/payment", status500({
            code: 806,
        }));
        this.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 1, "PLAYER1", "deviceId");
        const id3 = GameContextID.create("GM0003", 1, "PLAYER1", "deviceId");

        await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, BaseCleanupGameServiceSpec.gameTokenData.playmode),
            BaseCleanupGameServiceSpec.startGame, TEST_MODULE_NAME);

        const ctx2 = await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, BaseCleanupGameServiceSpec.gameTokenData.playmode),
            BaseCleanupGameServiceSpec.startGame, TEST_MODULE_NAME);

        await ctx2.updatePendingModification({ operation: "payment" } as any, { type: "newContext" }, {} as any);

        const ctx3 = await this.contextManager.findOrCreateGameContext(
            id3, await GameSession.generate(id2, BaseCleanupGameServiceSpec.gameTokenData.playmode),
            {
                ...BaseCleanupGameServiceSpec.startGame,
                ...{
                    settings: { maxPaymentRetryAttempts: 2 }
                }
            },
            TEST_MODULE_NAME);

        await ctx3.updatePendingModification({ operation: "payment" } as any,
            { type: "newContext" },
            {} as any,
            { type: "slot", roundEnded: false, data: {} });

        this.currentClock.restore();
        await this.contextManager.removeGameContextActivity(id1.asString(), 0);
        await this.contextManager.removeGameContextActivity(id2.asString(), 0);
        await this.contextManager.removeGameContextActivity(id3.asString(), 0);
        const result = await this.service.cleanUp([id1.asString(), id2.asString(), id3.asString()], false);
        expect(result).deep.equal([2, 1]);

        expect(await this.contextManager.findGameContextById(id1)).is.undefined;
        expect(await this.contextManager.findGameContextById(id2)).is.undefined;
        expect(await this.contextManager.findGameContextById(id3)).is.undefined;

        const dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(2);
        expect(dbItems[0].id).equal(ctx2.id.asString());
        expect(dbItems[0].requireLogout).is.false;
        expect(dbItems[0].broken).is.true;
        expect(dbItems[0].brokenPayment).is.true;
        expect(dbItems[0].retryAttempts).is.null;
        expect(dbItems[0].specialState).is.null;
        expect(dbItems[1].id).equal(ctx3.id.asString());
        expect(dbItems[1].requireLogout).is.false;
        expect(dbItems[1].broken).is.true;
        expect(dbItems[1].specialState).equal(SpecialState.CANNOT_COMPLETE_PAYMENT);
        expect(dbItems[1].brokenPayment).is.true;
        expect(dbItems[1].retryAttempts).equal(1);

        expect(this.request.args[0].url).equals("http://api:3006//v2/play/payment");
        expect(this.request.args.length).equals(1);

        expect(await activityList()).deep.equal([]);
        const playerContext = await getGameFlowContextManager().findPlayerContextById(id1.playerContextID);
        expect(playerContext.activeGames.map(id => id.id.asString())).deep.equal([]);
        expect(playerContext.brokenGames.map(id => id.id.asString()).sort()).deep.equal([
            id2.asString(), id3.asString()
        ]);
    }
}
