import { expect, should, use } from "chai";
import { SinonFakeTimers, useFakeTimers } from "sinon";
import {
    activityListCount,
    createGameToken,
    flushAll,
    getCleanupPlayerContextRequests,
    playersActivityList,
    playersActivityListCount,
    syncModels,
    TEST_MODULE_NAME
} from "../../helper";
import { CleanupPlayerContextJob } from "../../../skywind/services/cleanup/cleanupJob";
import config from "../../../skywind/config";
import { GameContextID } from "../../../skywind/services/contextIds";
import { GameData } from "../../../skywind/services/auth";
import { GameSession } from "../../../skywind/services/gameSession";
import { Redis } from "../../../skywind/storage/redis";
import { suite, test } from "mocha-typescript";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { getPlayerContextModel } from "../../../skywind/services/offlinestorage/models";
import { GameTokenData } from "../../../skywind/services/tokens";
import { CleanupGameContextService } from "../../../skywind/services/cleanup/cleanupGameContextService";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("Cleanup player context job")
class CleanupPlayerContextJobSpec {
    private static currentClock: SinonFakeTimers;
    private static settings = {
        coins: [1],
        defaultCoin: 1,
        maxTotalStake: 500,
        stakeAll: [0.1, 0.5, 1, 2, 3, 5],
        stakeDef: 1,
        stakeMax: 10,
        stakeMin: 0.1,
        winMax: 3000000,
        currencyMultiplier: 100,
    };
    private static gameID = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
    private static gameTokenData: GameTokenData = {
        playerCode: CleanupPlayerContextJobSpec.gameID.playerCode,
        gameCode: CleanupPlayerContextJobSpec.gameID.playerCode,
        brandId: CleanupPlayerContextJobSpec.gameID.brandId,
        currency: "USD",
        playmode: "real"
    };
    private static sessionId: GameSession;
    private static prevMaxSpinDuration: number;
    private static prevExpireIn: number;
    private contextManager = getGameFlowContextManager();
    private job: CleanupPlayerContextJob = new CleanupPlayerContextJob();
    private startGame: GameData = {
        gameTokenData: CleanupPlayerContextJobSpec.gameTokenData,
        gameId: CleanupPlayerContextJobSpec.gameTokenData.gameCode,
        limits: CleanupPlayerContextJobSpec.settings,
    };

    public static async before() {
        CleanupPlayerContextJobSpec.gameTokenData.token =
            await createGameToken(CleanupPlayerContextJobSpec.gameTokenData);
        CleanupPlayerContextJobSpec.sessionId = await GameSession.generate(CleanupPlayerContextJobSpec.gameID,
            CleanupPlayerContextJobSpec.gameTokenData.playmode);
        await syncModels();
        CleanupPlayerContextJobSpec.currentClock = useFakeTimers();
        CleanupPlayerContextJobSpec.prevExpireIn = config.cleanUp.expireIn;
        config.cleanUp.expireIn = 0;
        CleanupPlayerContextJobSpec.prevMaxSpinDuration = config.unloader.maxSpinPopDuration;
        config.unloader.maxSpinPopDuration = -1;
    }

    public static async after() {
        config.cleanUp.expireIn = CleanupPlayerContextJobSpec.prevExpireIn;
        config.unloader.maxSpinPopDuration = CleanupPlayerContextJobSpec.prevMaxSpinDuration;
        CleanupPlayerContextJobSpec.currentClock.restore();
    }

    public async after() {
        CleanupPlayerContextJobSpec.currentClock.reset();
    }

    public async before() {
        await getPlayerContextModel().truncate();
        return flushAll();
    }

    @test("doesn't do anything if we haven't any activity")
    public async noActivity() {
        await this.job.moveContextToQueue();
        expect(await getCleanupPlayerContextRequests(0, 1000)).deep.equal([]);
    }

    @test("doesn't move context which is not cold")
    public async doesntMoveNotColdContext() {
        const ctx = await this.contextManager.findOrCreateGameContext(CleanupPlayerContextJobSpec.gameID,
            CleanupPlayerContextJobSpec.sessionId, this.startGame, TEST_MODULE_NAME);

        CleanupPlayerContextJobSpec.currentClock.setSystemTime(10);
        await ctx.update();
        CleanupPlayerContextJobSpec.currentClock.setSystemTime(9);
        await this.job.moveContextToQueue();
        expect(await getCleanupPlayerContextRequests(0, 1000)).deep.equal([]);
    }

    @test("move context which is  cold and is broken")
    public async moveContextToQueue() {
        const ctx = await this.contextManager.findOrCreateGameContext(CleanupPlayerContextJobSpec.gameID,
            CleanupPlayerContextJobSpec.sessionId,
            this.startGame,
            TEST_MODULE_NAME);

        CleanupPlayerContextJobSpec.currentClock.setSystemTime(10);
        await ctx.update();
        CleanupPlayerContextJobSpec.currentClock.setSystemTime(600020);
        await this.job.moveContextToQueue();

        expect(await getCleanupPlayerContextRequests(0, 100)).deep.equal([{ id: ctx.id.playerContextID.asString() }]);

        await ctx.updatePendingModification({ type: "payment", bet: 10, win: 100 } as any, {}, undefined);

        await new CleanupGameContextService().cleanUp([ctx.id.asString()], true);
        await this.job.unloadData();

        expect(await getCleanupPlayerContextRequests(0, 100)).deep.equal([]);
        expect(await this.contextManager.findPlayerContextById(ctx.id.playerContextID)).is.undefined;
        const dbItems = await getPlayerContextModel().findAll();
        expect(dbItems.length).equals(1);
        expect(dbItems[0].id).equal(ctx.id.playerContextID.asString());
    }

    @test("skips removing player context, because it has active game")
    public async skipRemovingPlayerContext() {
        const ctx = await this.contextManager.findOrCreateGameContext(CleanupPlayerContextJobSpec.gameID,
            CleanupPlayerContextJobSpec.sessionId,
            this.startGame,
            TEST_MODULE_NAME);

        CleanupPlayerContextJobSpec.currentClock.setSystemTime(10);
        await ctx.update();
        CleanupPlayerContextJobSpec.currentClock.setSystemTime(600020);
        await this.job.moveContextToQueue();

        expect(await getCleanupPlayerContextRequests(0, 100)).deep.equal([{ id: ctx.id.playerContextID.asString() }]);

        await this.job.unloadData();

        expect(await getCleanupPlayerContextRequests(0, 100)).deep.equal([]);
        expect(await this.contextManager.findPlayerContextById(ctx.id.playerContextID)).is.not.undefined;
        const dbItems = await getPlayerContextModel().findAll();
        // save to db but don't remove from redis because of context is active
        expect(dbItems.length).equals(1);
        expect(await playersActivityList()).deep.equal([ctx.id.playerContextID.asString(), "1200020"]);
    }

    @test("remove activity if context is not found")
    public async removeActivityIfContextIsNotFound() {
        const id = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        expect(await activityListCount()).equal(0);
        const client = await Redis.get().get();
        CleanupPlayerContextJobSpec.currentClock.setSystemTime(10);
        try {
            await client.zadd(config.namespaces.lastPlayerActivityPrefix,
                Date.now().toString(),
                id.playerContextID.asString());
        } finally {
            await Redis.get().release(client);
        }

        expect(await playersActivityListCount()).equal(1);

        CleanupPlayerContextJobSpec.currentClock.setSystemTime(11);
        await this.job.moveContextToQueue();

        expect(await playersActivityListCount()).equal(0);

        // check that context is moved from redis
        expect(await this.contextManager.findPlayerContextById(id.playerContextID)).is.undefined;
    }
}
