import { expect, should, use } from "chai";
import { SinonFakeTimers, useFakeTimers } from "sinon";
import { flushAll, hGetAll } from "../../helper";
import { suite, test } from "mocha-typescript";
import JobStateTracker from "../../../skywind/services/cleanup/forceCleanupJobStateTracker";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("Force cleanup job tracker")
class ForceCleanupJobStateTrackerSpec̰ {

    private clock: SinonFakeTimers;

    public async before() {
        this.clock = useFakeTimers();
        return flushAll();
    }

    public after() {
        this.clock.restore();
    }

    @test()
    public async testInit() {
        this.clock.setSystemTime(1000);
        await JobStateTracker.init(1);
        await JobStateTracker.init(2);
        await JobStateTracker.init(3, "12345");
        expect(await JobStateTracker.getJobs()).deep.equal([
            {
                brandId: 1,
                lastTs: 1000,
                traceID: ""
            },
            {
                brandId: 2,
                lastTs: 1000,
                traceID: ""
            },
            {
                brandId: 3,
                lastTs: 1000,
                traceID: "12345"
            }
        ]);
    }

    @test()
    public async testMarkDone() {
        this.clock.setSystemTime(1000);
        await JobStateTracker.init(1, "12345");
        await JobStateTracker.init(2);
        await JobStateTracker.init(3);

        await JobStateTracker.markAsDone(1);

        expect(await JobStateTracker.getJobs()).deep.equal([
            {
                brandId: 2,
                lastTs: 1000,
                traceID: ""
            },
            {
                brandId: 3,
                lastTs: 1000,
                traceID: ""
            }
        ]);
    }

    @test()
    public async testMarkAsAlive() {
        this.clock.setSystemTime(1000);
        await JobStateTracker.init(1);
        await JobStateTracker.init(2);
        await JobStateTracker.init(3, "12345");
        this.clock.setSystemTime(2000);
        await JobStateTracker.markAsAlive(1);

        expect(await JobStateTracker.getJobs()).deep.equal([
            {
                brandId: 1,
                lastTs: 2000,
                traceID: ""
            },
            {
                brandId: 2,
                lastTs: 1000,
                traceID: ""
            },
            {
                brandId: 3,
                lastTs: 1000,
                traceID: "12345"
            }
        ]);
    }

    @test()
    public async testCatchJob() {
        this.clock.setSystemTime(1000);
        await JobStateTracker.init(1, "12345");

        const states = await JobStateTracker.getJobs();
        expect(states).deep.equal([
            {
                brandId: 1,
                lastTs: 1000,
                traceID: "12345"
            }
        ]);

        this.clock.setSystemTime(2000);
        expect(await JobStateTracker.catchJob(states[0])).is.true;
        expect(await JobStateTracker.getJobs()).deep.equal([
            {
                brandId: 1,
                lastTs: 2000,
                traceID: "12345"
            }
        ]);
    }

    @test()
    public async testCatchJobRejected() {
        this.clock.setSystemTime(1000);
        await JobStateTracker.init(1, "12345");

        const states = await JobStateTracker.getJobs();
        expect(states).deep.equal([
            {
                brandId: 1,
                lastTs: 1000,
                traceID: "12345"
            }
        ]);

        this.clock.setSystemTime(2000);
        expect(await JobStateTracker.catchJob(states[0])).is.true;
        expect(await JobStateTracker.catchJob(states[0])).is.false;
        expect(await JobStateTracker.getJobs()).deep.equal([
            {
                brandId: 1,
                lastTs: 2000,
                traceID: "12345"
            }
        ]);
    }
}
