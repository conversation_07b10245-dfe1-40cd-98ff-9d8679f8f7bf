import { expect, should, use } from "chai";
import { SinonFakeTimers, useFakeTimers } from "sinon";
import {
    activityListCount,
    createGameToken,
    flushAll,
    getCleanupContextRequests,
    getSessionHistory,
    syncModels,
    TEST_MODULE_NAME
} from "../../helper";
import { CleanupGameContextJob } from "../../../skywind/services/cleanup/cleanupJob";
import config from "../../../skywind/config";
import { GameContextID } from "../../../skywind/services/contextIds";
import { PaymentOperation } from "../../../skywind/services/wallet";
import { GameContextPersistencePolicy } from "@skywind-group/sw-game-core";
import { getGameContextEncoder } from "../../../skywind/services/encoder/contextEncoding";
import { GameData } from "../../../skywind/services/auth";
import { GameSession } from "../../../skywind/services/gameSession";
import { Redis } from "../../../skywind/storage/redis";
import { suite, test, timeout } from "mocha-typescript";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { getGameContextModel } from "../../../skywind/services/offlinestorage/models";
import { GameTokenData } from "../../../skywind/services/tokens";
import { GameFlowContextImpl } from "../../../skywind/services/context/gameContextImpl";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("Cleanup game context job")
class CleanupGameContextJobSpec {
    private static currentClock: SinonFakeTimers;
    private static settings = {
        coins: [1],
        defaultCoin: 1,
        maxTotalStake: 500,
        stakeAll: [0.1, 0.5, 1, 2, 3, 5],
        stakeDef: 1,
        stakeMax: 10,
        stakeMin: 0.1,
        winMax: 3000000,
        currencyMultiplier: 100,
    };
    private static gameID = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
    private static gameTokenData: GameTokenData = {
        playerCode: CleanupGameContextJobSpec.gameID.playerCode,
        gameCode: CleanupGameContextJobSpec.gameID.playerCode,
        brandId: CleanupGameContextJobSpec.gameID.brandId,
        currency: "USD",
        playmode: "real"
    };
    private static sessionId: GameSession;
    private static prevMaxSpinDuration: number;
    private static prevExpireIn;
    private contextManager = getGameFlowContextManager();
    private job: CleanupGameContextJob = new CleanupGameContextJob();
    private startGame: GameData = {
        gameTokenData: CleanupGameContextJobSpec.gameTokenData,
        gameId: CleanupGameContextJobSpec.gameTokenData.gameCode,
        limits: CleanupGameContextJobSpec.settings,
    };

    public static async before() {
        CleanupGameContextJobSpec.gameTokenData.token = await createGameToken(CleanupGameContextJobSpec.gameTokenData);
        CleanupGameContextJobSpec.sessionId = await GameSession.generate(CleanupGameContextJobSpec.gameID,
            CleanupGameContextJobSpec.gameTokenData.playmode);
        await syncModels();
        CleanupGameContextJobSpec.currentClock = useFakeTimers();
        CleanupGameContextJobSpec.prevExpireIn = config.cleanUp.expireIn;
        config.cleanUp.expireIn = 0;
        CleanupGameContextJobSpec.prevMaxSpinDuration = config.unloader.maxSpinPopDuration;
        config.unloader.maxSpinPopDuration = -1;
    }

    public static async after() {
        config.unloader.maxSpinPopDuration = CleanupGameContextJobSpec.prevMaxSpinDuration;
        CleanupGameContextJobSpec.currentClock.restore();
        config.cleanUp.expireIn = CleanupGameContextJobSpec.prevExpireIn;
    }

    public async after() {
        CleanupGameContextJobSpec.currentClock.reset();
    }

    public async before() {
        await getGameContextModel().truncate();
        return flushAll();
    }

    @test("doesn't do anything if we haven't any activity")
    public async noActivity() {
        await this.job.moveContextToQueue();
        expect(await getCleanupContextRequests(0, 1000)).deep.equal([]);
    }

    @test("doesn't move context which is not cold")
    public async doesntMoveNotColdContext() {
        const ctx = await this.contextManager.findOrCreateGameContext(CleanupGameContextJobSpec.gameID,
            CleanupGameContextJobSpec.sessionId, this.startGame, TEST_MODULE_NAME);

        CleanupGameContextJobSpec.currentClock.setSystemTime(10);
        await ctx.update();
        CleanupGameContextJobSpec.currentClock.setSystemTime(9);
        await this.job.moveContextToQueue();

        expect(await getCleanupContextRequests(0, 1000)).deep.equal([]);
    }

    @test("move context which is  cold and is unbroken but we have pending wallet operation")
    public async moveContextWithPendingWalletOperation() {
        const ctx = await this.contextManager
            .findOrCreateGameContext(GameContextID.create("GM0001", 1, "PLAYER1", "deviceId"),
                CleanupGameContextJobSpec.sessionId, this.startGame, TEST_MODULE_NAME);

        const request = { request: "req", requestId: 1 };

        const newState = {
            state: { currentScene: "sceneTest" },
        };

        const historyItem = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 2, 3] } };
        await ctx.updatePendingModification(
            { operation: "payment", transactionId: "1", bet: 100, win: 20, currency: "USD" } as PaymentOperation,
            newState, request, historyItem);

        CleanupGameContextJobSpec.currentClock.setSystemTime(10);
        await ctx.update();
        CleanupGameContextJobSpec.currentClock.setSystemTime(11);
        await this.job.moveContextToQueue();

        expect(await getCleanupContextRequests(0, 100)).deep.equal([{ id: ctx.id.asString() }]);
        await
            this.job.unloadData();
        expect(await getCleanupContextRequests(0, 100)).deep.equal([]);

        // checks that we stored context
        const storedContext = await
            this.contextManager.findOfflineGameContext(ctx.id);
        expect(storedContext.data).deep.equal(await
            getGameContextEncoder().encode(ctx as GameFlowContextImpl)
        )
        ;
        // checks that there is no activities
        const activityIds = await this.contextManager.getLruGameContextIDs(Number.MAX_SAFE_INTEGER, 10);
        expect(activityIds).deep.equal([]);
        // check that context is moved from redis
        expect(await this.contextManager.findGameContextById(ctx.id)).is.undefined;
    }

    @test("move context which is cold and unbroken but we have jackpot")
    public async moveContextWithJP() {
        const jpCtx = {
            token: "jpn-token",
            jackpotsInfo: [
                {
                    id: "test", jpGameId: "test", currency: "EUR", type: "test", baseType: "base",
                    isGameOwned: true, gameHistoryEnabled: true, paymentStatisticEnabled: true
                }
            ],
            contributionEnabled: true,
            jackpot: {
                transactionId: "1",
                result: [
                    {
                        jackpotId: "test",
                        event: "start-mini-game",
                        transactionId: "1",
                    }
                ]
            }
        };
        const ctx = await this.contextManager
            .findOrCreateGameContext(GameContextID.create("GM0001", 1, "PLAYER1", "deviceId"),
                CleanupGameContextJobSpec.sessionId, this.startGame, TEST_MODULE_NAME, undefined, undefined, jpCtx);

        CleanupGameContextJobSpec.currentClock.setSystemTime(10);
        await ctx.update();
        CleanupGameContextJobSpec.currentClock.setSystemTime(11);
        await this.job.moveContextToQueue();

        expect(await getCleanupContextRequests(0, 100)).deep.equal([{ id: ctx.id.asString() }]);
        await this.job.unloadData();
        expect(await getCleanupContextRequests(0, 100)).deep.equal([]);

        // checks that we stored context
        const storedContext = await this.contextManager.findOfflineGameContext(ctx.id);
        expect(storedContext.data).deep.equal(await getGameContextEncoder().encode(ctx as GameFlowContextImpl));
        // checks that there is no activities
        const activityIds = await this.contextManager.getLruGameContextIDs(Number.MAX_SAFE_INTEGER, 10);
        expect(activityIds).deep.equal([]);
        // check that context is moved from redis
        expect(await this.contextManager.findGameContextById(ctx.id)).is.undefined;
    }

    @test("delete context which is cold and unbroken")
    public async deleteColdUnbrokenContext() {
        const ctx = await this.contextManager.findOrCreateGameContext(GameContextID.create("GM0001",
            1,
            "PLAYER1",
            "deviceId"),
            CleanupGameContextJobSpec.sessionId,
            this.startGame,
            TEST_MODULE_NAME);

        const newState = {
            nextScene: "SOMESCENE",
        };

        const historyItem = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 2, 3] } };
        await ctx.updatePendingModification(
            { operation: "payment", transactionId: "1", bet: 100, win: 20, currency: "USD" } as PaymentOperation,
            newState, { request: "req", requestId: 1 }, historyItem);
        await ctx.commitPendingModification();

        CleanupGameContextJobSpec.currentClock.setSystemTime(10);
        await ctx.update();
        CleanupGameContextJobSpec.currentClock.setSystemTime(11);
        await this.job.moveContextToQueue();

        expect(await getCleanupContextRequests(0, 100)).deep.equal([{ id: ctx.id.asString() }]);
        await this.job.unloadData();
        expect(await getCleanupContextRequests(0, 100)).deep.equal([]);

        // checks that we stored context
        const storedContext = await this.contextManager.findOfflineGameContext(ctx.id);
        expect(storedContext).is.undefined;
        // checks that there is no activities
        const activityIds = await
            this.contextManager.getLruGameContextIDs(Number.MAX_SAFE_INTEGER, 10);
        expect(activityIds).deep.equal([]);
        // check that context is moved from redis
        expect(await this.contextManager.findGameContextById(ctx.id)).is.undefined;
    }

    @test("move cold part of broken contexts by batch size until we have 'cold' and leave others")
    @timeout(15000)
    public async moveBrokenAndLeftActive() {
        const coldSize = 100;
        const notColdSize = 200;
        const getGameID = (i) => {
            return GameContextID.create("GM0001", 1, "PLAYER1", "deviceId" + i);
        };
        const sessions = [];

        for (let i = 0; i < coldSize; i++) {
            const gameContextID = getGameID(i);
            const nextSession = await GameSession.generate(CleanupGameContextJobSpec.gameID,
                CleanupGameContextJobSpec.gameTokenData.playmode);
            sessions.push(nextSession);
            const ctx = await this.contextManager.findOrCreateGameContext(gameContextID,
                nextSession,
                this.startGame,
                TEST_MODULE_NAME);

            const newState = {
                nextScene: "SOMESCENE" + i,
            };

            await ctx.updatePendingModification(
                {
                    operation: "payment",
                    transactionId: "trx" + i,
                    bet: 100,
                    win: 20,
                    currency: "USD"
                } as PaymentOperation,
                newState, { request: "req", requestId: 1 });
            await ctx.commitPendingModification();

            CleanupGameContextJobSpec.currentClock.setSystemTime(10);
            await ctx.update();
        }

        for (let i = coldSize; i < coldSize + notColdSize; i++) {
            const nextSessionId = await GameSession.generate(CleanupGameContextJobSpec.gameID,
                CleanupGameContextJobSpec.gameTokenData.playmode);
            sessions.push(nextSessionId);
            const ctx = await this.contextManager.findOrCreateGameContext(getGameID(i),
                nextSessionId,
                this.startGame,
                TEST_MODULE_NAME);
            CleanupGameContextJobSpec.currentClock.setSystemTime(200);
            await ctx.update();
        }

        CleanupGameContextJobSpec.currentClock.setSystemTime(100);
        await this.job.moveContextToQueue();

        expect((await getCleanupContextRequests(0, coldSize)).length).equal(coldSize);

        await this.job.unloadData();

        expect((await getCleanupContextRequests(0, 100)).length).equal(0);

        // checks that we stored contexts
        const storedContexts: any[] = await getGameContextModel()
            .findAll();
        expect(storedContexts.length).equal(coldSize);
        // checks that there is no activities
        const activityIds = await this.contextManager.getLruGameContextIDs(Number.MAX_SAFE_INTEGER, 1000);
        expect(activityIds.length).deep.equal(notColdSize);
        // checks that the contexts are moved from redis
        for (let i = 0; i < coldSize; i++) {
            expect(await this.contextManager.findGameContextById(getGameID(i))).is.undefined;
        }
        // checks that not-'cold' contexts stay in redis
        for (let i = coldSize; i < coldSize + notColdSize; i++) {
            const context = await this.contextManager
                .findGameContextById(getGameID(i));
            expect(context.session.id).equal(sessions[i].id);
        }
    }

    @test("move context that isn't broken but has persistencePolicy !== NORMAL")
    public async moveWithPersistencePolicy() {
        const ctx = await this.contextManager
            .findOrCreateGameContext(GameContextID.create("GM0001", 1, "PLAYER1", "deviceId"),
                CleanupGameContextJobSpec.sessionId, this.startGame, TEST_MODULE_NAME);

        const request = { request: "req", requestId: 1 };

        const newState = {
            state: { currentScene: "sceneTest" },
        };

        const historyItem = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 2, 3] } };
        ctx.persistencePolicy = GameContextPersistencePolicy.LONG_TERM;
        await ctx.updatePendingModification(
            { operation: "payment", transactionId: "1", bet: 100, win: 20, currency: "USD" } as PaymentOperation,
            newState, request, historyItem);
        await ctx.commitPendingModification();

        CleanupGameContextJobSpec.currentClock.setSystemTime(10);
        await ctx.update();
        CleanupGameContextJobSpec.currentClock.setSystemTime(11);
        await this.job.moveContextToQueue();

        expect(await getCleanupContextRequests(0, 100)).deep.equal([{ id: ctx.id.asString() }]);

        await this.job.unloadData();

        expect(await getCleanupContextRequests(0, 100)).deep.equal([]);

        // checks that we stored context
        const storedContext = await this.contextManager.findOfflineGameContext(ctx.id);
        expect(storedContext.data).deep.equal(await getGameContextEncoder().encode(ctx as GameFlowContextImpl));
        // checks that there is no activities
        const activityIds = await this.contextManager.getLruGameContextIDs(Number.MAX_SAFE_INTEGER, 10);
        expect(activityIds).deep.equal([]);
        // check that context is moved from redis
        expect(await this.contextManager.findGameContextById(ctx.id)).is.undefined;
    }

    @test("remove old context from offline storage")
    public async removeOldContextFromOfflineStorage() {
        const id = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        let ctx = await this.contextManager.findOrCreateGameContext(id,
            CleanupGameContextJobSpec.sessionId,
            this.startGame,
            TEST_MODULE_NAME);

        ctx.persistencePolicy = GameContextPersistencePolicy.LONG_TERM;
        await ctx.update();

        CleanupGameContextJobSpec.currentClock.setSystemTime(10);
        await ctx.update();
        CleanupGameContextJobSpec.currentClock.setSystemTime(11);
        await this.job.moveContextToQueue();

        expect(await getCleanupContextRequests(0, 100)).deep.equal([{ id: ctx.id.asString() }]);
        await this.job.unloadData();
        expect(await getCleanupContextRequests(0, 100)).deep.equal([]);

        // checks that we stored context
        let storedContext = await this.contextManager.findOfflineGameContext(ctx.id);
        const data = await getGameContextEncoder().encode(ctx as GameFlowContextImpl);
        expect(storedContext.data).deep.equal(data);

        ctx = await this.contextManager.findOrCreateGameContext(id,
            CleanupGameContextJobSpec.sessionId,
            this.startGame,
            TEST_MODULE_NAME);

        ctx.persistencePolicy = GameContextPersistencePolicy.NORMAL;
        await ctx.update();

        CleanupGameContextJobSpec.currentClock.setSystemTime(10);
        await ctx.update();
        CleanupGameContextJobSpec.currentClock.setSystemTime(11);
        await this.job.moveContextToQueue();

        expect(await getCleanupContextRequests(0, 100)).deep.equal([{ id: ctx.id.asString() }]);
        await this.job.unloadData();
        expect(await getCleanupContextRequests(0, 100)).deep.equal([]);

        // checks that we stored context
        storedContext = await this.contextManager.findOfflineGameContext(ctx.id);
        expect(storedContext).is.undefined;

        // checks that there is no activities
        const activityIds = await this.contextManager.getLruGameContextIDs(Number.MAX_SAFE_INTEGER, 10);
        expect(activityIds).deep.equal([]);
        // check that context is moved from redis
        expect(await this.contextManager.findGameContextById(ctx.id)).is.undefined;
    }

    @test("remove activity if context is not found")
    public async removeActivityIfContextIsNotFound() {
        const id = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        expect(await activityListCount()).equal(0);
        const client = await Redis.get().get();
        CleanupGameContextJobSpec.currentClock.setSystemTime(10);
        try {
            await client.zadd(config.namespaces.lastGameActivityKey, Date.now().toString(), id.asString());
        } finally {
            await Redis.get().release(client);
        }

        expect(await activityListCount()).equal(1);

        CleanupGameContextJobSpec.currentClock.setSystemTime(11);
        await this.job.moveContextToQueue();

        expect(await activityListCount()).equal(0);

        // check that context is moved from redis
        expect(await this.contextManager.findGameContextById(id)).is.undefined;
    }

    @test("remove exclusively locked context")
    public async removeExlusivelyLockedContext() {
        const ctx = await this.contextManager.findOrCreateGameContext(GameContextID.create("GM0001",
            1,
            "PLAYER1",
            "deviceId"),
            CleanupGameContextJobSpec.sessionId,
            this.startGame,
            TEST_MODULE_NAME);

        const newState = {
            nextScene: "SOMESCENE",
        };

        const historyItem = { type: "slot", version: 1, roundEnded: true, data: { positions: [1, 2, 3] } };
        await ctx.updatePendingModification(
            { operation: "payment", transactionId: "1", bet: 100, win: 20, currency: "USD" } as PaymentOperation,
            newState, { request: "req", requestId: 1 }, historyItem);
        await ctx.commitPendingModification();

        CleanupGameContextJobSpec.currentClock.setSystemTime(10);
        await ctx.update();
        CleanupGameContextJobSpec.currentClock.setSystemTime(11);
        await this.job.moveContextToQueue();

        expect(await getCleanupContextRequests(0, 100)).deep.equal([{ id: ctx.id.asString() }]);
        await this.contextManager.findGameContextById(ctx.id, true);
        await this.job.unloadData();
        expect(await getCleanupContextRequests(0, 100)).deep.equal([]);
        // checks that we stored context
        const storedContext = await this.contextManager.findOfflineGameContext(ctx.id);
        expect(storedContext).is.undefined;
        // checks that there is no activities
        const activityIds = await
            this.contextManager.getLruGameContextIDs(Number.MAX_SAFE_INTEGER, 10);
        expect(activityIds).deep.equal([]);
        // check that context is moved from redis
        expect(await this.contextManager.findGameContextById(ctx.id)).is.undefined;
    }

    @test("check session ended once")
    public async testSessionEndedOnce() {
        let ctx = await this.contextManager.findOrCreateGameContext(CleanupGameContextJobSpec.gameID,
            await GameSession.generate(CleanupGameContextJobSpec.gameID,
                CleanupGameContextJobSpec.gameTokenData.playmode),
            this.startGame,
            TEST_MODULE_NAME);

        const newState = {
            state: { currentScene: "sceneTest" },
        };

        await ctx.updatePendingModification({
                operation: "payment",
                transactionId: "1",
                bet: 100,
                win: 20,
                currency: "USD"
            } as PaymentOperation,
            newState, { request: "req", requestId: 1 });
        await ctx.commitPendingModification();

        CleanupGameContextJobSpec.currentClock.setSystemTime(10);
        await ctx.update();
        CleanupGameContextJobSpec.currentClock.setSystemTime(11);
        await this.job.moveContextToQueue();

        expect(await getCleanupContextRequests(0, 100)).deep.equal([{ id: ctx.id.asString() }]);

        await this.job.unloadData();

        expect(await getCleanupContextRequests(0, 100)).deep.equal([]);

        // checks that we stored context
        let storedContext = await this.contextManager.findOfflineGameContext(ctx.id);
        ctx.round.broken = true;
        expect(ctx.session.isFinished).is.undefined;
        expect(storedContext.data).deep.equal(await getGameContextEncoder().encode(ctx as GameFlowContextImpl));
        // checks that there is no activities
        let activityIds = await this.contextManager.getLruGameContextIDs(Number.MAX_SAFE_INTEGER, 10);
        expect(activityIds).deep.equal([]);
        // check that context is moved from redis
        expect(await this.contextManager.findGameContextById(ctx.id)).is.undefined;
        let sessions = await getSessionHistory(0, 1000);

        expect(sessions.length).eq(2);
        expect(sessions[0].sessionId).eq(ctx.session.sessionId);
        expect(sessions[0].finishedAt).is.not.undefined;
        expect(sessions[1].sessionId).eq(ctx.session.sessionId);
        expect(sessions[1].finishedAt).is.undefined;

        ctx = await this.contextManager.findOrRestoreGameContext(ctx.id);
        expect(ctx.session.isFinished).is.true;
        sessions = await getSessionHistory(0, 1000);

        expect(sessions.length).eq(2);
        expect(sessions[0].sessionId).eq("0");
        expect(sessions[0].finishedAt).is.not.undefined;
        expect(sessions[1].sessionId).eq("0");
        expect(sessions[1].finishedAt).is.undefined;

        CleanupGameContextJobSpec.currentClock.setSystemTime(10);
        await ctx.update();
        CleanupGameContextJobSpec.currentClock.setSystemTime(11);
        await this.job.moveContextToQueue();

        expect(await getCleanupContextRequests(0, 100)).deep.equal([{ id: ctx.id.asString() }]);

        await this.job.unloadData();

        expect(await getCleanupContextRequests(0, 100)).deep.equal([]);

        // checks that we stored context
        storedContext = await this.contextManager.findOfflineGameContext(ctx.id);
        ctx.round.broken = true;
        expect(ctx.session.isFinished).is.true;
        expect(storedContext.data).deep.equal(await getGameContextEncoder().encode(ctx as GameFlowContextImpl));
        // checks that there is no activities
        activityIds = await this.contextManager.getLruGameContextIDs(Number.MAX_SAFE_INTEGER, 10);
        expect(activityIds).deep.equal([]);
        // check that context is moved from redis
        expect(await this.contextManager.findGameContextById(ctx.id)).is.undefined;

        sessions = await getSessionHistory(0, 1000);
        expect(sessions.length).eq(2);
        expect(sessions[0].sessionId).eq(ctx.session.sessionId);
        expect(sessions[0].finishedAt).is.not.undefined;
        expect(sessions[1].sessionId).eq(ctx.session.sessionId);
        expect(sessions[1].finishedAt).is.undefined;

        // new session
        ctx = await this.contextManager.findOrCreateGameContext(CleanupGameContextJobSpec.gameID,
            await GameSession.generate(CleanupGameContextJobSpec.gameID,
                CleanupGameContextJobSpec.gameTokenData.playmode),
            this.startGame,
            TEST_MODULE_NAME);

        CleanupGameContextJobSpec.currentClock.setSystemTime(10);
        await ctx.update();
        CleanupGameContextJobSpec.currentClock.setSystemTime(11);
        await this.job.moveContextToQueue();

        expect(await getCleanupContextRequests(0, 100)).deep.equal([{ id: ctx.id.asString() }]);

        await this.job.unloadData();

        expect(await getCleanupContextRequests(0, 100)).deep.equal([]);

        // checks that we stored context
        storedContext = await this.contextManager.findOfflineGameContext(ctx.id);
        ctx.round.broken = true;
        expect(ctx.session.isFinished).is.undefined;
        expect(storedContext.data).deep.equal(await getGameContextEncoder().encode(ctx as GameFlowContextImpl));
        // checks that there is no activities
        activityIds = await this.contextManager.getLruGameContextIDs(Number.MAX_SAFE_INTEGER, 10);
        expect(activityIds).deep.equal([]);
        // check that context is moved from redis
        expect(await this.contextManager.findGameContextById(ctx.id)).is.undefined;

        sessions = await getSessionHistory(0, 1000);
        expect(sessions.length).eq(4);
        expect(sessions[0].sessionId).eq("1");
        expect(sessions[0].finishedAt).is.not.undefined;
        expect(sessions[1].sessionId).eq("1");
        expect(sessions[1].finishedAt).is.undefined;
        expect(sessions[2].sessionId).eq("0");
        expect(sessions[2].finishedAt).is.not.undefined;
        expect(sessions[3].sessionId).eq("0");
        expect(sessions[3].finishedAt).is.undefined;

        ctx = await this.contextManager.findOrRestoreGameContext(ctx.id);
        expect(ctx.session.isFinished).is.true;
        sessions = await getSessionHistory(0, 1000);

        expect(sessions.length).eq(4);
        expect(sessions[0].sessionId).eq("1");
        expect(sessions[0].finishedAt).is.not.undefined;
        expect(sessions[1].sessionId).eq("1");
        expect(sessions[1].finishedAt).is.undefined;
        expect(sessions[2].sessionId).eq("0");
        expect(sessions[2].finishedAt).is.not.undefined;
        expect(sessions[3].sessionId).eq("0");
        expect(sessions[3].finishedAt).is.undefined;
    }
}
