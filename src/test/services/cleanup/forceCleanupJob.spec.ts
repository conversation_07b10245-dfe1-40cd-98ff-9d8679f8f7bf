import { expect, should, use } from "chai";
import { SinonFakeTimers, useFakeTimers } from "sinon";
import {
    activityListCount,
    createGameToken,
    flushAll,
    playersActivityListCount,
    syncModels,
    TEST_MODULE_NAME
} from "../../helper";
import { suite, test, timeout } from "mocha-typescript";
import { ForceCleanupJob } from "../../../skywind/services/cleanup/forceCleanupJob";
import { GameContextID } from "../../../skywind/services/contextIds";
import { GameSession } from "../../../skywind/services/gameSession";
import { getGameContextModel, getPlayerContextModel } from "../../../skywind/services/offlinestorage/models";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { GameTokenData } from "../../../skywind/services/tokens";
import { GameData } from "../../../skywind/services/auth";
import JobStateTracker from "../../../skywind/services/cleanup/forceCleanupJobStateTracker";
import { sleep, testing } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
import RequestMock = testing.RequestMock;
import requestMock = testing.requestMock;

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("Force cleanup job")
class ForceCleanupJobStateTrackerSpec̰ {
    private currentClock: SinonFakeTimers;
    private contextManager = getGameFlowContextManager();
    private request: RequestMock;

    private settings = {
        coins: [1],
        defaultCoin: 1,
        maxTotalStake: 500,
        stakeAll: [0.1, 0.5, 1, 2, 3, 5],
        stakeDef: 1,
        stakeMax: 10,
        stakeMin: 0.1,
        winMax: 3000000,
        currencyMultiplier: 100,
    };

    private gameID = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");

    private gameTokenData: GameTokenData = {
        playerCode: this.gameID.playerCode,
        gameCode: this.gameID.playerCode,
        brandId: this.gameID.brandId,
        currency: "USD",
        playmode: "real"
    };

    private startGame: GameData = {
        gameTokenData: this.gameTokenData,
        gameId: this.gameTokenData.gameCode,
        limits: this.settings,
    };

    private sessionId: GameSession;

    public async before() {
        this.request = requestMock(superagent);
        this.request.clearRoutes();
        this.gameTokenData.token = await createGameToken(this.gameTokenData);
        this.sessionId = await GameSession.generate(this.gameID,
            this.gameTokenData.playmode);
        await syncModels();
        this.currentClock = useFakeTimers();
        // without timeout for test purposes
        await getGameContextModel().truncate();
        await getPlayerContextModel().truncate();
        return flushAll();
    }

    public after() {
        this.request.unmock(superagent);
        this.currentClock.restore();
    }

    @test()
    @timeout(5000)
    public async testCleanupGamesAndPlayersContexts() {
        this.request.post("http://api:4004/force-cleanup/finish", testing.status(201));
        this.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 2, "PLAYER1", "deviceId");

        await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, this.gameTokenData.playmode),
            this.startGame, TEST_MODULE_NAME);

        const ctx2 = await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, this.gameTokenData.playmode),
            this.startGame, TEST_MODULE_NAME);

        await ctx2.updatePendingModification({ type: "payment" } as any,
            { type: "newContext" },
            {} as any);

        const job = new ForceCleanupJob(2);
        job.execute();
        this.currentClock.restore();
        await sleep(1000);

        expect(await this.contextManager.findGameContextById(id1)).is.not.undefined;
        expect(await this.contextManager.findGameContextById(id2)).is.undefined;

        const dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(1);
        expect(dbItems[0].id).equal(ctx2.id.asString());
        expect(await activityListCount()).equal(1);

        expect(await this.contextManager.findPlayerContextById(id1.playerContextID)).is.not.undefined;
        expect(await this.contextManager.findPlayerContextById(id2.playerContextID)).is.undefined;

        const dbPlayerItems = await getPlayerContextModel().findAll();
        expect(dbPlayerItems.length).equal(1);
        expect(dbPlayerItems[0].id).equal(ctx2.id.playerContextID.asString());
        expect(dbPlayerItems[0].games).deep.equal([{ id: id2.asString(), mode: "real" }]);

        expect(await playersActivityListCount()).equal(1);

        expect(await JobStateTracker.getJobs()).deep.equal([]);

        expect(this.request.args).deep.equal([

            {
                body: {
                    token: this.request.args[0].body.token
                },
                headers: {
                    "content-type": "application/json",
                    "x-gs": ""
                },
                method: "POST",
                query: {},
                url: "http://api:4004/force-cleanup/finish"
            }
        ]);
    }

    @test()
    @timeout(5000)
    public async testRepairCleanup() {
        this.request.post("http://api:4004/force-cleanup/finish", testing.status(201));
        this.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 2, "PLAYER1", "deviceId");

        await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, this.gameTokenData.playmode),
            this.startGame, TEST_MODULE_NAME);

        const ctx2 = await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, this.gameTokenData.playmode),
            this.startGame, TEST_MODULE_NAME);

        await ctx2.updatePendingModification({ type: "payment" } as any,
            { type: "newContext" },
            {} as any);

        expect(await JobStateTracker.init(2)).is.true;
        expect(await JobStateTracker.init(2)).is.false;
        this.currentClock.setSystemTime(9 + 1000);
        await ForceCleanupJob.repair(1000);
        this.currentClock.restore();
        await sleep(1000);

        expect(await this.contextManager.findGameContextById(id1)).is.not.undefined;
        expect(await this.contextManager.findGameContextById(id2)).is.undefined;

        const dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(1);
        expect(dbItems[0].id).equal(ctx2.id.asString());
        expect(await activityListCount()).equal(1);

        expect(await this.contextManager.findPlayerContextById(id1.playerContextID)).is.not.undefined;
        expect(await this.contextManager.findPlayerContextById(id2.playerContextID)).is.undefined;

        const dbPlayerItems = await getPlayerContextModel().findAll();
        expect(dbPlayerItems.length).equal(1);
        expect(dbPlayerItems[0].id).equal(ctx2.id.playerContextID.asString());
        expect(dbPlayerItems[0].games).deep.equal([{ id: id2.asString(), mode: "real" }]);

        expect(await JobStateTracker.getJobs()).deep.equal([]);

        expect(this.request.args).deep.equal([

            {
                body: {
                    token: this.request.args[0].body.token
                },
                headers: {
                    "content-type": "application/json",
                    "x-gs": ""
                },
                method: "POST",
                query: {},
                url: "http://api:4004/force-cleanup/finish"
            }
        ]);
    }

    @test()
    @timeout(5000)
    public async testRepair_skipCleanup() {
        this.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 2, "PLAYER1", "deviceId");

        await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, this.gameTokenData.playmode),
            this.startGame, TEST_MODULE_NAME);

        const ctx2 = await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, this.gameTokenData.playmode),
            this.startGame, TEST_MODULE_NAME);

        await ctx2.updatePendingModification({ type: "payment" } as any,
            { type: "newContext" },
            {} as any);

        expect(await JobStateTracker.init(2, "12345")).is.true;
        this.currentClock.setSystemTime(9 + 100);
        await ForceCleanupJob.repair(1000);
        this.currentClock.restore();
        await sleep(1000);

        expect(await this.contextManager.findGameContextById(id1)).is.not.undefined;
        expect(await this.contextManager.findGameContextById(id2)).is.not.undefined;

        const dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(0);

        expect(await this.contextManager.findPlayerContextById(id1.playerContextID)).is.not.undefined;
        expect(await this.contextManager.findPlayerContextById(id2.playerContextID)).is.not.undefined;

        const dbPlayerItems = await getPlayerContextModel().findAll();
        expect(dbPlayerItems.length).equal(0);

        expect(await JobStateTracker.getJobs()).deep.equal([{ brandId: 2, lastTs: 9, traceID: "12345" }]);
    }
}
