import { expect, should, use } from "chai";
import { SinonFakeTimers, useFakeTimers } from "sinon";
import {
    createGameToken,
    flushAll,
    getCleanupContextRequests,
    getCleanupPlayerContextRequests,
    playersActivityListCount,
    syncModels,
    TEST_MODULE_NAME
} from "../../helper";
import config from "../../../skywind/config";
import { GameTokenData } from "../../../skywind/services/tokens";
import { GameContextID } from "../../../skywind/services/contextIds";
import { GameData } from "../../../skywind/services/auth";
import { GameSession } from "../../../skywind/services/gameSession";
import { suite, test, timeout } from "mocha-typescript";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { getPlayerContextModel } from "../../../skywind/services/offlinestorage/models";
import { CleanupPlayerContextService } from "../../../skywind/services/cleanup/cleanupPlayerContextService";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("CleanupPlayerContextService")
@timeout(40000)
class CleanupPlayerContextServiceSpec {
    private static currentClock: SinonFakeTimers;
    private static settings = {
        coins: [1],
        defaultCoin: 1,
        maxTotalStake: 500,
        stakeAll: [0.1, 0.5, 1, 2, 3, 5],
        stakeDef: 1,
        stakeMax: 10,
        stakeMin: 0.1,
        winMax: 3000000,
        currencyMultiplier: 100,
    };
    private static gameID = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
    private static gameTokenData: GameTokenData = {
        playerCode: CleanupPlayerContextServiceSpec.gameID.playerCode,
        gameCode: CleanupPlayerContextServiceSpec.gameID.playerCode,
        brandId: CleanupPlayerContextServiceSpec.gameID.brandId,
        currency: "USD",
        playmode: "real"
    };
    private static startGame: GameData = {
        gameTokenData: CleanupPlayerContextServiceSpec.gameTokenData,
        limits: CleanupPlayerContextServiceSpec.settings,
    };
    private static sessionId: GameSession;
    private static prevExpireIn: number;
    private contextManager = getGameFlowContextManager();
    private service: CleanupPlayerContextService = new CleanupPlayerContextService();

    public static async before() {
        CleanupPlayerContextServiceSpec.gameTokenData.token =
            await createGameToken(CleanupPlayerContextServiceSpec.gameTokenData);
        CleanupPlayerContextServiceSpec.sessionId = await GameSession.generate(CleanupPlayerContextServiceSpec.gameID,
            CleanupPlayerContextServiceSpec.gameTokenData.playmode);
        await syncModels();
        CleanupPlayerContextServiceSpec.currentClock = useFakeTimers();
        // without timeout for test purposes
        CleanupPlayerContextServiceSpec.prevExpireIn = config.cleanUp.expireIn;
        config.cleanUp.expireIn = 0;
    }

    public static async after() {
        config.cleanUp.expireIn = CleanupPlayerContextServiceSpec.prevExpireIn;
        CleanupPlayerContextServiceSpec.currentClock.restore();
    }

    public async after() {
        CleanupPlayerContextServiceSpec.currentClock.reset();
    }

    public async before() {
        await getPlayerContextModel().truncate();
        return flushAll();
    }

    @test("cleanup, save and revmove contexts")
    public async cleanUpSaveAndRemove() {
        CleanupPlayerContextServiceSpec.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 1, "PLAYER2", "deviceId");

        const ctx1 = await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, CleanupPlayerContextServiceSpec.gameTokenData.playmode),
            CleanupPlayerContextServiceSpec.startGame, TEST_MODULE_NAME);

        const ctx2 = await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, CleanupPlayerContextServiceSpec.gameTokenData.playmode),
            CleanupPlayerContextServiceSpec.startGame, TEST_MODULE_NAME);

        await ctx1.remove();

        CleanupPlayerContextServiceSpec.currentClock.setSystemTime(10);
        const result = await this.service.cleanUp(
            [id1.playerContextID.asString(), id2.playerContextID.asString()], true);
        expect(result).deep.equal([2, 1]);

        expect(await this.contextManager.findPlayerContextById(id1.playerContextID)).is.undefined;
        expect(await this.contextManager.findPlayerContextById(id2.playerContextID)).is.undefined;

        const dbItems = await getPlayerContextModel().findAll();
        expect(dbItems.length).equal(1);
        expect(dbItems[0].id).equal(ctx2.id.playerContextID.asString());
        expect(dbItems[0].games).deep.equal([{ id: id2.asString(), mode: "real" }]);

        expect(await playersActivityListCount()).equal(0);
    }

    @test("doesn't move context which is not cold")
    public async preventCleanUpForActiveContext() {
        CleanupPlayerContextServiceSpec.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 1, "PLAYER2", "deviceId");

        const ctx1 = await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, CleanupPlayerContextServiceSpec.gameTokenData.playmode),
            CleanupPlayerContextServiceSpec.startGame, TEST_MODULE_NAME);

        const ctx2 = await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, CleanupPlayerContextServiceSpec.gameTokenData.playmode),
            CleanupPlayerContextServiceSpec.startGame, TEST_MODULE_NAME);

        await ctx1.remove();

        CleanupPlayerContextServiceSpec.currentClock.setSystemTime(10);
        const result = await this.service.cleanUp([id1.asString(), id2.asString()], false);
        expect(result).deep.equal([2, 0]);

        expect(await this.contextManager.findPlayerContextById(id1.playerContextID)).is.not.undefined;
        expect(await this.contextManager.findPlayerContextById(id2.playerContextID)).is.not.undefined;

        const dbItems = await getPlayerContextModel().findAll();
        expect(dbItems.length).equal(0);
        expect(await playersActivityListCount()).equal(2);
    }

    @test("removes previous offline db item")
    public async removePreviousItemFromDB() {
        CleanupPlayerContextServiceSpec.currentClock.setSystemTime(9);
        const id = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        let ctx = await this.contextManager.findOrCreateGameContext(
            id, await GameSession.generate(id, CleanupPlayerContextServiceSpec.gameTokenData.playmode),
            CleanupPlayerContextServiceSpec.startGame, TEST_MODULE_NAME);

        CleanupPlayerContextServiceSpec.currentClock.setSystemTime(10);
        await this.service.cleanUp([id.playerContextID.asString()], true);
        let dbItems = await getPlayerContextModel().findAll();
        expect(dbItems.length).equal(1);
        expect(dbItems[0].id).equal(id.playerContextID.asString());
        expect(dbItems[0].games).deep.equal([{ id: id.asString(), mode: "real" }]);

        ctx = await this.contextManager.findOrCreateGameContext(
            id, await GameSession.generate(id, CleanupPlayerContextServiceSpec.gameTokenData.playmode),
            CleanupPlayerContextServiceSpec.startGame, TEST_MODULE_NAME);

        await ctx.remove();
        CleanupPlayerContextServiceSpec.currentClock.setSystemTime(10);
        const result = await this.service.cleanUp([id.playerContextID.asString()], true);
        expect(result).deep.equal([1, 0]);

        expect(await this.contextManager.findGameContextById(id)).is.undefined;

        dbItems = await getPlayerContextModel().findAll();
        expect(dbItems.length).equal(0);
        expect(await playersActivityListCount()).equal(0);
    }

    @test("skips not found context")
    public async skipNotFoundContext() {
        CleanupPlayerContextServiceSpec.currentClock.setSystemTime(9);
        const id = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");

        const result = await this.service.cleanUp([id.asString()], true);
        expect(result).deep.equal([1, 0]);

        expect(await this.contextManager.findPlayerContextById(id.playerContextID)).is.undefined;

        const dbItems = await getPlayerContextModel().findAll();
        expect(dbItems.length).equal(0);
        expect(await playersActivityListCount()).equal(0);
    }

    @test("forces cleanup")
    public async forceCleanup() {
        CleanupPlayerContextServiceSpec.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 2, "PLAYER2", "deviceId");

        await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, CleanupPlayerContextServiceSpec.gameTokenData.playmode),
            CleanupPlayerContextServiceSpec.startGame, TEST_MODULE_NAME);

        const ctx2 = await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, CleanupPlayerContextServiceSpec.gameTokenData.playmode),
            CleanupPlayerContextServiceSpec.startGame, TEST_MODULE_NAME);

        CleanupPlayerContextServiceSpec.currentClock.setSystemTime(10);

        await this.service.forceCleanUp(2, 10);

        expect(await this.contextManager.findPlayerContextById(id1.playerContextID)).is.not.undefined;
        expect(await this.contextManager.findPlayerContextById(id2.playerContextID)).is.undefined;

        const dbItems = await getPlayerContextModel().findAll();
        expect(dbItems.length).equal(1);
        expect(dbItems[0].id).equal(id2.playerContextID.asString());
        expect(dbItems[0].games).deep.equal([{ id: id2.asString(), mode: "real" }]);

        expect(await playersActivityListCount()).equal(1);
    }

    @test("forces clean with batches")
    public async forceCleanupWithBatches() {
        CleanupPlayerContextServiceSpec.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 1, "PLAYER2", "deviceId");

        await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, CleanupPlayerContextServiceSpec.gameTokenData.playmode),
            CleanupPlayerContextServiceSpec.startGame, TEST_MODULE_NAME);

        await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, CleanupPlayerContextServiceSpec.gameTokenData.playmode),
            CleanupPlayerContextServiceSpec.startGame, TEST_MODULE_NAME);

        CleanupPlayerContextServiceSpec.currentClock.setSystemTime(10);

        await this.service.forceCleanUp(1, 1);

        expect(await this.contextManager.findPlayerContextById(id1.playerContextID)).is.undefined;
        expect(await this.contextManager.findPlayerContextById(id2.playerContextID)).is.undefined;

        const dbItems = await getPlayerContextModel().findAll();
        expect(dbItems.length).equal(2);

        expect(await playersActivityListCount()).equal(0);
    }

    @test("forces clean when we have multiple brands in list")
    public async forceCleanupFindCorrectBrands() {
        CleanupPlayerContextServiceSpec.currentClock.setSystemTime(9);

        const createContextForOtherBrands = async (from: number, to: number) => {

            for (let i = from; i < to; i++) {
                const id = GameContextID.create("GM0001", 2, "PLAYER" + i, "deviceId");
                await this.contextManager.findOrCreateGameContext(
                    id, await GameSession.generate(id, "real"),
                    CleanupPlayerContextServiceSpec.startGame, TEST_MODULE_NAME);
            }
        };

        await createContextForOtherBrands(0, 105);

        CleanupPlayerContextServiceSpec.currentClock.setSystemTime(10);

        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, "real"),
            CleanupPlayerContextServiceSpec.startGame, TEST_MODULE_NAME);

        CleanupPlayerContextServiceSpec.currentClock.setSystemTime(11);
        await createContextForOtherBrands(105, 205);
        CleanupPlayerContextServiceSpec.currentClock.setSystemTime(12);
        const id2 = GameContextID.create("GM0002", 1, "PLAYER1", "deviceId");
        await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, "real"),
            CleanupPlayerContextServiceSpec.startGame, TEST_MODULE_NAME);

        await this.service.forceCleanUp(1, 10);

        expect(await this.contextManager.findPlayerContextById(id1.playerContextID)).is.undefined;
        expect(await this.contextManager.findPlayerContextById(id2.playerContextID)).is.undefined;

        const dbItems = await getPlayerContextModel().findAll();
        expect(dbItems.length).equal(1);
        expect(dbItems[0].games)
            .deep.equal([{ id: id1.asString(), mode: "real" }, { id: id2.asString(), mode: "real" }]);

        expect(await playersActivityListCount()).equal(205);
    }

    @test("enqueues cleanup")
    public async enqueueCleanup() {
        CleanupPlayerContextServiceSpec.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 1, "PLAYER2", "deviceId");

        await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, CleanupPlayerContextServiceSpec.gameTokenData.playmode),
            CleanupPlayerContextServiceSpec.startGame, TEST_MODULE_NAME);

        await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, CleanupPlayerContextServiceSpec.gameTokenData.playmode),
            CleanupPlayerContextServiceSpec.startGame, TEST_MODULE_NAME);

        await this.service.enqueueContexts(600010, 1);

        await expect(this.contextManager.findPlayerContextById(id1.playerContextID)).is.not.undefined;
        await expect(this.contextManager.findPlayerContextById(id2.playerContextID)).is.not.undefined;

        const dbItems = await getPlayerContextModel().findAll();
        expect(dbItems.length).equal(0);

        expect(await playersActivityListCount()).equal(0);

        expect(await getCleanupPlayerContextRequests(0, 1000)).deep.equal([
            { id: id2.playerContextID.asString() }, { id: id1.playerContextID.asString() }
        ]);
    }

    @test("skips enqueue cleanup if contexts are active")
    public async skipEnqueueIfIsStillActive() {
        CleanupPlayerContextServiceSpec.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 1, "PLAYER2", "deviceId");
        await this.contextManager.findOrCreateGameContext(id1, await GameSession.generate(id1,
            CleanupPlayerContextServiceSpec.gameTokenData.playmode), CleanupPlayerContextServiceSpec.startGame,
            TEST_MODULE_NAME
        );
        await this.contextManager.findOrCreateGameContext(id2, await GameSession.generate(id2,
            CleanupPlayerContextServiceSpec.gameTokenData.playmode), CleanupPlayerContextServiceSpec.startGame,
            TEST_MODULE_NAME);
        await this.service.enqueueContexts(8, 1);
        await expect(this.contextManager.findPlayerContextById(id1.playerContextID)).is.not.undefined;
        await expect(this.contextManager.findPlayerContextById(id2.playerContextID)).is.not.undefined;
        const dbItems = await getPlayerContextModel().findAll();
        expect(dbItems.length).equal(0);
        expect(await playersActivityListCount()).equal(2);
        expect(await getCleanupContextRequests(0, 1000)).deep.equal([]);
    }
}
