import { expect, should, use } from "chai";
import {
    activityListCount,
    createGameToken,
    flushAll,
    syncModels,
    TEST_MODULE_NAME
} from "../../helper";
import { suite, test } from "mocha-typescript";
import { GameContextID } from "../../../skywind/services/contextIds";
import { GameSession } from "../../../skywind/services/gameSession";
import { getGameContextModel } from "../../../skywind/services/offlinestorage/models";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { GameTokenData } from "../../../skywind/services/tokens";
import { GameData } from "../../../skywind/services/auth";
import ReactivateGameService from "../../../skywind/services/cleanup/reactivateGameService";
import { CleanupGameContextService } from "../../../skywind/services/cleanup/cleanupGameContextService";
import { GameContextNotExists } from "../../../skywind/errors";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

@suite("Reactivate game service")
class ReactivateGameServiceSpec {
    private contextManager = getGameFlowContextManager();
    private cleanUpService = new CleanupGameContextService();

    private settings = {
        coins: [1],
        defaultCoin: 1,
        maxTotalStake: 500,
        stakeAll: [0.1, 0.5, 1, 2, 3, 5],
        stakeDef: 1,
        stakeMax: 10,
        stakeMin: 0.1,
        winMax: 3000000,
        currencyMultiplier: 100,
    };

    private gameID = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");

    private gameTokenData: GameTokenData = {
        playerCode: this.gameID.playerCode,
        gameCode: this.gameID.playerCode,
        brandId: this.gameID.brandId,
        currency: "USD",
        playmode: "real"
    };

    private startGame: GameData = {
        gameTokenData: this.gameTokenData,
        gameId: this.gameTokenData.gameCode,
        limits: this.settings,
    };

    public async before() {
        this.gameTokenData.token = await createGameToken(this.gameTokenData);
        await syncModels();
        await getGameContextModel().truncate();
        return flushAll();
    }

    @test()
    public async testReactivateGame() {
        const id = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");

        const ctx = await this.contextManager.findOrCreateGameContext(
            id, await GameSession.generate(id, this.gameTokenData.playmode),
            this.startGame, TEST_MODULE_NAME);

        await ctx.updatePendingModification({ type: "payment" } as any,
            { type: "newContext" },
            {} as any);

        await this.cleanUpService.forceCleanUp(1, 1);

        expect(await this.contextManager.findGameContextById(id)).is.undefined;

        let dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(1);
        expect(dbItems[0].id).equal(ctx.id.asString());
        expect(await activityListCount()).equal(0);

        await ReactivateGameService.reactivateGame(id.asString());

        expect(await this.contextManager.findGameContextById(id)).is.not.undefined;

        dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(0);
        expect(await activityListCount()).equal(1);
    }

    @test()
    public async testReactivateGameNotExists() {
        const id = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");

        await expect(ReactivateGameService.reactivateGame(id.asString())).to.be.rejectedWith(GameContextNotExists);
    }
}
