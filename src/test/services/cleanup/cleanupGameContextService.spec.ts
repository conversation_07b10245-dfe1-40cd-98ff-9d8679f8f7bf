import { expect, should, use } from "chai";
import { SinonFakeTimers, useFakeTimers } from "sinon";
import {
    activityListCount,
    createGameToken,
    flushAll,
    getCleanupContextRequests,
    syncModels,
    TEST_MODULE_NAME
} from "../../helper";
import config from "../../../skywind/config";
import { GameTokenData } from "../../../skywind/services/tokens";
import { GameContextID } from "../../../skywind/services/contextIds";
import { GameData } from "../../../skywind/services/auth";
import { GameSession } from "../../../skywind/services/gameSession";
import { suite, test, timeout } from "mocha-typescript";
import { getGameFlowContextManager } from "../../../skywind/services/contextmanager/contextManagerImpl";
import { getGameContextModel } from "../../../skywind/services/offlinestorage/models";
import { GameContextPersistencePolicy } from "@skywind-group/sw-game-core";
import { CleanupGameContextService } from "../../../skywind/services/cleanup/cleanupGameContextService";
import { SpecialState } from "../../../skywind/services/context/gamecontext";

require("source-map-support").install();

should();
use(require("chai-as-promised"));

export class BaseCleanupGameServiceSpec {
    protected static settings = {
        coins: [1],
        defaultCoin: 1,
        maxTotalStake: 500,
        stakeAll: [0.1, 0.5, 1, 2, 3, 5],
        stakeDef: 1,
        stakeMax: 10,
        stakeMin: 0.1,
        winMax: 3000000,
        currencyMultiplier: 100,
    };
    protected static gameID = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
    protected static gameTokenData: GameTokenData = {
        playerCode: BaseCleanupGameServiceSpec.gameID.playerCode,
        gameCode: BaseCleanupGameServiceSpec.gameID.playerCode,
        brandId: BaseCleanupGameServiceSpec.gameID.brandId,
        currency: "USD",
        playmode: "real"
    };
    protected static startGame: GameData = {
        gameTokenData: BaseCleanupGameServiceSpec.gameTokenData,
        gameId: BaseCleanupGameServiceSpec.gameTokenData.gameCode,
        limits: BaseCleanupGameServiceSpec.settings,
    };
    protected static sessionId: GameSession;
    protected static prevExpireIn: number;
    protected currentClock: SinonFakeTimers;
    protected contextManager = getGameFlowContextManager();
    protected service: CleanupGameContextService = new CleanupGameContextService();

    public static async before() {
        BaseCleanupGameServiceSpec.gameTokenData.token =
            await createGameToken(BaseCleanupGameServiceSpec.gameTokenData);
        BaseCleanupGameServiceSpec.sessionId = await GameSession.generate(BaseCleanupGameServiceSpec.gameID,
            BaseCleanupGameServiceSpec.gameTokenData.playmode);
        await syncModels();
        // without timeout for test purposes
        BaseCleanupGameServiceSpec.prevExpireIn = config.cleanUp.expireIn;
        config.cleanUp.expireIn = 0;
    }

    public static async after() {
        config.cleanUp.expireIn = BaseCleanupGameServiceSpec.prevExpireIn;

    }

    public async after() {
        this.currentClock.restore();
    }

    public async before() {
        this.currentClock = useFakeTimers();
        await getGameContextModel().truncate();
        return flushAll();
    }
}

@suite("CleanupGameContextService")
@timeout(40000)
class CleanupGameContextServiceSpec extends BaseCleanupGameServiceSpec {

    @test("cleanup, save and revmove contexts")
    public async cleanUpSaveAndRemove() {
        this.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 1, "PLAYER1", "deviceId");

        await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, CleanupGameContextServiceSpec.gameTokenData.playmode),
            CleanupGameContextServiceSpec.startGame, TEST_MODULE_NAME);

        const ctx2 = await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, CleanupGameContextServiceSpec.gameTokenData.playmode),
            CleanupGameContextServiceSpec.startGame, TEST_MODULE_NAME);

        await ctx2.updatePendingModification({ type: "payment" } as any, { type: "newContext" }, {} as any);

        this.currentClock.setSystemTime(10);
        const result = await this.service.cleanUp([id1.asString(), id2.asString()], true);
        expect(result).deep.equal([1, 1]);

        expect(await this.contextManager.findGameContextById(id1)).is.undefined;
        expect(await this.contextManager.findGameContextById(id2)).is.undefined;

        const dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(1);
        const item = dbItems[0];
        expect(item.id).equal(ctx2.id.asString());
        expect(item.brandId).equal(ctx2.id.brandId);
        expect(item.playerCode).equal(ctx2.id.playerCode);
        expect(item.gameCode).equal(ctx2.id.gameCode);
        expect(item.gameId).equal(ctx2.gameData.gameId);
        expect(item.deviceId).equal(ctx2.id.deviceId);

        expect(await activityListCount()).equal(0);
    }

    @test("doesn't move context which is not cold")
    public async preventCleanUpForActiveContext() {
        this.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 1, "PLAYER1", "deviceId");

        await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, CleanupGameContextServiceSpec.gameTokenData.playmode),
            CleanupGameContextServiceSpec.startGame, TEST_MODULE_NAME);

        const ctx2 = await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, CleanupGameContextServiceSpec.gameTokenData.playmode),
            CleanupGameContextServiceSpec.startGame, TEST_MODULE_NAME);

        await ctx2.updatePendingModification({ type: "payment" } as any, { type: "newContext" }, {} as any);

        this.currentClock.setSystemTime(10);
        const result = await this.service.cleanUp([id1.asString(), id2.asString()], false);
        expect(result).deep.equal([1, 1]);

        expect(await this.contextManager.findGameContextById(id1)).is.not.undefined;
        expect(await this.contextManager.findGameContextById(id2)).is.not.undefined;

        const dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(1);
        expect(dbItems[0].id).equal(ctx2.id.asString());
        expect(await activityListCount()).equal(2);
    }

    @test("removes previous offline db item")
    public async removePreviousItemFromDB() {
        this.currentClock.setSystemTime(9);
        const id = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        let ctx = await this.contextManager.findOrCreateGameContext(
            id, await GameSession.generate(id, CleanupGameContextServiceSpec.gameTokenData.playmode),
            CleanupGameContextServiceSpec.startGame, TEST_MODULE_NAME);
        ctx.persistencePolicy = GameContextPersistencePolicy.SHORT_TERM;
        await ctx.update({ type: "old_context" });
        await this.service.cleanUp([id.asString()], true);
        expect((await getGameContextModel().findAll()).length).equal(1);

        ctx = await this.contextManager.findOrCreateGameContext(
            id, await GameSession.generate(id, CleanupGameContextServiceSpec.gameTokenData.playmode),
            CleanupGameContextServiceSpec.startGame, TEST_MODULE_NAME);
        await ctx.updatePendingModification({ type: "payment" } as any, { type: "newContext" }, {} as any);

        this.currentClock.setSystemTime(10);
        const result = await this.service.cleanUp([id.asString()], true);
        expect(result).deep.equal([1, 0]);

        expect(await this.contextManager.findGameContextById(id)).is.undefined;

        const dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(1);
        expect(dbItems[0].id).equal(ctx.id.asString());
        expect(dbItems[0].data.metaInf).is.not.undefined;
        expect(dbItems[0].data.pending).is.not.undefined;
        expect(await activityListCount()).equal(0);
    }

    @test("skips not found context")
    public async skipNotFoundContext() {
        this.currentClock.setSystemTime(9);
        const id = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");

        const result = await this.service.cleanUp([id.asString()], true);
        expect(result).deep.equal([0, 0]);

        expect(await this.contextManager.findGameContextById(id)).is.undefined;

        const dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(0);
        expect(await activityListCount()).equal(0);
    }

    @test("forces cleanup")
    public async forceCleanup() {
        this.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 2, "PLAYER1", "deviceId");

        await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, CleanupGameContextServiceSpec.gameTokenData.playmode),
            CleanupGameContextServiceSpec.startGame, TEST_MODULE_NAME);

        const ctx2 = await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, CleanupGameContextServiceSpec.gameTokenData.playmode),
            CleanupGameContextServiceSpec.startGame, TEST_MODULE_NAME);

        await ctx2.updatePendingModification({ type: "payment" } as any,
            { type: "newContext" },
            {} as any);

        this.currentClock.setSystemTime(10);

        await this.service.forceCleanUp(2, 10);

        expect(await this.contextManager.findGameContextById(id1)).is.not.undefined;
        expect(await this.contextManager.findGameContextById(id2)).is.undefined;

        const dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(1);
        expect(dbItems[0].id).equal(ctx2.id.asString());
        expect(dbItems[0].requireCompletion).to.be.true;

        expect(await activityListCount()).equal(1);
    }

    @test("forces clean with batches")
    public async forceCleanupWithBatches() {
        this.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 1, "PLAYER1", "deviceId");

        await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, CleanupGameContextServiceSpec.gameTokenData.playmode),
            CleanupGameContextServiceSpec.startGame, TEST_MODULE_NAME);

        await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, CleanupGameContextServiceSpec.gameTokenData.playmode),
            CleanupGameContextServiceSpec.startGame, TEST_MODULE_NAME);

        this.currentClock.setSystemTime(10);

        await this.service.forceCleanUp(1, 1);

        expect(await this.contextManager.findGameContextById(id1)).is.undefined;
        expect(await this.contextManager.findGameContextById(id2)).is.undefined;

        const dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(0);

        expect(await activityListCount()).equal(0);
    }

    @test("forces clean when we have multiple brands in list")
    public async forceCleanupFindCorrectBrands() {
        this.currentClock.setSystemTime(9);

        const createContextForOtherBrands = async (from: number, to: number) => {

            for (let i = from; i < to; i++) {
                const id = GameContextID.create("GM0001", 2, "PLAYER" + i, "deviceId");
                await this.contextManager.findOrCreateGameContext(
                    id, await GameSession.generate(id, "real"),
                    CleanupGameContextServiceSpec.startGame, TEST_MODULE_NAME);
            }
        };

        await createContextForOtherBrands(0, 105);

        this.currentClock.setSystemTime(10);

        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, "real"),
            CleanupGameContextServiceSpec.startGame, TEST_MODULE_NAME);

        this.currentClock.setSystemTime(11);
        await createContextForOtherBrands(105, 205);
        this.currentClock.setSystemTime(12);
        const id2 = GameContextID.create("GM0001", 1, "PLAYER2", "deviceId");
        await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, "real"),
            CleanupGameContextServiceSpec.startGame, TEST_MODULE_NAME);

        await this.service.forceCleanUp(1, 10);

        expect(await this.contextManager.findGameContextById(id1)).is.undefined;
        expect(await this.contextManager.findGameContextById(id2)).is.undefined;

        const dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(0);

        expect(await activityListCount()).equal(205);
    }

    @test("enqueues cleanup")
    public async enqueueCleanup() {
        this.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 1, "PLAYER1", "deviceId");

        await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, CleanupGameContextServiceSpec.gameTokenData.playmode),
            CleanupGameContextServiceSpec.startGame, TEST_MODULE_NAME);

        await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, CleanupGameContextServiceSpec.gameTokenData.playmode),
            CleanupGameContextServiceSpec.startGame, TEST_MODULE_NAME);

        await this.service.enqueueContexts(10, 1);

        await expect(this.contextManager.findGameContextById(id1)).is.not.undefined;
        await expect(this.contextManager.findGameContextById(id2)).is.not.undefined;

        const dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(0);

        expect(await activityListCount()).equal(0);

        expect(await getCleanupContextRequests(0, 1000)).deep.equal([{ id: id2.asString() }, { id: id1.asString() }]);
    }

    @test("skips enqueue cleanup if contexts are active")
    public async skipEnqueueIfIsStillActive() {
        this.currentClock.setSystemTime(9);
        const id1 = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");
        const id2 = GameContextID.create("GM0002", 1, "PLAYER1", "deviceId");

        await this.contextManager.findOrCreateGameContext(
            id1, await GameSession.generate(id1, CleanupGameContextServiceSpec.gameTokenData.playmode),
            CleanupGameContextServiceSpec.startGame, TEST_MODULE_NAME);

        await this.contextManager.findOrCreateGameContext(
            id2, await GameSession.generate(id2, CleanupGameContextServiceSpec.gameTokenData.playmode),
            CleanupGameContextServiceSpec.startGame, TEST_MODULE_NAME);

        await this.service.enqueueContexts(8, 1);

        await expect(this.contextManager.findGameContextById(id1)).is.not.undefined;
        await expect(this.contextManager.findGameContextById(id2)).is.not.undefined;

        const dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(0);

        expect(await activityListCount()).equal(2);

        expect(await getCleanupContextRequests(0, 1000)).deep.equal([]);
    }

    @test("saves context in FINALIZING special state")
    public async savesFinalizing() {
        this.currentClock.setSystemTime(9);
        const id = GameContextID.create("GM0001", 1, "PLAYER1", "deviceId");

        let ctx = await this.contextManager.findOrCreateGameContext(
            id, await GameSession.generate(id, CleanupGameContextServiceSpec.gameTokenData.playmode),
            CleanupGameContextServiceSpec.startGame, TEST_MODULE_NAME);

        await ctx.setSpecialState(SpecialState.FINALIZING);

        this.currentClock.setSystemTime(10);
        let result = await this.service.cleanUp([id.asString()], true);
        expect(result).deep.equal([1, 0]);

        expect(await this.contextManager.findGameContextById(id)).undefined;

        let dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(1);
        const item = dbItems[0];
        expect(item.id).equal(ctx.id.asString());
        expect(item.brandId).equal(ctx.id.brandId);
        expect(item.playerCode).equal(ctx.id.playerCode);
        expect(item.gameCode).equal(ctx.id.gameCode);
        expect(item.gameId).equal(ctx.gameData.gameId);
        expect(item.deviceId).equal(ctx.id.deviceId);

        expect(await activityListCount()).equal(0);

        ctx = await this.contextManager.findOrCreateGameContext(
            id, await GameSession.generate(id, CleanupGameContextServiceSpec.gameTokenData.playmode),
            CleanupGameContextServiceSpec.startGame, TEST_MODULE_NAME);

        await ctx.setSpecialState(null);
        result = await this.service.cleanUp([id.asString()], true);
        expect(result).deep.equal([0, 1]);
        dbItems = await getGameContextModel().findAll();
        expect(dbItems.length).equal(0);
    }
}
