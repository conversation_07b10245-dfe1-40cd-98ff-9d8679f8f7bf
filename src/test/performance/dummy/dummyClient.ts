import * as request from "superagent";
import { keepalive, logging, measures } from "@skywind-group/sw-utils";
import { ClientResponse, GameInitRequest, GamePlayRequest } from "@skywind-group/sw-game-core";
import * as jwt from "jsonwebtoken";
import { io, Socket } from "socket.io-client";
import config from "../../../skywind/config";
import measure = measures.measure;
import HttpsAgent = keepalive.HttpsAgent;

const log = logging.logger("sw-slot-engine:dummy-client");

const DUMMY_CONFIG = {
    appPort: +process.env.APP_PORT || 4000,
    appHost: process.env.APP_HOST || "127.0.0.1",
    gameId: process.env.DUMMY_GAME_ID || "sw_al",
    players: +process.env.DUMMY_PLAYER_COUNT || 200,
    playersPrefix: process.env.DUMMY_PLAYER_PREFIX || "",
    iterations: +process.env.DUMMY_SPIN_COUNT || **********,
    clientType: process.env.DUMMY_CLIENT_TYPE || "SOCKET"
};

const keepaliveAgent = keepalive.createAgent({
    maxFreeSockets: DUMMY_CONFIG.players,
    freeSocketKeepAliveTimeout: 30000,
    socketActiveTTL: 60000
}) as HttpsAgent;

class DummyClient {
    @measure({ name: "DummyClient.init", isAsync: true })
    public async init(playerCode: string): Promise<string> {
        const startTokenData = {
            playerCode: playerCode,
            gameCode: DUMMY_CONFIG.gameId,
            providerGameCode: DUMMY_CONFIG.gameId,
            brandId: 1,
            gameModel: "real"
        };
        const token = jwt.sign(startTokenData, "SOMESESECRET");

        const req: GameInitRequest = {
            request: "init",
            requestId: 0,
            name: "Test",
            gameId: DUMMY_CONFIG.gameId,
            deviceId: "web",
            startGameToken: token
        };

        const response: ClientResponse = await this.makeRequest<ClientResponse>(req);
        return response.gameSession;
    }

    @measure({ name: "DummyClient.play", isAsync: true })
    public async play(gameSessionId: string, requestId: number): Promise<void> {
        const req: GamePlayRequest = {
            request: "spin",
            requestId: requestId,
            bet: 1,
            lines: 1,
            coin: 1,
            currencyMultiplier: 1,
            gameSession: gameSessionId
        } as GamePlayRequest;
        await this.makeRequest(req);
    }

    protected makeRequest<T>(req): Promise<T> {
        return request.post(`http://${DUMMY_CONFIG.appHost}:${DUMMY_CONFIG.appPort}/casino/game2`)
            .set({ "Content-Type": "application/json", "Connection": "keep-alive" })
            .timeout(60000)
            .agent(keepaliveAgent)
            .send(req)
            .then((res) => this.processResponse(undefined, res), (err) => this.processResponse(err, err.response));

    }

    private processResponse(error: Error, res: request.Response): Promise<any> {
        if (error && !res) {
            log.warn("Error sending request %j", error);
            return Promise.reject(error);
        } else if (res.status !== 200) {
            log.warn("Error response: code = %s , body = %j", res.status, res.body);
            return Promise.reject(new Error(`Error: statusCode;: ${res.status}, body: ${res.body}`));
        } else {
            return Promise.resolve(res.body);
        }
    }
}

class SocketSessionContext {
    private awaiter: any;

    constructor(public readonly socket: Socket,
                private lastResponse?: any) {
    }

    public set response(data: any) {
        if (this.awaiter) {
            this.awaiter(data);
            this.awaiter = undefined;
        } else {
            this.lastResponse = data;
        }
    }

    public waitForResponse(): Promise<any> {
        return new Promise<any>((resolve) => {
            if (this.lastResponse) {
                resolve(this.lastResponse);
                this.lastResponse = undefined;
            } else {
                this.awaiter = resolve;
            }
        });
    }
}

class DummySocketIOClient extends DummyClient {
    private sockets: Map<string, any> = new Map<string, any>();

    protected async makeRequest<T>(req): Promise<T> {
        if (req.request === "init") {
            const socket = io(`http://${DUMMY_CONFIG.appHost}:${DUMMY_CONFIG.appPort}${config.gameEntryPoint}`);
            await this.connect(socket);

            const sessionData = new SocketSessionContext(socket);
            socket.on("init", (data) => {
                sessionData.response = data;
            });
            socket.on("play", (data) => {
                sessionData.response = data;
            });
            socket.emit("init", req);
            const initResponse = await sessionData.waitForResponse();
            this.sockets.set(initResponse.gameSession, sessionData);
            return initResponse;

        } else {
            const sessionData = this.sockets.get(req.gameSession);
            sessionData.socket.emit("play", req);
            return sessionData.waitForResponse();
        }
    }

    private connect(socket: Socket): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            socket.on("connect", () => {
                resolve();
            });
        });
    }
}

function scheduleDumpStatistic() {
    // tslint:disable-next-line:no-console
    setInterval(() => console.log(measures.measureProvider.getMeasures()), 10000);
}

interface PlayerSession {
    sessionId: string;
    requestId: number;
}

async function run() {

    const client = DUMMY_CONFIG.clientType === "HTTP" ? new DummyClient() : new DummySocketIOClient();

    const players: string[] = [];
    for (let i = 0; i < DUMMY_CONFIG.players; i++) {
        players.push(`player000000000000${(DUMMY_CONFIG.playersPrefix || "0") + i}`);
    }

    const initResult: string[] = [];
    for (const p of players) {
        initResult.push(await client.init(p));
    }
    const sessions = initResult.map((item) => {
        return {
            sessionId: item,
            requestId: 0
        };
    });

    let finished = DUMMY_CONFIG.players;

    const play = async (item: PlayerSession) => {
        if (item.requestId < DUMMY_CONFIG.iterations) {
            await client.play(item.sessionId, ++item.requestId);
            setImmediate(() => play(item));
        } else {
            finished--;
            if (finished === 0) {
                process.exit(0);
            }
        }
    };

    sessions.map((item) => play(item));
}

scheduleDumpStatistic();

run();
