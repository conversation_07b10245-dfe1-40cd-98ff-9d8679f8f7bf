import { UnloadEventHistoryService } from "../../../skywind/history/unloadHistory";
import { GameEventHistory, RoundHistory, SessionHistory } from "../../../skywind/history/history";
import { UnloadRoundService } from "../../../skywind/history/unloadRounds";

import { UnloadSessionService } from "../../../skywind/history/unloadSessions";

const Measured = require("measured");
const meter = new Measured.Meter();

setInterval(() => {
    // tslint:disable-next-line:no-console
    console.log(meter.toJSON());
}, 10000);

export async function startUnload(): Promise<void> {
    new UnloadEventHistoryService({
        save: (items: GameEventHistory[]): Promise<void> => {
            meter.mark(items.length);
            return Promise.resolve();
        }
    }).start();

    new UnloadRoundService({
        save: (items: RoundHistory[]): Promise<void> => {
            return Promise.resolve();
        }
    }).start();

    new UnloadSessionService({
        save: (items: SessionHistory[]): Promise<void> => {
            return Promise.resolve();
        }
    }).start();
}

startUnload();
