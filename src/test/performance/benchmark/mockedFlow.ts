import {
    BalanceResponse,
    ContextUpdate,
    Contribution,
    GameContextPersistencePolicy,
    GameFlow,
    GameFlowInfo,
    GameHistory,
    GameWalletAction,
    JackpotResults,
    LogLevel,
    MiniGame,
    PaymentInfo,
    PushService,
    RandomGenerator,
    TickersResponse,
    WinJackpot,
    GameInfo,
    FreeBetsInfo,
} from "@skywind-group/sw-game-core";
import { CSRandomGenerator } from "@skywind-group/sw-random-cs";
import { deepClone } from "../../../skywind/utils/cloner";

export class MockedGameFlow implements GameFlow<any, any, any> {
    public currentRequest: any;
    public currentGameContext: any;
    public clonedContext: any;
    public rnd = new CSRandomGenerator();
    public contextUpdate: ContextUpdate<any>;

    constructor(private gameId: string) {
    }

    public request() {
        return this.currentRequest;
    }

    public info(): GameFlowInfo {
        return {
            gameId: this.gameId,
            gameCode: this.gameId,
            deviceId: "web",
            brandId: 1,
            playerCode: "playerCode001",
            currency: "USD",
            gameSessionId: "SESSION",
            roundId: "1",
            gameMode: "real",
            roundPID: "1"
        };
    }

    public rng(): RandomGenerator {
        return this.rnd;
    }

    public settings() {
        return {
            coins: [1],
            defaultCoin: 1,
            maxTotalStake: 500,
            stakeAll: [0.1, 0.5, 1, 2, 3, 5],
            stakeDef: 1,
            stakeMax: 10,
            stakeMin: 0.1,
            winMax: 3000000,
            currencyMultiplier: 100,
        };
    }

    public pushService(): PushService {
        return undefined;
    }

    public gameContext(): Promise<any> {
        if (!this.clonedContext && this.currentGameContext) {
            this.clonedContext = this.cloneContext(this.currentGameContext);
        }
        return Promise.resolve(this.currentGameContext);
    }

    public persistencePolicy(): GameContextPersistencePolicy {
        return GameContextPersistencePolicy.NORMAL;
    }

    // tslint:disable-next-line:no-empty
    public setPersistencePolicy(value: GameContextPersistencePolicy): void {
    }

    public getBalance(): Promise<BalanceResponse> {
        return Promise.resolve({
            currency: "USD",
            amount: 1000,
            real: { amount: 1000 },
            bonus: { amount: 1000 },

        });
    }

    public payment(payment: PaymentInfo | GameWalletAction[]): Promise<BalanceResponse> {
        return Promise.resolve({
            currency: "USD",
            amount: 1000,
            real: { amount: 1000 },
            bonus: { amount: 1000 },

        });
    }

    public storeHistory(history: GameHistory): Promise<void> {
        return Promise.resolve(undefined);
    }

    public updateGameContext(gameContext: any): Promise<any> {
        this.currentGameContext = gameContext;
        this.clonedContext = undefined;
        return Promise.resolve(this.currentGameContext);
    }

    public update(gameContext: any,
                  payment: PaymentInfo | GameWalletAction[],
                  history: GameHistory,
                  contribution?: Contribution): Promise<void> {

        this.currentGameContext = gameContext;
        this.clonedContext = undefined;
        return Promise.resolve(this.currentGameContext);
    }

    public updateMiniGame(gameContext: any, miniGame: MiniGame): Promise<void> {
        this.currentGameContext = gameContext;
        this.clonedContext = undefined;
        return Promise.resolve(this.currentGameContext);
    }

    // tslint:disable-next-line:no-empty
    public log(level: LogLevel, message: any, ...optionalParams: any[]): void {
    }

    public exchange(amount: number, targetCurrency: string): number {
        return amount;
    }

    public winJackpot(gameContext: any, winJackpot: WinJackpot, history: GameHistory): Promise<void> {
        this.currentGameContext = gameContext;
        this.clonedContext = undefined;
        return Promise.resolve(this.currentGameContext);

    }

    public jackpotTickers(): Promise<TickersResponse> {
        return undefined;
    }

    public jackpotResult(): Promise<JackpotResults> {
        return undefined;
    }

    public deferredUpdate(update: ContextUpdate<any>): void {
        this.contextUpdate = update;
        this.currentGameContext = update.context;
        this.clonedContext = undefined;
    }

    public async getFreeBets(info: GameInfo): Promise<FreeBetsInfo> {
        return undefined;
    }

    protected cloneContext(gameContext: any) {
        return deepClone(gameContext);
    }

    public commitDeferredUpdate() {
        // empty
    }
}
