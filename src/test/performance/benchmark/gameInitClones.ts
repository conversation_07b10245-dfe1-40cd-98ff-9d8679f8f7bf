// import { Suite } from "benchmark";
// import { CacheableGameModule} from "../../../skywind/services/gamemodule";
// import { MockedGameFlow } from "./mockedFlow";
// import * as _ from "lodash";
//
// const suite = new Suite("Test game init");
//
// const gamesJson = require("../../../../games.json");
//
// const gameModule: CacheableGameModule = getGameModules(_.pickBy(gamesJson,
//     (version, key) => key.indexOf(process.env.GAME_ID) !== -1))[0];
//
// class MockedGameFlowLodash extends MockedGameFlow {
//
//     protected cloneContext(gameContext: any): any {
//         return _.cloneDeep(gameContext);
//     }
// }
//
// const flow1 = new MockedGameFlow(process.env.GAME_ID);
// flow1.currentRequest = {
//     request: "init"
// };
//
// const flow2 = new MockedGameFlowLodash(process.env.GAME_ID);
// flow2.currentRequest = {
//     request: "init"
// };
//
// suite.add(`${process.env.GAME_ID}.init-lodashClone`, {
//     defer: true,
//     initCount: 50,
//     minSamples: 200,
//     fn: (deferred) => {
//         const game = gameModule.createGame();
//         game.init(flow2).then(() => deferred.resolve());
//     }
// });
//
// suite.add(`${process.env.GAME_ID}.init-deepClone`, {
//     defer: true,
//     initCount: 50,
//     minSamples: 200,
//     fn: (deferred) => {
//         const game = gameModule.createGame();
//         game.init(flow1).then(() => deferred.resolve());
//     }
// });
//
// suite.on("cycle", (event) => {
//     // tslint:disable-next-line:no-console
//     console.log(String(event.target));
// }).on("complete", function() {
//     // tslint:disable-next-line:no-console
//     console.log("Fastest is " + this.filter("fastest").map("name"));
// }).run({ "async": true });
