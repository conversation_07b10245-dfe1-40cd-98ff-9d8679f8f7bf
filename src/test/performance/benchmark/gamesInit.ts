// import { Suite } from "benchmark";
// import { CacheableGameModule, getGameModules } from "../../../skywind/services/gamemodule";
// import { MockedGameFlow } from "./mockedFlow";
//
// const suite = new Suite("Test game init");
//
// const gamesJson = require("../../../../games.json");
//
// const gameModules: CacheableGameModule[] = getGameModules(gamesJson);
//
// for (const module of gameModules) {
//     const flow = new MockedGameFlow(module.moduleName.name);
//     flow.currentRequest = {
//         request: "init"
//     };
//
//     suite.add(`${module.moduleName.name}.init`, {
//         defer: true,
//         initCount: 50,
//         minSamples: 200,
//         fn: (deferred) => {
//             if (this.failed) {
//                 return deferred.resolve();
//             }
//             const game = module.createGame();
//             game.init(flow).then(() => deferred.resolve()).catch(() => {
//                 this.failed = true;
//                 deferred.resolve();
//             });
//         },
//         onStart: () => {
//             this.failed = false;
//         },
//         onComplete: (event) => {
//             if (!this.failed) {
//                 // tslint:disable-next-line:no-console
//                 console.log(String(event.target));
//             } else {
//                 // tslint:disable-next-line:no-console
//                 console.log(`${module.moduleName.name}.init failed`);
//             }
//         }
//     });
// }
//
// suite.run({ "async": true });
