import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { getOrInit } from "../skywind/utils/common";

@suite()
class CommonSpec {

    @test()
    public testGetOrInit() {
        const obj1: any = {};
        const result1 = getOrInit(obj1, "testF1", { type: "test1" });
        expect(result1).deep.equal({ type: "test1" });
        expect(obj1).deep.equal({ testF1: { type: "test1" } });

        const obj2: any = { testF2: { type: "ready" } };
        const result2 = getOrInit(obj2, "testF2", { type: "test2" });
        expect(result2).deep.equal({ type: "ready" });
        expect(obj2).deep.equal({ testF2: { type: "ready" } });
    }
}
