import { GameLoader, GameModule } from "..";
import { GameModuleNotLoadedError } from "../skywind/errors";
import { readFileSync } from "fs";
import * as path from "path";
import config from "../skywind/config";

export class TestGameLoaderMock implements GameLoader {

    private readonly allGames: GameModule[];
    private readonly gamesMap: Map<string, GameModule>;

    constructor(...gameModuleName: string[]) {
        this.gamesMap = this.loadMockGames(...gameModuleName);
        this.allGames = [...this.gamesMap.values()];
    }

    public loadGame(gameCode: string): GameModule {
        return this.gamesMap.get(gameCode);
    }

    public gameList(): GameModule[] {
        return this.allGames;
    }

    private loadMockGames(...gameModuleName: string[]): Map<string, GameModule> {
        const result = new Map<string, GameModule>();

        gameModuleName.forEach((moduleName) => {
            const gameModule = this.loadSingleGameModule(moduleName);
            result.set(gameModule.moduleName.id, gameModule);
        });

        return result;
    }

    private loadSingleGameModule(moduleName: string): GameModule {
        try {
            const game = require(moduleName);
            const version = "mock";
            const gameCode = moduleName.substr(moduleName.lastIndexOf("/") + 1);

            const gameModule: GameModule = {
                game: game,
                moduleName: {
                    nameWithVersion: moduleName + "@" + version,
                    id: gameCode,
                    name: moduleName,
                    version: version,
                }
            };

            return gameModule;
        } catch (err) {
            console.error(err); //tslint:disable-line
            throw new GameModuleNotLoadedError(moduleName);
        }
    }

    public getFlavorRoute(): string {
        return "";
    }
}

export const getGamePackagesCodes = () => {
    try {
        const content = readFileSync(path.resolve(process.cwd(), "games.json"), "utf8");
        return JSON.parse(content).map(code => `${config.npm.moduleGroup}/${code}`);
    } catch (err) {
        console.log("Please, create file games.json with array of game codes which you wanna use."); //tslint:disable-line
        console.log("After that run `npm run install-games` command"); //tslint:disable-line
        throw err;
    }
};
