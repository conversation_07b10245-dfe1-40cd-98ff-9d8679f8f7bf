import { start } from "./skywind/app";
import { Game<PERSON>oader, GameModule, setGameLoader } from "./skywind/services/game/gamemodule";
import { registerEngine } from "./skywind/globals/engine";
import { getSessionIdGenerator } from "./skywind/services/gameSession";
import { getRoundIdGenerator } from "./skywind/services/onlinestorage/gameContextCommands";
import { Probs, ServerProbs } from "./skywind/services/probs/serverProbs";

export { GameModule, GameLoader, ModuleName } from "./skywind/services/game/gamemodule";
export { startCleanupJob, startExpireContextJob, startReactivateContextJob } from "./skywind/workers/cleanup";
export { forceCleanup } from "./skywind/forceCleanup";
export { startUnload as startHistoryUnloadJob } from "./skywind/workers/history";
export { startContextStorageService } from "./skywind/contextStorageServer";
export { startInternalServer } from "./skywind/internalserver";
export { startGameContextForQasApi } from "./skywind/gameContextApiForQas";
export { RandomGeneratorFactory, setRandomGeneratorFactory } from "./skywind/services/random";
export { Probs } from "./skywind/services/probs/serverProbs";
export { setGameServerVersion, getGameServerVersion } from "./skywind/services/gameServerVersion";

export async function startGameEngine(gameLoader: GameLoader) {
    registerEngine();
    await getRoundIdGenerator().init();
    await getSessionIdGenerator().init();
    setGameLoader(gameLoader);
    ServerProbs.markReady();
    return start();
}

export function getProbs(): Probs {
    return ServerProbs;
}
