import { Consumer, UnloadService } from "./unloadService";
import { GameHistoryQueue } from "../services/queue/redisQueue";
import config from "../config";
import { logging, measures } from "@skywind-group/sw-utils";
import { GameEventHistory } from "./history";
import measure = measures.measure;
import { IQueue, QueueItem } from "../services/queue/queue";
import { GAME_HISTORY_LATEST_TS_PERIOD } from "../utils/common";
const log = logging.logger("sw-slot-engine:unload-history:game");

/**
 * Base interface for game history consumers
 */
export interface HistoryConsumer extends Consumer<GameEventHistory> {
}

/**
 *  Composit pattern implementation for game history consumers
 */
export class CompositeConsumer<T> implements Consumer<T> {
    constructor(public readonly consumers: Consumer<T>[]) {
    }

    public async save(items: T[]): Promise<void> {
        await Promise.all(this.consumers.map((consumer) => consumer.save(items)));
    }

}

/**
 * Unload service for game history events
 */
export class UnloadEventHistoryService extends UnloadService<GameEventHistory> {
    public static UNLOAD_DATA_MEASURE: string = "UnloadEventHistoryService.unloadData";

    constructor(consumer: HistoryConsumer, queue: IQueue<GameEventHistory> = new GameHistoryQueue()) {
        super(queue, consumer, config.unloader.historyBatchSize);
    }

    @measure({ name: UnloadEventHistoryService.UNLOAD_DATA_MEASURE, isAsync: true})
    public unloadData(): Promise<QueueItem<GameEventHistory>[]> {
        return super.unloadData();
    }

    protected map(item: QueueItem<GameEventHistory>): GameEventHistory {
        const result: GameEventHistory = super.map(item);
        if (Date.now() - new Date(result.ts).getTime() >= GAME_HISTORY_LATEST_TS_PERIOD) {
            log.warn({ result }, "Fix old gamehistory ts");
            result.ts = new Date();
        }
        return result;
    }
}
