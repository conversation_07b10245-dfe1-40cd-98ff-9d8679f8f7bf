import { GameFlowContext, RequestContext, RoundStatistics, SpecialState } from "../services/context/gamecontext";
import { Balance, PaymentOperation } from "../services/wallet";
import { PlayMode } from "../services/playMode";
import { RecoveryType } from "../services/offlinestorage/offlineCommands";
import { JackpotPendingProcessResult } from "../services/jpn/jackpotFlow";
import { ExtraData, generateCtrl } from "../utils/common";
import { GameHistory } from "@skywind-group/sw-game-core";

/**
 * Game history event emitted after play request
 */
export interface GameEventHistory extends EventHistory, JackpotHistory, BaseHistory, RoundStatisticsSupport,
    PlayerIpInfo {
    disablePlayerPhantomFeatures?: boolean;
    instantJackpot?: boolean;
}

export type GameEventHistoryShort = Pick<GameEventHistory, "roundId" | "eventId" | "ctrl">;

export interface BaseHistory {
    gameId: string;
    brandId: number;
    playerCode: string;
    deviceId: string;
    gameCode: string;
    currency: string;
    insertedAt?: Date;
    ctrl: number;
    deviceData?: any;
}

/**
 *  Base interface for game event
 */
export interface EventHistory {
    sessionId: string;
    roundId: string;
    eventId: number;
    type: string;
    gameVersion: string;
    roundEnded: boolean;
    walletTransactionId: string;
    win: number;
    bet: number;
    balanceBefore?: number;
    balanceAfter?: number;
    ts: Date;
    result: any;
    test: boolean;
    extraData?: ExtraData;
    credit?: number;
    debit?: number;
    freeBetCoin?: number;
    lobbySessionId?: string;
    isHidden?: boolean;
}

export interface JackpotHistory {
    totalJpContribution?: number;
    totalJpWin?: number;
}

export interface RoundStatisticsSupport {
    totalRoundBet?: number;
    totalRoundWin?: number;
}

export interface PlayerIpInfo {
    playerIp?: string;
}

export interface RTPInfoBI {
    rtpDeduction?: number;
}

/**
 * Round event emitted after finishing round or storing broken round
 */
export interface RoundHistory extends RoundStatistics, JackpotHistory, BaseHistory, ExtraData, OperatorSiteParams,
    RTPInfoBI {
    id: string;
    sessionId: string;
    test: boolean;
    recoveryType?: RecoveryType;
    disablePlayerPhantomFeatures?: boolean;
}

export enum SessionInterruptionReason {
    SESSION_EXPIRED = "session_expired",
    PENDING_PAYMENT = "pending_payment",
    PENDING_JACKPOT = "pending_jackpot",
    BROKEN_GAME_CONTEXT = "broken_game_context"
}

export interface OperatorSiteParams {
    operatorSiteId?: number;
}

/**
 * Game session history
 */
export interface SessionHistory extends BaseHistory, OperatorSiteParams {
    sessionId: string;
    gameVersion: string;
    country?: string;
    language?: string;
    browser?: string;
    os?: string;
    platform?: string;
    screenSize?: string;
    test: boolean;
    broken?: boolean;
    startedAt: Date;
    finishedAt?: Date;
    playedFromCountry?: string;
    operatorCountry?: string;
    operatorPlayerCountry?: string;
    ip?: string;
    browserVersion?: string;
    interruptionReason?: SessionInterruptionReason;
    referrer?: string;
    extSessionId?: string;
}

export interface ExtendedGameHistory extends GameHistory {
    credit?: number;
    debit?: number;
    ts?: Date;
    instantJackpot?: boolean;
}

/**
 *  Cleanup request
 */
export interface CleanupContextRequest {
    id: string;
}

/**
 * Create game history information holder
 *
 */
export function createGameHistoryEvent(context: GameFlowContext,
                                       balance: Balance,
                                       jpContribution: number,
                                       roundEnded: boolean,
                                       roundId: string = context.roundId,
                                       eventId: number = context.gameSerialNumber,
                                       currency: string = context.currencyForHistory): GameEventHistory {

    const walletOperation = context.pendingModification?.walletOperation;
    const paymentOperation = walletOperation?.operation;
    const gamePayment = (paymentOperation === "payment" || paymentOperation === "split-payment") ?
                        walletOperation as PaymentOperation : undefined;

    const pendingHistory: ExtendedGameHistory = context.pendingModification.history;
    const result: GameEventHistory = {
        sessionId: context.session.sessionId,
        gameId: context.gameData.gameId,
        gameVersion: context.gameVersion,
        gameCode: context.id.gameCode,
        brandId: context.id.brandId,
        type: pendingHistory ? pendingHistory.type : undefined,
        playerCode: context.id.playerCode,
        deviceId: context.id.deviceId,
        roundEnded: roundEnded,
        walletTransactionId: walletOperation ? walletOperation.transactionId : undefined,
        eventId,
        currency,
        win: gamePayment ? gamePayment.win : 0,
        bet: gamePayment ? gamePayment.bet : 0,
        balanceBefore: balance ?
                       ((balance.previousValue === undefined) ? balance.main : balance.previousValue) :
                       undefined,
        balanceAfter: balance ? balance.main : undefined,
        roundId,
        ts: gamePayment?.ts || pendingHistory?.ts || new Date(),
        result: pendingHistory ? pendingHistory.data : {},
        test: context.gameData.gameTokenData.test,
        totalJpWin: 0,
        totalJpContribution: jpContribution || 0,
        credit: pendingHistory && pendingHistory.credit,
        debit: pendingHistory && pendingHistory.debit,
        freeBetCoin: walletOperation && walletOperation.freeBetCoin ? walletOperation.freeBetCoin : undefined,
        ...(context.gameData.lobbySessionId ? { lobbySessionId: context.gameData.lobbySessionId } : {}),
        ctrl: generateCtrl(),
        disablePlayerPhantomFeatures: context.gameData.gameTokenData.disablePlayerPhantomFeatures,
        isHidden: pendingHistory ? pendingHistory.isHidden : undefined,
        instantJackpot: pendingHistory ? pendingHistory.instantJackpot : undefined,
    };

    addExtraDataToGameHistoryEventIfPresent(context, result, balance);

    return result;
}

function addExtraDataToGameHistoryEventIfPresent(context: GameFlowContext,
                                                 event: GameEventHistory,
                                                 balance?: Balance): void {

    const fixedExtraData = context?.gameData?.settings?.fixedExtraData;
    if (fixedExtraData) {
        event.extraData = fixedExtraData;
    } else {
        const regulatoryData = context?.gameData?.gameTokenData?.regulatoryData;
        if (regulatoryData) {
            event.extraData = { regulatoryData };
        }
        const extRoundId = balance?.extraData?.extRoundId || context?.round?.extRoundId;
        if (extRoundId) {
            event.extraData = event.extraData || {};
            event.extraData.extRoundId = extRoundId;
        }
    }

    event.deviceData = context?.requestContext?.deviceData;
}

export function createForceFinishGameEvent(context: GameFlowContext,
                                           recoveryType: RecoveryType = RecoveryType.FORCE_FINISH,
                                           currency: string = context.currencyForHistory,
                                           gameResultBalance?: Balance): GameEventHistory {
    const walletOperation = context.jackpotPending
                            ? context?.jackpotPending?.walletOperation
                            : context?.pendingModification?.walletOperation;

    const paymentOperation = walletOperation?.operation;
    const gamePayment = (paymentOperation === "payment" || paymentOperation === "split-payment") ?
                        walletOperation as PaymentOperation : undefined;

    const result: GameEventHistory = {
        sessionId: context.session.sessionId,
        gameId: context.gameData.gameId,
        gameVersion: context.gameVersion,
        gameCode: context.id.gameCode,
        brandId: context.id.brandId,
        type: recoveryType,
        playerCode: context.id.playerCode,
        deviceId: context.id.deviceId,
        roundEnded: true,
        walletTransactionId: walletOperation?.transactionId,
        eventId: context.gameSerialNumber,
        currency,
        win: 0,
        bet: 0,
        balanceBefore: undefined,
        balanceAfter: gameResultBalance && gameResultBalance.main,
        roundId: context.roundId,
        ts: gamePayment?.ts || new Date(),
        result: {},
        test: context.gameData.gameTokenData.test,
        freeBetCoin: walletOperation && walletOperation.freeBetCoin ? walletOperation.freeBetCoin : undefined,
        ...(context.gameData.lobbySessionId ? { lobbySessionId: context.gameData.lobbySessionId } : {}),
        ctrl: generateCtrl(),
        isHidden: undefined
    };

    addExtraDataToGameHistoryEventIfPresent(context, result, gameResultBalance);

    return result;
}

/**
 * Create jackpot history information holder
 */
export function createJackpotGameHistoryEvent(context: GameFlowContext,
                                              jpResult: JackpotPendingProcessResult,
                                              roundEnded: boolean,
                                              roundId: string = context.roundId,
                                              eventId: number = context.gameSerialNumber,
                                              currency: string = context.currencyForHistory): GameEventHistory {

    const balance = jpResult && jpResult.balance;
    const totalWin = jpResult?.jpnResult?.totalJpWin;

    const walletOperation = context.jackpotPending.walletOperation;
    const paymentOperation = walletOperation?.operation;
    const gamePayment = (paymentOperation === "payment" ||
        paymentOperation === "split-payment" || paymentOperation === "bet" || paymentOperation === "win") ?
                        walletOperation as PaymentOperation : undefined;
    const history = context.jackpotPending.history;

    const result: GameEventHistory = {
        sessionId: context.session.sessionId,
        gameId: context.gameData.gameId,
        gameVersion: context.gameVersion,
        gameCode: context.id.gameCode,
        brandId: context.id.brandId,
        type: history ? history.type : undefined,
        playerCode: context.id.playerCode,
        deviceId: context.id.deviceId,
        roundEnded: roundEnded,
        walletTransactionId: walletOperation?.transactionId,
        eventId,
        currency,
        win: gamePayment ? gamePayment.win : 0,
        bet: 0,
        balanceBefore: balance ? balance.previousValue : undefined,
        balanceAfter: balance ? balance.main : undefined,
        roundId,
        ts: gamePayment?.ts || history?.ts || new Date(),
        result: history ? history.data : {},
        test: context.gameData.gameTokenData.test,
        totalJpWin: totalWin,
        totalJpContribution: 0,
        freeBetCoin: walletOperation && walletOperation.freeBetCoin ? walletOperation.freeBetCoin : undefined,
        ...(context.gameData.lobbySessionId ? { lobbySessionId: context.gameData.lobbySessionId } : {}),
        ctrl: generateCtrl(),
        isHidden: history ? history.isHidden : undefined
    };

    addExtraDataToGameHistoryEventIfPresent(context, result, balance);

    return result;
}

export function asEventHistory(item: GameEventHistory): EventHistory {
    return {
        sessionId: item.sessionId,
        roundId: item.roundId,
        eventId: item.eventId,
        type: item.type,
        gameVersion: item.gameVersion,
        roundEnded: item.roundEnded,
        walletTransactionId: item.walletTransactionId,
        win: item.win,
        bet: item.bet,
        balanceBefore: item.balanceBefore,
        balanceAfter: item.balanceAfter,
        ts: item.ts,
        result: JSON.stringify(item.result),
        test: item.test
    };
}

export function createRoundHistoryEvent(context: GameFlowContext,
                                        round: RoundStatistics = context.round,
                                        roundId: string = context.roundId,
                                        currency: string = context.currencyForHistory,
                                        gameResultBalance?: Balance): RoundHistory {
    const result: RoundHistory = {
        sessionId: context.session.sessionId,
        gameId: context.gameData.gameId,
        gameCode: context.id.gameCode,
        brandId: context.id.brandId,
        playerCode: context.id.playerCode,
        deviceId: context.id.deviceId,
        currency,
        totalWin: round.totalWin,
        totalBet: round.totalBet,
        totalEvents: round.totalEvents,
        balanceBefore: round.balanceBefore,
        balanceAfter: round.balanceAfter,
        id: roundId,
        startedAt: round.startedAt,
        // was round broken and needs to be updates
        broken: !!round.broken,
        finishedAt: round.finishedAt,
        test: context.gameData.gameTokenData.test,
        totalJpWin: round.totalJpWin,
        totalJpContribution: round.totalJpContribution,
        debit: round.debit,
        credit: round.credit,
        ctrl: generateCtrl(),
        operatorSiteId: context.gameData.operatorSiteId,
        disablePlayerPhantomFeatures: context.gameData.gameTokenData.disablePlayerPhantomFeatures
    };

    const rtpDeduction = context?.settings?.rtpConfigurator?.rtpDeduction;
    if (rtpDeduction) {
        result.rtpDeduction = rtpDeduction;
    }

    addExtraDataToRoundHistory(context, result, gameResultBalance);

    return result;
}

function addExtraDataToRoundHistory(context: GameFlowContext, roundHistory: RoundHistory, balance?: Balance): void {
    const extRoundId = balance?.extraData?.extRoundId || context?.round?.extRoundId;
    if (extRoundId) {
        roundHistory.extraData = { ...roundHistory.extraData, extRoundId };
    }
}

export function createSessionHistory(context: GameFlowContext, finishedAt?: Date): SessionHistory {
    if (!context.createdAt || context.session.isFinished) {
        return;
    }

    const platform = context.gameData.gameTokenData.platform ||
        context?.requestContext?.userAgent?.platform;

    return {
        sessionId: context.session.sessionId,
        gameId: context.gameData.gameId,
        gameVersion: context.gameVersion,
        gameCode: context.id.gameCode,
        brandId: context.id.brandId,
        playerCode: context.id.playerCode,
        deviceId: context.id.deviceId,
        currency: PlayMode.getCurrency(context.gameData.gameTokenData),
        country: context?.requestContext?.country,
        language: context?.requestContext?.language,
        browser: context?.requestContext?.userAgent?.browser,
        os: context?.requestContext?.userAgent?.os,
        platform,
        screenSize: context?.requestContext?.screenSize,
        test: context.gameData.gameTokenData.test,
        startedAt: context.createdAt,
        finishedAt: finishedAt,
        broken: context.broken,
        playedFromCountry: context?.requestContext?.playedFromCountry,
        operatorCountry: context?.requestContext?.operatorCountry,
        operatorPlayerCountry: context?.requestContext?.operatorPlayerCountry,
        ip: parseIp(context.requestContext),
        browserVersion: context?.requestContext?.userAgent?.browserVersion,
        ctrl: generateCtrl(),
        interruptionReason: getSessionInterruptionReason(context),
        referrer: context.gameData.referrer,
        operatorSiteId: context.gameData.operatorSiteId,
        extSessionId: context?.gameData?.gameTokenData?.merchantSessionId
    };
}

function getSessionInterruptionReason(context: GameFlowContext): SessionInterruptionReason {
    let interruptionReason: SessionInterruptionReason;
    if (context.brokenJackpot) {
        interruptionReason = SessionInterruptionReason.PENDING_JACKPOT;
    } else if (context.brokenPayment) {
        interruptionReason = SessionInterruptionReason.PENDING_PAYMENT;
    } else if (context.specialState === SpecialState.BROKEN_GAME_CONTEXT) {
        interruptionReason = SessionInterruptionReason.BROKEN_GAME_CONTEXT;
    } else if (context.unfinished) {
        interruptionReason = SessionInterruptionReason.SESSION_EXPIRED;
    }
    return interruptionReason;
}

function parseIp(requestContext: RequestContext): string {
    if (requestContext && requestContext.ip) {
        return requestContext.ip.split(",")[0].trim();
    }
    return undefined;
}
