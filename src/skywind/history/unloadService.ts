import { logging, measures } from "@skywind-group/sw-utils";
import { IQueue, QueueItem } from "../services/queue/queue";
import config from "../config";
import { Metrics } from "../utils/common";
import measureProvider = measures.measureProvider;
import { RedisDLQ } from "../services/queue/RedisDLQ";
import { Redis } from "../storage/redis";

const schedule = require("node-schedule");
const log = logging.logger("sw-slot-engine:unload-history:service");

/**
 * Event consumers
 * It's reponsible for storing events.
 *
 */
export interface Consumer<T> {
    save(items: T[]): Promise<void>;
}

/**
 * Unload service:
 * 1) gets events from a queue
 * 2) stores them using consumers
 * 3) remove stored items from queue
 *
 *
 * It's also responsible for:
 * 1) repairing data that suck in queue in unprocessed state.
 * 2) determining duplicates and move them to separate list.
 */
export class UnloadService<T> {
    public static readonly KAFKA_INVALID_MESSAGE = "InvalidMessage";

    constructor(private queue: IQueue<T>,
                private consumer: Consumer<T>,
                private maxBatchSize: number,
                private readonly dlq: RedisDLQ<T> = new RedisDLQ<T>(Redis)) {
        this.dlq.listName = `${this.queueName}:dlq`;
    }

    public get workerList() {
        return this.queue.workerList;
    }

    public get queueName() {
        return this.queue.listName;
    }

    /**
     * An iteration of unloading data
     */
    public async unloadData(): Promise<QueueItem<T>[]> {
        try {
            const items: QueueItem<T>[] = await this.queue.pop(this.maxBatchSize);
            log.info({ workerList: this.queue.workerList },
                "Get %s elements from queue", items.length);
            measureProvider.incrementGauge(Metrics.UNLOADED_ITEMS, items.length);
            await this.processItems(items);
            await this.queue.commit();
            return items;
        } catch (err) {
            measureProvider.saveError(err);
            log.error(err, { workerList: this.queue.workerList });
            await this.queue.repair();
        }
    }

    private async processItems(items: QueueItem<T>[]): Promise<void> {
        try {
            await this.consumer.save(items.map(i => this.map(i)));
        } catch (err) {
            if (err.message === UnloadService.KAFKA_INVALID_MESSAGE) {
                log.warn(err, `Got ${UnloadService.KAFKA_INVALID_MESSAGE}. Enter divide and conquer processing mode.`);
                // We can't know exactly which item is problematic,
                // so we find it using the "divide and conquer" paradigm
                await this.divideAndConquer(items);
            } else {
                throw err;
            }
        }
    }

    private async divideAndConquer(items: QueueItem<T>[]): Promise<void> {
        if (!items.length) {
            return;
        }
        try {
            await this.consumer.save(items.map(i => this.map(i)));
        } catch (err) {
            if (err.message === UnloadService.KAFKA_INVALID_MESSAGE) {
                if (items.length === 1) {
                    log.info(items[0], `Bad record found. Pushing it to the DLQ: ${this.dlq.listName}`);
                    return this.dlq.push(items[0]);
                } else {
                    await this.divideAndConquer(items.slice(0, items.length / 2));
                    await this.divideAndConquer(items.slice(items.length / 2));
                }
            } else {
                throw err;
            }
        }
    }

    public async start(): Promise<void> {
        this.scheduleWorkerRepairment();
        await this.unload();
    }

    /**
     * Run repairement for inactive workers.
     *
     * @param timestamp
     */
    public async repairOrphanWorkers(timestamp: number): Promise<void> {
        await measureProvider.runInTransaction(`Repair ${this.queue.listName}`, async () => {
            try {
                await this.queue.tryToRepairOrphanWorkers(timestamp);
            } catch (err) {
                measureProvider.saveError(err);
                log.error(err, "Error repairing workers");
            }
        });
    }

    protected map(item: QueueItem<T>): T {
        return item.data;
    }

    private async unload(): Promise<void> {
        log.info({ workerList: this.queue.workerList }, "Start unloading transaction");
        try {
            // noinspection InfiniteLoopJS
            while (true) {
                // wait for unit of work and reserve it in worker's queue
                await measureProvider.runInTransaction(`Unload ${this.queue.listName}`, async () => {
                    return this.unloadData();
                });
            }
        } catch (err) {
            log.error(err, { workerList: this.queue.workerList }, "Error uploading transaction");
        }
        log.info({ workerList: this.queue.workerList }, "Stop unloading transaction");
    }

    private async scheduleWorkerRepairment() {
        const logFireInfo = () => {
            if (job) {
                log.info("Next worker repairement at %s", job.nextInvocation().toString());
            }
        };
        const ttl = config.unloader.workerMaxOrphanTTL * 60 * 1000;
        await this.repairOrphanWorkers(Date.now() - ttl);
        const job = schedule.scheduleJob(config.unloader.repairWorkerCron, () => {
            logFireInfo();
            this.repairOrphanWorkers(Date.now() - ttl);
        });
        logFireInfo();
    }
}
