import * as request from "superagent";
import { HistoryConsumer } from "../unloadHistory";
import { GameEventHistory, asEventHistory, EventHistory } from "../history";
import config from "../../config";
import { measures, logging } from "@skywind-group/sw-utils";
import measure = measures.measure;

const log = logging.logger("sw-slot-engine:unload-history:alooma");

/**
 *  Consuming data through streaming service http://www.alooma.com
 */
export class AloomaHistoryConsumer implements HistoryConsumer {

    @measure({ name: "AloomaHistoryConsumer.save", isAsync: true })
    public async save(items: GameEventHistory[]): Promise<void> {
        const eventItems = items.map((item) => asEventHistory(item));
        const retries = config.unloader.aloomaConsumer.retries;
        for (let i = 0; i < retries; i++) {
            try {
                return await this.execute(eventItems);
            } catch (err) {
                log.error(err, "Alooma request error");
            }
        }

        return Promise.reject("Can't move data to Alooma!");
    }

    public static async create(): Promise<AloomaHistoryConsumer> {
        return new AloomaHistoryConsumer();
    }

    private execute(items: EventHistory[]): Promise<any> {
        log.info("Alooma request:", items);
        return request.post(`https://inputs.alooma.com/rest/${config.unloader.aloomaConsumer.token}`)
            .set("content-type", "application/json")
            .send({
                type: "game_history",
                items: items
            });
    }
}
