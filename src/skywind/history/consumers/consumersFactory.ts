import { Consumer } from "../unloadService";
import { AloomaHistoryConsumer } from "./aloomaHistoryConsumer";
import { GameHistoryConsumer } from "./gameHistoryConsumer";
import { GameEventHistory, RoundHistory, SessionHistory } from "../history";
import { kafka, logging } from "@skywind-group/sw-utils";
import { createKafkaConsumer } from "./kafka/kafkaConsumer";
import { RoundHistoryConsumer } from "./roundHistoryConsumer";
import { SessionHistoryConsumer } from "./sessionHistoryConsumer";
import config from "../../config";
import { CompositeConsumer } from "../unloadHistory";

const log = logging.logger("sw-slot-engine:unload-history:consumer");
/**
 * Conssumer factory registry
 */
const consumerTypes = {
    alooma: AloomaHistoryConsumer.create,
    gamehistory: GameHistoryConsumer.create
};

interface ExtendedKafkaConfig extends kafka.KafkaConfiguration {
    enabled: boolean;
}

/**
 * Create list of consumers for game event
 *
 * @param types consumer type names
 * @param kafkaConsumerConfig config for game events kafkaConsumer
 */
// tslint:disable-next-line:max-line-length
export async function createGameHistoryConsumers(types: string[], enableKafkaWriters: boolean,
                                                 kafkaConsumerConfig: ExtendedKafkaConfig): Promise<Consumer<GameEventHistory>> {
    log.info("Configured consumers", types);
    const consumers: Consumer<GameEventHistory>[] = [];
    if (types) {
        for (const c in consumerTypes) {
            if (types.indexOf(c) !== -1) {
                const factory = consumerTypes[c];
                consumers.push(await factory());
            }
        }
    }

    if (enableKafkaWriters && kafkaConsumerConfig.enabled) {
        try {
            consumers.push(
                await createKafkaConsumer<GameEventHistory>(kafkaConsumerConfig)
            );
            log.info("GameHistoryKafkaConsumer was successfully added.");
        } catch (err) {
            log.error(err);
        }
    }
    return compose(consumers);
}

// tslint:disable-next-line:max-line-length
export async function createRoundHistoryConsumers(enableDBConsumer: boolean, enableKafkaWriters: boolean,
                                                  kafkaConsumerConfig: ExtendedKafkaConfig): Promise<Consumer<RoundHistory>> {
    const consumers: Consumer<RoundHistory>[] = [];
    if (enableDBConsumer) {
        consumers.push(config.newRoundsConsumer ?
                       await RoundHistoryConsumer.create() :
                       await RoundHistoryConsumer.createOld());
    }
    if (enableKafkaWriters && kafkaConsumerConfig.enabled) {
        try {
            consumers.push(
                await createKafkaConsumer<RoundHistory>(kafkaConsumerConfig)
            );
            log.info("RoundHistoryKafkaConsumer was successfully added.");
        } catch (err) {
            log.error(err);
        }
    }
    return compose(consumers);
}

// tslint:disable-next-line:max-line-length
export async function createSessionHistoryConsumers(enableDBConsumer: boolean, enableKafkaWriters: boolean,
                                                    kafkaConsumerConfig: ExtendedKafkaConfig): Promise<Consumer<SessionHistory>> {
    const consumers: Consumer<SessionHistory>[] = [];
    if (enableDBConsumer) {
        consumers.push(await SessionHistoryConsumer.create());
    }

    if (enableKafkaWriters && kafkaConsumerConfig.enabled) {
        try {
            consumers.push(
                await createKafkaConsumer<SessionHistory>(kafkaConsumerConfig)
            );
            log.info("SessionHistoryKafkaConsumer was successfully added.");
        } catch (err) {
            log.error(err);
        }
    }
    return compose(consumers);
}

function compose<T>(consumers: Consumer<T>[]): Consumer<T> {
    if (consumers.length === 1) {
        return consumers[0];
    } else {
        return new CompositeConsumer(consumers);
    }
}
