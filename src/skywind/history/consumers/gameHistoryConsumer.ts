import { logging, measures } from "@skywind-group/sw-utils";
import { InferAttributes, InferCreationAttributes, Model } from "sequelize";
import { GameEventHistory } from "../history";
import { HistoryConsumer } from "../unloadHistory";
import config from "../../config";
import { findBatchWhereIn, getGameHistoryModels } from "../model/gameHistory";
import { BaseDBConsumer, ConsumerDBModels } from "./baseDBConsumer";
import measure = measures.measure;

const log = logging.logger("sw-slot-engine:unload-history:game");

/**
 * Storing data in old format.
 *
 * Should be deprecated in later versions.
 */
interface GameEventHistoryInstance extends Model<
    InferAttributes<GameEventHistoryInstance>,
    InferCreationAttributes<GameEventHistoryInstance>
>, GameEventHistory {}
export class GameHistoryConsumer extends BaseDBConsumer<GameEventHistory> implements HistoryConsumer {

    constructor(private readonly models: ConsumerDBModels<GameEventHistoryInstance>) {
        super();
    }

    protected key(item: GameEventHistory): string {
        return `${item.roundId}_${item.eventId}`;
    }

    @measure({ name: "GameHistoryConsumer.save", isAsync: true })
    protected async doSave(items: GameEventHistory[]): Promise<void> {
        log.debug(items, "Bulk create gamehistory");
        await this.models.original.bulkCreate(items);
    }

    @measure({ name: "GameHistoryConsumer.find", isAsync: true })
    protected async find(items: GameEventHistory[]): Promise<any[]> {
        return findBatchWhereIn(items);
    }

    @measure({ name: "GameHistoryConsumer.saveDuplicate", isAsync: true })
    protected async doSaveDuplicates(items: GameEventHistory[]): Promise<void> {
        log.debug(items, "Bulk create duplicates gamehistory");
        await this.models.duplicates.bulkCreate(items);
    }

    public static async create(): Promise<GameHistoryConsumer> {
        const models = getGameHistoryModels();
        if (config.db.syncOnStart) {
            await models.sync();
        }

        return new GameHistoryConsumer(models);
    }
}
