import { logging, measures } from "@skywind-group/sw-utils";
import { InferAttributes, InferCreationAttributes, Model, Op, Transaction } from "sequelize";
import { getOldRoundHistoryModels } from "../model/oldRoundHistory";
import { RoundHistory } from "../history";
import db from "../../../skywind/storage/db";
import config from "../../config";
import { BaseDBConsumer } from "./baseDBConsumer";
import { getRoundsHistoryModels, RoundConsumerDBModels } from "../model/roundHistory";
import measure = measures.measure;

const log = logging.logger("sw-slot-engine:unload-history:round");

/**
 * Storing rounds in database.
 *
 * If we need to finish previously saved round we will remove it and then insert a new record.
 */
interface RoundHistoryInstance extends Model<
    InferAttributes<RoundHistoryInstance>,
    InferCreationAttributes<RoundHistoryInstance>
>, RoundHistory {}
export class RoundHistoryConsumer extends BaseDBConsumer<RoundHistory> {

    constructor(private readonly models: RoundConsumerDBModels) {
        super();
    }

    @measure({ name: "RoundHistoryConsumer.save", isAsync: true })
    public async doSave(items: RoundHistory[]): Promise<void> {
        const [broken, newItems] = this.split(items);

        await db.get().transaction((trx) => {
            return this.saveRounds(trx, broken, newItems) as any;
        });
    }

    @measure({ name: "RoundHistoryConsumer.find", isAsync: true })
    protected async find(items: RoundHistory[]): Promise<RoundHistory[]> {
        // Search for finished and broken-finished round. Broken-unfinished rounds are processed during save operation.
        return this.models.original.findAll({
            where: {
                id: {
                    [Op.in]: items.map(i => i.id)
                }
            }
        });
    }

    protected key(item: RoundHistory) {
        return `${item.id}`;
    }

    @measure({ name: "RoundHistoryConsumer.doSaveDuplicates", isAsync: true })
    protected doSaveDuplicates(items: RoundHistory[]) {
        log.debug(items, "Bulk create duplicates round history");
        this.models.duplicates.bulkCreate(items);
    }

    public static async createOld(): Promise<RoundHistoryConsumer> {
        const models = getOldRoundHistoryModels();
        if (config.db.syncOnStart) {
            await models.sync();
        }

        return new RoundHistoryConsumer(models);
    }

    public static async create(): Promise<RoundHistoryConsumer> {
        const models = getRoundsHistoryModels();
        if (config.db.syncOnStart) {
            await models.sync();
        }

        return new RoundHistoryConsumer(models);
    }

    private split(items: RoundHistory[]): [RoundHistory[], RoundHistory[]] {
        const broken: RoundHistory[] = [];
        const newItems: RoundHistory[] = [];
        // find broken(unfinished) rounds
        items.forEach((item) => {
            if (item.broken || !item.finishedAt) {
                broken.push(item);
            } else {
                newItems.push(item);
            }
        });

        return [broken, newItems];
    }

    private async saveRounds(trx: Transaction, broken: RoundHistory[],
                             finishedItems: RoundHistory[]): Promise<void> {
        const unFinishedItems: RoundHistory[] = [];
        if (broken.length > 0) {
            const brokenDBItems: Map<string, RoundHistory> = await this.findBrokenRounds(trx, broken);

            for (const item of broken) {
                const dbItem: RoundHistory = brokenDBItems.get(item.id.toString());
                if (dbItem) {
                    if (+item.totalEvents >= +dbItem.totalEvents) {
                        log.info("Delete broken roundhistory %s", dbItem.id);
                        await this.deleteUnFinishedRounds(trx, item);
                    } else {
                        // we do not insert or update unfinished
                        continue;
                    }
                }

                if (item.finishedAt) {
                    finishedItems.push(item);
                } else {
                    unFinishedItems.push(item);
                }
            }
        }
        await this.models.original.bulkCreate(finishedItems, { transaction: trx });
        await this.models.unfinished.bulkCreate(unFinishedItems, { transaction: trx });

        log.debug(finishedItems, "Bulk create roundhistory");
    }

    @measure({ name: "RoundHistoryConsumer.deleteOldRounds", isAsync: true })
    private async deleteUnFinishedRounds(trx: Transaction, item) {
        return this.models.unfinished.destroy({
            transaction: trx,
            where: {
                id: item.id
            }
        });
    }

    private async findBrokenRounds(trx: Transaction,
                                   broken: RoundHistory[]): Promise<Map<string, RoundHistory>> {
        const keys = broken.map(i => i.id).sort();

        const brokenDBItems: RoundHistory[] = await this.models.unfinished.findAll({
            transaction: trx,
            lock: Transaction.LOCK.UPDATE,
            where: {
                id: {
                    [Op.in]: keys
                }
            }
        });

        const keyValues: any = brokenDBItems.map((i) => {
            return [i.id.toString(), i];
        });
        return new Map<string, RoundHistory>(keyValues);
    }
}
