import { InferAttributes, InferCreationAttributes, Model, DataTypes } from "sequelize";
import db from "../../storage/db";
import { SessionHistory, SessionInterruptionReason } from "../history";
import { baseHistoryRecordSchema } from "./baseHistoryRecord";
import { ConsumerDBModels } from "../consumers/baseDBConsumer";

const baseSessionHistorySchema = {
    ...baseHistoryRecordSchema,
    currency: { field: "currency_code", type: DataTypes.CHAR(3), allowNull: false },
    gameVersion: { field: "game_version", type: DataTypes.STRING, allowNull: true },
    country: { field: "player_country", type: DataTypes.STRING, allowNull: true },
    language: { field: "player_language", type: DataTypes.STRING, allowNull: true },
    screenSize: { field: "screen_size", type: DataTypes.STRING },
    browser: { field: "browser", type: DataTypes.STRING },
    os: { field: "os", type: DataTypes.STRING },
    platform: { field: "platform", type: DataTypes.STRING },
    broken: { field: "is_broken", type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false },
    startedAt: { field: "started_at", type: "timestamp without time zone", allowNull: false },
    finishedAt: { field: "finished_at", type: "timestamp without time zone", allowNull: true },
    test: { field: "is_test", type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false },
    playedFromCountry: { field: "played_from_country", type: DataTypes.STRING },
    operatorCountry: { field: "operator_country", type: DataTypes.STRING },
    operatorPlayerCountry: { field: "operator_player_country", type: DataTypes.STRING },
    ip: { field: "ip", type: DataTypes.INET },
    browserVersion: { field: "browser_version", type: DataTypes.STRING },
    interruptionReason: {
        field: "interruption_reason",
        type: DataTypes.ENUM(...Object.values(SessionInterruptionReason))
    },
    referrer: { field: "referrer", type: DataTypes.STRING },
    operatorSiteId: { field: "operator_site_id", type: DataTypes.INTEGER },
    extSessionId: { field: "ext_session_id", type: DataTypes.STRING },
};

interface SessionHistoryDBInstance extends Model<
    InferAttributes<SessionHistoryDBInstance>,
    InferCreationAttributes<SessionHistoryDBInstance>
>, SessionHistory {}
const sessionHistorySchema = {
    ...baseSessionHistorySchema,
    sessionId: { field: "id", type: DataTypes.BIGINT, allowNull: false, primaryKey: true },
};

interface DuplicateSessionHistoryDBInstance extends Model<
    InferAttributes<DuplicateSessionHistoryDBInstance>,
    InferCreationAttributes<DuplicateSessionHistoryDBInstance>
>, SessionHistory {}
const duplicateSessionHistorySchema = {
    ...baseSessionHistorySchema,
    id: { field: "pk_id", type: DataTypes.BIGINT, allowNull: false, primaryKey: true, autoIncrement: true },
    sessionId: { field: "id", type: DataTypes.BIGINT, allowNull: false, primaryKey: false },
};

/**
 * Session database original.
 */
interface SessionHistoryInstance extends Model<
    InferAttributes<SessionHistoryInstance>,
    InferCreationAttributes<SessionHistoryInstance>
>, SessionHistory {}
let models: ConsumerDBModels<SessionHistoryInstance>;
export function getSessionHistoryModels(): ConsumerDBModels<SessionHistoryInstance> {
    if (!models) {
        models = new ConsumerDBModels(
            db.get().define<SessionHistoryDBInstance, SessionHistory>(
                "sessions_history",
                sessionHistorySchema,
                { timestamps: false, freezeTableName: true }
            ),
            db.get().define<DuplicateSessionHistoryDBInstance, SessionHistory>(
                "sessions_history_duplicates",
                duplicateSessionHistorySchema,
                { timestamps: false, freezeTableName: true }
            ),
        );
    }

    return models;
}
