import { DataTypes, fn } from "sequelize";

export const baseHistoryRecordSchema = {
    brandId: { field: "brand_id", type: DataTypes.INTEGER, allowNull: false },
    playerCode: { field: "player_code", type: DataTypes.STRING, allowNull: false },
    gameId: { field: "game_id", type: DataTypes.STRING, allowNull: false },
    gameCode: { field: "game_code", type: DataTypes.STRING, allowNull: false },
    deviceId: { field: "device_id", type: DataTypes.STRING, allowNull: false },
    currency: { field: "currency", type: DataTypes.CHAR(3), allowNull: false },
    insertedAt: { field: "inserted_at", type: DataTypes.DATE, allowNull: false, defaultValue: fn("NOW") },
    ctrl: { field: "ctrl", type: DataTypes.INTEGER, allowNull: true },
};
