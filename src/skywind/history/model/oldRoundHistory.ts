import { InferAttributes, InferCreationAttributes, Model, DataTypes } from "sequelize";
import db from "../../storage/db";
import { RoundHistory } from "../history";
import { baseRoundHistorySchema } from "./baseRoundsHistory";
import { RoundConsumerDBModels } from "./roundHistory";

interface OldRoundHistoryDBInstance extends Model<
    InferAttributes<OldRoundHistoryDBInstance>,
    InferCreationAttributes<OldRoundHistoryDBInstance>
>, RoundHistory {}
const oldRoundHistorySchema = {
    ...baseRoundHistorySchema,
    id: { field: "id", type: DataTypes.BIGINT, allowNull: false, primaryKey: true },
    finishedAt: { field: "finished_at", type: "timestamp without time zone", allowNull: true },
};

interface OldDuplicateRoundHistoryDBInstance extends Model<
    InferAttributes<OldDuplicateRoundHistoryDBInstance>,
    InferCreationAttributes<OldDuplicateRoundHistoryDBInstance>
>, RoundHistory {}
const oldDuplicateRoundHistorySchema = {
    ...baseRoundHistorySchema,
    pkId: { field: "pk_id", type: DataTypes.BIGINT, allowNull: false, primaryKey: true, autoIncrement: true },
    id: { field: "id", type: DataTypes.BIGINT, allowNull: false, primaryKey: false },
    finishedAt: { field: "finished_at", type: "timestamp without time zone", allowNull: true },
};

let models: RoundConsumerDBModels;

/**
 * Round database model.
 */
export function getOldRoundHistoryModels(): RoundConsumerDBModels {
    if (!models) {
        const model = db.get().define<OldRoundHistoryDBInstance, RoundHistory>(
            "rounds_history",
            oldRoundHistorySchema,
            { timestamps: false, freezeTableName: true }
        );
        models = new RoundConsumerDBModels(
            model,
            model,
            db.get().define<OldDuplicateRoundHistoryDBInstance, RoundHistory>(
                "rounds_history_duplicates",
                oldDuplicateRoundHistorySchema,
                { timestamps: false, freezeTableName: true }
            )
        );
    }

    return models;
}
