import { InferAttributes, InferCreationAttributes, Model, DataTypes, ModelStatic } from "sequelize";
import db from "../../storage/db";
import { RoundHistory } from "../history";
import { baseRoundHistorySchema } from "./baseRoundsHistory";
import { ConsumerDBModels } from "../consumers/baseDBConsumer";

interface RoundHistoryInstance extends Model<
    InferAttributes<RoundHistoryInstance>,
    InferCreationAttributes<RoundHistoryInstance>
>, RoundHistory {}
export class RoundConsumerDBModels extends ConsumerDBModels<RoundHistoryInstance> {
    constructor(original: ModelStatic<RoundHistoryInstance>,
                public readonly unfinished: ModelStatic<RoundHistoryInstance>,
                duplicates: ModelStatic<RoundHistoryInstance>) {
        super(original, duplicates);
    }

    public async sync() {
        await super.sync();
        await this.unfinished.sync();
    }
}
interface FinishedRoundHistoryDBInstance extends Model<
    InferAttributes<FinishedRoundHistoryDBInstance>,
    InferCreationAttributes<FinishedRoundHistoryDBInstance>
>, RoundHistory {}
const finishedRoundHistorySchema = {
    ...baseRoundHistorySchema,
    id: { field: "id", type: DataTypes.BIGINT, allowNull: false, primaryKey: true },
    finishedAt: { field: "finished_at", type: "timestamp without time zone", allowNull: false },
};

interface UnFinishedRoundHistoryDBInstance extends Model<
    InferAttributes<UnFinishedRoundHistoryDBInstance>,
    InferCreationAttributes<UnFinishedRoundHistoryDBInstance>
>, RoundHistory {}
const unFinishedRoundHistorySchema = {
    ...baseRoundHistorySchema,
    id: { field: "id", type: DataTypes.BIGINT, allowNull: false, primaryKey: true },
};

interface DuplicateRoundHistoryDBInstance extends Model<
    InferAttributes<DuplicateRoundHistoryDBInstance>,
    InferCreationAttributes<DuplicateRoundHistoryDBInstance>
>, RoundHistory {}
const duplicateRoundHistorySchema = {
    ...baseRoundHistorySchema,
    pkId: { field: "pk_id", type: DataTypes.BIGINT, allowNull: false, primaryKey: true, autoIncrement: true },
    id: { field: "id", type: DataTypes.BIGINT, allowNull: false, primaryKey: false },
    finishedAt: { field: "finished_at", type: "timestamp without time zone", allowNull: true },
};

let models: RoundConsumerDBModels;

/**
 * Round database model.
 */
export function getRoundsHistoryModels(): RoundConsumerDBModels {
    if (!models) {
        models = new RoundConsumerDBModels(
            db.get().define<FinishedRoundHistoryDBInstance, RoundHistory>(
                "rounds_finished",
                finishedRoundHistorySchema,
                { timestamps: false, freezeTableName: true }
            ),
            db.get().define<UnFinishedRoundHistoryDBInstance, RoundHistory>(
                "rounds_unfinished",
                unFinishedRoundHistorySchema,
                { timestamps: false, freezeTableName: true }
            ),
            db.get().define<DuplicateRoundHistoryDBInstance, RoundHistory>(
                "rounds_history_duplicates",
                duplicateRoundHistorySchema,
                { timestamps: false, freezeTableName: true }
            )
        );
    }

    return models;
}
