import { Redis as RedisClient } from "ioredis";
import {
    IRedisConnection,
    connect,
    IWallet,
    IWalletManager,
    ITransaction,
    IDirectWalletConfiguration,
    IOperation,
} from "@skywind-group/sw-wallet";
import { Redis } from "./storage/redis";
import db from "../skywind/storage/db";

/**
 * Implement IRedisConnection from wallet which is needed for redis connections
 */
class RedisConnection implements IRedisConnection {
    public get(): Promise<RedisClient> {
        return Redis.get().get();
    }

    public async release(client: RedisClient): Promise<void> {
        await Redis.get().release(client);
    }

    public async waitForSync(client: RedisClient): Promise<void> {
        return Redis.get().waitForSync(client);
    }
}

// create instance of the connection
const redis: IRedisConnection = new RedisConnection();

const walletConfig: IDirectWalletConfiguration = {
    type: "direct",

    db: () => {
        return db.get();
    },

    connection: () => {
        return redis;
    },

    archiveDB: () => {
        return db.get();
    }
};

let walletManager: IWalletManager;

/**
 * getManager - singleton which return the current wallet management access
 * @returns {any}
 */
export async function getManager(): Promise<IWalletManager> {
    if (walletManager) {
        return walletManager;
    }
    return connect(walletConfig)
        .then((manager: IWalletManager) => {
            walletManager = manager;
            return walletManager;
        });
}

/**
 * get - get specific wallet account
 *
 * @param key - unique key of the wallet account
 * @returns {Promise<IWallet>}
 */
export function get(key: string): Promise<IWallet> {
    return getManager()
        .then((manager: IWalletManager) => {
            return manager.get(key);
        });
}

/**
 * start transaction
 *
 * @returns {Promise<ITransaction>}
 */
export async function startTransaction(id: string, option?: IOperation): Promise<ITransaction> {
    const manager: IWalletManager = await getManager();
    return manager.startTransaction(id, option);
}

/**
 *  Generate transaction identifier
 *
 * @returns {Promise<string>}
 */
export async function generateTransactionId(): Promise<string> {
    const manager: IWalletManager = await getManager();
    return manager.generateTransactionId().then(trxId => trxId.publicId);
}
