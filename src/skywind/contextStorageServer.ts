import { measures } from "@skywind-group/sw-utils";

measures.measureProvider.baseInstrument();

import { defineRoutes } from "./api/remotestorage/routes";

import { logging } from "@skywind-group/sw-utils";
import config from "./config";
import * as http from "http";
import * as fastify from "./fastify";
import { FastifyInstance } from "fastify";
import { setUpTimeouts } from "./fastify";
import { ContextVariables } from "./utils/contextVariables";
import { inspect } from "util";

const log = logging.logger("sw-context-service");

require("source-map-support").install();

export async function startContextStorageService(): Promise<http.Server> {
    const app: FastifyInstance = fastify.create();

    defineRoutes(app);

    process.on("uncaughtException", (err) => {
        log.error(err, "uncaughtException occurred. Server continuing to work");
    });

    process.on("unhandledRejection", (err, promise) => {
        log.error({
            ...err,
            ...ContextVariables.extractFromUnhandledPromiseRejection(promise)
        }, "unhandledRejection", inspect(promise));
    });

    if (config.isProduction()) {
        // tslint:disable:no-empty
        console.log = function() {
        };
    }

    return new Promise<http.Server>((resolve, reject) => {
        app.listen(config.offlineStorage.port, "::", function(err) {
            if (err) {
                return reject(err);
            }
            setUpTimeouts(app);
            resolve(app.server);
            log.info("Game context storage service started at : " + config.offlineStorage.port);
        });
    });
}
