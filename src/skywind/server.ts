import { init as initCurrencyExchange } from "./services/currencyexchange";
import { logging } from "@skywind-group/sw-utils";
import { initModuleCache } from "./services/game/game";
import { getApplication } from "./bootstrap";
import { setUpTimeouts } from "./fastify";
import { getVersion } from "./utils/version";
import { IoServerV2, IoServerV4 } from "./io-versions";
import config from "./config";

const log = logging.logger("sw-slot-engine:start-up");

export async function start(port: number): Promise<void> {
    await initCurrencyExchange();
    await initModuleCache();

    const app = getApplication();

    await IoServerV2.start(app);
    if (config.socketV4.enableNewSocketTerminal) {
        await IoServerV4.start(app);
    }

    new Promise((resolve, reject) => {
        app.listen(port, "::", function(err) {
            if (err) {
                return reject(err);
            }
            setUpTimeouts(app);
            resolve(app.server);

            log.info(`Game Core API listening on: ${port}`);
            log.info(`Game Core API AppVersion: ${getVersion("/../")}`);
        });
    });
}
