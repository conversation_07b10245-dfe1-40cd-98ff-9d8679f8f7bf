import { logging, measures } from "@skywind-group/sw-utils";
import { Server, Socket, Namespace } from "socket.io";
import processSocketIOProtocol from "../api/gameasync";
import config from "../config";
import { subscribeReconnect } from "../services/reconnect";
import * as cookieParser from "socket.io-cookie-parser";
import measureProvider = measures.measureProvider;
import Metrics = measures.Metrics;
import { corsOptionsDelegate } from "../api/corsSupport";
import { FastifyInstance } from "fastify";

export class IoServerV4 {
    public static async start(app: FastifyInstance) {
        const io = new Server(
            app.server,
            {
                path: config.socketV4.path,
                cors: corsOptionsDelegate,
                allowEIO3: true,
            }
        );
        await IoServerV4.useIO(io);
    }

    public static async useIO(io: Server) {
        const log = logging.logger("sw-slot-engine:io-v4");
        log.info("Waiting for socket.io connections");
        io.use(cookieParser());

        const mainNamespace = io.of("/");
        mainNamespace.on("connection", processSocketIOProtocol);

        const gameNamespace = io.of(config.gameEntryPoint);
        gameNamespace.on("connection", processSocketIOProtocol);

        this.countWebsockets(mainNamespace, gameNamespace);
        this.countWebsockets(gameNamespace, mainNamespace);

        return subscribeReconnect(io);
    }

    protected static countWebsockets(namespaceA: Namespace, namespaceB: Namespace): void {
        namespaceA.on("connect", (socket: Socket) => {
            measureProvider.setGauge(
                `${Metrics.WEBSOCKET_CONNECTION_COUNT}_v4`,
                namespaceA.sockets.size + namespaceB.sockets.size
            );
            socket.on("disconnect", () => {
                measureProvider.setGauge(
                    `${Metrics.WEBSOCKET_CONNECTION_COUNT}_v4`,
                    Math.abs(namespaceA.sockets.size - namespaceB.sockets.size)
                );
            });
        });
    }
}
