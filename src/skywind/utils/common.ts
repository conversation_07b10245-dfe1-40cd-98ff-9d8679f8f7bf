import { RequireRetransmitError } from "../errors";
import config from "../config";

export const SITE_COOKIE = "siteRefCookie";
export const RECONNECT_NOTIFICATION = "reconnect";
export const retryCondition = (err: Error) => err instanceof RequireRetransmitError;

export const UNSUPPORTED = () => {
    return Promise.reject(new Error("Unsupported method!"));
};

export enum Metrics {
    REPAIRED_ITEMS = "repaired_items",
    UNLOADED_ITEMS = "unloaded_items",
    DUPLICATE_ITEMS = "duplicate_items"
}

export enum ExtraHeaders {
    X_SW_GAME = "x-sw-game",
    X_SW_PLAYER = "x-sw-player",
}

export interface ExtraData {
    [field: string]: any;
}

/**
 *  Get field by key, and if it doesn't exists, init with default value
 */
export function getOrInit<T, K extends keyof T>(obj: T, field: K, defaultValue: T[K]): T[K] {
    let result = obj[field];
    if (!result) {
        result = defaultValue;
        obj[field] = result;
    }
    return result;
}

const MAX_INT = Math.pow(2, 31);

export function generateCtrl() {
    return Math.floor(Math.random() * MAX_INT);
}

export function addMinutes(time: number, minutes: number): number {
    return time + (minutes * 60 * 1000);
}

export const GAME_HISTORY_LATEST_TS_PERIOD = config.unloader.gameHistoryLatestTsPeriod * 24 * 60 * 60 * 1000;
