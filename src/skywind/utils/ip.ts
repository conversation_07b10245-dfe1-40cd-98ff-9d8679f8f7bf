import { logging } from "@skywind-group/sw-utils";

const log = logging.logger("utils");

// tslint:disable-next-line:max-line-length
const IP_REGEX = /\b(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\b|(([A-Fa-f0-9]{1,4}:){1,6}|::([A-Fa-f0-9]{1,4}:){0,4})(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})|::|((?:[A-Fa-f0-9]{1,4}:){7}[A-Fa-f0-9]{1,4})|((?:[A-Fa-f0-9]{1,4}:){1,6}:)|(::(?:[A-Fa-f0-9]{1,4}:){1,6}[A-Fa-f0-9]{1,4})|([A-Fa-f0-9]{1,4}::([A-Fa-f0-9]{1,4}:){1,6})\b/g;

export function processIP(ip: string): string {
    const regexMatches = ip && ip.match(IP_REGEX);
    const ipMatch = regexMatches?.length && regexMatches[0];
    if (!ipMatch) {
        log.warn(`Invalid IP address: ${ip}`);
    }
    return ipMatch;
}
