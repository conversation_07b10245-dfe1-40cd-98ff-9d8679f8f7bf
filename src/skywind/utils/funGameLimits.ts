import { getCurrencyExchange } from "../services/currencyexchange";
import { Limits } from "../services/tokens";
const predefinedFunMultipliers = require("../../../resources/funStartBalanceMultiplier.json");

const limitsPrototype = require("../../../resources/funDefaultLimitsPrototype.json");

const ROOT_CURRENCY = "USD";
const PREFERRED_REMAINDERS = [1, 2, 4, 5, 8];

export function getFunGameMultiplier(currency: string): number {
    return predefinedFunMultipliers[currency] || calculateMultiplier(currency);
}

/**
 * Return aligned by [1 | 2 | 4 | 5 | 8] * 10^x multiplier closest to exchange rate between USD and target currency.
 * Return 1 if currency not found.
 * @param {string} currency - target currency
 * @returns {number}
 */
function calculateMultiplier(currency: string): number {
    try {
        const rate = getCurrencyExchange().getExchangeRate(ROOT_CURRENCY, currency);
        return calculateMultiplierByRate(rate);
    } catch (err) {
        return 1;
    }
}

/**
 * Return aligned by [1 | 2 | 4 | 5 | 8] * 10^x multiplier closest to provided rate
 * @param {number} rate - exchange rate
 * @returns {number}
 */
function calculateMultiplierByRate(rate: number): number {

    // Calculate power of ten of the rate (i.e 732 => 2, 0.3 => -1, etc)
    const tExp = Math.floor(Math.log10(rate));

    // Construct power of ten number (i.e 100, 0.1  etc)
    const tFlatNum = Math.pow(10, tExp);

    // Calculate significant remainder (i.e 732 => 7, 0.3 => 3, etc )
    const rawRem = Math.floor(rate / tFlatNum);

    // Search the closest remainder to preferred. (i.e 7 => 8, 9 => 8 etc)
    const rem = PREFERRED_REMAINDERS.reduce((prev, curr) => {
        return (Math.abs(curr - rawRem) < Math.abs(prev - rawRem) ? curr : prev);
    });

    // Multiply power of ten number by preferred remainder. (Now we have [1|2|3|4|8] * 10^x values)
    return tFlatNum * rem;
}

export function getFunGameLimits(currency: string): Limits {
    // const limits: Limits = defaultLimitsByCurrency[currency];
    const multiplier = getFunGameMultiplier(currency);

    const limits: Limits = {};
    multiplyPrototypeLimits(limitsPrototype, limits, multiplier);

    return limits;
}

/**
 * Multiply all numeric fields (including numeric arrays) by multiplier recurrently in any abject.
 * @param prototype - immutable prototype(source) object.
 * @param dst - empty destination object
 * @param multiplier
 */
function multiplyPrototypeLimits(prototype: any, dst: any, multiplier: number) {
    for (const key of Object.keys(prototype)) {
        const value = prototype[key];
        if (isObject(value)) {
            const embeddedDst = Array.isArray(value) ? dst[key] = [] : dst[key] = {};
            multiplyPrototypeLimits(value, embeddedDst, multiplier);
        } else if (Number.isFinite(value)) {
            dst[key] = value * multiplier;
        }
    }
}

function isObject(obj: any): boolean {
    return obj !== null && typeof obj === "object";
}
