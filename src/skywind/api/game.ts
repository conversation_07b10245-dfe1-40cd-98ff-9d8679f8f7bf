import { BaseRequest } from "@skywind-group/sw-game-core";
import { getSyncGameController } from "../services/synccontroller";
import { getUserAgent } from "../services/useragent";
import { RequestContext, RequestReferrer } from "../services/context/gamecontext";
import { SITE_COOKIE } from "../utils/common";
import { BodyNotValid, ForbiddenRequest } from "../errors";
import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import * as http from "http";
import { validateRequestData, WithCookies } from "./middleware";
import { logging } from "@skywind-group/sw-utils";
import { validateCORS } from "./corsSupport";
import { getITGGameController } from "../services/itgGameController";
import config from "../config";
import { processIP } from "../utils/ip";
import * as requestIp from "request-ip";

const log = logging.logger("sw-slot-engine:game-api");

type GSRequest = BaseRequest & RequestContext & RequestReferrer;

const decorateRequest = (request: GSRequest, req: FastifyRequest<http.IncomingMessage> & WithCookies): void => {
    request.userAgent = getUserAgent(req.headers["user-agent"]);
    request.ip = processIP(requestIp.getClientIp(req));
    request.screenSize = req.body.screenSize;
    request.referrer = req.cookies[SITE_COOKIE];
};

export default function(router: FastifyInstance, options, done) {
    router.addHook("preHandler", validateRequestData);

    // TODO - add middleware to get user info from access token
    router.post("/",
        async (req: FastifyRequest<http.IncomingMessage> & WithCookies, reply: FastifyReply<http.ServerResponse>) => {
            if (validateCORS(req.req, reply.res)) {
                if (!req.body) {
                    log.error("Error /casino/game2 - body is required");
                    return Promise.reject(new BodyNotValid());
                }
                const controller = getSyncGameController();
                const request: GSRequest = req.body;
                decorateRequest(request, req);
                const response = await controller.process(request);
                reply.send(response);
            } else {
                log.error("Error /casino/game2 - forbidden origin");
                return Promise.reject(new ForbiddenRequest());
            }
        });

    if (config.itgApiEnabled) {
        router.post("/itg",
            async (req: FastifyRequest<http.IncomingMessage> & WithCookies,
                   reply: FastifyReply<http.ServerResponse>) => {
                const controller = getITGGameController();
                const request: GSRequest = req.body;
                const response = await controller.process(request);
                reply.send(response);
            });
        log.info("ITG API is enabled");
    }

    router.options("*", (req: FastifyRequest<http.IncomingMessage>, reply: FastifyReply<http.ServerResponse>) => {
        validateCORS(req.req, reply.res);
        reply.code(204).send();
    });

    done();
}
