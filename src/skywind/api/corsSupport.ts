import config from "../config";
import { ExtraHeaders } from "../utils/common";
import * as http from "http";
import type { CorsOptions } from "cors";

const whitelistedOrigins: string[] = config.corsWhitelist.game !== undefined ?
                                     config.corsWhitelist.game.split(",").map(item => item.trim()) : undefined;
const DEFAULT_MAX_AGE_OPTIONS = 1728000;
const DEFAULT_ALLOWED_CREDENTIALS = config.accessControlAllowCredentials;
const DEFAULT_ALLOWED_METHODS = ["GET", "POST", "OPTIONS"];
const DEFAULT_ALLOWED_HEADERS = "DNT,X-ACCESS-TOKEN,Keep-Alive,User-Agent,X-Requested-With," +
    "If-Modified-Since,Cache-Control,Content-Type,Check-Sum,Cookie," +
    "x-forwarded-for,Access-Control-Allow-Origin," +
    `${ExtraHeaders.X_SW_PLAYER},${ExtraHeaders.X_SW_GAME}`;
function setHeaders(res: http.ServerResponse) {
    res.setHeader("Access-Control-Allow-Credentials", String(DEFAULT_ALLOWED_CREDENTIALS));
    res.setHeader("Access-Control-Allow-Methods", DEFAULT_ALLOWED_METHODS);
    res.setHeader("Access-Control-Allow-Headers", DEFAULT_ALLOWED_HEADERS);
    res.setHeader("Access-Control-Max-Age", DEFAULT_MAX_AGE_OPTIONS);
}

function isAllowedOrigin(req: http.IncomingMessage) {
    if (whitelistedOrigins === undefined || whitelistedOrigins[0] === "*") {
        return true;
    }
    for (const origin of whitelistedOrigins) {
        if (origin === req.headers.origin) {
            return true;
        }
    }
    return false;
}

export function validateCORS(req: http.IncomingMessage, res: http.ServerResponse): boolean {
    if (isAllowedOrigin(req)) {
        setHeaders(res);
        if (req.headers.origin) {
            res.setHeader("Access-Control-Allow-Origin", req.headers.origin);
        }

        return true;
    }

    return false;
}

export function corsOptionsDelegate(req: http.IncomingMessage,
                                    callback: (err: Error | null, options?: CorsOptions) => void): void {
    let corsOptions: CorsOptions = {};
    if (isAllowedOrigin(req)) {
        corsOptions = {
            methods: DEFAULT_ALLOWED_METHODS,
            allowedHeaders: DEFAULT_ALLOWED_HEADERS,
            credentials: DEFAULT_ALLOWED_CREDENTIALS,
            maxAge: DEFAULT_MAX_AGE_OPTIONS
        };
    }
    callback(null, corsOptions);
}
