import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import * as http from "http";
import { WithCookies } from "./middleware";
import { getITGGameController } from "../services/itgGameController";
import { BaseRequest } from "@skywind-group/sw-game-core";
import { RequestContext, RequestReferrer } from "../services/context/gamecontext";
type GSRequest = BaseRequest & RequestContext & RequestReferrer;

export default function(router: FastifyInstance, options, done) {
    router.post("/itg",
        async (req: FastifyRequest<http.IncomingMessage> & WithCookies,
               reply: FastifyReply<http.ServerResponse>) => {
            const controller = getITGGameController();
            const request: GSRequest = req.body;
            const response = await controller.process(request);
            reply.send(response);
        });

    done();
}
