import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import * as http from "http";
import { getGameServerVersion } from "../services/gameServerVersion";

export default function(router: FastifyInstance, options, done) {
    router.get("/",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            res.send(getGameServerVersion());
        });

    done();
}
