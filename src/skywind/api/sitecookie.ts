import { SITE_COOKIE } from "../utils/common";
import { generateSiteRefToken } from "../services/tokens";
import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import * as http from "http";
import { URL } from "url";
import { SetCookies, WithCookies } from "./middleware";

// Strip off query part from string
function stripOffQuery(entry: string): string {
    const parsedUrl = new URL(entry);
    return parsedUrl.protocol + "//" + parsedUrl.hostname;
}

function getFirstLevelDomainFromHost(host: string): string {
    const urlParts = host.split(".");
    if (urlParts.length > 1) {
        return urlParts[urlParts.length - 2] + "." + urlParts[urlParts.length - 1];
    }
    return host;
}

export default function(router: FastifyInstance, options, done) {

    router.get("/", async (req: FastifyRequest<http.IncomingMessage>&WithCookies,
                           res: FastifyReply<http.ServerResponse>&SetCookies) => {
        const referer = req.headers["Referer"];
        if (referer !== undefined) {
            const ecryptedReferer = await generateSiteRefToken({siteReferrer: stripOffQuery(referer)});
            res.header("Access-Control-Expose-Headers", "Cookie");
            res.setCookie(SITE_COOKIE, ecryptedReferer, {
                httpOnly: true,
                domain: "." + getFirstLevelDomainFromHost(req.headers.host)
            });
        }
        res.code(204).send();
    });

    done();
}
