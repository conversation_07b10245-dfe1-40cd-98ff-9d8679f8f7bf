import { initMerchantGame, PlayerGameURLInfo } from "../services/playerGameInfo";
import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import * as http from "http";
import { logging } from "@skywind-group/sw-utils";
import { validateCORS } from "./corsSupport";

const appendQuery = require("append-query");

const log = logging.logger("sw-slot-engine:merchant-api");

export default function(router: FastifyInstance, options, done) {

    router.get("/:merchantType/game",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            validateCORS(req.req, res.res);
            const request: any = req.query;
            request.ip = req.query.ip;
            const urlInfo: PlayerGameURLInfo = await initMerchantGame(req.params.merchantType, request);

            const redirectUrl = appendQuery(urlInfo.url, {startGameToken: urlInfo.token},
                {encodeComponents: true, removeNull: true});

            if (req.query.notRedirect) {
                res.send({ url: redirectUrl });
            } else {
                res.redirect(302, redirectUrl);
            }

        });
    done();
}
