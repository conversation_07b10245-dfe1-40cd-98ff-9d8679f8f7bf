import { GameContextID } from "../services/contextIds";
import { GameFlowContext } from "../services/context/gamecontext";
import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import * as http from "http";
import * as Errors from "../errors";
import { GameContextNotExists } from "../errors";
import { getGameContextDecoder } from "../services/encoder/contextEncoding";
import { GameFlowContextImpl } from "../services/context/gameContextImpl";
import { getGameFlowContextManager } from "../services/contextmanager/contextManagerImpl";
import { logging } from "@skywind-group/sw-utils";
import { verifyInternalToken } from "../services/tokens";

const log = logging.logger("sw-slot-engine:game-context-api");

export interface GetGameContextRequest {
    id: string;
    type: ContextTypes;
}

export default function(router: FastifyInstance, options, done) {
    router.get("/context",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            log.info("Get game context", req.params.id);
            const data = await verifyInternalToken<GetGameContextRequest>(req.query.token);
            const gameContextID = GameContextID.createFromString(data.id);
            const context = await findContextByType(gameContextID, data.type);
            res.send(context);
        });

    done();
}

enum ContextTypes {
    offline = "offline",
    online = "online"
}

async function findContextByType(gameContextID: GameContextID, type: string): Promise<GameFlowContext> {

    switch (type) {
        case ContextTypes.online:
            return findContextInOnlineStorage(gameContextID);
        case ContextTypes.offline:
            return findContextInOfflineStorage(gameContextID);
        default:
            const context = await findContextInOnlineStorage(gameContextID, true);
            if (context) {
                return context;
            }
            return findContextInOfflineStorage(gameContextID);
    }
}

async function findContextInOnlineStorage(gameContextID: GameContextID,
                                          ignoreNotFoundError: boolean = false): Promise<GameFlowContext> {
    const result = await getGameFlowContextManager().findGameContextById(gameContextID);
    if (!result && !ignoreNotFoundError) {
        return Promise.reject(new GameContextNotExists());
    }

    return result;
}

async function findContextInOfflineStorage(gameContextID: GameContextID) {
    const contextData = await getGameFlowContextManager().findOfflineGameContext(gameContextID);
    if (!contextData) {
        return Promise.reject(new Errors.GameContextNotExists());
    }

    const context = new GameFlowContextImpl(gameContextID);
    await getGameContextDecoder().decode(context, contextData.data);

    return context;
}
