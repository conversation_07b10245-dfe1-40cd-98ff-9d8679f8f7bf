import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import * as http from "http";
import BrokenPayments from "../services/cleanup/brokenPaymentsStat";

export default function(router: FastifyInstance, options, done) {
    router.get("/",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            const brokenPaymentsStat = await BrokenPayments.get();
            res.send(await brokenPaymentsStat.getData());
        });
    router.delete("/",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            const brokenPaymentsStat = await BrokenPayments.get();
            brokenPaymentsStat.clear();
            res.code(201);
        });

    done();
}
