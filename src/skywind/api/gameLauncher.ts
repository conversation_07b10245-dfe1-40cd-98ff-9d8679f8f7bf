import { FastifyInstance, FastifyReply, FastifyRequest, DefaultParams } from "fastify";
import * as http from "http";
import { RequestReferrer } from "../services/context/gamecontext";
import { WithCookies } from "./middleware";
import {
    getGameLauncherService,
    WithLaunchUrl,
} from "../services/gameLauncher";
import { logging, measures } from "@skywind-group/sw-utils";
import measureProvider = measures.measureProvider;

const log = logging.logger("sw-slot-engine:game-launcher");

export interface ErrorInfo {
    message: string;
    status: number;
    code: number;
    traceId: string;
}

export default function(router: FastifyInstance, options, done) {
    router.get("/launch", launchGame);
    done();
}

async function launchGame(req: FastifyRequest<http.IncomingMessage> & WithCookies,
                          reply: FastifyReply<http.ServerResponse>) {

    const data: RequestReferrer & WithLaunchUrl = {
        referrer: req.headers.referer,
        launcherToken: req.query.token
    };

    let gameUrl: string;
    try {
        gameUrl = await getGameLauncherService(req.query).launch(data);
    } catch (error) {
        log.error(error, "Received error");

        return reply
            .status(error.responseStatus)
            .type("application/json")
            .send({
                message: error.message,
                status: error.responseStatus,
                code: error.code,
                traceId: measureProvider.getTraceID()
            });
    }

    reply.redirect(gameUrl);
}
