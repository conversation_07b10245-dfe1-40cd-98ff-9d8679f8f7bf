import { logging } from "@skywind-group/sw-utils";
import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import * as http from "http";
import { GetGameContextsRequest } from "../services/playerGameHistory";
import { verifyInternalToken } from "../services/tokens";
import { getUnfinishedGameHistoryService, UnfinishedHistoryRequest } from "../services/playerUnfinishedGameHistory";

const log = logging.logger("sw-slot-engine:game-activehistory");

export default function(router: FastifyInstance, options, done) {
    router.get("/rounds",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            const request = await verifyInternalToken<UnfinishedHistoryRequest>(req.query.token);
            log.debug("Get active game history %j", request);
            const response = await getUnfinishedGameHistoryService().getPlayerUnfinishedHistory(request);
            res.send(response);
        });

    router.get("/game-contexts",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            const request = await verifyInternalToken<GetGameContextsRequest>(req.query.token);
            log.debug("Get game contexts %j", request);
            const response = await getUnfinishedGameHistoryService().getGameContexts(request);
            res.send(response);
        });

    done();
}
