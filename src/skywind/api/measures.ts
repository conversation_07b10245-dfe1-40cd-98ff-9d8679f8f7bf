import { measures } from "@skywind-group/sw-utils";
import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import * as http from "http";

export default function(router: FastifyInstance, options, done) {

    router.get("/measures/:name",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            const measureName: string = req.params.name;
            if (measureName === "all") {
                res.send(await measures.measureProvider.getMeasures());
            } else {
                const measure = await measures.getMeasure(measureName);
                if (measure) {
                    res.send(measure);
                } else {
                    res.code(404).send();
                }
            }
    });

    done();
}
