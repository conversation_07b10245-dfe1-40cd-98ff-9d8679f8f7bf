import { logging } from "@skywind-group/sw-utils";
import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import * as http from "http";
import GameRecoveryService, {
    FinalizeRequestData,
    RecoveryRequestData
} from "../services/gamerecovery";
import { verifyInternalToken } from "../services/tokens";
import { RoundStatistics } from "../services/context/gamecontext";
import { GameFinalizationType } from "@skywind-group/sw-wallet-adapter-core";

const log = logging.logger("sw-slot-engine:game-recovery-api");

interface RecoveryRequest {
    token: string;
}

export default function(router: FastifyInstance, options, done) {
    router.post("/forcefinish",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            const recoveryRequest = req.body as RecoveryRequest;
            const recoveryData = await verifyInternalToken<RecoveryRequestData>(recoveryRequest.token);
            log.info(recoveryData, "Force finish");
            const response = await GameRecoveryService.forceFinish(recoveryData);
            res.send(response);
        });

    router.post("/revert",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            const recoveryRequest = req.body as RecoveryRequest;
            const recoveryData = await verifyInternalToken<RecoveryRequestData>(recoveryRequest.token);
            log.info(recoveryData, "Revert");
            const response = await GameRecoveryService.revert(recoveryData);
            res.send(response);
        });

    router.post("/retry",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            const recoveryRequest = req.body as RecoveryRequest;
            const recoveryData = await verifyInternalToken<RecoveryRequestData>(recoveryRequest.token);
            log.info(recoveryData, "Retry");
            const response = await GameRecoveryService.retryPending(recoveryData);
            res.send(response);
        });

    router.post("/transfer-out",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            const recoveryRequest = req.body as RecoveryRequest;
            const recoveryData = await verifyInternalToken<RecoveryRequestData>(recoveryRequest.token);
            log.info(recoveryData, "Transfer out");
            const response = await GameRecoveryService.transferOut(recoveryData);
            res.send(response);
        });

    router.post("/start-finalize",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            const recoveryRequest = req.body as RecoveryRequest;
            const finalizeData = await verifyInternalToken<FinalizeRequestData>(recoveryRequest.token);
            log.info(finalizeData, "Start finalize");
            let response;
            if (finalizeData.gameFinalizationType === GameFinalizationType.ITG_FINALIZATION) {
                response = await GameRecoveryService.startItgFinalize(finalizeData);
            } else {
                response = await GameRecoveryService.startFinalize(finalizeData);
            }
            res.send(response);
        });

    router.post("/complete-finalize",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            const recoveryRequest = req.body as RecoveryRequest;
            const finalizeData = await verifyInternalToken<FinalizeRequestData>(recoveryRequest.token);
            log.info(finalizeData, "Complete finalize");
            await GameRecoveryService.completeFinalize(finalizeData);
            res.code(201).send();
        });

    router.post("/finalize",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            const recoveryRequest = req.body as RecoveryRequest;
            const finalizeData = await verifyInternalToken<FinalizeRequestData>(recoveryRequest.token);
            log.info(finalizeData, "Finalize");
            let roundStatistics: RoundStatistics;
            if (finalizeData.gameFinalizationType === GameFinalizationType.ITG_FINALIZATION) {
                roundStatistics = await GameRecoveryService.finalizeItgGame(finalizeData);
            } else {
                roundStatistics = await GameRecoveryService.finalize(finalizeData);
            }
            res.code(201).send(roundStatistics);
        });

    done();
}
