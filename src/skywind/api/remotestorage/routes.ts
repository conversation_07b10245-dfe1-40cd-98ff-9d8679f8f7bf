import context from "./context";
import initSettings from "./initSettings";
import version from "../version";
import { errorHandler, notFoundHandler } from "../middleware";
import { logging } from "@skywind-group/sw-utils";
import { FastifyInstance } from "fastify";
import gameServerVersion from "../gameServerVersion";

export function defineRoutes(app: FastifyInstance) {

    app.register(context, { prefix: "/v1" });
    app.register(initSettings, { prefix: "/v1" });
    app.register(version, { prefix: "/v1/version" });
    app.register(gameServerVersion, { prefix: "/gameserver/v1/version" });

    // error handling
    app.setErrorHandler(errorHandler(logging.logger("sw-context-service:api")));
    app.setNotFoundHandler(notFoundHandler);
}
