import { GameInitSettings, InitSettingsStorage, InitSettingsStorageImpl } from "../../services/gameInitSettings";
import { logging } from "@skywind-group/sw-utils";
import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import * as http from "http";

const log = logging.logger("game-init-settings:api");
const storage: InitSettingsStorage = new InitSettingsStorageImpl();

async function storeInitSettings(req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) {
    log.debug("store game init settings: %j", req.body);
    const settings: GameInitSettings = req.body;
    await storage.create(settings);
    res.code(201).send(settings);
}

async function loadInitSettings(req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) {
    log.debug("load game init settings: %j", req.body);
    const data: GameInitSettings[] = await storage.findAll(req.body);
    res.send(data);
}

export default function(router: FastifyInstance, options, done) {

    router.post("/initSettings", storeInitSettings);
    router.post("/initSettings/load", loadInitSettings);

    done();
}
