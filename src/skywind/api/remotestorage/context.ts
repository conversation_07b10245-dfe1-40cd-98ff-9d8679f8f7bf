import { lazy, logging } from "@skywind-group/sw-utils";
import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import * as http from "http";
import { getLocalOfflineCommandExecutor } from "../../services/offlinestorage/offlineCommandExecutor";

const log = logging.logger("sw-context-service:api");

async function executeCommands(req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) {
    log.debug("Execute commands %j", req.body);
    const contextData = await executor.get().execute(req.body);
    res.send(contextData);
}

const executor = lazy(() => getLocalOfflineCommandExecutor());

export default function(router: FastifyInstance, options, done) {
    router.post("/commands", executeCommands);

    done();
}
