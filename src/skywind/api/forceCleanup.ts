import { logging } from "@skywind-group/sw-utils";
import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import * as http from "http";
import { ForceCleanupJob } from "../services/cleanup/forceCleanupJob";
import JobStateTracker from "../services/cleanup/forceCleanupJobStateTracker";
import { verifyInternalToken } from "../services/tokens";
import ForceInterruptPlayerService from "../services/cleanup/forceInterruptPlayerService";
import ReactivateGameService, { ReactivateRequestData } from "../services/cleanup/reactivateGameService";

const log = logging.logger("sw-slot-engine:force-cleanup");

export interface ForceCleanupRequest {
    brandId: number;
}

export interface InterruptPlayerRequest {
    brandId: number;
    playerCode: string;
}

export default function(router: FastifyInstance, options, done) {
    router.delete("/",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            log.info("Force cleanup request %j", req.body);
            const data = await verifyInternalToken<ForceCleanupRequest>(req.body.token);
            await ForceCleanupJob.start(data.brandId);
            res.code(201).send();
        });

    router.get("/",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            log.info("Force cleanup status request %j", req.query);
            const data = await verifyInternalToken<ForceCleanupRequest>(req.query.token);
            const lastTs = await JobStateTracker.getLastActivityTs(data.brandId);
            res.send({ lastTs });
        });

    router.delete("/interrupt-player",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            log.info("Interrupt player request %j", req.body);
            const data = await verifyInternalToken<InterruptPlayerRequest>(req.body.token);
            await ForceInterruptPlayerService.interrupt(data.brandId, data.playerCode);
            res.code(201).send();
        });

    router.post("/reactivate-game",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            log.info("Reactivate game %j", req.body);
            const data = await verifyInternalToken<ReactivateRequestData>(req.body.token);
            await ReactivateGameService.reactivateGame(data.gameContextId);
            res.code(201).send();
        });

    done();
}
