import {
    ApiNotFoundError,
    InternalServerError,
    isSW<PERSON>rror, MalformedJsonError,
    MAPIMerchantError,
    isUnsupportedMediaTypeError,
    ValidationError
} from "../errors";
import { SWError } from "@skywind-group/sw-wallet-adapter-core";
import { FastifyReply, FastifyRequest } from "fastify";
import * as http from "http";
import { logging, measures } from "@skywind-group/sw-utils";
import { getUserAgent } from "../services/useragent";
import measureProvider = measures.measureProvider;
import { validateCORS } from "./corsSupport";
import { processIP } from "../utils/ip";
import * as validator from "validator";
import config from "../config";

export interface WithCookies {
    cookies: any;
}

export interface SetCookies {
    setCookie: (name, value, options) => void;
}

export interface SlowRequestTime {
    start: number;
}

export function slowRequestTime(req: FastifyRequest<http.IncomingMessage> & SlowRequestTime,
                                res: FastifyReply<http.ServerResponse>, next) {
    req.start = Date.now();
    next();
}

export function slowRequestLogger(threshold: number) {
    const log = logging.logger("sw-slot-engine:slow-request");

    return function(req: FastifyRequest<http.IncomingMessage> & SlowRequestTime,
                    res: FastifyReply<http.ServerResponse>, payload, next) {
        const ts = Date.now() - req.start;
        if (ts > threshold) {
            log.warn({
                takeMilliseconds: ts,
                headers: req.headers,
                url: req.req.url,
                query: req.query,
                body: req.body,
            }, "Slow request");
        }
        next();
    };
}

interface LogData {
    url: string;
    method: string;
    query: object;
    body: object;
    userAgent: object;
    ip: any;
}

function buildLogData(req: FastifyRequest<http.IncomingMessage>): LogData {
    const userAgent = getUserAgent(req?.headers?.["user-agent"]);
    const ip = processIP(req?.headers?.["x-forwarded-for"] || req?.req?.connection?.remoteAddress);
    return {
        url: req?.req?.url,
        method: req?.req?.method,
        query: req.query,
        body: req.body,
        userAgent,
        ip
    };
}

export function errorHandler(log) {
    return (err: Error, req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>): any => {

        if (err) {
            const requestData = buildLogData(req);
            const swsError = logAndGetSWError(err, log, requestData);

            if (res.sent) {
                log.warn(err, { request: requestData }, "Error headers sent");
                return;
            } else {
                res.code(swsError.responseStatus).send({
                    code: swsError.code,
                    message: swsError.message,
                    extraData: swsError.extraData,
                    providerDetails: swsError.providerDetails
                });
            }
        }
    };
}

export function logAndGetSWError<REQ>(err: Error, log, req: REQ): SWError & MAPIMerchantError {
    measureProvider.saveError(err);

    if (err) {
        let error;
        if (isSWError(err)) {
            log.warn(err, { request: req }, "SW Error");
            error = err;
        } else if ((err as any).validation || isUnsupportedMediaTypeError(err)) {
            log.warn(err, { request: req }, "SW Validation Error");
            error = new ValidationError(err.message);
        } else if (err instanceof SyntaxError) {
            const syntaxErrorReason = err.message ? err.message : "N/A";
            log.warn(err, `Malformed json - ${syntaxErrorReason}`);
            error = new MalformedJsonError(syntaxErrorReason);
        } else {
            log.error(err, { request: req }, "Internal server error");
            error = new InternalServerError();
        }

        return error;
    }
}

export function addHeaderCORS(req: http.IncomingMessage, res: http.ServerResponse, next) {
    if (res.headersSent) {
        return next();
    }
    /* TODO:
     "*" must be replaced with the origins white list, when implemented */
    res.setHeader("Access-Control-Allow-Origin", "*");
    if (req.method !== "OPTIONS") {
        return next();
    }
    validateCORS(req, res);
    res.writeHead(204);
    res.end();
    next();
}

export function addHeaderCacheControl(req: http.IncomingMessage, res: http.ServerResponse, next) {
    if (req.method === "GET") {
        res.setHeader("Cache-Control", "no-cache, max-age=0");
    }
    next();
}

export function notFoundHandler(req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) {
    const error = new ApiNotFoundError();
    res.code(error.responseStatus).send({
        code: error.code,
        message: error.message,
    });
}

const log = logging.logger("sw-slot-engine:game-api");

export function validateRequestData(
    req: FastifyRequest<http.IncomingMessage>,
    reply: FastifyReply<http.ServerResponse>,
    done: () => void
) {
    if (req.raw.method === "OPTIONS") {
        return done();
    }

    const { deviceId, screenSize, deviceData } = req.body;

    try {
        if (deviceId) {
            if (!config.acceptedDeviceIds.includes(deviceId)) {
                log.warn(`Received invalid deviceId: ${deviceId}`);
                return Promise.reject(
                    new ValidationError(`Invalid deviceId. Acceptable values: ${config.acceptedDeviceIds.join()}`)
                );
            }
        }

        if (screenSize && (typeof screenSize !== "string" || !/^\d{2,4}x\d{2,4}$/.test(screenSize))) {
            log.info(`Received invalid screenSize: ${screenSize}`);
            delete req.body.screenSize;
        }

        if (deviceData?.browser?.name && (typeof deviceData.browser.name !== "string" ||
            !validator.isAlphanumeric(deviceData.browser.name.replace(/\s+|-/g, "")))) {
            log.info(`Received invalid browser name: ${deviceData.browser.name}`);
            delete req.body.deviceData.browser.name;
        }

        if (deviceData?.browser?.version && (typeof deviceData.browser.version !== "string" ||
            !validator.isNumeric(deviceData.browser.version.replace(/\./g, "")))) {
            log.info(`Received invalid browser version: ${deviceData.browser.version}`);
            delete req.body.deviceData.browser.version;
        }

        if (deviceData?.operatingSystem?.name && (typeof deviceData.operatingSystem.name !== "string" ||
            !validator.isAlphanumeric(deviceData.operatingSystem.name.replace(/\s+|\.|-/g, "")))) {
            log.info(`Received invalid operatingSystem name: ${deviceData.operatingSystem.name }`);
            delete req.body.deviceData.operatingSystem.name;
        }

        if (deviceData?.operatingSystem?.version && (typeof deviceData.operatingSystem.version !== "string" ||
            !validator.isAlphanumeric(deviceData.operatingSystem.version.replace(/\s+|\.|-/g, "")))) {
            log.info(`Received invalid operatingSystem version: ${deviceData.operatingSystem.version }`);
            delete req.body.deviceData.operatingSystem.version;
        }

        if (deviceData?.operatingSystem?.versionName && (typeof deviceData.operatingSystem.versionName !== "string" ||
            !validator.isAlphanumeric(deviceData.operatingSystem.versionName.replace(/\s+|\.|-/g, "")))) {
            log.info(`Received invalid operatingSystem versionName: ${deviceData.operatingSystem.versionName }`);
            delete req.body.deviceData.operatingSystem.versionName;
        }

        done();
    } catch (error) {
        reply.status(400).send({ code: error.code, message: error.message });
    }
}

export function allowHTTPMethods(request: FastifyRequest,
                                 reply: FastifyReply<any>,
                                 done: () => void) {
    const allowedMethods = new Set(config.allowedHTTPMethods);
    const method = request.req.method.toUpperCase();
    reply.header("Access-Control-Allow-Methods", config.allowedHTTPMethods.join(","));
    if (!allowedMethods.has(method)) {
        reply.status(405);
        reply.res.end();
    }
    done();
}
