import { FastifyInstance } from "fastify";
import * as fastify from "./fastify";
import config from "./config";
import { defineRoutes } from "./api/routes";

let app: FastifyInstance;
export function getApplication(): FastifyInstance {
    if (!app) {
        app = createApplication();
    }
    return app;
}

export function createApplication(): FastifyInstance {
    const application = fastify.create(config.addMetricsEndpointToGameEngine);
    defineRoutes(application);
    return application;
}
