import * as fs from "fs";
import * as program from "commander";
import { ForceCleanupJob } from "./services/cleanup/forceCleanupJob";

const version = fs.readFileSync(__dirname + "/version", "utf8");

export async function forceCleanup(): Promise<void> {
    const cmd = program
        .version(version, "-v, --version")
        .usage("-m 10")
        .option("-m, ---merchant <merchant>", "merchant id", (v) => parseInt(v, 10))
        .parse(process.argv);

    if (!cmd.Merchant || !cmd.batch) {
        cmd.outputHelp();
    } else {
        const job = new ForceCleanupJob(cmd.Merchant);
        await job.execute();
    }
    process.exit(0);
}
