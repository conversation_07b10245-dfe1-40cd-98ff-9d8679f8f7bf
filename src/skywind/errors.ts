import { ExtraData, SWError } from "@skywind-group/sw-wallet-adapter-core";
import { ErrorSpecialFlag } from "@skywind-group/sw-wallet-adapter-core";

export function isBNSInsufficientBalance(swError: SWError): boolean {
    return swError.responseStatus === 400 && swError.code === 691;
}

export interface MAPIMerchantError {
    extraData?: any;
    providerDetails?: object;
}

export abstract class RequireRetransmitError extends SWError {
}

export class GameContextNotExists extends SWError {
    constructor() {
        super(404, 10, "Game context does not exist");
    }
}

export class ConcurrentAccessToGameSession extends SWError {
    constructor() {
        super(400, 11, "Concurrent access to game session");
    }
}

export class NoPendingModifications extends SWError {
    constructor() {
        super(404, 12, "No pending modifications");
    }
}

export class TransactionDoesntMatch extends SWError {
    constructor() {
        super(400, 13, "Transaction doesn't match");
    }
}

export class SessionTokenError extends SWError {
    constructor() {
        super(401, 14, "Session token error");
    }
}

export class SessionExpiredError extends SWError {
    constructor() {
        super(400, 15, "Session token expired");
    }
}

export class GameHistoryDetailsNotFound extends SWError {
    constructor() {
        super(404, 16, "Game history details not found");
    }
}

export class SpinStakeIsNegativeError extends SWError {
    constructor() {
        super(400, 20, "Spin stake is negative");
    }
}

export class SpinStakeIncorrect extends SWError {
    constructor() {
        super(400, 21, "Spin total stake incorrect");
    }
}

export class SpinCoinIncorrect extends SWError {
    constructor() {
        super(400, 22, "Spin coin incorrect");
    }
}

export class SpinLinesIncorrect extends SWError {
    constructor() {
        super(400, 23, "Spin lines incorrect");
    }
}

export class SpinBetIncorrect extends SWError {
    constructor() {
        super(400, 24, "Spin bet incorrect");
    }
}

export class LimitsNotFound extends SWError {
    constructor() {
        super(404, 30, "Limits not found");
    }
}

export class LimitsNotValid extends SWError {
    constructor() {
        super(400, 31, "Limits are not valid");
    }
}

export class BodyNotValid extends SWError {
    constructor() {
        super(400, 32, "Body is required");
    }
}

export class AmountIsNegativeError extends SWError {
    constructor() {
        super(400, 90, "Amount is negative");
    }
}

export class UnknownRequest extends SWError {
    constructor() {
        super(400, 100, "Unknown request");
    }
}

export class ForbiddenRequest extends SWError {
    constructor() {
        super(403, 100, "Forbidden request");
    }
}

export class ForbiddenToForceFinishContextError extends SWError {
    constructor(message = "Forbidden request") {
        super(403, 101, message);
    }
}

export class InsufficientPlayerBalanceError extends SWError {
    constructor() {
        super(400, 200, "Not enough money to make bet");
    }
}

class MAPIMerchantErrorImpl extends SWError implements MAPIMerchantError {
    public extraData: any;
    public providerDetails: object;

    constructor(status: number, code: number, message: string, err?: MAPIMerchantError) {
        super(status, code, message);
        this.extraData = err && err.extraData || undefined;
        this.providerDetails = err && err.providerDetails || undefined;
    }
}

export class ManagementAPIError extends MAPIMerchantErrorImpl {
    constructor(statusCode?: number, err?: SWError & MAPIMerchantError) {
        const status = statusCode || 400;
        const code = err && err.code || 300;
        const message = err && err.message || "Management API respond with error";
        super(status, code, message, err);
    }
}

export class ManagementAPITransientError extends MAPIMerchantErrorImpl {
    constructor(statusCode?: number, err?: SWError & MAPIMerchantError) {
        const status = statusCode || 500;
        const code = err && err.code || 301;
        const message = err && err.message || "Management API transient error";
        super(status, code, message, err);
    }
}

export class ManagementAPIBrokenIntegration extends MAPIMerchantErrorImpl {
    constructor(statusCode?: number, err?: SWError & MAPIMerchantError) {
        const status = statusCode || 501;
        const code = err && err.code || 301;
        const message = err && err.message || "Management API transient error";
        super(status, code, message, err);
    }
}

export class MerchantGameInitError extends SWError {
    constructor(msg: string) {
        super(400, 400, msg);
    }
}

export class InternalServerError extends SWError {
    constructor() {
        super(500, 1, "Internal server error");
    }
}

export class ApiNotFoundError extends SWError {
    constructor() {
        super(404, 2, "API not found");
    }
}

export class NeedToRestartTheGame extends SWError {
    constructor() {
        super(500, 201, "Need to restart the game");
    }
}

export class GameNotFoundError extends SWError {
    constructor() {
        super(404, 202, "Game not found");
    }
}

export class GameModuleError extends SWError {
    constructor() {
        super(400, 203, "Game module is not correct");
    }
}

export class GameModuleNotLoadedError extends SWError {
    constructor(moduleName: string) {
        super(400, 204, `Game module is not loaded ${moduleName}`);
    }
}

export class InvalidWalletOperation extends SWError {
    constructor() {
        super(400, 300, "Invalid wallet operation");
    }
}

export class TransferNotEnabled extends SWError {
    constructor() {
        super(400, 301, "Balance transfer is not enabled");
    }
}

export class InvalidBalanceTransfer extends SWError {
    constructor() {
        super(400, 302, "Invalid balance transfer: amount is required");
    }
}

export class BrokenBalanceTransfer extends SWError {
    constructor(public reason: Error) {
        super(500, 303, "Broken balance transfer: " + reason.message);
    }
}

export class FunGameSessionExpired extends SWError {
    constructor() {
        super(400, 304, "Fun mode session expired");
    }
}

export class SessionTokenIsMissingError extends SWError {
    constructor() {
        super(401, 305, "Session token is missing");
    }
}

export class SessionTokenIsInvalidError extends SWError {
    constructor() {
        super(401, 306, "Session token is invalid");
    }
}

export class JPNBadRequestError extends SWError {
    constructor(statusCode?: number, err?: SWError) {
        const status = statusCode || 400;
        const code = err && err.code || 500;
        const message = err && err.message || "JPN bad request error";
        super(status, code, message);
    }
}

export class JPNInternalServerError extends SWError {
    constructor(statusCode?: number, err?: SWError) {
        const status = statusCode || 500;
        const code = err && err.code || 501;
        const message = err && err.message || "JPN internal server error";
        super(status, code, message);
    }
}

export class JackpotNotFound extends SWError {
    constructor() {
        super(404, 503, "Jackpot not found");
    }
}

export class JackpotStatusNotValid extends SWError {
    constructor() {
        super(400, 504, "Jackpot status not valid");
        this.setSpecialFlag(ErrorSpecialFlag.HACK_ATTEMPT_ERROR);
    }
}

export class InvalidJackpotOperation extends SWError {
    constructor() {
        super(400, 505, "Invalid jackpot operation");
    }
}

export class GameFlowIllegalInvocationException extends SWError {
    constructor(desc: string = "Invalid game flow invocation") {
        super(400, 506, desc);
    }
}

export class GameContextStateIsBroken extends SWError {
    constructor() {
        super(400, 507, "Game context flow is broken");
    }
}

export class DuplicationOfInitRequestError extends SWError {
    constructor() {
        super(400, 508, "Game try to init game more than 1 time on the same socket");
    }
}

export class GameNotMatch extends SWError {
    constructor() {
        super(400, 510, "Game doesn't match");
    }
}

export class GameSessionClosed extends SWError {
    constructor() {
        super(400, 511, "Game session is closed");
    }
}

export class ValidationError extends SWError {
    constructor(msg) {
        super(400, 512, msg);
    }
}

export class InvalidStartGameToken extends SWError {
    constructor() {
        super(400, 513, "Invalid startGameToken");
    }
}

export class GeneratorBoundaryError extends SWError {
    constructor(currentId, min, max) {
        super(500, 514, `Generator id reached boundary ${currentId.toString()}: ${min.toString()} - ${max.toString()}`);
    }
}

export class EnvIdChangedError extends SWError {
    constructor() {
        super(400, 515, "Environment id has changed");
    }
}

export class RemoteStorageServiceError extends SWError {
    constructor(statusCode?: number, err?: SWError) {
        const status = statusCode || 400;
        const code = err && err.code || 516;
        const message = err && err.message || "Game remote service error";
        super(status, code, message);
    }
}

export class RemoteStorageServiceTranssientError extends SWError {
    constructor(statusCode?: number, err?: SWError) {
        const status = statusCode || 500;
        const code = err && err.code || 517;
        const message = err && err.message || "Game remote transient service error";
        super(status, code, message);
    }
}

export class InitKafkaProducerTimeoutError extends SWError {
    constructor() {
        super(500, 518, "Error init kafka producer");
    }
}

export class RedeemBNSError extends SWError {
    constructor() {
        super(400, 519, "No bonus coins. Can't redeem");
    }
}

export class ForbidToRecoverGameWithJPError extends SWError {
    constructor() {
        super(500, 520, "Forbid to recover game with jackpot");
    }
}

export class ManualRedeemBNSError extends SWError {
    constructor() {
        super(400, 521,
            "Please finish your current game round and any bonus rounds first before redeeming your bonus coins.");
    }
}

export class GameError extends SWError {
    private readonly causeError: any;

    constructor(err) {
        if (err.code && err.message) {
            super(400, err.code, err.message);
        } else {
            super(500, 521, err.message || "Game error");
        }
        this.causeError = err;
        this.setSpecialFlag(ErrorSpecialFlag.GAME_MODULE_ERROR);
    }

    public cause() {
        return this.causeError;
    }
}

export class SiteRefTokenError extends SWError {
    constructor() {
        super(401, 522, "Site referrer token error");
    }
}

export class SiteRefTokenExpiredError extends SWError {
    constructor() {
        super(400, 523, "Site referrer token expired");
    }
}

export class InternalServerTokenError extends SWError {
    constructor() {
        super(401, 524, "Internal server token error");
    }
}

export class InternalServerTokenExpiredError extends SWError {
    constructor() {
        super(400, 525, "Internal server token expired");
    }
}

export abstract class ConnectionError extends RequireRetransmitError {
    constructor(code: number, cause: Error, message?: string) {
        super(500, code, message || cause.message);
    }
}

export class ManagementAPIConnectionError extends ConnectionError {
    constructor(cause: Error) {
        super(526, cause, "Management API connection error");
    }
}

export class JPNConnectionError extends ConnectionError {
    constructor(cause: Error) {
        super(527, cause);
    }
}

export class InternalEventIsNotSupported extends SWError {
    constructor() {
        super(400, 528, "Internal event is not supported!");
    }
}

export class InvalidContextCurrencyError extends SWError {
    constructor(extraData?: ExtraData) {
        super(400, 529, "Game context and auth token have different currency", undefined, extraData);
    }
}

export class RemoteStorageConnectionError extends ConnectionError {
    constructor(cause: Error) {
        super(530, cause);
    }
}

export class ForceCleanupJobForBrandExistError extends SWError {
    constructor() {
        super(400, 531, "Cleanup job already exists for brand");
    }
}

export class MalformedJsonError extends SWError {
    constructor(reason: string) {
        super(400, 532, `Malformed JSON : ${reason}`);
    }
}

export class RoundNotFoundError extends SWError {
    constructor(round: string) {
        super(404, 533, `Round with id ${round} not found`);
    }
}

export class ForceFlagIsRequiredForOperationError extends SWError {
    constructor(operation: string) {
        super(400, 534, `Operation requires force flag for ${operation}`);
    }
}

export class InsufficientBalanceToMakeTransferError extends SWError {
    constructor() {
        super(400, 535, "Insufficient player balance to make money transfer");
    }
}

export class GameReInitError extends SWError {
    constructor(message: string) {
        super(400, 536, `Cannot reinit the game: ${message}`);
    }
}

export class UnsupportedPlaymodeForRevert extends SWError {
    constructor(playmode: string) {
        super(400, 537, `Unsupported playmode for revert operation - ${playmode}`);
    }
}

export class RevertForBrokenPaymentError extends SWError {
    constructor() {
        super(403, 538, "Revert operation for round with broken payment is forbidden. Please use force finish.");
    }
}

export class WrongSocketHeadersError extends SWError {
    constructor() {
        super(403, 539, "Wrong headers, cannot balance request properly");
    }
}

export class LogoutForbiddenError extends SWError {
    constructor() {
        super(500, 540, "Can't logout if there is a broken payment");
    }
}

export class GameContextBrokenIntegration extends SWError {
    constructor() {
        super(500, 541, "Game is not available because of broken integration with merchant");
    }
}

export class GameFinalizedError extends SWError {
    constructor() {
        super(500, 542, "Game is under finalizing process");
    }
}

export class OperationRefundedError extends SWError {
    constructor(extraData?: any) {
        super(500, 543, "Operation is refunded by operator");
        this.extraData = extraData;
    }
}

export class CannotFinalizePaymentIsIncompleteError extends SWError {
    constructor() {
        super(500, 544, "Cannot finalize game. Payment is not completed");
    }
}

export class GameLaunchForbidden extends SWError {
    constructor() {
        super(403, 545, "This game can only be played in real money mode");
    }
}

export class JackpotTestModeError extends SWError {
    constructor() {
        super(403, 546, "Each jackpot should be in test mode");
    }
}

export class JackpotRealModeError extends SWError {
    constructor() {
        super(403, 547, "Each jackpot should be in real mode");
    }
}

export class JackpotDisabledError extends SWError {
    constructor() {
        super(400, 548, "Jackpot is disabled");
    }
}

export class CannotStartNewMerchantPlayerSessionError extends SWError {
    constructor() {
        super(400, 549, "Cannot start new session. Player has unfinished game");
    }
}

export class PartialTransferIsForbiddenError extends SWError {
    constructor() {
        super(400, 550, "It's forbidden to transfer-out partial amount");
    }
}

export class TransferDuringBonusFeatureIsForbiddenError extends SWError {
    constructor() {
        super(400, 551, "Can't transfer during bonus feature");
    }
}

export class TransactionIsInProgressError extends RequireRetransmitError {
    constructor(statusCode: number, err: SWError) {
        super(statusCode, err.code, err.message);
    }
}

export class NotImplementedError extends SWError {
    constructor(message = "Not implemented") {
        super(501, 552, message);
    }
}

export class ITGFinalizationError extends SWError {
    constructor(message = "ITG Finalization error") {
        super(500, 553, message);
    }
}

export function isSWError(err): err is SWError & Error {
    return err.responseStatus && err.code && err.message;
}

export function isUnsupportedMediaTypeError(err: Error): boolean {
    return err.message.startsWith("Unsupported Media Type");
}

export function isCannotCompletePaymentError(swError: SWError): boolean {
    // 806 is cannot complete payment code;
    return swError.responseStatus === 500 && swError.code === 806;
}

export function isInterruptSocketError(swError: SWError): boolean {
    return swError.responseStatus === 500 && swError.code === 850;
}

export function isOperationForbiddenError(swError: SWError): boolean {
    return swError.responseStatus === 403 && swError.code === 206;
}
