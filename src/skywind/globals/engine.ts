import { GameExtensionModule, SomeGame, SomeStage } from "@skywind-group/sw-game-core";
import { MultiStageGame } from "../services/game/multiStageGame";
import { GameEngine } from "@skywind-group/sw-game-core";

export class EngineImpl implements GameEngine {
    public createMultiStageGame(game: SomeGame, stages: SomeStage[]): SomeGame {
        return new MultiStageGame(game as any, stages);
    }
}

const engine = new EngineImpl();

export function registerEngine() {
    global["GameEngine"] = engine;
}
