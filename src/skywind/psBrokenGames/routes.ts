import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import * as http from "http";
import { finalizeBrokenGameService } from "./psBrokenGamesService";

export default function(router: FastifyInstance, options, done) {
    router.post("/broken-rounds",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {

            const response = await finalizeBrokenGameService.get()
                .getBrokenGamesInfo(req.body.gameCodes, req.body.disableCache);
            res.send(response);
        });

    router.post("/schedule-broken-rounds",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {

            const response = await finalizeBrokenGameService.get()
                .scheduleBrokenGamesInfoStoring(req.body.gameCodes, req.body.disableCache);
            res.send(response);
        });

    router.post("/complete-broken-rounds",
        async (req: FastifyRequest<http.IncomingMessage>, res: FastifyReply<http.ServerResponse>) => {
            const response = await finalizeBrokenGameService.get()
                .completeRounds(req.body.gameContextIds);
            res.send(response);
        });

    done();
}
