import { GameContextID } from "../contextIds";
import { GameData, LogoutType } from "../auth";
import { GameSettings } from "../tokens";
import { JackpotContext } from "../jpn/jackpot";
import { PlayerContextImpl } from "../playercontext/playerContext";
import { CommandExecutor } from "../command";
import { GameSession } from "../gameSession";
import * as uuid from "uuid";
import {
    createForceFinishGameEvent,
    createGameHistoryEvent,
    createJackpotGameHistoryEvent,
    createRoundHistoryEvent,
    createSessionHistory,
    ExtendedGameHistory,
    GameEventHistory,
    RoundHistory
} from "../../history/history";
import { PlayMode } from "../playMode";
import {
    Balance,
    FinalizeGameOperation, PaymentOperation,
    SmResultExtraData, SmResultTransaction, SplitPaymentOperation,
    WalletOperation,
    WalletOperationType
} from "../wallet";
import {
    GameAnalytics,
    GameContextStatus,
    GameFlowContext,
    JackpotPendingModification,
    MerchantLogoutResult,
    PendingModification,
    RequestContext,
    RoundStatistics,
    SpecialState
} from "./gamecontext";
import {
    Analytics,
    BaseRequest,
    GameContext,
    GameContextPersistencePolicy,
    GameMode, GameWalletAction
} from "@skywind-group/sw-game-core";
import { measures, publicId } from "@skywind-group/sw-utils";
import { RecoveryType } from "../offlinestorage/offlineCommands";
import { GameContextOnlineCommands, StoreHistory, StoreOptions } from "../onlinestorage/gameContextCommands";
import { Currencies, Currency } from "@skywind-group/sw-currency-exchange";
import { JackpotPendingProcessResult } from "../jpn/jackpotFlow";
import { JackpotUtil } from "../jpn/jackpotUtil";
import config from "../../config";
import { AnalyticsData } from "../analytics/analyticsService";
import { getOrInit } from "../../utils/common";
import { calculateJPStatistic } from "./jpStatistics";
import { deepClone } from "../../utils/cloner";
import { ContextFinalizationTimeCalculator } from "../contextFinalizationTimeCalculator";
import { createSmResult } from "@skywind-group/sw-sm-result-builder";
import { findGameInitSettings } from "../gameInitSettings";
import measure = measures.measure;
import { HistoryItemResultsData, buildAdditionalData } from "@skywind-group/sw-round-details-report";

const executorField = Symbol("executor");
const commandsField = Symbol("commands");
const playerContextField = Symbol("playerContext");

/**
 * Internal structure with auxiliary fields to handle pending modification.
 */
interface GameContextPendingModification extends PendingModification {
    separateRoundId?: string;
}

/**
 * Game flow context implementation
 */
export class GameFlowContextImpl implements GameFlowContext {
    public gameData: GameData;
    public gameSerialNumber: number;
    public totalEventId: number;
    public settings: GameSettings;
    public roundEnded: boolean;
    public roundId: string;
    public gameContext: GameContext;
    public persistencePolicy: number = GameContextPersistencePolicy.NORMAL;
    public pendingModification: GameContextPendingModification;
    public jackpotPending?: JackpotPendingModification;
    public jpContext: JackpotContext;
    public round: RoundStatistics;
    public lastRound: RoundStatistics;
    public gameVersion: string;
    public requestContext: RequestContext;
    public createdAt: Date;
    public updatedAt: Date;
    public retryAttempts: number = undefined;
    public logoutResult: MerchantLogoutResult = undefined;
    public specialState: SpecialState = undefined;
    public logoutId: string = undefined;

    private [playerContextField]: PlayerContextImpl;
    private [executorField]: CommandExecutor;
    private [commandsField]: GameContextOnlineCommands;
    private expectFullUpdate?: boolean;

    constructor(public id: GameContextID,
                public lockExclusively: boolean = false,
                public readonly reactive: boolean = true) {
    }

    // tslint:disable-next-line:variable-name
    private _sessionId: GameSession;
    // tslint:disable-next-line:variable-name
    private _prev_sessionId: GameSession;
    public lastRequestId: number;
    public version: number;

    public get session(): GameSession {
        return this._sessionId;
    }

    public set session(session: GameSession) {
        if (this._prev_sessionId === undefined) {
            this._prev_sessionId = this._sessionId;
        }
        this._sessionId = session;
    }

    public get playerContext(): PlayerContextImpl {
        return this[playerContextField];
    }

    public set playerContext(playerContext: PlayerContextImpl) {
        this[playerContextField] = playerContext;
    }

    public get executor(): CommandExecutor {
        return this[executorField];
    }

    public set executor(executor: CommandExecutor) {
        this[executorField] = executor;
    }

    public get commands(): GameContextOnlineCommands {
        return this[commandsField];
    }

    public set commands(commands: GameContextOnlineCommands) {
        this[commandsField] = commands;
    }

    public get prevSession(): GameSession {
        return this._prev_sessionId;
    }

    public get keepOffline(): boolean {
        const result: any = (this.persistencePolicy && this.persistencePolicy !== GameContextPersistencePolicy.NORMAL)
            || this.broken;

        return result;
    }

    public get broken(): boolean {
        if (this.unfinished || this.brokenPayment ||
            this.requireLogout ||
            this.specialState === SpecialState.BROKEN_INTEGRATION || this.specialState === SpecialState.FINALIZING) {
            return true;
        } else {
            return false;
        }
    }

    public get brokenPayment(): boolean {
        if (this.pendingModification || this.jackpotPending) {
            return true;
        } else {
            return false;
        }
    }

    public get brokenJackpot(): boolean {
        if (this.jpContext && this.jpContext.jackpot) {
            return true;
        }
        const jackpotOperation = this.jackpotPending?.jackpotOperation?.type;
        return jackpotOperation && jackpotOperation !== "contribution";
    }

    public get requireLogout(): boolean {
        const type: LogoutType = this.gameData?.logoutOptions?.type;
        return (typeof this.logoutResult !== "string" && ((type === LogoutType.ALL) ||
                (type === LogoutType.UNFINISHED && !this.brokenPayment && this.unfinished))) ||
            this.logoutResult === MerchantLogoutResult.PENDING_LOGOUT;
    }

    public get requireTransferOut(): boolean {
        return this.gameData?.gameTokenData?.transferEnabled === true && this.roundEnded === false;
    }

    public get requireFinalizeGame(): boolean {
        return this.pendingModification?.walletOperation?.operation === "finalize-game";
    }

    public get unfinished(): boolean {
        return !this.roundEnded || (this.jpContext &&
            (JackpotUtil.isStartMiniGameResult(this.jpContext) ||
                JackpotUtil.isStartInstantJpMiniGameResult(this.jpContext)));
    }

    public markSaved() {
        this._prev_sessionId = undefined;
        this.lockExclusively = false;
    }

    public get gameCode(): string {
        return this.id.gameCode;
    }

    public get currencyForHistory(): string {
        return PlayMode.getCurrency(this.gameData.gameTokenData);
    }

    public get playMode(): GameMode {
        return this.gameData?.gameTokenData?.playmode;
    }

    public async updateRoundStatistic(historyItem: GameEventHistory,
                                      jpnResult?: JackpotPendingProcessResult,
                                      round: RoundStatistics = this.round,
                                      balance?: Balance) {

        if (historyItem) {
            if (!round) {
                round = this.round = {
                    totalBet: 0,
                    totalWin: 0,
                    totalEvents: 0,
                    balanceBefore: historyItem.balanceBefore,
                    balanceAfter: undefined,
                    totalJpContribution: 0,
                    totalJpWin: 0,
                    currentBet: 0,
                    betsCount: 0
                };
            }

            if (!round.startedAt) {
                round.startedAt = historyItem.ts;
            }

            if (historyItem.bet) {
                round.totalBet = this.safeAdd(historyItem.currency, round.totalBet, historyItem.bet);
                round.currentBet = historyItem.bet;
                if (!round.betsCount) {
                    round.betsCount = 1;
                } else {
                    ++round.betsCount;
                }
            }

            if (historyItem.win) {
                round.totalWin = this.safeAdd(historyItem.currency, round.totalWin, historyItem.win);
            }

            round.totalEvents++;

            if (round.balanceBefore === undefined) {
                round.balanceBefore = historyItem.balanceBefore;
            }

            if (historyItem.balanceAfter !== undefined) {
                round.balanceAfter = historyItem.balanceAfter;
            }

            if (historyItem.roundEnded) {
                round.finishedAt = historyItem.ts;
            } else if (this.settings?.roundExpireAt && balance) {
                round.lastSuccessfulPaymentDate = Date.now();
            }

            if (historyItem.totalJpContribution) {
                round.totalJpContribution = JackpotUtil.safeAddWithPrecision(this.settings.contributionPrecision,
                    +historyItem.totalJpContribution,
                    +round.totalJpContribution);
            }

            if (historyItem.totalJpWin) {
                const currency = this.getPlayerCurrency();

                round.totalJpWin = JackpotUtil.safeAddWithPrecision(currency.exponent,
                    +historyItem.totalJpWin,
                    +round.totalJpWin);
            }

            if (historyItem.debit) {
                round.debit = (round.debit || 0) + historyItem.debit;
            }

            if (historyItem.credit) {
                round.credit = (round.credit || 0) + historyItem.credit;
            }
            if (balance?.extraData?.extRoundId) {
                round.extRoundId = balance.extraData.extRoundId;
            }
            // extra data for Phantom
            historyItem.totalRoundBet = round.totalBet;
            historyItem.totalRoundWin = round.totalWin;
            const ip = this.gameData?.ip;
            if (ip) {
                historyItem.playerIp = ip;
            }

            if (this.settings?.smResultEnabled || config.smResult.enabled) {
                const initResponse = await this.getInitResponse();
                const smResult = this.createSmResult(initResponse, historyItem);
                if (smResult) {
                    this.updateRoundStatisticWithSmResult(round, smResult);
                    const smResultExtraData = this.createSmResultExtraData(this, initResponse, historyItem);
                    this.updateRoundStatisticWithSmResultExtraData(round, smResultExtraData);
                }
            }

            if (config.roundDetailedResult.enabled && historyItem.roundEnded
                && this.settings.roundDetailsEnabled) {
                const roundDetailData: HistoryItemResultsData
                    = this.createRoundDetailedResult(historyItem);
                this.updateRoundStatisticWithRoundDetails(round, roundDetailData);
            }

            if (historyItem.instantJackpot) {
                round.instantJackpot = historyItem.instantJackpot;
            }
        }
        if (round && jpnResult && jpnResult.jpnResult) {
            calculateJPStatistic(jpnResult.jpnResult, getOrInit(round, "jpStatistic", {}),
                jpnResult.jackpotContext || this.jpContext);
        }
    }

    public beginNewRound(newRoundId: string) {
        this.roundId = newRoundId;
        this.round = undefined;
    }

    public corrupt() {
        this.id = undefined;
    }

    public get corrupted() {
        return this.id === undefined;
    }

    public get expireAt() {
        return ContextFinalizationTimeCalculator.getExpiredAt(this);
    }

    public get status(): GameContextStatus {
        if (this.specialState === SpecialState.BROKEN_INTEGRATION) {
            return "brokenIntegration";
        } else if (this.requireTransferOut) {
            return "requireTransferOut";
        } else if (this.brokenPayment) {
            return "broken";
        } else if (this.requireLogout) {
            return "requireLogout";
        } else if (this.specialState === SpecialState.FINALIZING || this.requireFinalizeGame) {
            return "finalizing";
        } else {
            return "unfinished";
        }
    }

    @measure({ name: "GameFlowContext.update", isAsync: true, debugOnly: true })
    public update(gameContext?: GameContext): Promise<any> {
        this.updateGameContext(gameContext);
        return this.executor.execute([
            this.commands.checkVersion(this, true, true),
            this.commands.store(this, true, this.clearFullUpdate(StoreOptions.GAME_STATE), undefined, true),
            this.id && this.commands.trackActive(this.id, GameFlowContextImpl.getGameActivityTimeout(this.gameData)),
            this.playerContext && this.playerContext.commands.trackActive(this.playerContext.id,
                GameFlowContextImpl.getPlayerActivityTimeout(this.gameData))
        ]).catch(e => this.processError(e));
    }

    @measure({ name: "GameFlowContext.updateGameData", isAsync: true, debugOnly: true })
    public updateGameData(): Promise<any> {
        return this.executor.execute([
            this.commands.checkVersion(this, true, true),
            this.commands.store(this, true, this.clearFullUpdate(StoreOptions.INIT_INFO), undefined, true),
            this.id && this.commands.trackActive(this.id, GameFlowContextImpl.getGameActivityTimeout(this.gameData)),
            this.playerContext && this.playerContext.commands.trackActive(this.playerContext.id,
                GameFlowContextImpl.getPlayerActivityTimeout(this.gameData))
        ]).catch(e => this.processError(e));
    }

    @measure({ name: "GameFlowContext.commitPendingModification", isAsync: true, debugOnly: true })
    public async commitPendingModification(gameResultBalance?: Balance,
                                           jackpotResult?: JackpotPendingProcessResult,
                                           jackpotContext?: JackpotContext,
                                           recoveryType?: RecoveryType): Promise<StoreHistory> {
        const pendingModification = this.pendingModification;
        const useSeparateRound = pendingModification?.history?.separateRound;
        const gameAnalytics: GameAnalytics = pendingModification?.analytics;
        const operation: WalletOperationType = pendingModification?.walletOperation?.operation;

        let options: StoreOptions;
        let storeHistory: StoreHistory;
        const analytics = gameAnalytics && !config.analytics.sendOnline ?
                          AnalyticsData(this, gameAnalytics) :
                          undefined;

        if (operation === "finalize-game") {
            [options, storeHistory] = await this.doCommitFinalizeGame(gameResultBalance);
        } else if (useSeparateRound) {
            [options, storeHistory] = await this.doCommitInSeparateRound(gameResultBalance,
                jackpotResult,
                jackpotContext,
                recoveryType);
        } else {
            [options, storeHistory] = await this.doCommit(gameResultBalance,
                jackpotResult,
                jackpotContext,
                recoveryType);
        }

        if (analytics) {
            storeHistory.analytics = analytics;
        }

        if (recoveryType === "revert") {
            return this.executor.execute([
                this.commands.checkVersion(this, false, true),
                this.commands.remove(this, false, storeHistory, true),
                this.playerContext && this.playerContext.commands.removeGame(this.playerContext.id, this.id, false)
            ]).catch(e => this.processError(e)).then(() => storeHistory);
        } else if (this.reactive) {
            return this.executor.execute([
                this.commands.checkVersion(this, false, true),
                this.commands.store(this, false, this.clearFullUpdate(options), storeHistory),
                this.commands.trackActive(this.id, GameFlowContextImpl.getGameActivityTimeout(this.gameData)),
                this.playerContext && this.playerContext.commands.trackActive(this.playerContext.id,
                    GameFlowContextImpl.getPlayerActivityTimeout(this.gameData))
            ]).catch(e => this.processError(e)).then(() => storeHistory);
        } else {
            return this.executor
                .execute([
                    this.commands.checkVersion(this, false, true),
                    this.commands.store(this, false, this.clearFullUpdate(options), storeHistory),
                ])
                .catch(e => this.processError(e))
                .then(() => storeHistory);
        }
    }

    private async doCommit(gameResultBalance?: Balance,
                           jackpotResult?: JackpotPendingProcessResult,
                           jackpotContext?: JackpotContext,
                           recoveryType?: RecoveryType): Promise<[StoreOptions, StoreHistory]> {

        let options = StoreOptions.ROUND_INFO;

        let item: GameEventHistory;
        let jpItem: GameEventHistory;

        const pending = this.pendingModification;
        const jpPending = this.jackpotPending;
        const hasJpHistory = jpPending && (jpPending.history || jpPending.walletOperation);
        const hasGameHistory = this.pendingModification &&
            (this.pendingModification.history || this.pendingModification.walletOperation);
        const roundEnded = pending?.history?.roundEnded;
        if (pending) {
            if (roundEnded) {
                this.roundEnded = roundEnded;
            } else if (!(hasJpHistory || hasGameHistory)) {
                // if we have only analytics we should save previous state
                this.roundEnded = this.roundEnded || false;
            } else {
                this.roundEnded = false;
            }

            options |= StoreOptions.GAME_STATE | StoreOptions.PENDING_MODIFICATION;
            if (pending.history || pending.walletOperation) {
                const originContrib = jackpotResult?.jpnResult?.totalJpContribution;
                const jpContrib = JackpotUtil.normalizeAmountByPrecision(this.settings.contributionPrecision,
                    originContrib);

                item = createGameHistoryEvent(this, gameResultBalance, jpContrib, this.roundEnded && !hasJpHistory);
                await this.updateRoundStatistic(item, undefined, undefined, gameResultBalance);
                this.gameSerialNumber++;
                this.totalEventId++;
            }

            this.updateGameContext(pending.newState);
            this.pendingModification = undefined;
        }

        if (jpPending) {
            options |= StoreOptions.JACKPOT_PENDING;
            if (hasJpHistory) {
                jpItem = createJackpotGameHistoryEvent(this, jackpotResult, this.roundEnded);
                await this.updateRoundStatistic(jpItem, jackpotResult, undefined, gameResultBalance);
                this.gameSerialNumber++;
                this.totalEventId++;
            } else {
                await this.updateRoundStatistic(undefined, jackpotResult, undefined, gameResultBalance);
            }
            this.jackpotPending = undefined;
        }

        if (jackpotContext !== undefined) {
            this.jpContext = jackpotContext;
            options |= StoreOptions.JP_CONTEXT;
        }

        this.lastRound = this.round;

        let round: RoundHistory;
        // TODO smalkov pay attention on this.specialState !== SpecialState.FINALIZING it should be removed
        //  in case of we will need retry + lockContext
        if (roundEnded && this.specialState !== SpecialState.FINALIZING) {
            round = createRoundHistoryEvent(this, undefined, undefined, undefined, gameResultBalance);
            this.gameSerialNumber = 0;
            if (recoveryType) {
                round.recoveryType = recoveryType;
            }
            // create new round
            const newRoundId = await this.commands.generateRoundId();
            this.beginNewRound(newRoundId);
        }

        return [options, { round, item, jpItem }];
    }

    private async doCommitFinalizeGame(gameResultBalance?: Balance): Promise<[StoreOptions, StoreHistory]> {
        const recoveryType: RecoveryType =
            (this.pendingModification?.walletOperation as FinalizeGameOperation)?.recoveryType;
        this.roundEnded = true;
        const item = createForceFinishGameEvent(this, recoveryType, this.currencyForHistory, gameResultBalance);
        await this.updateRoundStatistic(item);
        const round = createRoundHistoryEvent(this, undefined, undefined, undefined, gameResultBalance);
        round.recoveryType = recoveryType;
        round.finishedAt = new Date();
        const newRoundId = await this.commands.generateRoundId();
        this.beginNewRound(newRoundId);
        this.updateGameContext(this.pendingModification.newState);
        this.pendingModification = undefined;
        this.jackpotPending = undefined;
        this.gameSerialNumber = 0;
        this.jpContext = undefined;
        this.specialState = null;
        this.logoutId = null;
        this.logoutResult = null;

        return [StoreOptions.ALL, { round, item }];
    }

    private async doCommitInSeparateRound(gameResultBalance?: Balance,
                                          jackpotResult?: JackpotPendingProcessResult,
                                          jackpotContext?: JackpotContext,
                                          recoveryType?: RecoveryType): Promise<[StoreOptions, StoreHistory]> {
        let options = StoreOptions.ROUND_INFO;

        let item: GameEventHistory;
        let jpItem: GameEventHistory;

        const pending = this.pendingModification;
        const jpPending = this.jackpotPending;
        const hasJpHistory = jpPending && (jpPending.history || jpPending.walletOperation);
        const separateRound: RoundStatistics = {
            totalBet: 0,
            totalWin: 0,
            totalEvents: 0,
            balanceBefore: undefined,
            balanceAfter: undefined,
            currentBet: 0
        };
        const newRoundId = this.pendingModification.separateRoundId;
        let eventId: number = 0;
        const currency = pending?.walletOperation?.currency || this.currencyForHistory;
        if (pending) {
            options |= StoreOptions.GAME_STATE | StoreOptions.PENDING_MODIFICATION;
            if (pending.history || pending.walletOperation) {

                const originContrib = jackpotResult?.jpnResult?.totalJpContribution;
                const jpContrib = JackpotUtil.normalizeAmountByPrecision(this.settings.contributionPrecision,
                    originContrib);

                item = createGameHistoryEvent(this, gameResultBalance, jpContrib, !hasJpHistory, newRoundId, eventId,
                    currency);
                eventId++;
                await this.updateRoundStatistic(item, undefined, separateRound, gameResultBalance);
            }

            this.updateGameContext(pending.newState);
            this.pendingModification = undefined;
        }

        if (jpPending) {
            options |= StoreOptions.JACKPOT_PENDING;
            if (hasJpHistory) {
                jpItem = createJackpotGameHistoryEvent(this, jackpotResult, true, newRoundId, eventId, currency);
                await this.updateRoundStatistic(jpItem, jackpotResult, separateRound, gameResultBalance);
            }
            this.jackpotPending = undefined;
        }

        if (jackpotContext !== undefined) {
            this.jpContext = jackpotContext;
            options |= StoreOptions.JP_CONTEXT;
        }

        const round: RoundHistory = createRoundHistoryEvent(this, separateRound, newRoundId,
            currency, gameResultBalance);
        if (recoveryType) {
            round.recoveryType = recoveryType;
        }

        return [options, { round, item, jpItem }];
    }

    @measure({ name: "GameFlowContext.updatePendingModification", isAsync: true, debugOnly: true })
    public async updatePendingModification(walletOperation: WalletOperation,
                                           newState: GameContext,
                                           request?: BaseRequest & RequestContext,
                                           history?: ExtendedGameHistory,
                                           jackpotPending?: JackpotPendingModification,
                                           analytics?: Analytics): Promise<any> {
        let options: StoreOptions = 0;
        let gameEventId = this.gameSerialNumber;
        let totalEventId = this.totalEventId;
        const jpWalletOperation = jackpotPending?.walletOperation;
        if (walletOperation || history || newState || analytics) {
            this.pendingModification = {
                walletOperation, history, newState, analytics: this.createAnalyticsData(analytics)
            };
            options |= StoreOptions.PENDING_MODIFICATION;

            if (history?.separateRound) {
                const separateRoundId = await this.commands.generateRoundId();
                this.pendingModification.separateRoundId = separateRoundId;

                if (walletOperation) {
                    walletOperation.roundId = separateRoundId;
                    walletOperation.roundPID = publicId.instance.encode(+separateRoundId);
                }

                if (jpWalletOperation) {
                    jpWalletOperation.roundId = separateRoundId;
                    jpWalletOperation.roundPID = publicId.instance.encode(+separateRoundId);
                }
            }
            if (walletOperation) {
                walletOperation.eventId = gameEventId;
                walletOperation.totalEventId = totalEventId;
            }

            if (walletOperation || history) {
                gameEventId++;
                totalEventId++;
            }
        }

        const ts = new Date();
        this.injectTimestamp(this.pendingModification, ts);
        this.injectTimestamp(jackpotPending, ts);

        if (jackpotPending) {
            this.jackpotPending = jackpotPending;
            options |= StoreOptions.JACKPOT_PENDING;
            const jackpotWalletOperation = jackpotPending.walletOperation;
            this.checkFinalization(jackpotPending);
            if (jackpotWalletOperation) {
                jackpotWalletOperation.eventId = gameEventId;
                jackpotWalletOperation.totalEventId = totalEventId;
            }
        }

        if (walletOperation?.roundEnded || jackpotPending?.walletOperation?.roundEnded) {
            await this.updateWalletTotalDetails(walletOperation, jpWalletOperation);
        }

        if (options > 0) {
            return this.makeUpdatePending(options);
        }
    }

    private async updateWalletTotalDetails(walletOperation: WalletOperation, jpWalletOperation: WalletOperation) {
        const totalBet = this.safeAdd(this.gameData.gameTokenData.currency,
            this.round?.totalBet, walletOperation?.bet, jpWalletOperation?.bet);

        const totalWin = this.safeAdd(this.gameData.gameTokenData.currency,
            this.round?.totalWin, walletOperation?.win, jpWalletOperation?.win);

        if (walletOperation?.roundEnded && !jpWalletOperation?.roundEnded) {
            walletOperation.totalBet = totalBet;
            walletOperation.totalWin = totalWin;
            if (this.gameData.settings?.smResultEnabled || config.smResult.enabled) {
                const initSettings = await this.getInitResponse();
                const lastSmResult = this.createSmResult(initSettings);
                const smResults = this.round?.smResults;
                walletOperation.smResult = getSmResult(smResults, lastSmResult);
                if (walletOperation.smResult) {
                    const lastSmResultExtraData = this.createSmResultExtraData(this, initSettings);
                    const smResultExtraData = this.round?.smResultExtraData;
                    walletOperation.smResultExtraData = getSmResultExtraData(smResultExtraData, lastSmResultExtraData);
                }
            }

            if (config.roundDetailedResult.enabled && this.settings.roundDetailsReportEnabled) {
                walletOperation.roundDetails = this.createRoundDetailedResult();
            }
        } else if (jpWalletOperation?.roundEnded) {
            jpWalletOperation.totalBet = totalBet;
            jpWalletOperation.totalWin = totalWin;
            if (walletOperation) {
                walletOperation.totalBet = undefined;
                walletOperation.totalWin = undefined;
            }
        }
    }

    private async getInitResponse(): Promise<any> {
        return findGameInitSettings(this.gameData.gameId, this.gameVersion);
    }

    private createHistoryItem() {
        const jpPending = this.jackpotPending;
        const hasJpHistory = jpPending && (jpPending.history || jpPending.walletOperation);
        const isRoundEnded = this.roundEnded && !hasJpHistory;
        return createGameHistoryEvent(this, undefined, undefined, isRoundEnded);
    }

    private createSmResult(initResponse: any, historyItem?: GameEventHistory): string {

        if (!historyItem) {
            historyItem = this.createHistoryItem();
        }

        if (historyItem.result && Object.keys(historyItem.result).length && historyItem.type) {
            const historyData = [deepClone(historyItem)];
            return createSmResult(historyData, initResponse, historyItem.gameId);
        } else {
            return "";
        }
    }

    private createRoundDetailedResult(historyItem?: GameEventHistory): HistoryItemResultsData {

        if (!historyItem) {
            historyItem = this.createHistoryItem();
        }

        if (historyItem.result && Object.keys(historyItem.result).length && historyItem.type) {
            return {
                sessionId: historyItem.sessionId,
                gameId: historyItem.gameId,
                brandId: historyItem.brandId,
                gameCode: historyItem.gameCode,
                playerCode: historyItem.playerCode,
                currency: historyItem.currency,
                win: historyItem.win,
                bet: historyItem.bet,
                totalJpWin: historyItem.totalJpWin,
                totalJpContribution: historyItem.totalJpContribution,
                result: {
                    payload: {
                        table: historyItem.result?.payload?.table,
                        ...buildAdditionalData(historyItem.result.payload)
                    }
                },
                roundId: historyItem.roundId,
            };
        } else {
            return undefined;
        }
    }

    private createSmResultExtraData(context: GameFlowContext,
                                    initResponse: any,
                                    historyItem?: GameEventHistory): SmResultExtraData {

        if (!historyItem) {
            historyItem = this.createHistoryItem();
        }

        const gameTitle = initResponse && initResponse.name;
        const linesDefinition = initResponse && initResponse.slot && initResponse.slot.linesDefinition;
        const lines = linesDefinition?.fixedMultiplierForTotalBet || linesDefinition?.fixedLinesCount;

        const walletOperation = context.pendingModification?.walletOperation;
        const paymentOperation = walletOperation?.operation;

        const transactions = [];
        if (paymentOperation === "split-payment") {
            const splitPayment = walletOperation as SplitPaymentOperation;
            transactions.push(...this.mapGameWalletActionToSmTransaction(historyItem, splitPayment.debitActions));
            transactions.push(...this.mapGameWalletActionToSmTransaction(historyItem, splitPayment.creditActions));
        } else if (paymentOperation === "payment") {
            const payment = walletOperation as PaymentOperation;
            transactions.push(...this.mapGameWalletActionToSmTransaction(historyItem, payment.actions));
        }

        return {
            gameId: historyItem.gameId,
            gameTitle,
            lines: +lines,
            transactions
        };
    }

    private mapGameWalletActionToSmTransaction(historyItem: GameEventHistory,
                                               actions: GameWalletAction[]): SmResultTransaction[] {

        return actions.map(action => {
            const result = {
                transactionId: historyItem.walletTransactionId,
                type: action.changeType,
                ts: historyItem.ts,
                amount: action.amount
            };
            return result;
        });
    }

    private updateRoundStatisticWithSmResult(round: RoundStatistics = this.round, smResult: string) {
        if (round?.smResults?.length) {
            round.smResults.push(smResult);
        } else {
            round.smResults = [smResult];
        }
    }

    private updateRoundStatisticWithSmResultExtraData(round: RoundStatistics = this.round,
                                                      extraData: SmResultExtraData) {

        if (round?.smResultExtraData?.transactions?.length) {
            round.smResultExtraData.transactions.push(...extraData.transactions);
        } else if (round?.smResultExtraData) {
            round.smResultExtraData.transactions = [...extraData.transactions];
        } else {
            round.smResultExtraData = { transactions: [...extraData.transactions] };
        }
        if (!round?.smResultExtraData?.gameId) {
            round.smResultExtraData.gameId = extraData.gameId;
            round.smResultExtraData.lines = extraData.lines;
        }
    }

    private updateRoundStatisticWithRoundDetails(round: RoundStatistics = this.round, roundDetails: any) {
        round.roundDetails = roundDetails;
    }

    public async updatePending(): Promise<any> {
        return this.makeUpdatePending(StoreOptions.PENDING_MODIFICATION);
    }

    public async updateJackpotPending(): Promise<any> {
        return this.makeUpdatePending(StoreOptions.JACKPOT_PENDING);
    }

    public async prepareFinalizeGame(walletOperation: FinalizeGameOperation,
                                     newState: GameContext = null): Promise<any> {
        this.prepareRoundStatisticsSmResult(walletOperation);
        return this.updatePendingModification(walletOperation, newState);
    }

    private prepareRoundStatisticsSmResult(walletOperation: FinalizeGameOperation): void {
        if (walletOperation.roundStatistics?.smResults && !walletOperation.roundStatistics?.smResult) {
            walletOperation.roundStatistics.smResult = getSmResult(walletOperation.roundStatistics?.smResults);
        }
    }

    private async makeUpdatePending(options: StoreOptions): Promise<any> {
        return this.executor.execute([
            this.commands.checkVersion(this, true, true),
            this.commands.store(this, true, this.clearFullUpdate(options)),
            this.pendingModification?.walletOperation && this.settings.minPaymentRetryTimeout > 0 ?
            this.commands.trackActive(this.id, Date.now() + this.settings.minPaymentRetryTimeout * 1000) :
            undefined
        ]).catch(e => this.processError(e));
    }

    @measure({ name: "GameFlowContext.rollbackPendingModification", isAsync: true, debugOnly: true })
    public async rollbackPendingModification(skipStartingNewRound?: boolean): Promise<any> {
        const option = StoreOptions.PENDING_MODIFICATION | StoreOptions.JACKPOT_PENDING | StoreOptions.ROUND_INFO
            | StoreOptions.SPECIAL_STATE;
        if (!this.round && !skipStartingNewRound || !this.roundId) {
            this.roundId = await this.commands.generateRoundId();
        }
        /* We have no special state field in PendingModification.
           In case of payment was declined (validation error for example),
           let's assume that FINALIZING special state was added with this payment - we need to clear special state also.
        */
        if (this.specialState === SpecialState.FINALIZING) {
            this.specialState = null;
        }
        this.totalEventId++;
        this.pendingModification = undefined;
        this.jackpotPending = undefined;
        if (this.reactive) {
            return this.executor.execute([
                this.commands.checkVersion(this, true, true),
                this.commands.store(this, false, this.clearFullUpdate(option)),
                this.commands.trackActive(this.id, GameFlowContextImpl.getGameActivityTimeout(this.gameData)),
                this.playerContext && this.playerContext.commands.trackActive(this.playerContext.id,
                    GameFlowContextImpl.getPlayerActivityTimeout(this.gameData))
            ]).catch(e => this.processError(e));
        } else {
            return this.executor.execute(this.commands.store(this, false, this.clearFullUpdate(option)))
                .catch(e => this.processError(e));
        }
    }

    @measure({ name: "GameFlowContext.rollbackPendingModificationAndCloseRound", isAsync: true, debugOnly: true })
    public async rollbackPendingModificationAndCloseRound(): Promise<any> {
        let options = StoreOptions.PENDING_MODIFICATION | StoreOptions.JACKPOT_PENDING | StoreOptions.ROUND_INFO;
        let round: RoundHistory;
        if (!this.roundEnded) {
            this.roundEnded = true;
            this.round.finishedAt = new Date();
            round = createRoundHistoryEvent(this);
            this.gameSerialNumber = 0;
            options |= StoreOptions.ROUND_INFO;
        }
        this.totalEventId++;
        this.pendingModification = undefined;
        this.jackpotPending = undefined;
        return this.executor.execute(this.commands.store(this, false, this.clearFullUpdate(options), { round }))
            .catch(e => this.processError(e));
    }

    @measure({ name: "GameFlowContext.updateJackpotPendingModification", isAsync: true, debugOnly: true })
    public async updateJackpotPendingModification(jackpotPending: JackpotPendingModification,
                                                  jpResult?: JackpotPendingProcessResult,
                                                  jpContext?: JackpotContext): Promise<any> {
        let options = StoreOptions.ROUND_INFO;

        const jpPending = this.jackpotPending;
        let jpItem: GameEventHistory;
        const walletOperation = this.pendingModification?.walletOperation;
        const jpWalletOperation = jackpotPending?.walletOperation;
        if (jpPending) {
            // propagate finalizationType
            jackpotPending.finalizationType = this.jackpotPending.finalizationType;
            options |= StoreOptions.JACKPOT_PENDING;
            if (jpPending.history || jpPending.walletOperation) {
                jpItem = createJackpotGameHistoryEvent(this, jpResult, this.roundEnded);
                await this.updateRoundStatistic(jpItem);
                this.gameSerialNumber++;
                this.totalEventId++;
            }
            this.jackpotPending = undefined;
        }

        this.injectTimestamp(jackpotPending, new Date());
        this.jackpotPending = jackpotPending;
        this.checkFinalization(jackpotPending);

        if (jpWalletOperation) {
            const hasEvent = (walletOperation || this.pendingModification?.history);
            jackpotPending.walletOperation.eventId =
                hasEvent ? this.gameSerialNumber + 1 : this.gameSerialNumber;
            jackpotPending.walletOperation.totalEventId =
                hasEvent ? this.totalEventId + 1 : this.totalEventId;

            const separateRoundId = this.pendingModification?.separateRoundId;
            if (separateRoundId) {
                if (jpWalletOperation) {
                    jpWalletOperation.roundId = separateRoundId;
                    jpWalletOperation.roundPID = publicId.instance.encode(+separateRoundId);
                }
            }
        }

        if (walletOperation?.roundEnded || jpWalletOperation?.roundEnded) {
            await this.updateWalletTotalDetails(walletOperation, jpWalletOperation);
        }

        if (jpContext) {
            this.jpContext = jpContext;
            options |= StoreOptions.JP_CONTEXT;
        }

        return this.executor.execute(this.commands.store(this, true, this.clearFullUpdate(options), { jpItem }))
            .catch(e => this.processError(e));
    }

    private checkFinalization(jackpotPending: JackpotPendingModification) {
        if (jackpotPending?.finalizationType) {
            if (jackpotPending.walletOperation) {
                jackpotPending.walletOperation.finalizationType = jackpotPending.finalizationType;
            }
            if (jackpotPending.history) {
                jackpotPending.history.data.isFinalization = true;
            }
        }
    }

    @measure({ name: "GameFlowContext.remove", isAsync: true, debugOnly: true })
    public async remove(removeActive: boolean = true): Promise<any> {
        let round: RoundHistory;
        if (this.round !== undefined) {
            round = createRoundHistoryEvent(this);
        }
        const sessionEnd = createSessionHistory(this, this.updatedAt);
        const merchantSessionId = this.gameData.gameTokenData.merchantSessionId;
        return this.executor.execute([
            this.commands.checkVersion(this, true, true),
            this.commands.remove(this, true, { sessionEnd, round }, removeActive),
            this.playerContext && this.playerContext.commands.removeGame(this.id.playerContextID, this.id, this.broken),
            merchantSessionId && this.commands.removeMerchantSessionLink(this.id, merchantSessionId)
        ]).catch(e => this.processError(e));
    }

    private clearFullUpdate(options: StoreOptions): StoreOptions {
        let result = options;
        if (this.expectFullUpdate) {
            result |= StoreOptions.ALL;
            this.expectFullUpdate = undefined;
        }

        return result;
    }

    @measure({ name: "GameFlowContext.forceCloseRound", isAsync: true, debugOnly: true })
    public async forceCloseRound(recoveryType: RecoveryType = RecoveryType.FORCE_FINISH,
                                 // newState field comes when forceFinish is called from two-step finalization process,
                                 // so when it comes - it comes with 'finalize' recovery type and expects to only
                                 // 'close' round in our context as a final step of two-step finalization
                                 newState: GameContext = null): Promise<any> {
        let round: RoundHistory;
        let item: GameEventHistory;
        if (this.round) {
            if (recoveryType === "revert") {
                const revertedRoundStatistic = {
                    ...this.round,
                    balanceAfter: this.round.balanceBefore,
                    totalBet: 0,
                    totalWin: 0
                };
                round = createRoundHistoryEvent(this, revertedRoundStatistic);
            } else {
                round = createRoundHistoryEvent(this);
            }

            round.recoveryType = recoveryType;
            round.finishedAt = new Date();
            item = createForceFinishGameEvent(this, recoveryType);
        }

        if (!newState) {
            const merchantSessionId = this.gameData.gameTokenData.merchantSessionId;
            return this.executor.execute([
                this.commands.checkVersion(this, true, true),
                this.commands.remove(this, true, { round, item }, true),
                this.playerContext && this.playerContext.commands.removeGame(this.id.playerContextID,
                    this.id,
                    false),
                merchantSessionId && this.commands.removeMerchantSessionLink(this.id, merchantSessionId)
            ]).catch(e => this.processError(e));
        } else {
            this.updateGameContext(newState);
            this.roundEnded = true;
            this.round = null;
            this.specialState = null;
            this.logoutResult = null;
            this.logoutId = null;

            const roundId = await this.commands.generateRoundId();
            this.beginNewRound(roundId);

            return this.executor.execute([
                this.commands.checkVersion(this, true, true),
                this.commands.store(this, true, StoreOptions.ALL, { round, item }, true),
            ]).catch(e => this.processError(e));
        }
    }

    @measure({ name: "GameFlowContext.incrementRetryAttempts", isAsync: true, debugOnly: true })
    public incrementRetryAttempts(): Promise<any> {
        this.retryAttempts = (this.retryAttempts || 0) + 1;
        return this.executor.execute(this.commands.store(this, true, StoreOptions.RETRY_ATTEMPTS))
            .catch(e => this.processError(e));
    }

    @measure({ name: "GameFlowContext.flushRetryAttempts", isAsync: true, debugOnly: true })
    public flushRetryAttempts(): Promise<any> {
        this.retryAttempts = 0;
        return this.executor.execute(this.commands.store(this, true, StoreOptions.RETRY_ATTEMPTS))
            .catch(e => this.processError(e));
    }

    @measure({ name: "GameFlowContext.activateNextRetryAttempt", isAsync: true, debugOnly: true })
    public activateNextRetryAttempt(): Promise<any> {
        return this.executor.execute([
            this.commands.trackActive(this.id,
                Date.now() + Math.pow(2, this.retryAttempts) * 60000)
        ]).catch(e => this.processError(e));
    }

    @measure({ name: "GameFlowContext.reaActivate", isAsync: true, debugOnly: true })
    public reactivate(): Promise<any> {
        return this.executor.execute([
            this.commands.trackActive(this.id, GameFlowContextImpl.getGameActivityTimeout(this.gameData))
        ]).catch(e => this.processError(e));
    }

    @measure({ name: "GameFlowContext.setLogoutResult", isAsync: true, debugOnly: true })
    public setLogoutResult(state?: MerchantLogoutResult, newLogoutId: string = null): Promise<any> {
        this.logoutResult = state;
        if (this.logoutResult === MerchantLogoutResult.PENDING_LOGOUT && !this.logoutId) {
            this.logoutId = this.session.sessionId;
        } else if (!this.logoutResult || this.logoutResult === MerchantLogoutResult.SKIP_LOGIN) {
            this.logoutId = newLogoutId || null;
        }
        return this.executor.execute([
            this.commands.checkVersion(this, true, true),
            this.commands.store(this, true, StoreOptions.LOGOUT_RESULT)
        ])
            .catch(e => this.processError(e));
    }

    @measure({ name: "GameFlowContext.setSpecialState", isAsync: true, debugOnly: true })
    public setSpecialState(state?: SpecialState): Promise<any> {
        this.specialState = state;
        return this.executor.execute(this.commands.store(this, true, StoreOptions.SPECIAL_STATE))
            .catch(e => this.processError(e));
    }

    public clearSpecialState(): Promise<void> {
        return this.setSpecialState(null);
    }

    public markForFullUpdate() {
        this.expectFullUpdate = true;
    }

    private safeAdd(currencyCode: string, ...v: number[]): number {
        const currency = Currencies.get(currencyCode);
        const sumInMinor = v.reduce<number>((v1: number, v2: number) => v1 + (v2 ? currency.toMinorUnits(v2) : 0), 0);
        return currency.toMajorUnits(sumInMinor);
    }

    public static getGameActivityTimeout(gameData?: GameData) {
        return GameFlowContextImpl.getActivityTimeout(gameData, config.cleanUp.expireIn);
    }

    public static getPlayerActivityTimeout(gameData?: GameData) {
        // make player context activity timestamp 10 minutes more that game context to eliminate racing on cleanup
        return GameFlowContextImpl.getActivityTimeout(gameData, config.cleanUp.expireIn)
            + config.cleanUpPlayers.expireIn * 60000;
    }

    public static getActivityTimeout(gameData: GameData, defaultValue: number): number {
        return Date.now() +
            (gameData?.logoutOptions?.maxSessionTimeout || defaultValue) * 60000;
    }

    private createAnalyticsData(analytics: Analytics): GameAnalytics {
        if (analytics) {
            return AnalyticsData(this, {
                id: uuid.v4(),
                ...analytics
            });
        }
    }

    private injectTimestamp(pending: { walletOperation?: WalletOperation, history?: ExtendedGameHistory }, ts: Date) {
        if (!pending) {
            return;
        }

        if (pending.walletOperation) {
            pending.walletOperation.ts = ts;
        } else if (pending.history) {
            pending.history.ts = ts;
        }
    }

    private async processError(err: Error): Promise<Error> {
        this.corrupt();

        return Promise.reject(err);
    }

    private getPlayerCurrency(): Currency {
        const currencyCode = this.pendingModification?.walletOperation?.currency || this.currencyForHistory;
        return Currencies.get(currencyCode);
    }

    private updateGameContext(gameContext: GameContext) {
        this.gameContext = deepClone(gameContext);
    }
}

export function getSmResult(smResults: string[] = [], lastSmResult: string = ""): string {
    const smResult = smResults.length ? [...smResults, lastSmResult].join("") : lastSmResult;
    const maxLength = config.smResult.maxLength;
    return smResult.length > maxLength ? smResult.substring(0, maxLength) : smResult;
}

export function getSmResultExtraData(extraData: SmResultExtraData = {}, lastExtraData: SmResultExtraData = {}) {
    const transactions = extraData.transactions;
    const lastTransactions = lastExtraData.transactions;
    return {
        gameId: extraData.gameId || lastExtraData.gameId,
        gameTitle: extraData.gameTitle || lastExtraData.gameTitle,
        lines: extraData.lines || lastExtraData.lines,
        transactions: transactions && transactions.length ? [...transactions, ...lastTransactions] : lastTransactions
    };
}
