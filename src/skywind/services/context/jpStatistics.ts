import { JackpotContext, JackpotOperationResult } from "../jpn/jackpot";
import { getOrInit } from "../../utils/common";
import { JackpotResult, JackpotWinResult } from "@skywind-group/sw-game-core";
import { deepClone } from "../../utils/cloner";

export interface JackpotValues {
    seed?: number;
    progressive?: number;
}

export interface JackpotPoolDetails {
    contribution: JackpotValues;
    win: number;
    seedWin?: number;
    progressiveWin?: number;
    isLocal?: boolean;
}

export interface JackpotIdDetails {
    [pool: string]: JackpotPoolDetails;
}

export interface JackpotStatistic {
    [jpId: string]: JackpotIdDetails;
}

export interface JackpotTypeStatistic {
    [jpType: string]: string[];
}

export function calculateJPStatistic(jpnResult: JackpotOperationResult,
                                     jpStatistics: JackpotStatistic = {},
                                     jpContext: JackpotContext,
                                     clone: boolean = false): JackpotStatistic {
    const result = clone ? deepClone(jpStatistics) : jpStatistics;
    if (jpnResult && jpnResult.contributionResult) {
        for (const contribution of jpnResult.contributionResult) {
            const jpInfo = jpContext.jackpotsInfo.find((jp) => jp.id === contribution.jackpotId);
            if (!jpInfo.paymentStatisticEnabled) {
                continue;
            }
            const jackpotIdDetails: JackpotIdDetails = getOrInit(result, contribution.jackpotId, {});
            const poolDetails: JackpotPoolDetails = getOrInit(jackpotIdDetails, contribution.pool,
                {
                    contribution: { seed: 0, progressive: 0 },
                    win: 0,
                });

            if (contribution.seed) {
                poolDetails.contribution.seed += contribution.seed;
            }

            if (contribution.progressive) {
                poolDetails.contribution.progressive += contribution.progressive;
            }

            if (jpInfo.isLocal !== undefined) {
                poolDetails.isLocal = jpInfo.isLocal;
            }
        }
    }
    const jackpotResults: JackpotResult[] = jpnResult?.jackpot?.result;
    if (jackpotResults) {
        for (const item of jackpotResults) {
            if (item.event === "win") {
                const winItem = item as JackpotWinResult;
                const jpSettings = jpContext.jackpotsInfo.find((jp) => jp.id === item.jackpotId);
                if (!jpSettings.paymentStatisticEnabled) {
                    continue;
                }
                const jackpotIdDetails: JackpotIdDetails = getOrInit(result, item.jackpotId, {});
                const poolDetails: JackpotPoolDetails = getOrInit(jackpotIdDetails, item.pool,
                    {
                        contribution: { seed: 0, progressive: 0 },
                        win: 0,
                    });
                poolDetails.win += winItem.amount;
                if (winItem.seedWin !== undefined) {
                    poolDetails.seedWin = (poolDetails.seedWin || 0) + winItem.seedWin;
                }
                if (winItem.progressiveWin !== undefined) {
                    poolDetails.progressiveWin = (poolDetails.progressiveWin || 0) + winItem.progressiveWin;
                }
                if (jpSettings.isLocal !== undefined) {
                    poolDetails.isLocal = jpSettings.isLocal;
                }
            }
        }
    }

    return result;
}
