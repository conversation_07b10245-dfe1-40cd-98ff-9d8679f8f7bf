import {
    Analytics,
    BaseRequest,
    GameContext,
    GameHistory,
    GameInitRequest,
    GameMode
} from "@skywind-group/sw-game-core";
import { GameSettings } from "../tokens";
import { Balance, FinalizeGameOperation, SmResultExtraData, WalletOperation } from "../wallet";
import { JackpotContext, JackpotOperation } from "../jpn/jackpot";
import { GameData } from "../auth";
import { GameSession } from "../gameSession";
import { UserAgent } from "../useragent";
import { GameContextID } from "../contextIds";
import { PlayerContext } from "../playercontext/playerContext";
import { RecoveryType } from "../offlinestorage/offlineCommands";
import { JackpotPendingProcessResult } from "../jpn/jackpotFlow";
import { ExtendedGameHistory } from "../../history/history";
import { JackpotStatistic } from "./jpStatistics";
import { StoreHistory } from "../onlinestorage/gameContextCommands";
import { HistoryItemResultsData } from "@skywind-group/sw-round-details-report";

export type GameContextStatus =
    "broken"
    | "unfinished"
    | "requireLogout"
    | "brokenIntegration"
    | "finalizing"
    | "requireTransferOut";

export interface RequestContext {
    ip?: string;
    screenSize?: string;
    language?: string;
    country?: string;
    userAgent?: UserAgent;
    playedFromCountry?: string;
    operatorCountry?: string;
    operatorPlayerCountry?: string;
    deviceData?: any;
}

export type ExtendedGameInitRequest = GameInitRequest & RequestContext & RequestReferrer;

export interface ReplayRequest extends RequestContext, RequestReferrer {
    replayToken: string;
}

export interface RequestReferrer {
    referrer?: string;
}

export interface GameAnalytics extends Analytics {
    readonly id: string;
}

export interface PendingModification {
    /**
     *  Information of last wallet operations. It's needed for sending repeats
     */
    readonly walletOperation: WalletOperation;

    /**
     * History element
     */
    readonly history: ExtendedGameHistory;

    /**
     * New game state
     */
    readonly newState: GameContext;

    /**
     * Analytics that should be sent to BI
     */
    readonly analytics?: GameAnalytics;
}

export interface JackpotGameHistory extends ExtendedGameHistory {
    data: {
        [field: string]: any
        isFinalization?: boolean
    };
}

export interface JackpotPendingModification {
    /**
     *  Information of last wallet operations. It's needed for sending repeats
     */
    readonly walletOperation?: WalletOperation;
    /**
     * History element
     */
    readonly history?: JackpotGameHistory;
    /**
     * Last Jackpot operation. Stored for recovery purpose when it needs to be retried
     */
    readonly jackpotOperation: JackpotOperation;

    finalizationType?: string;
}

/**
 * Round statistic
 */
export interface RoundStatistics {
    currentBet?: number;
    /**
     * Total bet amount
     */
    totalBet: number;
    /**
     * Total win amount
     */
    totalWin: number;
    /**
     * Total events count
     */
    totalEvents: number;
    /**
     * Balance in the begining of round
     */
    balanceBefore: number;

    /**
     * Balance after finishing round
     */
    balanceAfter: number;

    /**
     * Is round was broken?
     * We use this flag when we "update" round state in database
     */
    broken?: boolean;
    /**
     * The time when round was started
     */
    startedAt?: Date;
    /**
     * The time when round was finished
     */
    finishedAt?: Date;

    /**
     * Total jackpot contribution amount in player currency
     */
    totalJpContribution?: number;

    /**
     * Total jackpot win amount in player currency
     */
    totalJpWin?: number;

    /**
     * Credit of the round
     */
    credit?: number;

    /**
     * Total debit of the round, for example two transfer in operations
     */
    debit?: number;

    /**
     * Detailed information for each of jackpotId (total contribution, total win)
     */
    jpStatistic?: JackpotStatistic;

    refund?: number;

    roundId?: string;

    extRoundId?: string;

    // Timestamp the of last successful payment
    lastSuccessfulPaymentDate?: number;

    // Counter for bets in round
    betsCount?: number;

    // List of sm_result for Portugal jurisdiction
    smResults?: string[];

    // Aggregated smResults into one string
    smResult?: string;

    // Extra Data that we send along with smResult (required for ISoftBet)
    smResultExtraData?: SmResultExtraData;

    // Flag that indicates started in the end of round Instant JP if it was won
    instantJackpot?: boolean;

    // HistoryItemResultsData object required by Stars to be send as round results for Live games
    roundDetails?: HistoryItemResultsData;
}

/**
 *  The status of logging out player from the game on merchant side
 */
export enum MerchantLogoutResult {
    /**
     * Operation of logout has been started
     */
    PENDING_LOGOUT = "pending_logout",

    /**
     * Do not restore session next time on merchant side
     */
    SKIP_LOGIN = "skip_login",

    /**
     * Restore session next time
     */
    REQUIRE_LOGIN = "require_login"
}

export enum SpecialState {
    BROKEN_INTEGRATION = 1,
    FINALIZING = 2,
    CANNOT_COMPLETE_PAYMENT = 3,
    BROKEN_GAME_CONTEXT = 4
}

/**
 * This interface extends GameContext with specific fields which are needed
 * for storing additional context information  and are  very important to support
 * game flow consistent but which don't concern to game logic.
 */
export interface GameFlowContext {
    /**
     * Unique identifier for game context for storage purpose
     */
    readonly id: GameContextID;

    /**
     *  Player context
     */
    readonly playerContext: PlayerContext;

    /**
     * Game session
     */
    readonly session: GameSession;

    /**
     * Game module version
     */
    readonly gameVersion: string;

    /**
     * Previous request id
     */
    lastRequestId: number;

    /**
     * Game token data
     */
    readonly gameData: GameData;

    /**
     *  Information of last wallet operations
     */
    readonly pendingModification?: PendingModification | undefined;

    /**
     * Last Jackpot operation. Stored for recovery purpose when it needs to be retried
     */
    readonly jackpotPending?: JackpotPendingModification;

    /**
     * Should we store game context in offline storage
     * if we are going to empty online operational storage
     */
    readonly keepOffline: boolean;

    /**
     * If context in transition state: unfinished payment, unfinished round
     */
    readonly broken: boolean;

    /**
     * If context has unfinished payment
     */
    readonly brokenPayment: boolean;

    /**
     * If context has unfinished jackpot
     */
    readonly brokenJackpot: boolean;

    /**
     *  Is the game not finished and expects another player's action
     */
    readonly unfinished: boolean;

    /**
     *  Does the game require logout on player finish session.
     */
    readonly requireLogout: boolean;

    /**
     * Indicate that the game has incomplete transfer
     */
    readonly requireTransferOut: boolean;

    /**
     * Spins counter.
     * This field is used as serial number generator
     */
    readonly gameSerialNumber: number;

    /**
     * Spins counter in scope of the game context
     */
    readonly totalEventId: number;

    /**
     * Game round identifier
     */
    readonly roundId: string;

    readonly round: RoundStatistics;

    readonly lastRound: RoundStatistics;

    /**
     * Is the last round is finished
     */
    readonly roundEnded: boolean;

    readonly  gameContext: GameContext;

    /**
     * Game context persistence policy
     */
    persistencePolicy: number;

    readonly settings: GameSettings;

    readonly jpContext?: JackpotContext;

    /**
     * Request info
     */
    readonly requestContext?: RequestContext;

    /**
     * Indicates if game context is corrupted and not eligible to be used.
     */
    readonly corrupted: boolean;

    /**
     * Game context creation timestamp
     */
    readonly createdAt: Date;

    /**
     * Game context last activity timestamp
     */
    readonly updatedAt: Date;

    /**
     * Game coontext expiration setting
     */
    readonly expireAt?: Date | string | number;

    readonly currencyForHistory: string;

    readonly playMode: GameMode;

    /**
     * How many times we made retries.
     */
    readonly retryAttempts?: number;

    /**
     * Game context state. It could be used to mark context  for logout/requireLogin
     */
    readonly logoutResult: MerchantLogoutResult;
    /**
     * Unique id generated for every logout
     */
    readonly logoutId: string;

    /**
     * Special terminal state of the game context
     */
    readonly specialState?: SpecialState;

    /**
     * Is the game context mark for finalization
     */
    readonly requireFinalizeGame: boolean;

    /**
     * return the status of the game
     */
    readonly status: GameContextStatus;

    /**
     * Update game context.
     *
     * During update, we check that  version of requestID and sessionID are equal to those
     * which are stored in DB . If it's not true we will throw ConcurrentAccessToGameSession.
     *
     * @param flowContext game context
     */
    commitPendingModification(gameResultBalance?: Balance,
                              jackpotResult?: JackpotPendingProcessResult,
                              newJackpotContext?: JackpotContext,
                              recoveryType?: RecoveryType): Promise<StoreHistory>;

    rollbackPendingModification(skipStartingNewRound?: boolean): Promise<void>;

    rollbackPendingModificationAndCloseRound(): Promise<void>;

    /**
     * Updates game context and save history of spin result.
     *
     * During update, we check that  version of requestID and sessionID are equal to those
     * which are stored in DB . If it's not true we will throw ConcurrentAccessToGameSession.
     *
     * @param flowContext game flow context
     * @param walletOperation wallet operation details
     * @param newContext new game context information
     * @param history
     * @param jackpotPending jackpot pending operations
     * @param analytics
     */
    updatePendingModification(walletOperation: WalletOperation,
                              newContext: GameContext,
                              request?: BaseRequest & RequestContext,
                              history?: GameHistory,
                              jackpotPending?: JackpotPendingModification,
                              analytics?: Analytics): Promise<void>;

    updatePending(): Promise<void>;

    updateJackpotPending(): Promise<void>;

    /**
     * Create pending "finalize-game" operation
     *
     */
    prepareFinalizeGame(walletOperation: FinalizeGameOperation, newState?: GameContext): Promise<any>;

    /**
     * Updates pending jackpot operation.
     * @param flowContext
     * @param jackpotPending
     */
    updateJackpotPendingModification(jackpotPending: JackpotPendingModification,
                                     jpResult?: JackpotPendingProcessResult,
                                     jpContext?: JackpotContext): Promise<void>;

    /**
     * Update game flow context
     *
     * @param flowContext game flow context
     */
    update(gameContext?: GameContext): Promise<void>;

    /**
     * Update game data part of context
     *
     * @param flowContext game flow context
     */
    updateGameData(): Promise<void>;

    /**
     * Finds game context by their identifiers.
     *
     * @param flowContext game flow context
     * @param removeActive  remove still active context  (for force cleanup). Default is true
     * @return true if the context was deleted
     */
    remove(removeActive?: boolean): Promise<void>;

    /**
     * Force close current broken round with cleaning up all pending and game state
     */
    forceCloseRound(recoveryType?: RecoveryType, newState?: GameContext): Promise<void>;

    /**
     * Increment retry attempts count
     */
    incrementRetryAttempts(): Promise<void>;

    /**
     * Zero out the retry attempts count
     */
    flushRetryAttempts(): Promise<void>;

    /**
     *  re-activate the GameContext for next retry;
     */
    activateNextRetryAttempt(): Promise<void>;

    /**
     *  re-activate the GameContext to keep it in fast db;
     */
    reactivate(): Promise<void>;

    /**
     *  Mark gameContext with a logout result
     */
    setLogoutResult(result?: MerchantLogoutResult, newLogoutId?: string): Promise<void>;

    /**
     * Update game flow context special state
     *
     */
    setSpecialState(state: SpecialState): Promise<void>;

    /**
     * Remove special state marker
     *
     */
    clearSpecialState(): Promise<void>;
}
