import { kafka, lazy, logging, measures } from "@skywind-group/sw-utils";
import { GameAnalytics, GameFlowContext } from "../context/gamecontext";
import config from "../../config";
import measure = measures.measure;

export interface AnalyticsData extends GameAnalytics {
    readonly roundId: string;
    readonly eventId: number;
    readonly gameId: string;
    readonly gameCode: string;
    readonly brandId: number;
    readonly playerCode: string;
    readonly deviceId: string;
    readonly ts: number;
}

export function AnalyticsData(ctx: GameFlowContext, gameAnalytics: GameAnalytics) {
    return gameAnalytics &&  {
        ...gameAnalytics,
        ts: Date.now(),
        roundId: ctx.roundId,
        eventId: ctx.gameSerialNumber,
        gameId: ctx.gameData.gameId,
        gameCode: ctx.gameData.gameTokenData.gameCode,
        brandId: ctx.id.brandId,
        playerCode: ctx.id.playerCode,
        deviceId: ctx.id.deviceId
    };
}

export class AnalyticsService {
    private kafkaWriter: kafka.KafkaWriter;
    private readonly logger = logging.logger("sw-slot-engine:analytics");

    @measure({ name: "AnalyticsService.send", isAsync: true })
    public async send(analytics: AnalyticsData | AnalyticsData[]): Promise<void> {
        if (!this.kafkaWriter) {
            this.kafkaWriter = await kafka.createWriter(config.analytics.kafka, this.logger);
        }
        const items = Array.isArray(analytics) ?
                      analytics.map(item => JSON.stringify(item)) :
            [JSON.stringify(analytics)];
        return this.kafkaWriter.sendMessages(items);
    }
}
