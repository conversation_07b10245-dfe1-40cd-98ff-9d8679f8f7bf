import { GameFlowContext, SpecialState } from "./context/gamecontext";
import MerchantGameSessionService from "./merchantGameSessionService";
import { GameFlowFactory } from "./gameFlowFactory";
import { logging } from "@skywind-group/sw-utils";
import { EngineGameFlow } from "./gameflow";
import { ContextVariables } from "../utils/contextVariables";
import { GameContextBrokenIntegration, GameFinalizedError, isCannotCompletePaymentError } from "../errors";
import logger = logging.logger;

const log = logger("sw-slot-engine:finalize-game-context");

/**
 * Service is responsible for retry and logout logic.
 * Retries could be inspired by operator to finish the game (all pending payments and logout);
 * Completion is used by offline cleanup job.
 */
export class PendingCompletionService {

    /**
     * Retry pending payments and logout
     */
    public async retry(ctx: GameFlowContext): Promise<void> {
        if (ctx.brokenPayment) {
            await this.completePayment(ctx);
        }

        if (ctx.requireLogout) {
            await MerchantGameSessionService.logout(ctx);
        }
    }

    /**
     * Check if we need to retry pending payment or/and logout
     *
     * @param ctx
     */
    public requireRetry(ctx: GameFlowContext): boolean {
        return ctx.brokenPayment || ctx.requireLogout;
    }

    /**
     *  Check if we need to reconcile game context and to finish payment/logout
     *  Used by Cleanup job
     */
    public requireCompletion(ctx: GameFlowContext) {
        const retryAttempts = ctx.retryAttempts || 0;
        const paymentAttempts = this.getPaymentAttempts(ctx);
        const logoutAttempts = this.getLogoutAttempts(ctx) || 1; // at least one attempt
        const requireToCompleteBrokenPayment: boolean = ctx.brokenPayment && paymentAttempts
            && retryAttempts < paymentAttempts;
        const requireToCompleteLogout: boolean = !ctx.brokenPayment && ctx.requireLogout
            && retryAttempts < logoutAttempts;
        return (requireToCompleteBrokenPayment || requireToCompleteLogout) && this.isSpecialStateAllowToComplete(ctx);
    }

    private isSpecialStateAllowToComplete(ctx: GameFlowContext): boolean {
        return ctx.specialState !== SpecialState.BROKEN_INTEGRATION
            && ctx.specialState !== SpecialState.FINALIZING
            && ctx.specialState !== SpecialState.CANNOT_COMPLETE_PAYMENT;
    }

    /**
     *  Complete payments/merchant logout
     *  Increase attempt counters to be able to schedule offline job  for future
     *
     *  Used by Cleanup job and Finalization job
     */
    public async complete(ctx: GameFlowContext, flow?: EngineGameFlow): Promise<void> {
        ContextVariables.setUpRound(ctx.roundId);
        const contextShouldBeLocked = ctx.pendingModification?.history?.roundEnded === true;
        try {
            if (ctx.brokenPayment) {
                await ctx.incrementRetryAttempts();
                ContextVariables.setRetryAttempt(ctx.retryAttempts);
                try {
                    await this.completePayment(ctx, flow);
                } catch (err) {
                    const ignorePayments = ctx?.settings?.logoutControl?.ignorePayments?.offlineRetry;
                    if (ignorePayments) {
                        await this.completeLogout(ctx, ignorePayments);
                    }

                    throw err;
                }
                ContextVariables.setRetryAttempt(undefined);
                await ctx.flushRetryAttempts();
            }

            await this.completeLogout(ctx);
        } catch (err) {
            if (isCannotCompletePaymentError(err) && contextShouldBeLocked) {
                if (flow?.flowContext?.pendingModification?.history) {
                    /* Restore roundEnded flag because context is in cannot complete payment state to prevent case
                    when player will start game and continue already finished round */
                    flow.flowContext.pendingModification.history.roundEnded = true;
                    await flow.flowContext.updatePending();
                }
            }
            throw err;
        } finally {
            ContextVariables.cleanupRound();
        }
    }

    private async completeLogout(ctx: GameFlowContext, ignorePayments = false): Promise<void> {
        if (ctx.requireLogout) {
            await ctx.incrementRetryAttempts();
            ContextVariables.setRetryAttempt(ctx.retryAttempts);
            await MerchantGameSessionService.logout(ctx, ignorePayments);
            ContextVariables.setRetryAttempt(undefined);
            await ctx.flushRetryAttempts();
        }
    }

    public async completeBeforeRelaunch(ctx: GameFlowContext): Promise<void> {
        ContextVariables.setUpRound(ctx.roundId);
        try {
            const ignorePayments = ctx?.settings?.logoutControl?.ignorePayments?.gameRelaunch;
            if (ctx.brokenPayment && !ignorePayments) {
                // Check for special state to prevent unnecessary payment retry
                if (ctx.pendingModification) {
                    if (ctx.specialState === SpecialState.BROKEN_INTEGRATION) {
                        throw new GameContextBrokenIntegration();
                    }
                    if (ctx.specialState === SpecialState.FINALIZING) {
                        throw new GameFinalizedError();
                    }
                }
                log.info("Commit pending payment before logout");
                await ctx.incrementRetryAttempts();
                ContextVariables.setRetryAttempt(ctx.retryAttempts);
                await this.completePayment(ctx);
                ContextVariables.setRetryAttempt(undefined);
                await ctx.flushRetryAttempts();
            }

            if (ctx.requireLogout) {
                await ctx.incrementRetryAttempts();
                ContextVariables.setRetryAttempt(ctx.retryAttempts);
                await MerchantGameSessionService.logout(ctx, ignorePayments);
                ContextVariables.setRetryAttempt(undefined);
                await ctx.flushRetryAttempts();
            }
        } finally {
            ContextVariables.cleanupRound();
        }
    }

    private getPaymentAttempts(ctx: GameFlowContext): number {
        return ctx?.gameData?.settings?.maxPaymentRetryAttempts;
    }

    private getLogoutAttempts(ctx: GameFlowContext): number {
        return ctx?.gameData?.logoutOptions?.maxRetryAttempts;
    }

    public async completePayment(context: GameFlowContext, flow?: EngineGameFlow) {
        const contextId: string = context.id && context.id.asString();
        if (!flow) {
            flow = await GameFlowFactory.createForRecovery(context);
        }
        try {
            await flow.checkPending();
        } catch (err) {
            log.error(err, "Error try to complete pending for %s", contextId);
            throw err;
        }
    }
}

export default new PendingCompletionService();
