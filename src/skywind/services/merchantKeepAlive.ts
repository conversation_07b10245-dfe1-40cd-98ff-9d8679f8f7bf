import { ManagementAPISupport } from "./managementapihelper";
import config from "../config";
import { lazy } from "@skywind-group/sw-utils";
import { EngineGameFlow } from "./gameflow";

class MerchantKeepAlive extends ManagementAPISupport {
    private static KEEP_ALIVE_PING_URL = "/v2/play/keepalive";
    constructor() {
        super(config.managementAPI.url);
    }

    public ping(flow: EngineGameFlow): Promise<void> {
        const gameTokenData = flow.flowContext.gameData.gameTokenData;
        const gameToken = gameTokenData.token;
        if (gameTokenData.playmode === "fun") {
            return;
        }
        return this.post<void>(MerchantKeepAlive.KEEP_ALIVE_PING_URL, { gameToken: gameToken });
    }
}

const merchantKeepAliveService = lazy(() => new MerchantKeepAlive());

export const getMerchantKeepAliveService = () => merchantKeepAliveService.get();
