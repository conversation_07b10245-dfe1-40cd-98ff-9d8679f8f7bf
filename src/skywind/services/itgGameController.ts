import {
    BalanceResponse,
    BaseRequest,
    ClientResponse,
    ContextUpdate,
    FreeBetsInfo,
    GameContext,
    GameExtensionModule,
    GameInfo,
    GamePlayRequest,
    GamePlayResponse,
    GameStateInfoRequest,
    GameWalletAction,
    GameWalletActionType,
    JackpotAction,
    JurisdictionSettings
} from "@skywind-group/sw-game-core";
import { ContextVariables } from "../utils/contextVariables";
import { ExtendedGameInitRequest } from "./context/gamecontext";
import { BrandSettings, GameSettings, Limits, Settings, StartGameTokenData } from "./tokens";
import { lazy, logging, measures } from "@skywind-group/sw-utils";
import { getService as getAuthService, StartGameResult } from "./auth";
import { getGameFlowContextManager } from "./contextmanager/contextManagerImpl";
import { GameContextID } from "./contextIds";
import { SyncGameController } from "./synccontroller";
import PendingCompletionService from "./pendingCompletionService";
import { GameFlowFactory, NO_GAME } from "./gameFlowFactory";
import { EngineGameFlow } from "./gameflow";
import * as Errors from "../errors";
import { JackpotStatusNotValid, NotImplementedError } from "../errors";
import { GameRecoveryService } from "./gamerecovery";
import { LoadedGameResult } from "./game/game";
import config from "../config";
import { initJackpot } from "./jpn/jackpot";
import {
    ContributionRequest,
    DeferredContributionResult,
    JackpotResult,
    JackpotWinEvent
} from "@skywind-group/sw-jpn-core";
import * as jwt from "jsonwebtoken";
import { instantJpExtension } from "./game/globalExtensions";
import { JackpotUtil } from "./jpn/jackpotUtil";
import { injectRandomGenerator } from "./random";
import { GameFinalizationType } from "@skywind-group/sw-wallet-adapter-core";
import measure = measures.measure;

const flowDataLogger = logging.logger("sw-slot-engine:itg-gamecontroller:flow");

type ITGExtendedGameInitRequest = ExtendedGameInitRequest & GameVersion;

interface GameVersion {
    gameVersion: string;
}

interface ITGClientResponse extends ClientResponse {
    limits?: Limits;
    settings?: Settings;
    jrsdSettings?: JurisdictionSettings;
    gameSettings?: GameSettings;
    brandSettings?: BrandSettings;
    playerCode?: string;
    jurisdictionCode?: string;
    playedFromCountry?: string;
    lastRequestId?: number;
}

export interface ITGStateResponse extends ITGClientResponse {
    gameId: string;
    gameState: any;
    jackpotValue?: number;
}

type ITGFreeBetsInfoResponse = ITGClientResponse & FreeBetsInfo;

export interface ITGDeferredUpdateRequest {
    request: "deferred-update";
    requestId: number;
    gameSession: string;
    gameState?: any;
    gameType?: "normal" | "freegame" | "bonusgame";
    gameStatus?: "settled" | "freegame" | "bonusgame";
    bet?: number;
    win?: number;
    finalizationType?: GameFinalizationType;
    history?: {
        type: "ITG";
        roundEnded: boolean;
        data: any;
    };
    freeBetCoin?: number;
}

interface ITGFreeBetInfoRequest extends GameInfo {
    request: "free-bet-info";
    gameSession: string;
    bet: number;
}

interface ITGDeferredContributionRequest {
    request: "jp-deferred-contribution";
    gameSession: string;
    bet: number;
}

export interface ITGDeferredContributionResponse extends ITGClientResponse {
    contributionResult: "win" | "no-win";
    jackpotValue: number;
}

export class ITGGameController extends SyncGameController {
    private readonly globalExtensionsBefore: GameExtensionModule[] = [];

    public process<T extends BaseRequest>(request: T): Promise<ITGClientResponse> {
        ContextVariables.setUpForRequest(request);
        const req = request as any;
        switch (request.request) {
            case "init":
                return this.initItgGame(req);
            case "state":
                return this.getStateForItg(req);
            case "deferred-update":
            case "start-instant-jp-mini-game":
            case "play-instant-jp-mini-game":
                return this.commitDeferredUpdate(req);
            case "jp-deferred-contribution":
                return this.deferredContribution(req);
            case "free-bet-info":
                return this.getFreeBetInfo(req);
            case "balance":
                return this.getPlayerBalance(req);
            case "player-action":
                return this.performPlayerAction(req);
            case "keep-alive":
                return this.keepAlive(req);
            case "jp-ticker":
                return this.getJackpotTicker(req);
            case "choose-game":
                return this.chooseGame(req);
            default:
                return Promise.reject(new NotImplementedError());
        }
    }

    @measure({ name: "ITGGameController.initItgGame", isAsync: true })
    public async initItgGame(request: ITGExtendedGameInitRequest): Promise<ITGClientResponse> {
        const startGameTokenData: StartGameTokenData = await this.validateInitRequest(request);
        ContextVariables.setUpForInit(startGameTokenData);
        const gameFlowContextManager = getGameFlowContextManager(startGameTokenData.playmode);
        const ctx = await gameFlowContextManager
            .findOrRestoreGameContext(GameContextID.create(startGameTokenData.gameCode,
                startGameTokenData.brandId,
                startGameTokenData.playerCode,
                request.deviceId, startGameTokenData.playmode));

        this.checkIfMerchantSessionStateful(ctx, startGameTokenData);

        if (ctx?.requireLogout) {
            await PendingCompletionService.completeBeforeRelaunch(ctx);
        }

        const { gameId, startGameToken, ip, language, deviceId, deviceData } = request;
        const startGameResult: StartGameResult = await getAuthService(startGameTokenData.playmode)
            .startGame(
                gameId,
                startGameToken,
                ip,
                undefined,
                language,
                deviceId,
                deviceData
            );
        const { gameData } = startGameResult;
        const [jpContext, jackpots] = await initJackpot(gameData);
        const game = this.createMockITGGameModule(request);
        const flow = await GameFlowFactory.createForInit(
            ctx,
            gameData,
            request,
            game,
            this.pushService,
            jpContext,
            jackpots,
            startGameTokenData.isNewSession,
        );
        injectRandomGenerator(flow);
        flowDataLogger.info(flow.getLogData(), "ITG Init Request");
        this.initExtensions(flow.flowContext.settings);
        const balance = await flow.getBalance();
        const initResponse = {
            ...await this.createItgDeferredUpdateResponse(flow),
            gameSession: flow.info().gameSessionId,
            balance,
            playerCode: gameData?.gameTokenData?.playerCode,
            limits: gameData?.limits,
            gameSettings: gameData?.gameSettings,
            jrsdSettings: gameData?.jrsdSettings,
            brandSettings: gameData?.brandSettings,
            jurisdictionCode: gameData?.jurisdictionCode,
            playedFromCountry: gameData?.playedFromCountry
        } as ITGClientResponse;
        await this.runExtensions((module) => module.afterInit(undefined, flow, initResponse["result"] as any));
        flowDataLogger.info({ response: initResponse }, "ITG Init Response");
        return initResponse;
    }

    private initExtensions(settings: Settings) {
        // Only the instant jackpot extension is currently supported
        if (settings.instantJpEnabled) {
            const module = instantJpExtension.get();
            if (!this.globalExtensionsBefore.includes(module)) {
                this.globalExtensionsBefore.push(module);
            }
        }
    }

    @measure({ name: "ITGGameController.getStateForItg", isAsync: true })
    public async getStateForItg(req: GameStateInfoRequest): Promise<ITGStateResponse> {
        const flow = await this.createFlowForRequest<GameStateInfoRequest>(req, false, true);
        injectRandomGenerator(flow);
        await this.runExtensions((module) => module.beforeStateInfo(undefined, flow));
        flowDataLogger.info(flow.getLogData(), "ITG GetState Request");
        const balance = req.getBalance && await this.getBalanceWithFreeBetInfo(flow);
        const { gameData, gameContext } = flow.flowContext;
        const stateResponse = {
            playerCode: gameData?.gameTokenData?.playerCode,
            gameId: gameData?.gameId,
            gameState: gameContext,
            limits: gameData?.limits,
            settings: gameData?.settings,
            gameSettings: gameData?.gameSettings,
            jrsdSettings: gameData?.jrsdSettings,
            brandSettings: gameData?.brandSettings,
            lastRequestId: flow.flowContext.lastRequestId,
            balance
        } as ITGStateResponse;
        const jackpots = flow.gameData.jackpots;
        // Only send the jackpot value for internal (non EHUB) jackpots
        const hasJackpots = !!(jackpots && jackpots.filter(jp => !["mwjp", "instant"].includes(jp.type)).length);
        if (hasJackpots) {
            stateResponse.jackpotValue = await this.getItgJackpotValue(flow);
        }
        flowDataLogger.info({ response: stateResponse }, "ITG GetState Response");
        return stateResponse;
    }

    @measure({ name: "ITGGameController.commitDeferredUpdate", isAsync: true })
    public async commitDeferredUpdate(req: ITGDeferredUpdateRequest): Promise<ITGClientResponse> {
        const isFinalizationUpdate = !!req.finalizationType;
        const flow = await this.createFlowForRequest<ITGDeferredUpdateRequest>(
            req,
            config.itgConcurrencyRequestCheckEnabled,
            false,
            isFinalizationUpdate
        );
        injectRandomGenerator(flow);
        const beforePlayResult = await this.runExtensions((module) => module.beforePlay(undefined, flow));
        if (isFinalizationUpdate) {
            return this.processItgFinalizationDeferredUpdate(flow, req);
        }
        return this.processItgDeferredUpdate(flow, req, beforePlayResult);
    }

    @measure({ name: "ITGGameController.deferredContribution", isAsync: true })
    public async deferredContribution(req: ITGDeferredContributionRequest): Promise<ITGDeferredContributionResponse> {
        const flow = await this.createFlowForRequest<ITGDeferredContributionRequest>(
            req,
            false,
            true
        );
        ContextVariables.setUpRound(flow.flowContext.roundId);
        flowDataLogger.info(flow.getLogData(), "ITG DeferredContribution Request");

        const isInstantJackpot = JackpotUtil.isInstantJackpot(flow);
        if (isInstantJackpot) {
            const response = {
                contributionResult: "no-win",
                jackpotValue: await this.getItgJackpotValue(flow)
            } as ITGDeferredContributionResponse;
            flowDataLogger.info({ response }, "ITG DeferredContribution Response");
            return response;
        }

        const jackpotAction = {
            type: "contribution",
            amount: req.bet,
            deferredWinsEnabled: true
        };
        const update = await flow.preparePending({ jackpotAction });
        const payload = update.jackpotPending.jackpotOperation.payload as ContributionRequest;
        const deferredContributionResult = payload.deferredContribution;
        const [decodedResult] = this.decodeDeferredContributionData(deferredContributionResult);
        if (this.checkIfDeferredContributionResultIsWin(decodedResult.gameResult)) {
            const jpOperationResult = await flow.jackpotFlow.jackpotService.processJackpotEvents(
                payload.transactionId,
                decodedResult.events
            );
            // Prepare win-confirm modification - can be changed later to win-rollback instead
            const jackpotPendingModification = await flow.jackpotPendingFactory.createConfirmWinModification(
                jpOperationResult.jackpot,
                flow.jackpotContext,
                jpOperationResult,
                true
            );
            await flow.updateJackpotPending(jackpotPendingModification);
            const response = {
                contributionResult: "win",
                jackpotValue: decodedResult.events
                    .filter((event) => event.type === "win")
                    .reduce((acc: number, curr: JackpotWinEvent) => acc + curr.amount, 0)
            } as ITGDeferredContributionResponse;
            flowDataLogger.info({ response }, "ITG DeferredContribution Response");
            return response;
        }
        // Don't send deferredWinsEnabled in the payload to /jpn/contribute
        delete payload.deferredWinsEnabled;
        await flow.updateJackpotPending(update.jackpotPending);
        const response = {
            contributionResult: "no-win",
            jackpotValue: await this.getItgJackpotValue(flow)
        } as ITGDeferredContributionResponse;
        flowDataLogger.info({ response }, "ITG DeferredContribution Response");
        return response;
    }

    private async getItgJackpotValue(flow: EngineGameFlow): Promise<number> {
        // ITG games will have only one jackpot with one pool each
        const [ticker] = await flow.jackpotTickers();
        const [pool] = Object.values(ticker.pools);
        return pool.amount;
    }

    private checkIfDeferredContributionResultIsWin(gameResult: JackpotResult) {
        if (!gameResult) {
            return false;
        }
        if (Array.isArray(gameResult)) {
            return gameResult.some(gr => gr.type === "win");
        } else {
            return gameResult.type === "win";
        }
    }

    private decodeDeferredContributionData(data: string): DeferredContributionResult[] {
        return (jwt.decode(data) as any).payload;
    }

    @measure({ name: "ITGGameController.getFreeBetInfo", isAsync: true })
    public async getFreeBetInfo(req: ITGFreeBetInfoRequest): Promise<ITGFreeBetsInfoResponse> {
        const flow = await this.createFlowForRequest<ITGFreeBetInfoRequest>(req, false, true);
        flowDataLogger.info(flow.getLogData(), "ITG GetFreeBetInfo Request");
        const freeBetInfo = await flow.getFreeBets(req);
        flowDataLogger.info({ response: freeBetInfo }, "ITG GetFreeBetInfo Response");
        return freeBetInfo as ITGFreeBetsInfoResponse;
    }

    public async internalEvent(request: GamePlayRequest): Promise<ClientResponse> {
        return Promise.reject(new Errors.InternalEventIsNotSupported());
    }

    protected async createFlowForRequest<T extends BaseRequest>(
        req: T,
        checkConcurrency?: boolean,
        checkPending?: boolean,
        isFinalizationUpdate?: boolean
    ): Promise<EngineGameFlow<T>> {
        return GameFlowFactory.createForRequest(
            req,
            NO_GAME,
            checkConcurrency,
            checkPending,
            true,
            isFinalizationUpdate
        );
    }

    private createMockITGGameModule(request: ITGExtendedGameInitRequest): LoadedGameResult {
        return {
            game: undefined,
            moduleName: {
                id: request.gameId,
                nameWithVersion: `${request.gameId}@${request.gameVersion}`,
                name: request.name,
                version: request.gameVersion
            }
        };
    }

    private async getBalanceWithFreeBetInfo(flow: EngineGameFlow): Promise<BalanceResponse> {
        const balance = await this.getBalance(flow);
        const totalBetMultiplier = flow?.gameData?.gameSettings?.totalBetMultiplier;
        if (balance.freeBets && totalBetMultiplier) {
            const freeBetInfo = await flow.getFreeBets({
                totalBetMultiplier: flow?.gameData?.gameSettings?.totalBetMultiplier,
                stakeAll: flow?.gameData?.limits?.["stakeAll"],
                skipCoinValidation: true
            });
            if (freeBetInfo) {
                balance.freeBets["coinValue"] = freeBetInfo.coin;
                balance.freeBets["totalBetMultiplier"] = totalBetMultiplier;
            }
        }
        return balance;
    }

    private async processItgDeferredUpdate(
        flow: EngineGameFlow<GamePlayRequest>,
        req: ITGDeferredUpdateRequest,
        result: GamePlayResponse
    ): Promise<ITGClientResponse> {
        try {
            ContextVariables.setUpRound(flow.flowContext.roundId);
            if (config.logLevel === "debug") {
                flowDataLogger.debug(flow.getLogData(), "ITG DeferredUpdate Request");
            } else {
                flowDataLogger.info(flow.getItgReducedLogData(), "ITG DeferredUpdate Request");
            }
            const isJackpotStateInvalid = !flow.validateJackpotState();
            if (isJackpotStateInvalid) {
                const isInstantJackpot = JackpotUtil.isInstantJackpotMiniGame(flow.flowContext);
                if (!isInstantJackpot || (isInstantJackpot && req.request !== "deferred-update")) {
                    return Promise.reject(new JackpotStatusNotValid());
                }
            }
            flow.deferredUpdate(this.prepareDeferredUpdate(req, flow));
            await flow.commitDeferredUpdate();
            const response = await this.createItgDeferredUpdateResponse(flow, result);
            flowDataLogger.info({ response }, "ITG DeferredUpdate Response");
            return response;
        } finally {
            flow.close();
        }
    }

    private async createItgDeferredUpdateResponse(
        flow: EngineGameFlow<GamePlayRequest>,
        result?: GamePlayResponse,
        isFinalization?: boolean
    ): Promise<ITGClientResponse> {
        const round = flow.flowContext.lastRound;
        const response = {
            currencyReplacement: flow.gameData.currencyReplacement,
            lastRequestId: flow.flowContext.lastRequestId,
            result: result || {
                request: flow.request().request,
                settings: flow.flowContext.settings,
                totalBet: round?.totalBet,
                totalWin: round?.totalWin
            },
            roundEnded: flow.flowContext.roundEnded,
            roundTotalBet: round?.totalBet,
            roundTotalWin: round?.totalWin,
            settings: flow.gameData.settings,
            balance: isFinalization ? await this.getBalanceForFinalization(flow) : await this.getBalance(flow),
            extraData: flow.getExtraData()
        } as any;

        const gameContext = await flow.gameContext();
        if (gameContext && gameContext["previousProcessSceneResult"]) {
            response.result["previousResult"] = gameContext["previousProcessSceneResult"];
        }

        const tickers = await this.gameJackpotTickers(flow);
        const jackpot = await this.gameJackpotResult(flow);
        this.responseWithJPInfo(response, tickers, jackpot);

        return response as ITGClientResponse;
    }

    private async getBalanceForFinalization(flow: EngineGameFlow): Promise<BalanceResponse> {
        try {
            return await this.getBalance(flow);
        } catch (error) {
            flowDataLogger.info({ error }, "Error on getting balance during finalization. Return zero balance.");
            return {
                currency: flow.gameData?.gameTokenData?.currency || flow.flowContext?.currencyForHistory,
                amount: 0,
                real: {
                    amount: 0
                },
                bonus: {
                    amount: 0
                }
            };
        }
    }

    private async processItgFinalizationDeferredUpdate(
        flow: EngineGameFlow<GamePlayRequest>,
        req: ITGDeferredUpdateRequest
    ): Promise<ITGClientResponse> {
        try {
            ContextVariables.setUpRound(flow.flowContext.roundId);
            flowDataLogger.info(flow.getLogData(), "ITG Finalization DeferredUpdate Request");
            if (!flow.validateJackpotState()) {
                return Promise.reject(new JackpotStatusNotValid());
            }
            await GameRecoveryService.applyFinalizationUpdate(
                flow,
                flow.flowContext,
                await flow.preparePending(this.prepareDeferredUpdate(req, flow)),
                req.finalizationType
            );
            const response = await this.createItgDeferredUpdateResponse(flow, undefined, true);
            flowDataLogger.info({ response }, "ITG Finalization DeferredUpdate Response");
            return response;
        } finally {
            flow.close();
        }
    }

    private cleanUpPreviousProcessSceneResult(req: ITGDeferredUpdateRequest,
                                              contextUpdate: ContextUpdate<GameContext>) {
        if (req.request === "deferred-update" && req.gameStatus === "settled") {
            if (contextUpdate?.context["previousProcessSceneResult"]) {
                contextUpdate.context["previousProcessSceneResult"] = undefined;
            }
        }
    }

    private prepareDeferredUpdate(req: ITGDeferredUpdateRequest, flow: EngineGameFlow<GamePlayRequest>) {
        const contextUpdate: ContextUpdate<GameContext> = {};
        if (req.gameState) {
            contextUpdate.context = req.gameState;
        }
        this.cleanUpPreviousProcessSceneResult(req, contextUpdate);
        if (req.history) {
            contextUpdate.history = req.history;
            if (req.history.data && req.finalizationType) {
                contextUpdate.history.data.isFinalization = true;
            }
        }
        if (req.bet !== undefined || req.win !== undefined) {
            contextUpdate.payment = {
                gameType: req.gameType,
                gameStatus: req.gameStatus,
                actions: this.createActions(req),
                roundEnded: req.history?.roundEnded ?? false
            };
            if (req.freeBetCoin) {
                if (this.isFreeSpinOrBonusGame(req.gameType)) {
                    contextUpdate.payment.freeBetMode = true;
                } else {
                    contextUpdate.payment.freeBetCoin = req.freeBetCoin;
                }
            }
        }
        const isInstantJackpot = JackpotUtil.isInstantJackpot(flow);
        if (isInstantJackpot && req.bet) {
            contextUpdate.jackpotAction = {
                type: "contribution",
                amount: req.bet,
            } as JackpotAction;
        }
        return contextUpdate;
    }

    private createActions(req: ITGDeferredUpdateRequest): GameWalletAction[] {
        const betAction = req.freeBetCoin && !this.isFreeSpinOrBonusGame(req.gameType) ?
                          "freebet" as GameWalletActionType :
                          "debit";
        return [
            {
                action: betAction,
                attribute: "balance",
                amount: req.bet || 0,
                changeType: "bet"
            },
            {
                action: "credit",
                attribute: "balance",
                amount: req.win || 0,
                changeType: "win"
            }
        ];
    }

    private isFreeSpinOrBonusGame(gameType: string): boolean {
        return gameType === "freegame" || gameType === "bonusgame";
    }

    private async runExtensions(action: (module: GameExtensionModule) => Promise<void | any>) {
        let result;
        for (const gm of this.globalExtensionsBefore) {
            const actionResult = await action(gm);
            if (actionResult) {
                result = actionResult;
            }
        }
        return result;
    }
}

const ITGGameControllerInstance = lazy(() => new ITGGameController());

export const getITGGameController = () => ITGGameControllerInstance.get();
