/**
 * Access to player wallet through 'sw-management-api' module
 */
import { ManagementAPISupport } from "./managementapihelper";
import { measures, publicId } from "@skywind-group/sw-utils";
import config from "../config";
import { GameContextNotExists, ValidationError } from "../errors";
import { GameContextID } from "./contextIds";
import { GameFlowContext } from "./context/gamecontext";
import * as request from "superagent";
import { PlayMode } from "./playMode";
import { ContextManager } from "./contextmanager/contextManager";
import { ContextUtil, getGameFlowContextManager } from "./contextmanager/contextManagerImpl";
import { PlayerContext } from "./playercontext/playerContext";
import { GameSessionData, verifySessionToken } from "./tokens";
import measure = measures.measure;

export interface BaseHistoryRequest {
    gameSessionId: string;
}

export interface GameHistoryRequest extends BaseHistoryRequest {
    offset?: number;
    limit?: number;
    ts?: string;
    ts__lt?: string;
    ts__lte?: string;
    ts__gt?: string;
    ts__gte?: string;
    roundId?: number;
    roundId__lt?: number;
    roundId__lte?: number;
    roundId__gt?: number;
    roundId__gte?: number;
    sortOrder?: "ASC" | "DESC";
    roundsForLiveGames?: boolean;
}

export interface RoundHistoryRequest extends BaseHistoryRequest {
    roundId: number;
    offset?: number;
    limit?: number;
    type__in?: string;
    isPayment?: boolean;
    isHidden__not?: boolean;
}

export interface EventHistoryRequest extends BaseHistoryRequest {
    roundId: number;
    eventId: number;
}

export interface EventsHistoryRequest extends BaseHistoryRequest {
    limit?: number;
    offset?: number;
    ts?: string;
    ts__lt?: string;
    ts__lte?: string;
    ts__gt?: string;
    ts__gte?: string;
    type__in?: string;
    isPayment?: boolean;
}

export interface GameVersionRequest extends BaseHistoryRequest {
    gameVersion?: string;
}

export interface ReplayGameVersionRequest {
    replayToken: string;
    gameVersion: string;
    gameCode: string;
}

export interface RoundHistory {
    roundId: any;
    gameId?: string;
    brandId: number;
    playerCode: string;
    deviceId?: string;
    gameCode: string;
    currency: string;
    isTest: boolean;
    bet: number;
    win: number;
    revenue?: number;
    totalEvents?: number;
    balanceBefore?: number;
    balanceAfter?: number;
    broken?: boolean;
    firstTs?: Date;
    ts?: Date;
    finished?: boolean;
    device?: string;
}

/**
 * Interface for game events.
 */
export interface EventHistory {
    roundId?: number;
    spinNumber: number;
    type?: string;
    gameVersion?: string;
    gameId?: string;
    endOfRound: boolean;
    walletTransactionId?: string;
    win: number;
    bet: number;
    balanceBefore: number;
    balanceAfter: number;
    ts: string;
    details?: Record<string, any>;
    result?: Record<string, any>;
    test: boolean;
    currency?: string;
}

export interface HistoryInfo {
    url: string;
    historyRenderType: number;
}

export interface EventHistoryDetails {
    roundId: number;
    spinNumber: number;
    gameId: string;
    gameVersion: string;
    details: string;
    initSettings: string;
    historyInfo?: HistoryInfo;
}

export interface EventHistoryDetailsWithRoundInfo extends EventHistory {
    roundId: number;
    spinNumber: number;
    gameId: string;
    gameVersion: string;
    initSettings: Record<string, any>;
    historyInfo?: HistoryInfo;
}

export interface PageInfo {
    limit: number;
    total: number;
    offset: number;
}

export interface GameVersionDetails {
    gameId: string;
    gameVersion: string;
    title: string;
    initSettings: Record<string, any>;
    jrsdSettings?: Record<string, any>;
    jurisdictionCode?: string;
}

export interface GameReplayDetails extends GameVersionDetails {
    settings?: Record<string, any>;
    gameSettings?: Record<string, any>;
    brandSettings?: Record<string, any>;
}

export interface GetGameContextsRequest {
    brandId: number;
    playerCode: string;
    gameCode: string;
}

const DEFAULT_SORT_ORDER = "DESC";

interface TSBoundParam {
    param: string;
    startHistoryDate: Date;
}

export class GameHistoryService extends ManagementAPISupport {
    private static HISTORY_URL = "/v1/history/rounds";
    private static EVENTS_URL = "/v1/history/events";
    private static REPLAY_URL = "/v1/history/replay";
    private static REPLAY_GAME_VERSION_URL = "/v1/history/replay/gameVersion";
    private static GAME_VERSION_URL = "/v1/history/gameVersion";
    private static HISTORY_APP_SPIN_DETAILS_URL = "/v1/history/gh-app/spin-details";
    private static HISTORY_APP_ROUND_DETAILS_URL = "/v1/history/gh-app/round-details";
    private static HISTORY_APP_ROUND_INFO = "/v1/history/gh-app/round-info";

    constructor(private readonly url: string,
                private readonly manager: ContextManager) {
        super(url);
    }

    private static defineLowerTsValue({ ts__gt, ts__gte }: GameHistoryRequest): TSBoundParam {
        const date = new Date();
        let timeBound: number = date.getTime() - config.playerHistoryPeriod;
        let paramName = "ts__gte";

        if (ts__gt) {
            const tb = Date.parse(ts__gt);
            if (tb > timeBound) {
                timeBound = tb;
                paramName = "ts__gt";
            }
        }

        if (ts__gte) {
            const tb = Date.parse(ts__gte);
            if (tb >= timeBound) {
                timeBound = tb;
                paramName = "ts__gte";
            }
        }

        date.setTime(timeBound);

        return {
            param: paramName,
            startHistoryDate: date
        };
    }

    private static requiresCurrencyReplacementInEvents(events: EventHistory[], context: GameFlowContext) {
        return events.length && context.gameData.currencyReplacement && events[0].currency;
    }

    private static requiresCurrencyReplacementInRounds(rounds: RoundHistory[], context: GameFlowContext) {
        return rounds.length && context.gameData.currencyReplacement;
    }

    @measure({ name: "GameHistoryService.getHistory", isAsync: true })
    public async getHistory(req: GameHistoryRequest): Promise<RoundHistory[]> {
        const tsBoundParam = GameHistoryService.defineLowerTsValue(req);
        const [context, playerContext] = await this.getContexts(req.gameSessionId);

        const roundHistoryData: any = {
            gameToken: context.gameData.gameTokenData.token,
            offset: 0,
            limit: req.limit,
            ts: req.ts,
            ts__lt: req.ts__lt,
            ts__lte: req.ts__lte,
            sortOrder: req.sortOrder || DEFAULT_SORT_ORDER,
        };
        if (req.roundsForLiveGames && !!context?.gameData?.settings?.providerSettings) {
            roundHistoryData.roundsForLiveGames = true;
        }

        roundHistoryData[tsBoundParam.param] = JSON.stringify(tsBoundParam.startHistoryDate).replace(/"/g, "");
        if (req.roundId) {
            roundHistoryData["roundId"] = req.roundId;
        }
        let result: RoundHistory[] = await this.get<RoundHistory[]>(GameHistoryService.HISTORY_URL, roundHistoryData);
        result = this.appendRoundFromContext(result, context, tsBoundParam.startHistoryDate, req);

        const anotherGameContextId = this.findContextIdInDifferentGameMode(playerContext, context);
        if (anotherGameContextId) {
            result = this.appendRoundFromContext(
                result,
                await this.manager.findGameContextWithoutRestoring(anotherGameContextId),
                tsBoundParam.startHistoryDate,
                req
            );
        }
        result = result.length > req.limit ? result.slice(0, req.limit) : result;

        if (GameHistoryService.requiresCurrencyReplacementInRounds(result, context)) {
            result.forEach(round => round.currency = context.gameData.currencyReplacement);
        }

        return result;
    }

    private appendRoundFromContext(rounds: RoundHistory[], context: GameFlowContext,
                                   threshold: Date, req: GameHistoryRequest): RoundHistory[] {
        if (context && context.round && !context.round.finishedAt) {
            const roundInfo = GameHistoryService.asRoundHistoryInfo(context);
            if (roundInfo.firstTs < threshold
                || (req.roundId && roundInfo.roundId !== req.roundId)
                || (req.ts__lt && roundInfo.firstTs >= new Date(req.ts__lt))
                || (req.ts__lte && roundInfo.firstTs > new Date(req.ts__lte))) {
                return rounds;
            }
            // tslint:disable-next-line:triple-equals
            const id = rounds.findIndex(item => item.roundId == roundInfo.roundId);
            if (id !== -1) {
                rounds[id] = roundInfo;
            } else {
                rounds = [roundInfo].concat(rounds);
            }
        }
        return rounds;
    }

    @measure({ name: "GameHistoryService.getRound", isAsync: true })
    public async getRound(req: RoundHistoryRequest): Promise<EventHistory[] & PageInfo> {
        const context = await ContextUtil.getGameContext(req.gameSessionId);
        const payload = {
            gameToken: context.gameData.gameTokenData.token,
            offset: req.offset,
            limit: req.limit
        } as any;
        if (req.type__in !== undefined) {
            payload.type__in = req.type__in;
        }
        if (req.isPayment !== undefined) {
            payload.isPayment = req.isPayment;
        }
        if (+req.roundId === +context.roundId) {
            payload.ts__gte = context.round.startedAt.toISOString();
        }
        if (req.isHidden__not !== undefined) {
            payload.isHidden__not = req.isHidden__not;
        }
        const result =
            await this.get<EventHistory[] & PageInfo>(`${GameHistoryService.HISTORY_URL}/${req.roundId}`, payload);

        if (GameHistoryService.requiresCurrencyReplacementInEvents(result, context)) {
            result.forEach(event => event.currency = context.gameData.currencyReplacement);
        }

        return result;
    }

    @measure({ name: "GameHistoryService.getEvent", isAsync: true })
    public async getEvent(req: EventHistoryRequest): Promise<EventHistoryDetails> {
        const context = await ContextUtil.getGameContext(req.gameSessionId);
        return this.get<EventHistoryDetails>(`${GameHistoryService.HISTORY_URL}/${req.roundId}/events/${req.eventId}`,
            { gameToken: context.gameData.gameTokenData.token });
    }

    @measure({ name: "GameHistoryService.getSpinDetailsForGHApp", isAsync: true })
    public async getSpinDetailsForGHApp(token: string): Promise<EventHistoryDetails> {
        if (!token) {
            return Promise.reject(new ValidationError("Token is missing"));
        }
        return this.get<EventHistoryDetails>(GameHistoryService.HISTORY_APP_SPIN_DETAILS_URL, { token });
    }

    @measure({ name: "GameHistoryService.getRoundDetailsForGHApp", isAsync: true })
    public async getRoundDetailsForGHApp(token: string, eventTypes: string): Promise<EventHistoryDetailsWithRoundInfo> {
        if (!token) {
            return Promise.reject(new ValidationError("Token is missing"));
        }
        return this.get<EventHistoryDetailsWithRoundInfo>(GameHistoryService.HISTORY_APP_ROUND_DETAILS_URL,
            { token, type__in: eventTypes });
    }

    @measure({ name: "GameHistoryService.getRoundInfoForGHApp", isAsync: true })
    public async getRoundInfoForGHApp(token: string): Promise<RoundHistory> {
        if (!token) {
            return Promise.reject(new ValidationError("Token is missing"));
        }

        return this.get<RoundHistory>(GameHistoryService.HISTORY_APP_ROUND_INFO, { token });
    }

    @measure({ name: "GameHistoryService.getEvents", isAsync: true })
    public async getEvents(req: EventsHistoryRequest): Promise<EventHistory[] & PageInfo> {
        const context = await ContextUtil.getGameContext(req.gameSessionId);
        const payload: any = {
            gameToken: context.gameData.gameTokenData.token,
            limit: req.limit,
            offset: req.offset,
            ts: req.ts,
            ts__lt: req.ts__lt,
            ts__lte: req.ts__lte,
            ts__gt: req.ts__gt,
            ts__gte: req.ts__gte,
            type__in: req.type__in,
            isPayment: req.isPayment,
        };
        if (!config.showAllEvents) {
            payload.isHidden__not = true;
        }
        return this.get<EventHistory[] & PageInfo>(GameHistoryService.EVENTS_URL, payload);
    }

    @measure({ name: "GameHistoryService.getReplayEvents", isAsync: true })
    public async getReplayEvents(replayToken: string): Promise<EventHistory[] & PageInfo> {
        return this.get<EventHistory[] & PageInfo>(GameHistoryService.REPLAY_URL, { sortOrder: "ASC", replayToken });
    }

    @measure({ name: "GameHistoryService.getGameVersion", isAsync: true })
    public async getGameVersion(req: GameVersionRequest): Promise<GameVersionDetails> {
        const context = await ContextUtil.getGameContext(req.gameSessionId);
        return this.get<GameVersionDetails>(GameHistoryService.GAME_VERSION_URL,
            {
                gameToken: context.gameData.gameTokenData.token,
                gameVersion: req.gameVersion || context.gameVersion
            });
    }

    @measure({ name: "GameHistoryService.getGameVersionForReplay", isAsync: true })
    public async getReplayGameVersion(req: ReplayGameVersionRequest): Promise<GameReplayDetails> {
        return this.get<GameReplayDetails>(GameHistoryService.REPLAY_GAME_VERSION_URL, req);
    }

    protected parseData(response: request.Response, body: any): any {
        const result: PageInfo = body;
        if (result) {
            const headers = response.header;
            if (headers && headers["x-paging-total"]) {
                result.total = +headers["x-paging-total"];
                result.limit = +headers["x-paging-limit"];
                result.offset = +headers["x-paging-offset"];
            }
        }

        return result;
    }

    private async getContexts(gameSessionId: string): Promise<[GameFlowContext, PlayerContext]> {
        const gameSessionData: GameSessionData = await verifySessionToken(gameSessionId);
        const id = GameContextID.createFromString(gameSessionData.id);
        const [gameContext, playerContext] = await this.manager.findContexts(id);

        if (!gameContext) {
            return Promise.reject(new GameContextNotExists());
        }

        return [gameContext, playerContext];
    }

    private findContextIdInDifferentGameMode(playerContext: PlayerContext, context: GameFlowContext): GameContextID {
        if (context && playerContext) {
            const id = context.id;
            const result = playerContext.activeGames.find((item) =>
                item.id.gameCode === id.gameCode &&
                item.id.deviceId === id.deviceId &&
                item.mode !== context.gameData.gameTokenData.playmode);
            if (result) {
                return result.id;
            }

            const brokenGameInfo = playerContext.brokenGames.find((item) =>
                item.id.gameCode === id.gameCode &&
                item.id.deviceId === id.deviceId &&
                item.mode !== context.gameData.gameTokenData.playmode);
            return brokenGameInfo?.id;
        }

        return undefined;
    }

    public static asRoundHistoryInfo(context: GameFlowContext): RoundHistory {
        let startedAt = context?.round?.startedAt;
        const walletOperation = context?.pendingModification?.walletOperation;
        if (!startedAt) {
            startedAt = walletOperation?.ts || context.updatedAt || context.createdAt;
        }
        // in the case of a pending win, the round object will not contain the totalBet and totalWin amounts,
        // but the win wallet operation itself will have them
        const bet = context?.round?.totalBet || walletOperation?.totalBet || 0;
        const win = context?.round?.totalWin || walletOperation?.totalWin || 0;
        return {
            roundId: publicId.instance.encode(+context.roundId),
            brandId: publicId.instance.encode(context.id.brandId),
            playerCode: context.id.playerCode,
            device: context.id.deviceId,
            gameCode: context.gameData.gameTokenData.gameCode,
            currency: PlayMode.getCurrency(context.gameData.gameTokenData),
            isTest: context.gameData.gameTokenData.test,
            bet: bet,
            win: win,
            revenue: bet - win,
            totalEvents: context?.round?.totalEvents || 0,
            balanceBefore: context?.round?.balanceBefore,
            balanceAfter: context?.round?.balanceAfter,
            broken: true,
            firstTs: new Date(startedAt),
            ts: undefined,
            finished: false,
        };
    }
}

export default new GameHistoryService(config.managementAPIGameHistory, getGameFlowContextManager());
