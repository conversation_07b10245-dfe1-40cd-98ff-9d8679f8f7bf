import config from "../config";
import * as request from "superagent";
import { ITGFinalizationError } from "../errors";
import { logging } from "@skywind-group/sw-utils";
import { ITGStateResponse } from "./itgGameController";

const log = logging.logger("sw-slot-engine:itg-http-gateway");

export interface ITGFinalizationRequest extends ITGStateResponse {
    gameSession: string;
    gameState: any;
    gameId: string;
    operatorSupportsFinalization?: boolean;
    brandFinalizationType?: string;
}

export class ITGHttpGateway {
    public async triggerItgFinalization(itgFinalizationRequest: ITGFinalizationRequest): Promise<void> {
        log.info(itgFinalizationRequest, "ITG Finalization Request");
        return this.post<void>(config.itgFinalizationUrl, itgFinalizationRequest);
    }

    private post<T>(url: string, req: any): Promise<T> {
        return this.processResponse<T>(request.post(`${config.itgGameServerBaseUrl}/${url}`).send(req));
    }

    private processResponse<T>(result: request.SuperAgentRequest): Promise<T> {
        return result
            .then(res => this.handleFinalizationResponse(res))
            .catch(err => this.handleFinalizationError(err));
    }

    private handleFinalizationResponse(res: request.Response) {
        if (res.status !== 200) {
            throw new ITGFinalizationError("Error when calling ITG's finalization endpoint");
        }
        return res.body;
    }

    private handleFinalizationError(error: request.ResponseError) {
        throw new ITGFinalizationError(error.message);
    }
}
