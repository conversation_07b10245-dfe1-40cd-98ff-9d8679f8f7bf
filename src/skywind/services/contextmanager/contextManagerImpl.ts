import { Command, CommandExecutor, PromisedCommand, SomeCommand } from "../command";
import { FunRedis, Redis } from "../../storage/redis";
import { getOfflineCommandExecutor } from "../offlinestorage/offlineCommandExecutor";
import { ActivityTracker, FunActivityTracker, GameActivityTrackerImpl } from "../activitytracker";
import { ContextManager } from "./contextManager";
import { GameContextID, PlayerContextID } from "../contextIds";
import { GameFlowContextImpl } from "../context/gameContextImpl";
import { PlayerContext, PlayerContextImpl } from "../playercontext/playerContext";
import { GameSession } from "../gameSession";
import { GameData } from "../auth";
import { JackpotContext } from "../jpn/jackpot";
import { GameSessionData, GameSettings, verifySessionToken } from "../tokens";
import { getGameContextDecoder } from "../encoder/contextEncoding";
import { PlayMode } from "../playMode";
import { createSessionHistory } from "../../history/history";
import { GameFlowContext, RequestContext } from "../context/gamecontext";
import { lazy, measures } from "@skywind-group/sw-utils";
import { JackpotShortInfo } from "@skywind-group/sw-jpn-core";
import { GameMode } from "@skywind-group/sw-game-core";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { RedisCommandExecutor } from "../redisCommandExecutor";
import {
    FunGameContextOfflineCommands,
    FunPlayerContextOfflineCommands,
    GameContextOfflineCommandsImpl,
    PlayerContextOfflineCommandsImpl
} from "../offlinestorage/offlineCommandsImpl";
import {
    DBArchivedContext,
    DBContext,
    DBPlayerContext,
    GameContextOfflineCommands,
    PlayerContextOfflineCommands,
    RecoveryType
} from "../offlinestorage/offlineCommands";
import {
    FunGameContextOnlineCommands,
    GameContextOnlineCommands,
    GameContextOnlineCommandsImpl,
    PlayMoneyGameContextOnlineCommands,
    StoreOptions
} from "../onlinestorage/gameContextCommands";
import {
    FunPlayerContextCommands,
    PlayerContextCommands,
    PlayerContextCommandsImpl
} from "../onlinestorage/playerContextCommands";
import { ModuleName } from "../game/gamemodule";
import { getPlayerContextDecoder } from "../playercontext/encoding/playerContextEncoding";
import { GameContextNotExists, InvalidContextCurrencyError } from "../../errors";
import { ExtraData } from "@skywind-group/sw-wallet-adapter-core";
import measure = measures.measure;

const defaultCoins = require("../../../../resources/defaultCoins.json");

/**
 * Game context manager implementation.
 * Provides methods for finding,creating and removgin context.
 *
 * Implementation details: Context is stored in redis hash with id as a convolution of
 * brandId, gameId, playerCode and deviceId.
 */
export class ContextManagerImpl implements ContextManager {
    constructor(private readonly commands: GameContextOnlineCommands,
                private readonly playerCommands: PlayerContextCommands,
                private readonly gameOfflineCommands: GameContextOfflineCommands,
                private readonly playerOfflineCommands: PlayerContextOfflineCommands,
                private readonly executor: CommandExecutor,
                private readonly offlineExecutor: CommandExecutor,
                private readonly tracker: ActivityTracker) {
    }

    @measure({ name: "ContextManager.findGameContextById", isAsync: true })
    public async findGameContextById(id: GameContextID,
                                     exclusiveLock: boolean = false,
                                     reactive: boolean = true): Promise<GameFlowContextImpl> {
        return this.findContexts(id, exclusiveLock, reactive).then(([context]) => context);
    }

    @measure({ name: "ContextManager.findPlayerContextById", isAsync: true })
    public async findPlayerContextById(id: PlayerContextID): Promise<PlayerContextImpl> {
        const playerContext: PlayerContextImpl = await this.executor.execute(this.playerCommands.load(id));
        if (playerContext) {
            playerContext.executor = this.executor;
            playerContext.commands = this.playerCommands;
        }

        return playerContext;
    }

    public async findContexts(id: GameContextID,
                              exclusiveLock: boolean = false,
                              reactive: boolean = true): Promise<[GameFlowContextImpl, PlayerContextImpl]> {
        const [gameFlowContext, playerContext] = await this.executor.execute(
            [this.commands.load(id, exclusiveLock, reactive), this.playerCommands.load(id.playerContextID)]);

        if (gameFlowContext) {
            gameFlowContext.executor = this.executor;
            gameFlowContext.commands = this.commands;
            gameFlowContext.playerContext = playerContext;
        }

        if (playerContext) {
            playerContext.commands = this.playerCommands;
            playerContext.executor = this.executor;
        }
        return [gameFlowContext, playerContext];
    }

    @measure({ name: "ContextManager.findOrCreateGameContext", isAsync: true, debugOnly: true })
    public async findOrCreateGameContext(id: GameContextID,
                                         session: GameSession,
                                         gameData: GameData,
                                         moduleName: ModuleName,
                                         requestCtx?: RequestContext,
                                         jackpotShortInfo?: JackpotShortInfo[],
                                         jpContext?: JackpotContext,
                                         isNewSession?: boolean): Promise<GameFlowContextImpl> {
        const [gameContext, playerContext] = await this.findContexts(id, true);

        // find player context in offline storage
        const findOfflineCommands: [SomeCommand<DBPlayerContext>, SomeCommand<DBContext>] = [
            !playerContext ? this.playerOfflineCommands.findPlayerContext(id.playerContextID) : undefined,
            !gameContext ? this.gameOfflineCommands.findContext(id) : undefined
        ];

        const [dbPlayerContext, dbGameContext]: [DBPlayerContext, DBContext] =
            await this.offlineExecutor.execute(findOfflineCommands);

        if (!gameContext && !dbGameContext && isNewSession === false) {
            return Promise.reject(new GameContextNotExists());
        }

        const merchantSessionId = gameContext?.gameData?.gameTokenData?.merchantSessionId ||
            dbGameContext?.merchantSessionId;

        const onlineCommands: SomeCommand<any>[] = [
            this.commands.checkMigrationStatus(id),
            gameContext || dbGameContext ?
            this.buildCreateContextFromCopy(id,
                gameContext || dbGameContext.data,
                session,
                gameData,
                moduleName,
                this.makeSettings(gameData, jackpotShortInfo),
                requestCtx,
                jpContext) :
            this.buildCreateNewContext(id,
                session,
                gameData,
                moduleName,
                this.makeSettings(gameData, jackpotShortInfo),
                requestCtx,
                jpContext),
            this.commands.trackActive(id, GameFlowContextImpl.getGameActivityTimeout(gameData)),
            !playerContext && dbPlayerContext ?
            this.playerCommands.save(id.playerContextID, dbPlayerContext) : undefined,
            this.playerCommands.activateGame(id.playerContextID, id, gameData.gameTokenData.playmode),
            this.playerCommands.trackActive(id.playerContextID, GameFlowContextImpl.getPlayerActivityTimeout(gameData)),
            (gameData.gameTokenData.merchantSessionId || merchantSessionId) &&
            this.commands.updateMerchantSessionLink(id,
                merchantSessionId,
                gameData.gameTokenData.merchantSessionId)
        ];

        const removeOfflineCommand = [
            dbPlayerContext ? this.playerOfflineCommands.removePlayerContext([id.playerContextID]) : undefined,
            dbGameContext ? this.gameOfflineCommands.remove([id]) : undefined
        ];

        const [, restoredGameContext, , , restoredPlayerContext] = await this.executor.execute(onlineCommands);
        await this.offlineExecutor.execute(removeOfflineCommand);

        if (restoredPlayerContext) {
            restoredPlayerContext.commands = this.playerCommands;
            restoredPlayerContext.executor = this.executor;
        }
        restoredGameContext.playerContext = restoredPlayerContext;

        return restoredGameContext;
    }

    @measure({ name: "ContextManager.findOrCreateGameContext", isAsync: true, debugOnly: true })
    public async createOrUpdate(id: GameContextID,
                                gameContext: GameFlowContext,
                                session: GameSession,
                                gameData: GameData,
                                moduleName: ModuleName,
                                requestCtx?: RequestContext,
                                jackpotShortInfo?: JackpotShortInfo[],
                                jpContext?: JackpotContext,
                                isNewSession?: boolean): Promise<GameFlowContextImpl> {

        if (!gameContext && isNewSession === false) {
            return Promise.reject(new GameContextNotExists());
        }
        // Support to not re-authenticate the pending payment on init if the operator wishes so
        const shouldUpdateGameData = !gameData.settings?.skipPendingPaymentReAuthentication ||
            !gameContext?.pendingModification?.walletOperation;
        const updatedGameData = shouldUpdateGameData ? gameData : gameContext.gameData;

        const merchantSessionId = gameContext?.gameData?.gameTokenData?.merchantSessionId;
        let checkVersionCommand: SomeCommand;
        if (gameContext) {
            checkVersionCommand = this.commands.checkVersion(gameContext, true, true);
        } else if (id) {
            const ctx = new GameFlowContextImpl(id);
            ctx.session = session;
            ctx.version = gameContext?.["version"] || 0;
            checkVersionCommand = this.commands.checkVersion(ctx, true, false);
        }
        const onlineCommands: SomeCommand<any>[] = [
            this.commands.checkMigrationStatus(id),
            checkVersionCommand,
            gameContext ?
            this.buildCreateContextFromCopy(id,
                gameContext,
                session,
                updatedGameData,
                moduleName,
                this.makeSettings(gameData, jackpotShortInfo),
                requestCtx,
                jpContext,
                true) :
            this.buildCreateNewContext(id,
                session,
                gameData,
                moduleName,
                this.makeSettings(gameData, jackpotShortInfo),
                requestCtx,
                jpContext),
            this.commands.trackActive(id, GameFlowContextImpl.getGameActivityTimeout(gameData)),
            this.playerCommands.activateGame(id.playerContextID, id, gameData.gameTokenData.playmode),
            this.playerCommands.trackActive(id.playerContextID, GameFlowContextImpl.getPlayerActivityTimeout(gameData)),
            (gameData.gameTokenData.merchantSessionId || merchantSessionId) &&
            this.commands.updateMerchantSessionLink(id, merchantSessionId, gameData.gameTokenData.merchantSessionId)
        ];

        const [, , restoredGameContext, , restoredPlayerContext] = await this.executor.execute(onlineCommands);

        if (restoredPlayerContext) {
            restoredPlayerContext.commands = this.playerCommands;
            restoredPlayerContext.executor = this.executor;
        }
        restoredGameContext.playerContext = restoredPlayerContext;

        return restoredGameContext;
    }

    public async findOrRestoreGameContext(id: GameContextID): Promise<GameFlowContextImpl> {
        const [gameContext, playerContext] = await this.findContexts(id);

        // find player context in offline storage
        const findOfflineCommands: [SomeCommand<DBPlayerContext>, SomeCommand<DBContext>] = [
            !playerContext ? this.playerOfflineCommands.findPlayerContext(id.playerContextID) : undefined,
            !gameContext ? this.gameOfflineCommands.findContext(id) : undefined
        ];

        const [dbPlayerContext, dbGameContext]: [DBPlayerContext, DBContext] =
            await this.offlineExecutor.execute(findOfflineCommands);

        return this.restoreGameContext(id, gameContext, dbGameContext, playerContext, dbPlayerContext);
    }

    public async findGameContextWithoutRestoring(id: GameContextID): Promise<GameFlowContext> {
        let gameContext = await this.findGameContextById(id);

        if (!gameContext) {
            const dbGameContext = await this.findOfflineGameContext(id);
            if (dbGameContext) {
                gameContext = new GameFlowContextImpl(id);
                await getGameContextDecoder().decode(gameContext, dbGameContext.data);
            }
        }

        return gameContext;
    }

    public async findOrRestoreGameByMerchantSessionId(brandId: number,
                                                      merchantSessionId: string): Promise<GameFlowContextImpl> {
        let playerContext: PlayerContextImpl;
        let dbGameContext: DBContext;
        let dbPlayerContext: DBPlayerContext;
        let playerContextId: PlayerContextID;
        let id: GameContextID;

        const gameContext: GameFlowContextImpl = await this.executor.execute(
            this.commands.loadByMerchantSessionId(brandId, merchantSessionId)
        );

        if (!gameContext) {

            dbGameContext = await this.offlineExecutor.execute(
                this.gameOfflineCommands.findContextByMerchantSessionId(brandId, merchantSessionId)
            );
            if (!dbGameContext) {
                return undefined;
            }
            id = GameContextID.createFromString(dbGameContext.id);
            playerContextId = dbGameContext && id.playerContextID;
        } else {
            id = gameContext.id;
            playerContextId = id.playerContextID;
        }

        playerContext = await this.findPlayerContextById(playerContextId);

        if (!playerContext) {
            dbPlayerContext = await this.offlineExecutor
                .execute(this.playerOfflineCommands.findPlayerContext(playerContextId));
        }

        return this.restoreGameContext(id, gameContext, dbGameContext, playerContext, dbPlayerContext);
    }

    private async restoreGameContext(id: GameContextID,
                                     gameContext: GameFlowContextImpl,
                                     dbGameContext: DBContext,
                                     playerContext: PlayerContextImpl,
                                     dbPlayerContext: DBPlayerContext) {
        const lastMerchantSessionId = gameContext?.gameData?.gameTokenData?.merchantSessionId
            || dbGameContext?.merchantSessionId;

        const hasGameContext: boolean = !!gameContext || !!dbGameContext;
        const hasPlayerContext: boolean = !!playerContext || !!dbPlayerContext;
        let restoreGameContextCmd: Command<GameFlowContext>;
        [gameContext, restoreGameContextCmd] =
            !gameContext && dbGameContext ? await this.buildCreateFromDBContext(id, dbGameContext.data) :
                [gameContext, undefined];

        const onlineCommands: SomeCommand<any>[] = [
            this.commands.checkMigrationStatus(id),
            hasGameContext ?
            this.commands.checkVersion(gameContext || dbGameContext.data, true, !!(!gameContext && dbGameContext)) :
            undefined,
            restoreGameContextCmd,
            hasGameContext ? this.commands.trackActive(id, GameFlowContextImpl.getGameActivityTimeout()) : undefined,
            hasPlayerContext ?
            this.playerCommands.trackActive(id.playerContextID, GameFlowContextImpl.getPlayerActivityTimeout()) :
            undefined,
            !playerContext && !!dbPlayerContext ?
            this.playerCommands.save(id.playerContextID, dbPlayerContext) : undefined,
            hasGameContext ?
            this.playerCommands.activateGame(id.playerContextID, id, gameContext?.playMode) :
            undefined,
            hasGameContext && lastMerchantSessionId ?
            this.commands.updateMerchantSessionLink(id, lastMerchantSessionId, lastMerchantSessionId) : undefined
        ];

        const removeOfflineCommand = [
            this.playerOfflineCommands.removePlayerContext([id.playerContextID]),
            this.gameOfflineCommands.remove([id])
        ];

        const [, , restoredGameContext, , , , restoredPlayerContext] = await this.executor.execute(onlineCommands);
        await this.offlineExecutor.execute(removeOfflineCommand);

        gameContext = gameContext || restoredGameContext;

        if (gameContext) {
            playerContext = restoredPlayerContext || playerContext;
            if (playerContext) {
                playerContext.commands = this.playerCommands;
                playerContext.executor = this.executor;

                gameContext.playerContext = playerContext;
            }
            gameContext.commands = this.commands;
            gameContext.executor = this.executor;
        }

        return gameContext;
    }

    public async createContextFrom(contextID: GameContextID,
                                   copy: any,
                                   newSession: GameSession,
                                   startGameData: GameData,
                                   moduleName: ModuleName,
                                   newSettings: GameSettings,
                                   requestCtx?: RequestContext,
                                   newJPContext?: JackpotContext): Promise<GameFlowContextImpl> {
        const [result, _] = await this.executor.execute(
            [
                this.buildCreateContextFromCopy(contextID,
                    copy,
                    newSession,
                    startGameData,
                    moduleName,
                    newSettings,
                    requestCtx,
                    newJPContext),
                this.commands.trackActive(contextID, GameFlowContextImpl.getGameActivityTimeout(startGameData)),
                this.playerCommands.activateGame(contextID.playerContextID, contextID, "real"),
                this.playerCommands.trackActive(contextID.playerContextID,
                    GameFlowContextImpl.getPlayerActivityTimeout(startGameData))
            ]);

        return result;
    }

    /**
     * Compares currency code in context with auth token, will throw error on distinguish.
     *
     * @param {GameFlowContext} context
     * @param {GameData} gameData
     * @throws InvalidContextCurrencyError
     */
    private checkContextCurrency(context: GameFlowContext, gameData: GameData) {
        const contextCurrency = context?.gameData?.gameTokenData?.currency;

        if (!contextCurrency) {
            return;
        }

        const tokenCurrency = gameData?.gameTokenData?.currency;

        if (!tokenCurrency) {
            return;
        }

        if (tokenCurrency !== contextCurrency) {
            const message = "You have an unfinished game with currency {key1}, please finish it first before " +
                "starting a new game with currency {key2}";
            const extraData: ExtraData = {
                messageArray: [
                    {
                        message,
                        textReplacements: {
                            key1: contextCurrency,
                            key2: tokenCurrency
                        },
                        translate: true
                    }
                ]
            };
            throw new InvalidContextCurrencyError(extraData);
        }
    }

    private async buildCreateContextFromCopy(contextID: GameContextID,
                                             copy: any,
                                             newSession: GameSession,
                                             startGameData: GameData,
                                             moduleName: ModuleName,
                                             newSettings: GameSettings,
                                             requestCtx?: RequestContext,
                                             newJPContext?: JackpotContext,
                                             checkIfExists: boolean = false): PromisedCommand<GameFlowContextImpl> {
        const result: GameFlowContextImpl = new GameFlowContextImpl(contextID);
        if (!(copy instanceof GameFlowContextImpl)) {
            await getGameContextDecoder().decode(result, copy);
        } else {
            Object.assign(result, copy);
        }

        this.checkContextCurrency(result, startGameData);
        const { roundHistory, gameEventHistory } = PlayMode.clearContextAndCreateHistoryIfNeeded(result, startGameData);

        result.id = contextID;
        result.commands = this.commands;
        result.executor = this.executor;
        result.session = newSession;
        result.lastRequestId = 0;
        result.gameData = startGameData;
        result.settings = newSettings;
        result.gameVersion = moduleName?.version || "1.0.0";
        result.requestContext = requestCtx;

        if (newJPContext) {
            result.jpContext = result.jpContext || newJPContext;
            result.jpContext.jackpotsInfo = newJPContext.jackpotsInfo;
            result.jpContext.token = newJPContext.token;
        } else {
            result.jpContext = undefined;
        }

        if (result.roundId === undefined) {
            result.roundId = await this.commands.generateRoundId();
        }

        if (result.retryAttempts) {
            result.retryAttempts = 0;
        }

        result.createdAt = new Date();

        const storeSession = result.session.sessionId !== copy?.session?.sessionId;
        const sessionStart = storeSession ? createSessionHistory(result) : undefined;
        const sessionEnd = storeSession && copy ? createSessionHistory(copy, copy.updatedAt) : undefined;

        return this.commands.store(result,
            true,
            StoreOptions.ALL,
            { sessionStart, sessionEnd, item: gameEventHistory, round: roundHistory },
            checkIfExists);
    }

    private async buildCreateNewContext(contextID: GameContextID,
                                        session: GameSession,
                                        startGameData: GameData,
                                        moduleName: ModuleName,
                                        settings: GameSettings,
                                        requestCtx: RequestContext,
                                        jpContext?: JackpotContext): PromisedCommand<GameFlowContextImpl> {
        const context: GameFlowContextImpl = new GameFlowContextImpl(contextID);
        context.commands = this.commands;
        context.executor = this.executor;
        context.session = session;
        context.lastRequestId = 0;
        context.settings = settings;
        context.gameContext = undefined;
        context.gameData = startGameData;
        context.gameSerialNumber = 0;
        context.totalEventId = 0;
        context.version = 0;
        context.roundId = await this.commands.generateRoundId();
        context.roundEnded = true;
        context.jpContext = jpContext;
        context.gameVersion = moduleName?.version || "1.0.0";
        context.requestContext = requestCtx;
        context.createdAt = new Date();

        const sessionStart = createSessionHistory(context);

        return this.commands.store(context, true, StoreOptions.ALL, { sessionStart }, false);
    }

    private async buildCreateFromDBContext(id: GameContextID,
                                           data: any): Promise<[GameFlowContextImpl, Command<GameFlowContext>]> {
        const result = new GameFlowContextImpl(id);
        await getGameContextDecoder().decode(result, data);
        result.session.markFinished();
        result.executor = this.executor;
        result.commands = this.commands;
        return [result, await this.commands.store(result, true, StoreOptions.ALL, undefined, false)];
    }

    private makeSettings(gameData: GameData, jackpotShortInfo: JackpotShortInfo[]): GameSettings {
        const settings: any = { ...gameData.settings, ...gameData.limits };

        if (!settings.defaultCoin) {
            settings.defaultCoin = defaultCoins.defaultCoin;
        }
        if (!settings.coins) {
            settings.coins = defaultCoins.coins;
        }

        const currency = Currencies.get(PlayMode.getCurrency(gameData.gameTokenData));
        if (currency.clientMoneyFormat) {
            settings.moneyFormat = currency.clientMoneyFormat;
        }
        settings.currencyMultiplier = currency.multiplier;

        if (jackpotShortInfo) {
            settings.jackpots = jackpotShortInfo;
        }

        return settings;
    }

    public getLruGameContextIDs(maxTimestamp: number, limit: number): Promise<string[]> {
        return this.tracker.getLruGameContextIDs(maxTimestamp, limit);
    }

    public getLruPlayerContextIDs(maxTimestamp: number, limit: number): Promise<string[]> {
        return this.tracker.getLruPlayerContextIDs(maxTimestamp, limit);
    }

    public findTopActiveGameContextIDsByBrand(brandId: number, limit: number) {
        return this.tracker.findTopActiveGameContextIDsByBrand(brandId, limit);
    }

    public findTopActivePlayerContextIDsByBrand(brandId: number, limit: number) {
        return this.tracker.findTopActivePlayerContextIDsByBrand(brandId, limit);
    }

    public removeGameContextActivity(id: string, ts: number): Promise<void> {
        return this.tracker.removeGameContextActivity(id, ts);
    }

    public findOfflineGameContext(id: GameContextID): Promise<DBContext> {
        return this.offlineExecutor.execute(this.gameOfflineCommands.findContext(id));
    }

    public async findOfflinePlayerContext(id: PlayerContextID): Promise<PlayerContext> {
        const dbPlayerContext = await this.offlineExecutor.execute(this.playerOfflineCommands.findPlayerContext(id));
        return dbPlayerContext && getPlayerContextDecoder().decodeDBData(new PlayerContextImpl(id), dbPlayerContext);
    }

    public saveGameContextOffline(contexts: GameFlowContextImpl[], requireCompletion: boolean = false): Promise<any> {
        return this.offlineExecutor.execute(contexts.map(c => this.gameOfflineCommands.save(c, requireCompletion)));
    }

    public removeGameContextOffline(ids: GameContextID[]): Promise<void> {
        return this.offlineExecutor.execute(this.gameOfflineCommands.remove(ids));
    }

    public archiveGameContext(context: GameFlowContext | DBArchivedContext,
                              recoveryType?: RecoveryType): Promise<void> {
        return this.offlineExecutor.execute(this.gameOfflineCommands.archive(context, recoveryType));
    }

    public findExpiredGameContext(expiredAt: Date, limit: number): Promise<DBContext[]> {
        return this.offlineExecutor.execute(this.gameOfflineCommands.findExpired(expiredAt, limit));
    }

    public updateExpirationDate(ctx: DBContext, expiredAt: Date): Promise<any> {
        return this.offlineExecutor.execute(this.gameOfflineCommands.updateGameContextExpiration(ctx, expiredAt));
    }

    public savePlayerContextOffline(contexts: PlayerContext[]): Promise<any> {
        return this.offlineExecutor.execute(
            contexts.map(c => this.playerOfflineCommands.savePlayerContext(c)));
    }

    public removePlayerContextOffline(contexts: PlayerContext[]): Promise<void> {
        return this.offlineExecutor.execute(
            this.playerOfflineCommands.removePlayerContext(contexts.map(c => c.id)));
    }

    public findGameContextsForCompletion(limit: number): Promise<DBContext[]> {
        return this.offlineExecutor.execute(this.gameOfflineCommands.findGameContextsForCompletion(limit));
    }

    public findGameContextByRoundId(roundId: number): Promise<DBContext> {
        return this.offlineExecutor.execute(this.gameOfflineCommands.findGameContextByRoundId(roundId));
    }
}

const mainManager = lazy(() => new ContextManagerImpl(
    new GameContextOnlineCommandsImpl(),
    new PlayerContextCommandsImpl(),
    new GameContextOfflineCommandsImpl(),
    new PlayerContextOfflineCommandsImpl(),
    new RedisCommandExecutor(Redis),
    getOfflineCommandExecutor(),
    new GameActivityTrackerImpl(),
));
const funManager = lazy(() => new ContextManagerImpl(
    new FunGameContextOnlineCommands(),
    new FunPlayerContextCommands(),
    new FunGameContextOfflineCommands(),
    new FunPlayerContextOfflineCommands(),
    new RedisCommandExecutor(FunRedis),
    getOfflineCommandExecutor(),
    new FunActivityTracker()));

const playMoneyManager = lazy(() => new ContextManagerImpl(
    new PlayMoneyGameContextOnlineCommands(),
    new PlayerContextCommandsImpl(),
    new GameContextOfflineCommandsImpl(),
    new PlayerContextOfflineCommandsImpl(),
    new RedisCommandExecutor(Redis),
    getOfflineCommandExecutor(),
    new GameActivityTrackerImpl(),
));

export function getGameFlowContextManager(gameMode?: GameMode): ContextManagerImpl {
    switch (gameMode) {
        case "fun":
            return funManager.get();
        case "play_money":
            return playMoneyManager.get();
        default:
            return mainManager.get();
    }
}

export class ContextUtil {
    public static async getGameContext(gameSessionId: string, gameMode?: GameMode): Promise<GameFlowContext> {
        const gameSessionData: GameSessionData = await verifySessionToken(gameSessionId);
        const id = GameContextID.createFromString(gameSessionData.id);
        const contextManager = getGameFlowContextManager(gameMode);
        const context = await contextManager.findGameContextById(id);
        if (!context) {
            return Promise.reject(new GameContextNotExists());
        }
        return context;
    }
}
