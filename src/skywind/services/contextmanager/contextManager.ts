import { ActivityTracker } from "../activitytracker";
import { GameContextID, PlayerContextID } from "../contextIds";
import { PlayerContext } from "../playercontext/playerContext";
import { GameSession } from "../gameSession";
import { GameData } from "../auth";
import { ModuleName } from "../../../";
import { JackpotContext } from "../jpn/jackpot";
import { GameFlowContext, RequestContext } from "../context/gamecontext";
import { JackpotShortInfo } from "@skywind-group/sw-jpn-core";
import { DBArchivedContext, DBContext, RecoveryType } from "../offlinestorage/offlineCommands";

/**
 *  Manage Game context;
 */
export interface ContextManager extends ActivityTracker {
    /**
     * Find game context in redis
     */
    findGameContextById(id: GameContextID, exclusiveLock?: boolean, reactive?: boolean): Promise<GameFlowContext>;

    /**
     * Find player context in redis
     */
    findPlayerContextById(id: PlayerContextID): Promise<PlayerContext>;

    /**
     * Find game and player contexts in redis
     */
    findContexts(id: GameContextID): Promise<[GameFlowContext, PlayerContext]>;

    /**
     * Find or create context in redis
     *
     */
    findOrCreateGameContext(id: GameContextID,
                            session: GameSession,
                            gameData: GameData,
                            moduleName: ModuleName,
                            requestCtx?: RequestContext,
                            jackpotShortInfo?: JackpotShortInfo[],
                            jpContext?: JackpotContext): Promise<GameFlowContext>;

    /**
     * Find context in redis or restore it from Postgres
     */
    findOrRestoreGameContext(id: GameContextID): Promise<GameFlowContext>;

    /**
     * Find context either in Redis or in Postgres.
     * If it's in postgres - do not restore it back to redis
     */
    findGameContextWithoutRestoring(id: GameContextID): Promise<GameFlowContext>;

    /**
     * Find context in redis or restore it from Postgres
     */
    findOrRestoreGameByMerchantSessionId(brandId: number, merchantSessionId): Promise<GameFlowContext>;

    /**
     * Find game context in Postgres
     */
    findOfflineGameContext(id: GameContextID): Promise<DBContext>;

    /**
     * Find player context in Postgres
     */
    findOfflinePlayerContext(id: PlayerContextID): Promise<PlayerContext>;

    /**
     *  Save game contexts in Postgres
     */
    saveGameContextOffline(contexts: GameFlowContext[], requireCompletion?: boolean): Promise<void>;

    /**
     * Save player contexts in Postgres
     */
    savePlayerContextOffline(contexts: PlayerContext[]): Promise<void>;

    /**
     * Remove player contexts from Postgres
     */
    removePlayerContextOffline(contexts: PlayerContext[]): Promise<void>;

    /**
     * Remove game contexts from Postgres
     */
    removeGameContextOffline(ids: GameContextID[]): Promise<void>;

    /**
     * Archive game contexts in a separate table in Postgres
     */
    archiveGameContext(context: GameFlowContext | DBArchivedContext, recoveryType?: RecoveryType): Promise<void>;

    /**
     * Find game contexts that should be expired
     */
    findExpiredGameContext(expiredAt: Date, limit: number): Promise<DBContext[]>;

    updateExpirationDate(ctx: DBContext, expiredAt: Date): Promise<any>;

    /**
     * Find game contexts that require completion
     */
    findGameContextsForCompletion(limit: number): Promise<DBContext[]>;

    /**
     * Find game context in Postgres by round ID
     * @param roundId
     */
    findGameContextByRoundId(roundId: number): Promise<DBContext>;
}
