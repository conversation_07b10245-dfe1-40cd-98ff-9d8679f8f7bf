import { GameMode } from "@skywind-group/sw-game-core";
import config from "../config";

const playerContextField = Symbol("playerContextID");

export class GameContextID {
    private [playerContextField]: PlayerContextID;

    private constructor(public readonly gameCode: string,
                        public readonly brandId: number,
                        public readonly playerCode: string,
                        public readonly deviceId: string,
                        private readonly idValue: string) {
        this[playerContextField] = PlayerContextID.create(brandId, playerCode);
    }

    public static createFromString(id: string): GameContextID {
        const ids: string[] = id.split(":");

        return new GameContextID(ids[ids.length - 2],
            parseInt(ids[ids.length - 4], 10),
            ids[ids.length - 3],
            ids[ids.length - 1],
            id);
    }

    public static create(gameCode: string,
                         brandId: number,
                         playerCode: string,
                         deviceId: string,
                         playMode: GameMode = "real") {
        const prefix = config.namespaces.contextPrefix[playMode || "real"];
        return new GameContextID(gameCode,
            brandId,
            playerCode,
            deviceId,
            `${prefix}:${brandId}:${playerCode}:${gameCode}:${deviceId}`);
    }

    public static byBrandMatch(brandId: number, playMode: GameMode): [string, string] {
        return [
            `[${config.namespaces.contextPrefix[playMode]}:${brandId}:`,
            `[${config.namespaces.contextPrefix[playMode]}:${brandId}Z`
        ];
    }

    public asString(): string {
        return this.idValue;
    }

    public get playerContextID() {
        return this[playerContextField];
    }
}

export class PlayerContextID {
    private constructor(public readonly brandId: number,
                        public readonly playerCode: string,
                        private readonly idValue: string) {
    }

    public asString(): string {
        return this.idValue;
    }

    public static createFromString(id: string): PlayerContextID {
        const ids: string[] = id.split(":");

        return new PlayerContextID(parseInt(ids[ids.length - 2], 10), ids[ids.length - 1], id);
    }

    public static create(brandId: number,
                         playerCode: string): PlayerContextID {
        return new PlayerContextID(brandId, playerCode,
            `${config.namespaces.playerContextPrefix}:${brandId}:${playerCode}`);
    }

    public static byBrandMatch(brandId: number): [string, string] {
        return [
            `[${config.namespaces.playerContextPrefix}:${brandId}:`,
            `[${config.namespaces.playerContextPrefix}:${brandId}Z`
        ];
    }
}
