import { KafkaQueue } from "./kafkaQueue";
import config from "../../config";
import { GameEventHistory, RoundHistory, SessionHistory } from "../../history/history";

export class KafkaRoundQueue extends KafkaQueue<RoundHistory> {
    constructor() {
        super(config.unloader.roundHistoryKafkaConsumer.topicName,
            config.unloader.roundHistoryKafkaConsumer.kafkaBrokerHostnames,
            config.unloader.kafkaToDBConfig.rounds,
            config.unloader.maxSpinPopDuration);
    }
}

export class KafkaGameHistoryQueue extends KafkaQueue<GameEventHistory> {
    constructor() {
        super(config.unloader.gameHistoryKafkaConsumer.topicName,
            config.unloader.gameHistoryKafkaConsumer.kafkaBrokerHostnames,
            config.unloader.kafkaToDBConfig.gameHistory,
            config.unloader.maxSpinPopDuration);
    }
}

export class KafkaSessionQueue extends KafkaQueue<SessionHistory> {
    constructor() {
        super(config.unloader.sessionHistoryKafkaConsumer.topicName,
            config.unloader.sessionHistoryKafkaConsumer.kafkaBrokerHostnames,
            config.unloader.kafkaToDBConfig.sessions,
            config.unloader.maxSpinPopDuration);
    }
}
