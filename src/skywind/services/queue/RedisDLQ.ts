import { Lazy, redis } from "@skywind-group/sw-utils";
import { QueueItem } from "./queue";

/**
 * Redis DLQ (Dead Letter Queue)
 * Used to store items which could not be processed by Kafka when unloading from Redis to Kafka
 */
export class RedisDLQ<T> {
    private name: string;
    constructor(private readonly redisPool: Lazy<redis.RedisPool<redis.RedisClient>>) {}
    public async push(item: QueueItem<T>): Promise<void> {
        const redis = await this.redisPool.get().get();
        try {
            await redis.lpush(this.listName, JSON.stringify(item));
        } finally {
            await this.redisPool.get().release(redis);
        }
    }

    public get listName(): string {
        return this.name;
    }

    public set listName(name: string) {
        this.name = name;
    }
}
