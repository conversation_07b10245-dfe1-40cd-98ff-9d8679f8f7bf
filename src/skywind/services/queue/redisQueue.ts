import { ChainableCommander, Redis as RedisClient } from "ioredis";
import { Lazy, logging, measures, redis, sleep } from "@skywind-group/sw-utils";
import { Redis } from "../../storage/redis";
import config from "../../config";
import { CleanupContextRequest, GameEventHistory, RoundHistory, SessionHistory } from "../../history/history";
import * as uuid from "uuid";
import { Metrics } from "../../utils/common";
import { AnalyticsData } from "../analytics/analyticsService";
import { RedisProc } from "../redisCommandExecutor";
import { IQueue, QueueItem } from "./queue";

const log = logging.logger("sw-slot-engine:unload-history");

/*export interface QueueItem<T> {
    data: T;
    value: string;
}*/

interface WorkerAccessStat {
    [worker: string]: string;
}

/**
 * Base queue for game events
 *
 * It encapsulates 3 different Redis lists:
 *
 * 1) main event list where  we put our game events when update game context
 * 2) worker list, that helps to implement reliable queue processing on top of Redis
 * 3) duplicate list is used for storing events for which we have found duplicates(it's possible due to service restart)
 *
 */
export class RedisQueue<T> implements IQueue<T> {
    private workerId: string;
    public workerList: string;
    private popProc = new RedisProc(__dirname + "/../../../../resources/lua/queue.pop.lua");
    private copyListProc = new RedisProc(__dirname + "/../../../../resources/lua/queue.copyList.lua");
    private activeBatch: QueueItem<T, string>[];

    constructor(public readonly listName: string,
                private workerPrefixName: string,
                private redisPool: Lazy<redis.RedisPool<RedisClient>>,
                private maxSpinPopDuration: number = config.unloader.maxSpinPopDuration,
                private spinPopSleepDuration: number = config.unloader.spinPopSleepDuration) {
        this.initWorker();
    }

    /**
     * Saves event in Redis in transaction
     */
    public save(multi: ChainableCommander, data: T): void {
        multi.lpush(this.listName, JSON.stringify((data)));
    }

    /**
     * Gets bunch of events from queue.
     * In this methods events is moved to worker list and is returned.
     *
     * If there is no events, we will "wait" until we will get some.
     *
     * @param batchSize the max amount of events we want to get.
     */
    public async pop(batchSize: number = 1): Promise<QueueItem<T, string>[]> {
        if (this.activeBatch) {
            throw new Error("Wrong, state. Active batch is not committed!");
        }
        const redis = await this.redisPool.get().get();
        try {
            await this.updateLastAccessTs(redis);
            const result: QueueItem<T, string>[] = await this.spinPopData(redis, batchSize);
            if (result.length === 0) {
                const data = await this.waitForData(redis);
                result.push(data);
            }
            this.activeBatch = result;
            return result;
        } finally {
            await this.redisPool.get().release(redis);
        }
    }

    /**
     * Remove element from worker list
     */
    public async commit(): Promise<void> {
        if (this.activeBatch) {
            const redis = await this.redisPool.get().get();
            try {
                await redis.del(this.workerList);
                this.activeBatch = undefined;
            } finally {
                await this.redisPool.get().release(redis);
            }
        }
    }

    /**
     * Try to repair old orphan worker list by checking  theirs access timestamp
     */
    public async tryToRepairOrphanWorkers(ts: number): Promise<void> {
        const redis = await this.redisPool.get().get();
        try {
            const orphanWorkers = await this.getOrphanWorkers(redis, ts);
            if (orphanWorkers.length > 0) {
                for (const w of orphanWorkers) {
                    log.warn("Found 'orphan' worker %s", w);
                    const counter = await this.repairWorker(redis, w);
                    measures.measureProvider.incrementGauge(Metrics.REPAIRED_ITEMS, counter);
                    log.warn("Repaired elements for worker %s = %s", w, counter);
                }
            }
        } finally {
            await this.redisPool.get().release(redis);
        }
    }

    public async repair(): Promise<void> {
        const redis = await this.redisPool.get().get();
        try {
            this.activeBatch = undefined;
            await this.repairWorker(redis, this.workerId);
        } catch (err) {
            log.warn({ worker: this.workerList }, "Cannot repair worker. Re init worker");
            this.initWorker();
            log.warn({ worker: this.workerList }, "Create new worker");
        } finally {
            await this.redisPool.get().release(redis);
        }
    }

    private initWorker() {
        this.workerId = uuid.v4();
        this.workerList = this.workerPrefixName + ":" + this.workerId;
    }

    private async spinPopData(redis: RedisClient, batchSize: number) {
        let size = batchSize;
        let result: QueueItem<T, string>[] = [];
        const ts = Date.now();
        do {
            const items: QueueItem<T, string>[] = await this.popData(redis, size);
            if (items.length) {
                size -= items.length;
                result = result.concat(items);
            } else if (this.maxSpinPopDuration > 0) {
                await sleep(this.spinPopSleepDuration);
            }
        } while (size > 0 && (Date.now() - ts < this.maxSpinPopDuration));

        return result;
    }

    private async waitForData(redis: RedisClient): Promise<QueueItem<T, string>> {
        while (true) {
            const result = await redis.brpoplpush(this.listName, this.workerList, 30);

            if (result) {
                return { data: this.parseTrxData(result as string), value: result as string };
            } else {
                await this.updateLastAccessTs(redis);
            }
        }
    }

    private async popData(redis: RedisClient, batchSize: number): Promise<QueueItem<T, string>[]> {
        const result: string[] = await this.popProc.exec<string[]>(redis,
            [this.listName, this.workerList], [batchSize.toString()]);
        await this.redisPool.get().waitForSync(redis);
        if (result) {
            return result.map(data => {
                return { data: this.parseTrxData(data), value: data };
            });
        } else {
            return undefined;
        }
    }

    private async updateLastAccessTs(redis: RedisClient): Promise<any> {
        return redis.hset(this.workerPrefixName, this.workerId, Date.now().toString());
    }

    private async getOrphanWorkers(redis: RedisClient, ts: number): Promise<string[]> {
        return redis.hgetall(this.workerPrefixName)
            .then((stat: WorkerAccessStat) => this.filterStaleWorkers(stat, ts));
    }

    private filterStaleWorkers(stats: WorkerAccessStat, ts: number): string[] {
        const result: string[] = [];
        if (stats) {
            for (const key of Object.keys(stats)) {
                const value = +stats[key];
                if (value < ts) {
                    result.push(key);
                }
            }
        }
        return result;
    }

    private async repairWorker(redis: RedisClient, worker: string): Promise<number> {
        return this.copyListProc.exec<number>(redis, [this.listName, this.workerPrefixName], [worker]);
    }

    private parseTrxData(data: string): T {
        return JSON.parse(data);
    }
}

export const GAME_HISTORY_LOG = config.namespaces.historyPrefix;
export const ROUND_HISTORY_LOG = config.namespaces.roundsPrefix;
export const SESSION_HISTORY_LOG = config.namespaces.sessionsPrefix;
export const CLEAN_GAME_CONTEXT_QUEUE_NAME = config.namespaces.cleanupGameContextPrefix;
export const CLEAN_PLAYER_CONTEXT_QUEUE_NAME = config.namespaces.cleanupPlayerContextPrefix;
export const ANALYTICS_QUEUE_NAME = config.namespaces.analyticsPrefix;

/**
 * Game history queue where we store all game events, that should be persisted in DWH
 */
export class GameHistoryQueue extends RedisQueue<GameEventHistory> {
    constructor() {
        super(GAME_HISTORY_LOG,
            config.namespaces.historyPrefix + "-worker",
            Redis);
    }
}

/**
 * RoundData history queue
 */
export class GameRoundQueue extends RedisQueue<RoundHistory> {
    constructor() {
        super(ROUND_HISTORY_LOG,
            config.namespaces.roundsPrefix + "-worker",
            Redis);
    }
}

/**
 * Session history queue
 */
export class GameSessionQueue extends RedisQueue<SessionHistory> {
    constructor() {
        super(SESSION_HISTORY_LOG,
            config.namespaces.sessionsPrefix + "-worker",
            Redis);
    }
}

/**
 * Cleanup game contexts queue
 */
export class CleanupGameContextQueue extends RedisQueue<CleanupContextRequest> {
    constructor() {
        super(CLEAN_GAME_CONTEXT_QUEUE_NAME, CLEAN_GAME_CONTEXT_QUEUE_NAME + "-worker", Redis);
    }
}

/**
 * Cleanup players contexts queue
 */
export class CleanupPlayerContextQueue extends RedisQueue<CleanupContextRequest> {
    constructor() {
        super(CLEAN_PLAYER_CONTEXT_QUEUE_NAME, CLEAN_PLAYER_CONTEXT_QUEUE_NAME + "-worker", Redis);
    }
}

export class AnalyticsQueue extends RedisQueue<AnalyticsData> {
    constructor() {
        super(ANALYTICS_QUEUE_NAME, ANALYTICS_QUEUE_NAME + "-worker", Redis);
    }
}
