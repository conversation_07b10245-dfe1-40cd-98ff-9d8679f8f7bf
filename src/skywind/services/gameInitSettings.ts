import {
    InferAttributes,
    InferCreationAttributes,
    Model,
    DataTypes,
    ModelStatic,
    UniqueConstraintError,
    Op,
} from "sequelize";
import db from "../storage/db";
import { CacheableGameModule, ModuleName } from "./game/gamemodule";
import { GameInitResponse } from "@skywind-group/sw-game-core";
import { RemoteStorage } from "./remoteStorage";
import config from "../config";

interface Key {
    key: string;
}
const cache = new WeakMap<Key, string>();

/**
 * Interface for game events.
 * It's supposed that we store game event with primary key pair: roundId + eventId
 */
export interface GameInitSettings {
    gameId: string;
    version: string;
    data: any;
}

function createKey(id: string, version: string): string {
    return `${id}@${version}`;
}

export interface InitSettingsStorage {
    create(settings: GameInitSettings): Promise<void>;
    findAll(filter: { gameId: string, version: string }[]): Promise<GameInitSettings[]>;
}

export interface GameInitSettingsDBInstance extends Model<
    InferAttributes<GameInitSettingsDBInstance>,
    InferCreationAttributes<GameInitSettingsDBInstance>
>, GameInitSettings {}
const schema = {
    gameId: { field: "game_id", type: DataTypes.STRING, primaryKey: true },
    version: { field: "version", type: DataTypes.STRING, primaryKey: true },
    data: { field: "data", type: DataTypes.JSONB, allowNull: false },
};

export type IGameInitSettingsModel = ModelStatic<GameInitSettingsDBInstance>;
const model: IGameInitSettingsModel = db.get().define<GameInitSettingsDBInstance, GameInitSettings>(
    "game_init_settings",
    schema,
    { timestamps: false, freezeTableName: true }
);

export function getGameInitSettingsModel(): IGameInitSettingsModel {
    return model;
}

export class InitSettingsStorageImpl implements InitSettingsStorage {
    public async create(settings: GameInitSettings): Promise<void> {
        try {
            await model.create(settings);
        } catch (err) {
            if (!(err instanceof UniqueConstraintError)) {
                return Promise.reject(err);
            }
        }
    }

    public findAll(filter): Promise<GameInitSettings[]> {
        return model.findAll({
            where: {
                [Op.or]: filter
            }
        }) as any;
    }
}

export class InitSettingsRemoteStorageImpl extends RemoteStorage implements InitSettingsStorage {

    constructor(baseUrl: string) {
        super(baseUrl);
    }

    public create(settings: GameInitSettings): Promise<void> {
        return this.post("/initSettings", settings);
    }

    public findAll(filter): Promise<GameInitSettings[]> {
        return this.post<GameInitSettings[]>("/initSettings/load", filter);
    }
}

export function getStorage(): InitSettingsStorage {
    return config.offlineStorage.useRemote ?
           new InitSettingsRemoteStorageImpl(config.offlineStorage.url) : new InitSettingsStorageImpl();
}

export async function findGameInitSettings(gameId: string, version: string) {
    const key = { key: createKey(gameId, version) };
    let result = cache.get(key);
    if (!result) {
        const filter = [{ gameId, version }];
        const gameInitDBSettings = await getStorage().findAll(filter);
        if (gameInitDBSettings && gameInitDBSettings.length) {
            result = gameInitDBSettings[0].data;
            cache.set(key, result);
        }
    }

    return result;
}

export class GameInitSettingsService {
    private readonly savedModules = new Set<string>();
    private readonly storage: InitSettingsStorage = getStorage();

    public async checkIfSaved(name: ModuleName, data: GameInitResponse): Promise<void> {
        const key = createKey(name.id, name.version);
        if (!this.savedModules.has(key)) {
            await this.storage.create({ gameId: name.id, version: name.version, data });
            this.savedModules.add(key);
        }
    }

    public async checkAll(gameModules: CacheableGameModule[]) {
        const items = await this.storage.findAll(
            gameModules.map((m) => {
                return {
                    gameId: m.moduleName.id,
                    version: m.moduleName.version
                };
            }));

        items.forEach((item) => this.savedModules.add(createKey(item.gameId, item.version)));
    }
}

export default new GameInitSettingsService();
