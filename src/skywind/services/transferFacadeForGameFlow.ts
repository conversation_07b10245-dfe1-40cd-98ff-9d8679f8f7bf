import { BalanceResponse, } from "@skywind-group/sw-game-core";
import { GameTokenData } from "./tokens";
import * as Errors from "../errors";
import { ExtendedGameHistory } from "../history/history";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { EngineGameFlow, TransferInfo } from "./gameflow";

export class TransferFacade {

    constructor(private engineGameFlow: EngineGameFlow) {

    }

    // TODO: refactor, I see two options:
    // 1) make a factory here and move implementations of transfer logic out to separate instances
    // 2) in ideal world, code of 'strictSingleRoundTransfer' should move to b365 adapter,
    // but it requires adding new fields to transfer request that goes to gameprovider
    public async makeTransfer(transferInfo: TransferInfo,
                              raiseError: boolean,
                              initiatedByServer: boolean): Promise<void> {
        if (!this.engineGameFlow.settings().transferEnabled) {
            if (raiseError) {
                return Promise.reject(new Errors.TransferNotEnabled());
            }
            return;
        }

        if (this.engineGameFlow.settings().enforceSingleTransferInAndOutPerRound) {
            await this.strictSingleRoundTransfer(transferInfo, initiatedByServer);
        } else {
            const balance: BalanceResponse = await this.engineGameFlow.getBalance();
            if (this.getGameTokenData().walletPerGame && await this.requireFullTransferOut(transferInfo, balance)) {
                await this.fullTransfer(transferInfo, balance);
            } else {
                await this.transferOperation(transferInfo);
            }
        }
    }

    private async transferOperation(transferInfo: TransferInfo): Promise<void> {
        if (transferInfo.operation === "transfer-in") {
            await this.validateTransferIn(transferInfo);
        }

        const roundEnded = await this.shouldEndTransferRound(transferInfo);
        const walletOperation = await this.engineGameFlow.walletOperationFactory.createTransferOperation(
            transferInfo, roundEnded);

        const history: ExtendedGameHistory = {
            type: transferInfo.operation,
            roundEnded,
            data: {
                request: transferInfo.operation,
                coinsAmount: transferInfo.amount,
                amount: walletOperation.amount,
            },
        };
        if (transferInfo.operation === "transfer-in") {
            history.debit = walletOperation.amount;
        } else {
            history.credit = walletOperation.amount;
        }

        await this.engineGameFlow.updatePending(this.engineGameFlow.flowContext.gameContext, walletOperation, history);
        await this.engineGameFlow.commitPending();
    }

    private async validateTransferIn(transferInfo: TransferInfo): Promise<void> {
        const balance = await this.engineGameFlow.getPlayerBalance();

        if (balance.external < this.engineGameFlow.fromGameAmount(transferInfo.amount)) {
            return Promise.reject(new Errors.InsufficientBalanceToMakeTransferError());
        }
    }

    /**
     * Define transfer round state
     * transferIn - always returns false
     * transferOut - always returns true except the case when game is in bonus feature mode.
     * @param transferInfo
     */
    private async shouldEndTransferRound(transferInfo: TransferInfo): Promise<boolean> {
        if (transferInfo.operation !== "transfer-out") {
            return false;
        }

        const isBonusFeatureMode = await this.isBonusFeatureMode();
        if (isBonusFeatureMode && this.engineGameFlow.settings().disableCloseRoundForBonusMode) {
            return false;
        }

        return true;
    }

    private async isBonusFeatureMode(): Promise<boolean> {
        if (this.engineGameFlow?.game?.isBonusFeatureMode) {
            return this.engineGameFlow.game.isBonusFeatureMode(this.engineGameFlow as EngineGameFlow<any>);
        }

        return false;
    }

    /**
     * This method must be used only when enforceSingleTransferInAndOutPerRound settings is true.
     * Its goal is to ensure that there is no more than one transfer-in and out operation in a round.
     * So if player does transfer-in a round that already had transfer-in operation - we firstly transfer-out all the
     * money (with newRound flag) and then do the transfer-in (which will happen already in a new round). For
     * transfer-out we just need to ensure that player transfers all of his money out and the game is not in a
     * 'bonusFeatureMode'.
     * @param transferInfo
     * @param balance
     */
    public async strictSingleRoundTransfer(transferInfo: TransferInfo, initiatedByServer: boolean): Promise<void> {
        // if transfer request was initiated by server,
        // then this is probably a transfer out on disconnect or gamerecovery
        // so we dont need to check external balance, as this may cause issues
        const balance: BalanceResponse = await this.engineGameFlow.getBalance(initiatedByServer ? true : false);
        const isBonusFeatureMode = await this.isBonusFeatureMode();
        if (isBonusFeatureMode) {
            return Promise.reject(new Errors.TransferDuringBonusFeatureIsForbiddenError());
        }

        const isTransferIn = transferInfo.operation === "transfer-in";

        if (isTransferIn) {
            let transferInAmount = transferInfo.amount;
            // transfer all out, I think we can do this even if we are in bonusFeatureMode
            // we do full transfer-out on transfer-in if:
            // a) player has money in internal wallet
            // b) its not his first action in round - then even if he has 0 balance -
            // he probably lost it after previous transfer-in
            if (balance.amount || this.engineGameFlow.flowContext.gameSerialNumber > 1) {
                await this.transferOperation({
                    operation: "transfer-out",
                    amount: balance.amount,
                });

                const currency = Currencies.get(this.engineGameFlow.info().currency);
                transferInAmount = currency.toFixedByExponent(balance.amount + transferInfo.amount);
            }

            // now transfer-in back what was transferred out + initial transfer-in amount
            await this.transferOperation({
                operation: "transfer-in",
                amount: transferInAmount
            });
            return;
        }

        // transfer-out case
        if (balance.amount !== transferInfo.amount) {
            return Promise.reject(new Errors.PartialTransferIsForbiddenError());
        }

        // cases with 0-amount transfer-out on disconnect
        if (balance.amount === 0) {
            // case when actual transfer-out failed during request to merchant
            // in that case trx is already in our wallet
            if (this.engineGameFlow.pendingModification || this.engineGameFlow.jackpotPending) {
                await this.engineGameFlow.commitPending();
                return;
            }

            // case with 0-amount transfer-out on disconnect after player made full transfer-out
            if (this.engineGameFlow.flowContext.gameSerialNumber === 0) {
                return;
            }
        }

        await this.transferOperation(transferInfo);
    }

    /**
     * This method must be used only when walletPerGame settings is true.
     * Its goal is: on partial transfer - do the full transfer out and then transfer-in the amount that was supposed to
     * be left by player. Such approach causes a new round creation on 're-deposit'=transfer-in.
     * @param transferInfo
     * @param balance
     */
    public async fullTransfer(transferInfo: TransferInfo, balance: BalanceResponse): Promise<void> {
        const operation = transferInfo.operation === "transfer-in" ? transferInfo.amount : -transferInfo.amount;

        const currency = Currencies.get(this.engineGameFlow.info().currency);
        const leftAmount = currency.toFixedByExponent(balance.amount + operation);

        // transfer all out
        await this.transferOperation({
            operation: "transfer-out",
            amount: balance.amount,
        });

        // redeposit left amount
        if (leftAmount > 0) {
            await this.transferOperation({
                operation: "transfer-in",
                amount: leftAmount
            });
        }
    }

    // !NB, this check is applicable only for 'walletPerGame: true' setting
    private async requireFullTransferOut(transferInfo: TransferInfo, balance: BalanceResponse) {
        const totalBets = this.engineGameFlow?.flowContext?.round?.totalBet || 0;

        const isBonusFeatureMode = await this.isBonusFeatureMode();

        if (transferInfo.operation === "transfer-in") {
            return (balance.amount > 0 || totalBets > 0) && !isBonusFeatureMode;
        }

        if (isBonusFeatureMode) {
            return false;
        }
        return balance.amount !== transferInfo.amount;
    }

    private getGameTokenData(): GameTokenData {
        return this.engineGameFlow.flowContext.gameData.gameTokenData;
    }
}
