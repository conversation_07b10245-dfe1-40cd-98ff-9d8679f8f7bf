import { GameFlowContext } from "../context/gamecontext";
import { <PERSON><PERSON>ontextID, PlayerContextID } from "../contextIds";
import { Command, PromisedCommand, SomeCommand } from "../command";
import { PlayerContext, PlayerGameInfo } from "../playercontext/playerContext";
import { getGameContextEncoder } from "../encoder/contextEncoding";
import { UNSUPPORTED } from "../../utils/common";
import { GameFlowContextImpl } from "../context/gameContextImpl";
import {
    BaseDBContext,
    DBArchivedContext,
    DBContext,
    DBPlayerContext,
    GameContextOfflineCommands,
    PlayerContextOfflineCommands,
    RecoveryType
} from "./offlineCommands";
import config from "../../config";

export type OfflineCommandType =
    "findGameContext"
    | "findPlayerContext"
    | "saveGameContext"
    | "savePlayerContext"
    | "removeGameContext"
    | "removePlayerContext"
    | "findExpiredGameContexts"
    | "archiveGameContext"
    | "findContextByMerchantSessionId"
    | "updateGameContextExpiration"
    | "findGameContextsForCompletion"
    | "findGameContextByRoundId";

export interface OfflineCommand<T> extends Command<T> {
    type: OfflineCommandType;
}

export interface CheckEnvIdRequest {
    brandId: number;
    envId: string;
}

export interface FindGameContextCommand extends OfflineCommand<DBContext>, CheckEnvIdRequest {
    type: "findGameContext";
    id: string;
}

export interface FindPlayerContextCommand extends OfflineCommand<DBPlayerContext>, CheckEnvIdRequest  {
    type: "findPlayerContext";
    id: string;
}

export interface RemoveGameContextCommand extends OfflineCommand<void> {
    type: "removeGameContext";
    ids: string[];
}

export interface RemovePlayerContexCommand extends OfflineCommand<void> {
    type: "removePlayerContext";
    ids: string[];
}

export interface SavePlayerContextCommand extends OfflineCommand<void> {
    type: "savePlayerContext";
    playerContext: DBPlayerContext;

}

export interface SaveGameContextCommand extends OfflineCommand<void> {
    type: "saveGameContext";
    gameContext: DBContext;

}

export interface FindExpiredGameContextsCommand extends OfflineCommand<DBContext[]> {
    type: "findExpiredGameContexts";
    expiredAt: string;
    limit: number;

}

export interface FindGameContextsForCompletionCommand extends OfflineCommand<DBContext[]> {
    type: "findGameContextsForCompletion";
    limit: number;
}

export interface FindGameContextByRoundIdCommand extends OfflineCommand<DBContext> {
    type: "findGameContextByRoundId";
    roundId: number;
}

export interface ArchiveGameContextCommand extends OfflineCommand<void> {
    type: "archiveGameContext";
    archive: DBArchivedContext;
    recoveryType: RecoveryType;
}

export interface FindContextByMerchantSessionIdCommand extends OfflineCommand<DBContext>, CheckEnvIdRequest {
    type: "findContextByMerchantSessionId";
    brandId: number;
    merchantSessionId: string;
}

export interface UpdateGameContextExpirationCommand extends OfflineCommand<void> {
    type: "updateGameContextExpiration";
    id: string;
    lastExpiredAt: string;
    newExpiredAt: string;
}

export class GameContextOfflineCommandsImpl implements GameContextOfflineCommands {

    public findContext(id: GameContextID): FindGameContextCommand {
        return { type: "findGameContext", id: id.asString(), brandId: id.brandId, envId: config.environmentId };
    }

    public findContextByMerchantSessionId(brandId: number,
                                          merchantSessionId: string): FindContextByMerchantSessionIdCommand {
        return { type: "findContextByMerchantSessionId", brandId, merchantSessionId, envId: config.environmentId };
    }

    public async save(context: GameFlowContext, requireCompletion: boolean = false): Promise<SaveGameContextCommand> {
        return { type: "saveGameContext", gameContext: await this.getContextDB(context, requireCompletion) };
    }

    public remove(ids: GameContextID[]): RemoveGameContextCommand {
        return { type: "removeGameContext", ids: ids.map(id => id.asString()) };
    }

    public findExpired(ts: Date, batchSize: number): FindExpiredGameContextsCommand {
        return { type: "findExpiredGameContexts", expiredAt: ts.toISOString(), limit: batchSize };
    }

    public async updateGameContextExpiration(ctx: GameFlowContext | DBContext,
                                             expireAt: Date): Promise<UpdateGameContextExpirationCommand> {
        const dbContext = await this.getContextDB(ctx);
        return {
            type: "updateGameContextExpiration",
            id: dbContext.id,
            lastExpiredAt: dbContext.expireAt ? new Date(dbContext.expireAt).toISOString() : undefined,
            newExpiredAt: expireAt && expireAt.toISOString()
        };
    }

    public findGameContextsForCompletion(batchSize: number): FindGameContextsForCompletionCommand {
        return { type: "findGameContextsForCompletion", limit: batchSize };
    }

    public findGameContextByRoundId(roundId: number): FindGameContextByRoundIdCommand {
        return { type: "findGameContextByRoundId", roundId };
    }

    public async archive(context: GameFlowContext | DBArchivedContext,
                         recoveryType?: RecoveryType): Promise<ArchiveGameContextCommand> {
        return {
            type: "archiveGameContext",
            archive: await this.getArchiveContextDB(context, recoveryType),
            recoveryType
        };
    }

    private async getContextDB(item: GameFlowContext | DBContext,
                               requireCompletion: boolean = false): Promise<DBContext> {
        if (this.isGameContextInstance(item)) {
            if (!item.roundEnded && item.round) {
                item.round.broken = true;
            }
            return {
                id: item.id.asString(),
                ...await this.getBaseDBContext(item),
                requireCompletion
            };
        } else {
            return item as DBContext;
        }
    }

    private async getArchiveContextDB(item: GameFlowContext | DBArchivedContext,
                                      recoveryType?: RecoveryType): Promise<DBArchivedContext> {
        if (this.isGameContextInstance(item)) {
            return {
                contextId: item.id.asString(),
                recoveryType: recoveryType,
                ...await this.getBaseDBContext(item),
            };
        } else {
            return item as DBArchivedContext;
        }
    }

    private async getBaseDBContext(item: GameFlowContextImpl): Promise<BaseDBContext> {
        const data = await getGameContextEncoder().encode(item);
        return {
            data,
            roundId: item.roundId || item.round?.roundId,
            brandId: item.id.brandId,
            playerCode: item.id.playerCode,
            gameCode: item.id.gameCode,
            gameId: item.gameData.gameId,
            deviceId: item.id.deviceId,
            policy: item.persistencePolicy,
            broken: item.broken,
            brokenPayment: item.brokenPayment,
            requireLogout: item.requireLogout,
            requireTransferOut: item.requireTransferOut,
            retryAttempts: item.retryAttempts,
            specialState: item.specialState,
            merchantSessionId: item.gameData.gameTokenData.merchantSessionId,
            expireAt: item.expireAt ? new Date(item.expireAt) : undefined
        };
    }

    private isGameContextInstance(item: GameFlowContext | DBArchivedContext | DBContext): item is GameFlowContextImpl {
        return item instanceof GameFlowContextImpl;
    }
}

export class PlayerContextOfflineCommandsImpl implements PlayerContextOfflineCommands {
    public findPlayerContext(id: PlayerContextID): FindPlayerContextCommand {
        return { type: "findPlayerContext", id: id.asString(), brandId: id.brandId, envId: config.environmentId };
    }

    public savePlayerContext(context: PlayerContext): SavePlayerContextCommand {
        return { type: "savePlayerContext", playerContext: this.getDBPlayerContext(context) };
    }

    public removePlayerContext(ids: PlayerContextID[]): RemovePlayerContexCommand {
        return { type: "removePlayerContext", ids: ids.map(id => id.asString()) };
    }

    private getDBPlayerContext(item: PlayerContext): DBPlayerContext {
        const broken = (item.brokenGames || []).map((game: PlayerGameInfo) => {
            return { id: game.id.asString(), mode: game.mode };
        });

        const active = (item.activeGames || []).map((game: PlayerGameInfo) => {
            return { id: game.id.asString(), mode: game.mode };
        });

        return {
            id: item.id.asString(),
            data: undefined,
            games: broken.concat(active),
            version: item.version,
            dataVersion: item.dataVersion
        };

    }
}

/**
 * Implementation for fun games.
 */
export class FunGameContextOfflineCommands implements GameContextOfflineCommands {

    public findContext(id: GameContextID): SomeCommand<DBContext> {
        return undefined;
    }

    public findContextByMerchantSessionId(brandId: number,
                                          merchantSessionId: string): SomeCommand<DBContext> {
        return undefined;
    }

    public save(context: GameFlowContext): SomeCommand {
        return UNSUPPORTED();
    }

    public async remove(id: GameContextID[]): PromisedCommand {
        return undefined;
    }

    public findExpired(ts: Date, batchSize: number): PromisedCommand<DBContext[]> {
        return UNSUPPORTED();
    }

    public archive(context: GameFlowContext | DBArchivedContext, recoveryType?: RecoveryType): PromisedCommand {
        return UNSUPPORTED();
    }

    public updateGameContextExpiration(ctx: DBContext, expiredAt: Date): PromisedCommand {
        return UNSUPPORTED();
    }

    public findGameContextsForCompletion(batchSize: number): PromisedCommand<DBContext[]> {
        return UNSUPPORTED();
    }

    public findGameContextByRoundId(roundId: number): PromisedCommand<DBContext> {
        return UNSUPPORTED();
    }
}

/**
 * Implementation for fun games.
 */
export class FunPlayerContextOfflineCommands implements PlayerContextOfflineCommands {

    public findPlayerContext(id: PlayerContextID): SomeCommand<DBPlayerContext> {
        return undefined;
    }

    public savePlayerContext(context: PlayerContext): PromisedCommand {
        return UNSUPPORTED();
    }

    public removePlayerContext(ids: PlayerContextID[]): PromisedCommand {
        return undefined;
    }
}
