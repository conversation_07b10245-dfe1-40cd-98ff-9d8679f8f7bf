import * as pg from "pg";
import config from "../../config";
import { OfflineCommandExecutor, SafeEnvIdOfflineCommandExecutor } from "./offlineCommandExecutorImpl";
import { RemoteOfflineCommandExecutor } from "./remoteOfflineCommandExecutor";

pg.types.setTypeParser(1114, stringValue => {
    return new Date(stringValue + "+0000");
});

export function getLocalOfflineCommandExecutor() {
    return config.environmentId ? new SafeEnvIdOfflineCommandExecutor() : new OfflineCommandExecutor();
}

export function getOfflineCommandExecutor() {
    return config.offlineStorage.useRemote ?
           new RemoteOfflineCommandExecutor(config.offlineStorage.url) :
           getLocalOfflineCommandExecutor();
}
