import { InferAttributes, InferCreationAttributes, Model, ModelStatic, DataTypes } from "sequelize";
import db from "../../storage/db";
import { DBArchivedContext, DBContext, DBPlayerContext } from "./offlineCommands";
import config from "../../config";

const baseContextSchema = {
    brandId: { type: DataTypes.INTEGER, field: "brand_id", primaryKey: false },
    playerCode: { type: DataTypes.STRING, field: "player_code", primaryKey: false },
    gameCode: { type: DataTypes.STRING, field: "game_code", primaryKey: false },
    gameId: { type: DataTypes.STRING, field: "game_id", primaryKey: false },
    deviceId: { type: DataTypes.STRING, field: "device_id", primaryKey: false },
    roundId: { field: "round_id", type: DataTypes.BIGINT },
    policy: { type: DataTypes.INTEGER, allowNull: true },
    broken: { type: DataTypes.BOOLEAN, field: "is_broken", allowNull: true },
    brokenPayment: { type: DataTypes.BOOLEAN, field: "is_broken_payment", allowNull: true },
    requireLogout: { type: DataTypes.BOOLEAN, field: "is_require_logout", allowNull: true },
    requireTransferOut: { type: DataTypes.BOOLEAN, field: "is_require_transfer_out", allowNull: true },
    retryAttempts: { type: DataTypes.INTEGER, field: "retry_attempts", allowNull: true },
    merchantSessionId: { type: DataTypes.STRING, field: "merchant_session_id", allowNull: true },
    data: { type: DataTypes.JSONB },
    specialState: { type: DataTypes.INTEGER, field: "special_state", allowNull: true },
    createdAt: { type: DataTypes.DATE, field: "created_at" },
    updatedAt: { type: DataTypes.DATE, field: "updated_at" },
    expireAt: { type: DataTypes.DATE, field: "expire_at" },
};

export interface ContextDBInstance extends Model<
    InferAttributes<ContextDBInstance>,
    InferCreationAttributes<ContextDBInstance>
>, DBContext {}
const contextSchema = {
    ...baseContextSchema,
    id: { type: DataTypes.STRING, primaryKey: true },
    requireCompletion: { type: DataTypes.BOOLEAN, field: "is_require_completion", allowNull: true },
};

export interface ArchivedContextDBInstance extends Model<
    InferAttributes<ArchivedContextDBInstance>,
    InferCreationAttributes<ArchivedContextDBInstance>
>, DBArchivedContext {}
const archivedContextSchema = {
    ...baseContextSchema,
    id: { type: DataTypes.BIGINT, primaryKey: true, autoIncrement: true },
    contextId: { field: "context_id", type: DataTypes.STRING },
    recoveryType: { field: "recovery_type", type: DataTypes.ENUM("force-finish", "revert", "finalize") },
};

export interface PlayerContextDBInstance extends Model<
    InferAttributes<PlayerContextDBInstance>,
    InferCreationAttributes<PlayerContextDBInstance>
>, DBPlayerContext {}
const playerContextSchema = {
    id: { type: DataTypes.STRING, primaryKey: true },
    data: { type: DataTypes.JSONB },
    games: { type: DataTypes.JSONB },
    version: { type: DataTypes.BIGINT },
    createdAt: { type: "timestamp without time zone", field: "created_at" },
    updatedAt: { type: "timestamp without time zone", field: "updated_at" },
    dataVersion: { type: DataTypes.INTEGER, field: "data_version" },
};

export const LIST_OF_GAMES_FOR_TEMP_SOLUTION: string | string[] = config.tempListOfGameForFinalization;

export function buildTempSolutionListOfGames(gamesArray: string | string[]): string {
    if (typeof gamesArray === "string")  {
        gamesArray = [gamesArray];
    }
    return "'" + gamesArray.join("', '") + "'";
}

export const UPSERT_GAME_CONTEXT =
    "INSERT INTO game_contexts (id, brand_id, player_code, game_code, game_id, device_id, policy," +
    " is_broken, is_broken_payment, is_require_logout, is_require_transfer_out, retry_attempts," +
    " special_state, merchant_session_id, data, created_at, updated_at, expire_at, is_require_completion," +
    " round_id) " +
    "VALUES (:id, :brandId, :playerCode, :gameCode, :gameId, :deviceId, :policy, :broken," +
    " :brokenPayment, :requireLogout, :requireTransferOut, :retryAttempts, :specialState," +
    " :merchantSessionId, :data, :ts, :ts, :expireAt, :requireCompletion, :roundId) ON CONFLICT(id) " +
    "DO UPDATE SET (policy, is_broken, is_broken_payment, is_require_logout, is_require_transfer_out, " +
    "retry_attempts, special_state, merchant_session_id, data, updated_at, expire_at, is_require_completion," +
    " round_id)" +
    "=(:policy, :broken, :brokenPayment, :requireLogout, :requireTransferOut, :retryAttempts, " +
    ":specialState, :merchantSessionId, :data, :ts, :expireAt, :requireCompletion, :roundId) " +
    "RETURNING id";

export const UPSERT_PLAYER_CONTEXT =
    "INSERT INTO player_contexts (id, data, games, version, data_version, created_at, updated_at) " +
    "VALUES (:id, :data, :games, :version, :dataVersion, :ts, :ts) ON CONFLICT(id) " +
    "DO UPDATE SET (data, games, version, data_version, created_at, updated_at)=" +
    "(:data, :games, :version, :dataVersion, :ts, :ts) " +
    "RETURNING id";

export const LONG_TERM_CONTEXT_GAMES_FINALIZATION_FIX =
    "OR (game_code in (" + buildTempSolutionListOfGames(LIST_OF_GAMES_FOR_TEMP_SOLUTION) + ")) ";

export const LOCK_AND_GET_EXPIRED_CONTEXTS =
    "WITH upd AS (" +
        "SELECT id FROM game_contexts WHERE expire_at <= :expireAt AND " +
        "(special_state IS NULL OR special_state = :finalizing) AND " +
        "(policy IS NULL OR policy = :normal " + LONG_TERM_CONTEXT_GAMES_FINALIZATION_FIX + ") " +
        "ORDER BY id " +
        "LIMIT :limit " +
        "FOR UPDATE SKIP LOCKED" +
    ") " +
    "UPDATE game_contexts g SET " +
        "expire_at = :newExpiredAt " +
    "FROM upd " +
    "WHERE upd.id = g.id " +
    "RETURNING g.*";

export type IContextDBModel = ModelStatic<ContextDBInstance>;
const model: IContextDBModel = db.get().define<ContextDBInstance, DBContext>(
    "gamecontext",
    contextSchema,
    { underscored: true, tableName: "game_contexts" }
);
export type IArchivedContextDBModel = ModelStatic<ArchivedContextDBInstance>;
const archiveModel: IArchivedContextDBModel = db.get().define<ArchivedContextDBInstance, DBArchivedContext>(
    "archivedgamecontext",
    archivedContextSchema,
    { underscored: true, tableName: "archived_game_contexts", updatedAt: false }
);
export type IPlayerContextDBModel = ModelStatic<PlayerContextDBInstance>;
const playerContextModel: IPlayerContextDBModel = db.get().define<PlayerContextDBInstance, DBPlayerContext>(
    "playercontext",
    playerContextSchema,
    { underscored: true, tableName: "player_contexts" }
);

export function getGameContextModel(): IContextDBModel {
    return model;
}

export function getArchiveContextModel(): IArchivedContextDBModel {
    return archiveModel;
}

export function getPlayerContextModel(): IPlayerContextDBModel {
    return playerContextModel;
}
