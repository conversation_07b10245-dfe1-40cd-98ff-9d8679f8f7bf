import { GameFlowContext } from "../context/gamecontext";
import { GameContextID, PlayerContextID } from "../contextIds";
import { SomeCommand } from "../command";
import { PlayerContext } from "../playercontext/playerContext";

export enum RecoveryType {
    FORCE_FINISH = "force-finish",
    REVERT = "revert",
    FINALIZE = "finalize"
}

export interface BaseDBContext {
    brandId: number;
    playerCode: string;
    gameId: string;
    gameCode: string;
    deviceId: string;
    roundId: string;
    policy?: number;
    broken?: boolean;
    brokenPayment: boolean;
    requireLogout: boolean;
    requireTransferOut: boolean;
    retryAttempts: number;
    merchantSessionId: string;
    data: any;
    specialState: number;
    expireAt?: Date;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface DBContext extends BaseDBContext {
    id: string;
    requireCompletion: boolean;
}

export interface DBArchivedContext extends BaseDBContext {
    contextId: string;
    recoveryType?: RecoveryType;
}

export interface PlayerGameInfo {
    id: string;
    mode: string;
}

export interface DBPlayerContext {
    id: string;
    data: any;
    games: PlayerGameInfo[];
    createdAt?: Date;
    updatedAt?: Date;
    dataVersion: number;
    version: number;
}

/**
 * This class is responsible for storing GameContext which is not active and broken or unfinished in RDBMS
 */
export interface GameContextOfflineCommands {
    /**
     * Find context in offline storage
     */
    findContext(id: GameContextID): SomeCommand<DBContext>;

    /**
     *  Find game context by brandId and merchantSessionId
     */
    findContextByMerchantSessionId(brandId: number, merchantSessionId: string): SomeCommand<DBContext>;

    /**
     * Save context in offline storage
     */
    save(context: GameFlowContext, requireCompletion?: boolean): SomeCommand;

    /**
     * Remove  game context
     */
    remove(id: GameContextID[]): SomeCommand;

    /**
     * Find expired game context
     */
    findExpired(ts: Date, batchSize: number): SomeCommand<DBContext[]>;

    /**
     * Update expiration, expireAt = null means clear expiration
     */
    updateGameContextExpiration(ctx: GameFlowContext | DBContext, expireAt: Date): SomeCommand<void>;

    /**
     * Find game contexts that require completion
     */
    findGameContextsForCompletion(batchSize: number): SomeCommand<DBContext[]>;

    /**
     * Find game context in Postgres by round ID
     */
    findGameContextByRoundId(roundId: number): SomeCommand<DBContext>;

    /**
     * Archive game context and put it in special storage
     */
    archive(context: GameFlowContext | DBArchivedContext, recoveryType?: RecoveryType): SomeCommand<void>;
}

/**
 * This class is responsible for storing GameContext which is not active and broken or unfinished in RDBMS
 */
export interface PlayerContextOfflineCommands {

    /**
     * Find player context in offline storage
     */
    findPlayerContext(id: PlayerContextID): SomeCommand<DBPlayerContext>;

    /**
     * Save player context in offline storage
     *
     */
    savePlayerContext(context: PlayerContext): SomeCommand;

    /**
     * Remove  player contexts
     */
    removePlayerContext(ids: PlayerContextID[]): SomeCommand;
}
