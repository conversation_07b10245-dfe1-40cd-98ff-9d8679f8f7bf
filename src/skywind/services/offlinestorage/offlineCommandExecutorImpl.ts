import { BaseCommandExecutor } from "../command";
import {
    ArchiveGameContextCommand,
    CheckEnvIdRequest,
    FindContextByMerchantSessionIdCommand,
    FindExpiredGameContextsCommand,
    FindGameContextCommand, FindGameContextByRoundIdCommand,
    FindGameContextsForCompletionCommand,
    FindPlayerContextCommand,
    OfflineCommand,
    RemoveGameContextCommand,
    RemovePlayerContexCommand,
    SaveGameContextCommand,
    SavePlayerContextCommand,
    UpdateGameContextExpirationCommand
} from "./offlineCommandsImpl";
import db from "../../storage/db";
import { QueryTypes, Transaction, Op } from "sequelize";
import { DBContext, DBPlayerContext } from "./offlineCommands";
import {
    ContextDBInstance,
    getArchiveContextModel,
    getGameContextModel,
    getPlayerContextModel, LOCK_AND_GET_EXPIRED_CONTEXTS, PlayerContextDBInstance,
    UPSERT_GAME_CONTEXT,
    UPSERT_PLAYER_CONTEXT
} from "./models";
import { logging, measures } from "@skywind-group/sw-utils";
import { GameContextPersistencePolicy } from "@skywind-group/sw-game-core";
import { ManagementAPISupport } from "../managementapihelper";
import config from "../../config";
import { generateInternalToken } from "../tokens";
import { SpecialState } from "../context/gamecontext";
import { addMinutes } from "../../utils/common";
import measure = measures.measure;

const log = logging.logger("slot-engine:offlinecommand-executor");

export class OfflineCommandExecutor extends BaseCommandExecutor {
    protected async processPipeline(cmds: OfflineCommand<any>[]): Promise<any[]> {
        return await db.get().transaction(async (transaction) => {
            const result: Promise<any>[] = [];
            for (const cmd of cmds) {
                result.push(await this.processOne(cmd, transaction));
            }
            return result;
        });
    }

    protected async processOne(cmd: OfflineCommand<any>, transaction?: Transaction): Promise<any> {
        const func = this[cmd.type];
        return func ? func.apply(this, [cmd, transaction]) :
               Promise.reject(`Unknown command: ${cmd.type}`);
    }

    @measure({ name: "OfflineCommandExecutor.findGameContext", isAsync: true })
    public async findGameContext(cmd: FindGameContextCommand, transaction?: Transaction): Promise<DBContext> {
        const dbItem: ContextDBInstance = await getGameContextModel().findByPk(cmd.id, { transaction });
        if (!dbItem) {
            return undefined;
        }
        const result: DBContext = dbItem.get();
        if (typeof result.data === "string") {
            result.data = JSON.parse(result.data);
        }

        return result;
    }

    @measure({ name: "OfflineCommandExecutor.findContextByMerchantSessionId", isAsync: true })
    public async findContextByMerchantSessionId(cmd: FindContextByMerchantSessionIdCommand,
                                                transaction?: Transaction): Promise<DBContext> {
        const dbItem: ContextDBInstance = await getGameContextModel().findOne({
            where: {
                brandId: cmd.brandId,
                merchantSessionId: cmd.merchantSessionId
            },
            transaction
        });
        if (!dbItem) {
            return undefined;
        }
        const result: DBContext = dbItem.get();
        if (typeof result.data === "string") {
            result.data = JSON.parse(result.data);
        }

        return result;
    }

    @measure({ name: "OfflineCommandExecutor.findPlayerContext", isAsync: true })
    public async findPlayerContext(cmd: FindPlayerContextCommand, transaction: Transaction): Promise<DBPlayerContext> {
        const dbItem: PlayerContextDBInstance = await getPlayerContextModel().findByPk(cmd.id, { transaction });
        if (!dbItem) {
            return undefined;
        }
        return dbItem.get();
    }

    @measure({ name: "OfflineCommandExecutor.saveGameContext", isAsync: true })
    public async saveGameContext(cmd: SaveGameContextCommand, transaction: Transaction): Promise<void> {
        await db.get().query(UPSERT_GAME_CONTEXT, {
            type: QueryTypes.UPSERT,
            transaction,
            replacements: {
                id: cmd.gameContext.id,
                brandId: cmd.gameContext.brandId || null,
                playerCode: cmd.gameContext.playerCode || null,
                gameCode: cmd.gameContext.gameCode || null,
                gameId: cmd.gameContext.gameId || null,
                deviceId: cmd.gameContext.deviceId || null,
                roundId: cmd.gameContext.roundId || null,
                data: JSON.stringify(cmd.gameContext.data),
                broken: cmd.gameContext.broken,
                brokenPayment: cmd.gameContext.brokenPayment,
                requireLogout: cmd.gameContext.requireLogout,
                requireTransferOut: cmd.gameContext.requireTransferOut,
                requireCompletion: cmd.gameContext.requireCompletion,
                retryAttempts: cmd.gameContext.retryAttempts || null,
                specialState: cmd.gameContext.specialState || null,
                merchantSessionId: cmd.gameContext.merchantSessionId || null,
                policy: cmd.gameContext.policy,
                ts: new Date(),
                expireAt: cmd.gameContext.expireAt ? new Date(cmd.gameContext.expireAt).toISOString() : null
            }
        });
    }

    @measure({ name: "OfflineCommandExecutor.savePlayerContext", isAsync: true })
    public async savePlayerContext(cmd: SavePlayerContextCommand, transaction: Transaction): Promise<any> {
        return db.get().query(UPSERT_PLAYER_CONTEXT, {
            type: QueryTypes.UPSERT,
            transaction,
            replacements: {
                id: cmd.playerContext.id,
                data: JSON.stringify(cmd.playerContext.data) || null,
                games: JSON.stringify(cmd.playerContext.games),
                ts: new Date().toISOString(),
                version: cmd.playerContext.version,
                dataVersion: cmd.playerContext.dataVersion
            }
        });
    }

    @measure({ name: "OfflineCommandExecutor.removeGameContext", isAsync: true })
    public async removeGameContext(cmd: RemoveGameContextCommand, transaction: Transaction): Promise<any> {
        return getGameContextModel().destroy({
            transaction,
            where: {
                id: { [Op.in]: cmd.ids },
            }
        });
    }

    @measure({ name: "OfflineCommandExecutor.removePlayerContext", isAsync: true })
    public async removePlayerContext(cmd: RemovePlayerContexCommand, transaction: Transaction): Promise<any> {
        return getPlayerContextModel().destroy({
            transaction,
            where: {
                id: { [Op.in]: cmd.ids },
            }
        });
    }

    @measure({ name: "OfflineCommandExecutor.findExpiredGameContexts", isAsync: true })
    public async findExpiredGameContexts(cmd: FindExpiredGameContextsCommand,
                                         transaction: Transaction): Promise<DBContext[]> {
        /* Optimistic lock implementation - we are  getting contexts and updating expireAt.
        Other finalization workers will not find same contexts. We can use any value, but postponeInterval has some
        sense */
        const newExpiredAt = new Date(addMinutes(Date.now(), config.expireJob.postponeInterval));
        const result: ContextDBInstance[] = await db.get().query(LOCK_AND_GET_EXPIRED_CONTEXTS,
            {
                replacements: {
                    newExpiredAt,
                    expireAt: cmd.expiredAt,
                    finalizing: SpecialState.FINALIZING,
                    normal: GameContextPersistencePolicy.NORMAL,
                    longTerm: GameContextPersistencePolicy.LONG_TERM,
                    limit: cmd.limit
                },
                transaction,
                model: getGameContextModel(),
                mapToModel: true,
            });
        return result.map(dbItem => dbItem.get());
    }

    @measure({ name: "OfflineCommandExecutor.UpdateGameContextExpirationCommand", isAsync: true })
    public async updateGameContextExpiration(cmd: UpdateGameContextExpirationCommand,
                                             transaction: Transaction): Promise<any> {
        const partialUpdate: any = { expireAt: cmd.newExpiredAt && new Date(cmd.newExpiredAt) };
        return getGameContextModel().update(partialUpdate, {
            transaction,
            where: {
                id: cmd.id
            }
        });
    }

    @measure({ name: "OfflineCommandExecutor.findGameContextsForCompletion", isAsync: true })
    public async findGameContextsForCompletion(cmd: FindGameContextsForCompletionCommand,
                                               transaction: Transaction): Promise<DBContext[]> {
        return getGameContextModel().findAll({
            transaction,
            where: {
                requireCompletion: true
            },
            limit: cmd.limit
        });
    }

    @measure({ name: "OfflineCommandExecutor.findGameContextByRoundId", isAsync: true })
    public async findGameContextByRoundId(cmd: FindGameContextByRoundIdCommand): Promise<DBContext | null> {
        return getGameContextModel().findOne({
            where: {
                roundId: cmd.roundId
            }
        });
    }

    @measure({ name: "OfflineCommandExecutor.archiveGameContext", isAsync: true })
    public async archiveGameContext(cmd: ArchiveGameContextCommand, transaction: Transaction): Promise<any> {
        return getArchiveContextModel()
            .create(Object.assign({}, cmd.archive, { recoveryType: cmd.recoveryType }), { transaction });
    }
}

/**
 * Prevent returning the game&player contexts if the environment of the request origin is wrong
 */
export class SafeEnvIdOfflineCommandExecutor extends OfflineCommandExecutor {
    private checker = new EnvironmentIdCheckHelper();

    public async findGameContext(cmd: FindGameContextCommand, transaction?: Transaction): Promise<DBContext> {
        await this.checker.validateEnvironmentId(cmd);
        return super.findGameContext(cmd, transaction);
    }

    public async findPlayerContext(cmd: FindPlayerContextCommand,
                                   transaction: Transaction): Promise<DBPlayerContext> {
        await this.checker.validateEnvironmentId(cmd);
        return super.findPlayerContext(cmd, transaction);
    }

    public async findContextByMerchantSessionId(cmd: FindContextByMerchantSessionIdCommand,
                                                transaction?: Transaction): Promise<DBContext> {
        await this.checker.validateEnvironmentId(cmd);
        return super.findContextByMerchantSessionId(cmd, transaction);
    }
}

/*
    *  This method is working in the "main domain" and check if the request comes from the
    *  correct environment
    *  TODO We need more lightweight way to check the envId.
    */
export class EnvironmentIdCheckHelper extends ManagementAPISupport {
    private static CHECK_ENV_ID_URL: string = "/check-environment";

    constructor() {
        super(config.managementAPI.internalServerUrl);
    }

    public async validateEnvironmentId(request: CheckEnvIdRequest): Promise<void> {
        if (request.envId) {
            try {
                const token = await generateInternalToken({ brandId: request.brandId, envId: request.envId });

                await this.post(EnvironmentIdCheckHelper.CHECK_ENV_ID_URL, { token });
            } catch (err) {
                log.error(err, "Failed to check environment ID", request);
                throw err;
            }
        }
    }
}
