import { measures } from "@skywind-group/sw-utils";
import config from "../../config";
import { Redis } from "../../storage/redis";
import { RedisProc } from "../redisCommandExecutor";
import measure = measures.measure;

export interface JobsState {
    /**
     * Brand identifier
     */
    brandId: number;
    /**
     * Last time the job state for the brand was updated in redis successfully.
     * this Field will help to check if the job is alive
     */
    lastTs: number;

    /**
     * last traceID, that initialize the migration
     */
    traceID?: string;
}

class ForceCleanupJobStateTracker {
    private readonly catchJobProc = new RedisProc(`${__dirname}/../../../../resources/lua/catchForceCleanupJob.lua`);
    private readonly initJobProc = new RedisProc(`${__dirname}/../../../../resources/lua/initForceCleanupJob.lua`);

    /**
     * Init state record in database.
     * Returns ForceJobForBrandExistsError in case the records exists already
     */
    @measure({ name: "ForceCleanupJobStateTracker.init", isAsync: true })
    public async init(brandId: number, traceID?: string): Promise<boolean> {
        const client = await Redis.get().get();
        try {
            const result = await this.initJobProc.exec<number>(client,
                [this.jobsKey, this.jobsTraceIDKey],
                [brandId.toString(), Date.now().toString(), traceID || null]);

            return result === 1;
        } finally {
            await Redis.get().release(client);
        }
    }

    /**
     * This will help to "catch' the job only on one of the cluster node.
     * Only one "force cleanup" is executed in cluster.
     *
     * Returns true if the job was caught
     */
    @measure({ name: "ForceCleanupJobStateTracker.catchJob", isAsync: true })
    public async catchJob(state: JobsState): Promise<boolean> {
        const client = await Redis.get().get();
        try {
            const result: number = await this.catchJobProc.exec<number>(client,
                [this.jobsKey],
                [state.brandId.toString(), state.lastTs.toString(), Date.now().toString()]);
            return result === 1;
        } finally {
            await Redis.get().release(client);
        }
    }

    /**
     * Mark job done
     *
     * @param brandId
     */
    @measure({ name: "ForceCleanupJobStateTracker.markAsDone", isAsync: true })
    public async markAsDone(brandId: number) {
        const client = await Redis.get().get();
        const multi = client.multi();
        try {
            multi.hdel(this.jobsKey, brandId.toString());
            multi.hdel(this.jobsTraceIDKey, brandId.toString());
            await multi.exec();
        } catch (e) {
            await multi.discard();
        } finally {
            await Redis.get().release(client);
        }
    }

    /**
     * Prolong the last update timestamp to ensure that the job is alive and don't need to
     * restart it.
     */
    @measure({ name: "ForceCleanupJobStateTracker.markAsAlive", isAsync: true })
    public async markAsAlive(brandId: number) {
        const client = await Redis.get().get();
        try {
            await client.hset(this.jobsKey, brandId.toString(), Date.now());
        } finally {
            await Redis.get().release(client);
        }
    }

    /**
     * Get last activity timestamp
     * restart it.
     */
    @measure({ name: "ForceCleanupJobStateTracker.getLastActivityTs", isAsync: true })
    public async getLastActivityTs(brandId: number) {
        const client = await Redis.get().get();
        try {
            return await client.hget(this.jobsKey, brandId.toString());
        } finally {
            await Redis.get().release(client);
        }
    }

    /**
     * Return all existing jobs states
     */
    @measure({ name: "ForceCleanupJobStateTracker.getJobState", isAsync: true })
    public async getJobs(): Promise<JobsState[]> {
        const client = await Redis.get().get();
        const multi = client.multi();
        try {
            const [[, stateResponse], [, traceIDResponse]] = await multi.hgetall(this.jobsKey)
                .hgetall(this.jobsTraceIDKey).exec();
            const result: JobsState[] = [];
            // tslint:disable-next-line:forin
            for (const property in stateResponse as any) {
                const value = stateResponse[property];
                if (value) {
                    result.push({ brandId: +property, lastTs: +value, traceID: traceIDResponse[property] });
                }
            }

            return result;
        } catch (e) {
            await multi.discard();
        } finally {
            await Redis.get().release(client);
        }
    }

    private get jobsKey(): string {
        return config.namespaces.forceCleanupJob;
    }

    private get jobsTraceIDKey() {
        return `${this.jobsKey}:traceId`;
    }
}

export default new ForceCleanupJobStateTracker();
