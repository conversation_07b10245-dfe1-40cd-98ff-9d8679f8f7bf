import { GameFlowContext } from "../context/gamecontext";
import { logging, measures } from "@skywind-group/sw-utils";
import { GameContextID } from "../contextIds";
import { ConcurrentAccessToGameSession } from "../../errors";
import { Redis } from "../../storage/redis";
import config from "../../config";
import { CleanupService } from "./cleanupService";
import { ContextManager } from "../contextmanager/contextManager";
import { getGameFlowContextManager } from "../contextmanager/contextManagerImpl";
import CompletionService from "../pendingCompletionService";
import { RedisProc } from "../redisCommandExecutor";
import { GameFlowFactory } from "../gameFlowFactory";
import BrokenPayments from "./brokenPaymentsStat";
import { ContextVariables } from "../../utils/contextVariables";
import measure = measures.measure;
import measureParam = measures.measureParam;
import measureProvider = measures.measureProvider;

const log = logging.logger("skywind:slot-engine:cleanup");

interface FilteredContextTypes {
    toSave: GameFlowContext[];
    toRemove: GameFlowContext[];
    toComplete: GameFlowContext[];
}

export class CleanupGameContextService implements CleanupService {

    private contextManager: ContextManager = getGameFlowContextManager();
    private enqueueContextsProc = new RedisProc(
        __dirname + "/../../../../resources/lua/gameContextManager.lua",
        __dirname + "/../../../../resources/lua/enqueueColdGameContext.lua");

    @measure({ name: "CleanupGameContextService.enqueueContexts", isAsync: true })
    public async enqueueContexts(maxTimestamp: number, batchSize: number): Promise<number> {
        let result: number = 0;
        while (true) {
            const client = await Redis.get().get();
            try {
                const count = await this.enqueueContextsProc.exec<number>(client,
                    [config.namespaces.lastGameActivityKey, config.namespaces.cleanupGameContextPrefix],
                    [maxTimestamp.toString(), batchSize.toString()]);
                if (!count) {
                    break;
                }
                result += count;
            } finally {
                await Redis.get().release(client);
            }
        }

        return result;
    }

    @measure({ name: "CleanupGameContextService.forceCleanUp", isAsync: true })
    public async forceCleanUp(@measureParam() brandId: number, batchSize: number,
                              progressCallback?: (count: number) => Promise<void>): Promise<[number, number]> {
        let totalCount = 0;
        let saved = 0;

        while (true) {
            const contextIds = await this.contextManager.findTopActiveGameContextIDsByBrand(brandId, batchSize);
            if (!contextIds || contextIds.length === 0) {
                break;
            }
            const [processed, moved] = await this.doCleanUp(contextIds, true);
            if (progressCallback) {
                await progressCallback(processed);
            }
            totalCount += processed;
            saved += moved;
        }

        return [totalCount, saved];
    }

    @measure({ name: "CleanupGameContextService.processContexts", isAsync: true })
    public async cleanUp(ids: string[], force: boolean): Promise<[number, number]> {
        return this.doCleanUp(ids.map(i => GameContextID.createFromString(i)), force);
    }

    private async doCleanUp(contextIds: GameContextID[], force: boolean): Promise<[number, number]> {
        const contexts = await this.findAllContexts(contextIds).then(result => this.filterContexts(result, force));

        if (contexts.toComplete.length > 0) {
            await this.completeGames(contexts);
        }

        if (contexts.toRemove.length > 0) {
            contexts.toRemove.sort((c1, c2) => c1.id.asString().localeCompare(c2.id.asString()));
            await this.contextManager.removeGameContextOffline(contexts.toRemove.map(ctx => ctx.id));
        }

        if (contexts.toSave.length > 0) {
            contexts.toSave.sort((c1, c2) => c1.id.asString().localeCompare(c2.id.asString()));
            await this.contextManager.saveGameContextOffline(contexts.toSave, force);
        }

        await this.removeAllContexts(contexts.toSave.concat(contexts.toRemove), force);

        return [contexts.toSave.length, contexts.toRemove.length];
    }

    private async completeGames(contexts: FilteredContextTypes): Promise<any> {
        const toKeepActive: GameFlowContext[] = [];
        const filteredContexts: FilteredContextTypes = {
            toSave: contexts.toSave,
            toRemove: toKeepActive,
            toComplete: contexts.toComplete
        };
        await Promise.all(filteredContexts.toComplete.map(async (ctx) =>
            measureProvider.runInTransaction("Retransmission", async () => this.tryToComplete(ctx, filteredContexts))));
        await Promise.all(toKeepActive.map(c => c.reactivate()));
    }

    public async tryToComplete(ctx: GameFlowContext, contexts: FilteredContextTypes) {
        const stat = await BrokenPayments.get();
        const contextId: string = ctx.id && ctx.id.asString();
        ContextVariables.setUpWithContext(ctx);
        try {
            stat.visitBeforeCommit(ctx);
            const offlineFlow = await GameFlowFactory.createOfflineFlow(ctx);
            await CompletionService.complete(ctx, offlineFlow);
            await this.filterContext(ctx, contexts);
            stat.visitAfterCommit(ctx);
        } catch (err) {
            stat.visitAfterFailCommit(ctx);
            measureProvider.saveError(err);
            if (err instanceof ConcurrentAccessToGameSession) {
                log.info({ contextId: contextId }, "Skip completion because of concurrent access");
                return;
            }
            log.error(err, "Failed to complete context %s", contextId);
            if (CompletionService.requireCompletion(ctx)) {
                try {
                    await ctx.activateNextRetryAttempt();
                } catch (e) {
                    if (e instanceof ConcurrentAccessToGameSession) {
                        log.info({ contextId: contextId },
                            "Skip completion because of concurrent access");
                        return;
                    }

                    return Promise.reject(e);
                }
            } else {
                await this.filterContext(ctx, contexts);
            }
        }
    }

    private async filterContexts(contexts: GameFlowContext[], force: boolean): Promise<FilteredContextTypes> {
        const result = { toSave: [], toRemove: [], toComplete: [] };
        await Promise.all(contexts.map(ctx => this.filterContext(ctx, result, force)));
        return result;
    }

    private async filterContext(ctx: GameFlowContext, contexts: FilteredContextTypes,
                                force: boolean = false): Promise<void> {
        if (ctx) {
            if (ctx.requireTransferOut && !ctx.brokenPayment && !force) {
                await this.tryToTransferOut(ctx);
            }
            if (ctx.keepOffline) {
                if ((CompletionService.requireCompletion(ctx)) && !force) {
                    contexts.toComplete.push(ctx);
                } else {
                    contexts.toSave.push(ctx);
                }
            } else {
                contexts.toRemove.push(ctx);
            }
        }
    }

    private async tryToTransferOut(ctx: GameFlowContext): Promise<void> {
        try {
            const flow = await GameFlowFactory.createOfflineFlow(ctx, true);
            await flow.transferAllOut();
        } catch (e) {
            log.error({ contextId: ctx.id.asString() }, "Cannot transfer all out");
        }
    }

    private async findAllContexts(contextIds: GameContextID[]): Promise<GameFlowContext[]> {
        return Promise.all(contextIds.map((id) => this.findContext(id)));
    }

    private async findContext(id: GameContextID): Promise<GameFlowContext> {
        return this.contextManager.findGameContextById(id, true, false);
    }

    private async removeAllContexts(contexts: GameFlowContext[], force: boolean) {
        return Promise.all(contexts.map(ctx => this.removeContext(ctx, force)));
    }

    private async removeContext(context: GameFlowContext, force: boolean): Promise<void> {
        if (context) {
            const id = context.id.asString();
            try {
                await context.remove(force);
            } catch (err) {
                if (err instanceof ConcurrentAccessToGameSession) {
                    log.info("Game context got active back  %s. Skip removing context ", id);
                } else {
                    throw err;
                }
            }
        }
    }
}
