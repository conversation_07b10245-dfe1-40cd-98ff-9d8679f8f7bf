import { logging, measures, sleep } from "@skywind-group/sw-utils";
import { CleanupGameContextService } from "./cleanupGameContextService";
import { CleanupPlayerContextService } from "./cleanupPlayerContextService";
import config from "../../config";
import JobStateTracker from "./forceCleanupJobStateTracker";
import { ManagementAPISupport } from "../managementapihelper";
import { generateInternalToken } from "../tokens";
import measureProvider = measures.measureProvider;

const log = logging.logger("skywind:slot-engine:force-cleanup");

export class Force<PERSON>leanupJob extends ManagementAPISupport {
    private static readonly FORCE_CLEANUP_FINISH = "/force-cleanup/finish";
    private gameCleanupService = new CleanupGameContextService();
    private playerCleanupService = new CleanupPlayerContextService();

    constructor(private readonly brandId: number, private readonly traceID?: string) {
        super(config.managementAPI.internalServerUrl);
    }

    public async doWork(): Promise<void> {
        log.info("Begin force cleanup of game contexts for brand  %s", this.brandId);
        const [totalGames, savedGames] = await this.gameCleanupService.forceCleanUp(this.brandId,
            config.cleanUp.batchSize, async (processed: number) => {
                log.info("Processed  %s game contexts for brand", processed, this.brandId);
                return JobStateTracker.markAsAlive(this.brandId);
            });
        log.info("Force cleanup finished: processed %s game contexts  saved to db %s contexts for brand %s",
            totalGames, savedGames, this.brandId);

        log.info("Begin force cleanup of players contexts for brand  %s", this.brandId);
        const [totalPlayers, savedPlayers] = await this.playerCleanupService.forceCleanUp(this.brandId,
            config.cleanUp.batchSize, async (processed: number) => {
                log.info("Processed  %s player contexts for brand", processed, this.brandId);
                return JobStateTracker.markAsAlive(this.brandId);
            });
        log.info("Force cleanup finished: processed %s player contexts  saved to db %s contexts for brand %s",
            totalPlayers, savedPlayers, this.brandId);

        await this.post(ForceCleanupJob.FORCE_CLEANUP_FINISH,
            { token: await generateInternalToken({ brandId: this.brandId }) });
    }

    public async execute() {
        return measureProvider.runInTransaction("Force cleanup", async () => {
            if (this.traceID) {
                measureProvider.setTraceID(this.traceID);
            }
            let isDone = false;
            while (!isDone) {
                try {
                    await this.doWork();
                    isDone = true;
                } catch (error) {
                    log.error(error, "Failed to force cleanup brand %s", this.brandId);
                    await sleep(1000);
                }
            }
            await JobStateTracker.markAsDone(this.brandId);
        });
    }

    public static async start(brandId: number) {
        const traceID = measureProvider.getTraceID();
        const job = new ForceCleanupJob(brandId, traceID);
        const isNewJob = await JobStateTracker.init(brandId, traceID);
        // start job asynchronously
        if (isNewJob) {
            job.execute();
        } else {
            log.warn({ brandId, traceID }, "Detect retransmission for start force cleanup");
        }
    }

    public static async repair(timeout: number) {
        const jobs = await JobStateTracker.getJobs();
        for (const state of jobs) {
            try {
                if (Date.now() - state.lastTs >= timeout) {
                    const job = new ForceCleanupJob(state.brandId);
                    const isCaught = await JobStateTracker.catchJob(state);
                    if (isCaught) {
                        // start job asynchronously
                        job.execute();
                    }
                }
            } catch (error) {
                log.error(error, "Error to repair force cleanup job");
            }
        }
    }
}
