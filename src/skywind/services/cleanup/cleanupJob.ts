import config from "../../config";
import { logging } from "@skywind-group/sw-utils";
import { CleanupService } from "./cleanupService";
import { Consumer, UnloadService } from "../../history/unloadService";
import { CleanupGameContextQueue, CleanupPlayerContextQueue, RedisQueue } from "../queue/redisQueue";
import { CleanupContextRequest } from "../../history/history";
import { CleanupGameContextService } from "./cleanupGameContextService";
import { CleanupPlayerContextService } from "./cleanupPlayerContextService";

const log = logging.logger("skywind:slot-engine:cleanup");

interface CleanupConfig {
    batchSize: number;
}

class CleanupConsumer<ID> implements Consumer<CleanupContextRequest> {
    constructor(private readonly cleanupService: CleanupService) {
    }

    public async save(items: CleanupContextRequest[]): Promise<void> {
        const [savedCount, removedCount] = await this.cleanupService.cleanUp(items.map(i => i.id), false);
        log.info("Processed %s contexts, save to db %s contexts, removed %s",
            savedCount + removedCount,
            savedCount,
            removedCount);
    }
}

export abstract class CleanupJob extends UnloadService<CleanupContextRequest> {

    constructor(private readonly cleanupService: CleanupService,
                private readonly cleanupConfig: CleanupConfig,
                queue: RedisQueue<CleanupContextRequest>) {
        super(queue, new CleanupConsumer(cleanupService), config.cleanUp.batchSize);
    }

    public async moveContextToQueue(): Promise<void> {
        const batchSize = this.cleanupConfig.batchSize;

        const maxTimestamp = Date.now();
        const count = await this.cleanupService.enqueueContexts(maxTimestamp, batchSize);

        if (count > 0) {
            log.info("Enqueue for cleanup %s 'cold' contexts, ts= %s to %s queue", count, maxTimestamp, this.queueName);
        }
    }
}

export class CleanupGameContextJob extends CleanupJob {
    constructor() {
        super(new CleanupGameContextService(), config.cleanUp, new CleanupGameContextQueue());
    }
}

export class CleanupPlayerContextJob extends CleanupJob {
    constructor() {
        super(new CleanupPlayerContextService(), config.cleanUpPlayers, new CleanupPlayerContextQueue());
    }
}
