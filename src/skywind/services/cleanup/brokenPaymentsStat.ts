import { GameFlowContext } from "../context/gamecontext";
import { lazy, measures, sleep, } from "@skywind-group/sw-utils";
import { Redis as RedisClient } from "ioredis";
import { getFactory, Redis } from "../../storage/redis";
import * as uuid from "uuid";
import CompletionService from "../pendingCompletionService";
import config from "../../config";
import measureProvider = measures.measureProvider;

type BrandKeyValue = [string, number];

export class BrandMap extends Map<string, number> {

    public toJSON() {
        return [...this.entries()].reduce((map, [brandId, value]) => {
            map.push([brandId, value]);
            return map;
        }, []);
    }

    public merge(values: BrandKeyValue[]): void {
        for (const [key, v] of values) {
            let value: number = this.get(key);
            if (!value) {
                value = v;
            } else {
                value += v;

            }
            this.set(key, value);
        }
    }

    public normalize() {
        return [...this.entries()].reduce((map, [brandId, pendingPayments]) => {
            if (pendingPayments > 0) {
                map.push({ brandId, pendingPayments });
            }
            return map;
        }, []);
    }
}

export class BrokenPaymentsStat {
    private byBrand: BrandMap = new BrandMap();
    private static BROKEN_PAYMENT_CHANNEL = "broken_payments";
    private static BROKEN_PAYMENT_CLEAR_CHANNEL = "broken_payments_clear";

    constructor(private readonly sub: RedisClient, private readonly id: string = uuid.v4()) {
        this.sub.subscribe(BrokenPaymentsStat.BROKEN_PAYMENT_CHANNEL, BrokenPaymentsStat.BROKEN_PAYMENT_CLEAR_CHANNEL);
        this.sub.on("message", async (channelId, requestId) => {
            if (channelId === BrokenPaymentsStat.BROKEN_PAYMENT_CLEAR_CHANNEL) {
                await this.byBrand.clear();
            } else {
                const client = await Redis.get().get();
                const multi = client.multi();
                try {
                    multi.hset(requestId, this.id, JSON.stringify(this.byBrand))
                        .pexpire(requestId, 2 * config.cleanUp.brokenPaymentMonitoringRequestTimeout);
                    await multi.exec();
                } catch (e) {
                    await multi.discard();
                } finally {
                    await Redis.get().release(client);
                }
            }
        });
    }

    public async clear() {
        const client = await Redis.get().get();
        try {
            await client.publish(BrokenPaymentsStat.BROKEN_PAYMENT_CLEAR_CHANNEL, "");
        } finally {
            await Redis.get().release(client);
        }
    }

    public visitBeforeCommit(ctx: GameFlowContext): void {
        measureProvider.incrementGauge("retransmission", 1);
        if (ctx.retryAttempts === 1) {
            this.inc(ctx, 1);
        }
    }

    public visitAfterCommit(ctx: GameFlowContext): void {
        this.inc(ctx, -1);
    }

    public visitAfterFailCommit(ctx: GameFlowContext): void {
        measureProvider.incrementGauge("failed_retransmission", 1);
        if (!CompletionService.requireCompletion(ctx)) {
            this.inc(ctx, -1);
        }
    }

    private inc(ctx: GameFlowContext, v: number) {
        const brandId = ctx.corrupted ? ctx?.gameData?.gameTokenData?.brandId : ctx.id.brandId;
        let value = this.byBrand.get(brandId.toString());
        if (!value) {
            value = v;
        } else {
            value += v;

        }
        this.byBrand.set(brandId.toString(), value);
    }

    public async getData(): Promise<any> {
        const requestId = `${config.namespaces.cleanupGameContextPrefix}:broken_payments_monitoring:${uuid.v4()}`;
        const result = new BrandMap();
        let client = await Redis.get().get();
        try {
            await client.publish(BrokenPaymentsStat.BROKEN_PAYMENT_CHANNEL, requestId);
        } finally {
            await Redis.get().release(client);
        }

        await sleep(config.cleanUp.brokenPaymentMonitoringRequestTimeout);
        client = await Redis.get().get();
        try {
            const response = await client.hgetall(requestId);
            for (const value of Object.values(response)) {
                if (value) {
                    result.merge(JSON.parse(value as string));
                }
            }
        } finally {
            await Redis.get().release(client);
        }

        return result.normalize();
    }
}

export default lazy<Promise<BrokenPaymentsStat>>(async () => new BrokenPaymentsStat(await getFactory().createClient()));
