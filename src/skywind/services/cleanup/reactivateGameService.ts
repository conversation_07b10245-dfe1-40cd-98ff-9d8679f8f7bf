import { ContextManager } from "../contextmanager/contextManager";
import { getGameFlowContextManager } from "../contextmanager/contextManagerImpl";
import { GameContextID } from "../contextIds";
import { measures } from "@skywind-group/sw-utils";
import { GameContextNotExists } from "../../errors";
import measure = measures.measure;

export interface ReactivateRequestData {
    gameContextId: string;
    brandId: number;
}

/**
 * Reactivate game service.
 */
export class ReactivateGameService {
    private readonly contextManager: ContextManager = getGameFlowContextManager();

    @measure({ name: "ReactivateGameService.reactivateGame", isAsync: true })
    public async reactivateGame(gameContextId: string): Promise<void> {
        const id = GameContextID.createFromString(gameContextId);
        const context = await this.contextManager.findOrRestoreGameContext(id);
        if (!context) {
            return Promise.reject(new GameContextNotExists());
        }
    }
}

export default new ReactivateGameService();
