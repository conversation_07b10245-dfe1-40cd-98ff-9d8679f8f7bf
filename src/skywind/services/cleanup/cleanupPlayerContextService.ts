import { logging, measures } from "@skywind-group/sw-utils";
import { PlayerContextID } from "../contextIds";
import { Redis } from "../../storage/redis";
import config from "../../config";
import { CleanupService } from "./cleanupService";
import { PlayerContext } from "../playercontext/playerContext";
import { ContextManager } from "../contextmanager/contextManager";
import { getGameFlowContextManager } from "../contextmanager/contextManagerImpl";
import { RedisProc } from "../redisCommandExecutor";
import measure = measures.measure;
import measureParam = measures.measureParam;

const log = logging.logger("skywind:slot-engine:cleanup");

export class CleanupPlayerContextService implements CleanupService {

    private contextManager: ContextManager = getGameFlowContextManager();
    private enqueueContextsProc = new RedisProc(
        __dirname + "/../../../../resources/lua/playerContextManager.lua",
        __dirname + "/../../../../resources/lua/enqueueColdPlayerContext.lua");

    @measure({ name: "CleanupPlayerContextService.enqueueContexts", isAsync: true })
    public async enqueueContexts(maxTimestamp: number, batchSize): Promise<number> {
        let result: number = 0;
        while (true) {
            const client = await Redis.get().get();
            try {
                const count = await this.enqueueContextsProc.exec<number>(client,
                    [config.namespaces.lastPlayerActivityPrefix, config.namespaces.cleanupPlayerContextPrefix],
                    [maxTimestamp.toString(), batchSize.toString()]);
                if (!count) {
                    break;
                }
                result += count;
            } finally {
                await Redis.get().release(client);
            }
        }

        return result;
    }

    @measure({ name: "CleanupPlayerContextService.forceCleanUp", isAsync: true })
    public async forceCleanUp(@measureParam()brandId: number, batchSize: number,
                              progressCallback?: (count: number) => Promise<void>): Promise<[number, number]> {
        let totalCount = 0;
        let saved = 0;

        while (true) {
            const contextIds = await this.contextManager.findTopActivePlayerContextIDsByBrand(brandId, batchSize);
            if (!contextIds || contextIds.length === 0) {
                break;
            }
            const [processed, moved] = await this.doCleanUp(contextIds, true);
            if (progressCallback) {
                await progressCallback(processed);
            }
            totalCount += processed;
            saved += moved;
        }

        return [totalCount, saved];
    }

    @measure({ name: "CleanupPlayerContextService.processContexts", isAsync: true })
    public async cleanUp(ids: string[], force: boolean): Promise<[number, number]> {
        return this.doCleanUp(ids.map(i => PlayerContextID.createFromString(i)), force);
    }

    private async doCleanUp(contextIds: PlayerContextID[], force: boolean): Promise<[number, number]> {
        const contexts = await this.findAllContexts(contextIds);

        const { toSave, toRemove } = this.filterContexts(contexts);

        if (toRemove && toRemove.length > 0) {
            toRemove.sort((c1, c2) => c1.id.asString().localeCompare(c2.id.asString()));
            await this.contextManager.removePlayerContextOffline(toRemove);
        }

        if (toSave && toSave.length > 0) {
            toSave.sort((c1, c2) => c1.id.asString().localeCompare(c2.id.asString()));
            await this.contextManager.savePlayerContextOffline(toSave);
        }

        await this.removeAllContexts(contexts, force);

        return [contexts.length, toSave.length];
    }

    private filterContexts(contexts: PlayerContext[]) {
        const toSave: PlayerContext[] = [];
        const toRemove: PlayerContext[] = [];
        for (const ctx of contexts) {
            if (ctx) {
                if (ctx.keepOffline) {
                    toSave.push(ctx);
                } else {
                    toRemove.push(ctx);
                }
            }
        }

        return { toSave, toRemove };
    }

    private async findAllContexts(contextIds: PlayerContextID[]): Promise<PlayerContext[]> {
        return Promise.all(contextIds.map((id) => this.contextManager.findPlayerContextById(id)));
    }

    private async removeAllContexts(contexts: PlayerContext[], force: boolean) {
        return Promise.all(contexts.map(async (ctx) => {
            if (ctx) {
                const removeResult = await ctx.remove(force);
                if (!removeResult && !force) {
                    log.warn({ id: ctx.id.asString() }, "Cannot remove player context");
                    await ctx.markActive();
                }
            }
        }));
    }
}
