import { BaseRequest, ClientResponse } from "@skywind-group/sw-game-core";
import { AbstractGameController } from "./gamecontroller";
import { measures } from "@skywind-group/sw-utils";
import { EngineGameFlow } from "./gameflow";
import { GameFlowFactory } from "./gameFlowFactory";

/**
 *  Implementation of slot game controller
 */
export class SyncGameController extends AbstractGameController {
    protected async createFlowForRequest<T extends BaseRequest>(req: T,
                                                                checkConcurrency?: boolean,
                                                                checkPending?: boolean): Promise<EngineGameFlow<T>> {
        return GameFlowFactory.createForRequest(req, this.loadGame, checkConcurrency, checkPending);
    }
}

let controller: SyncGameController;

export function getSyncGameController(): SyncGameController {
    if (!controller) {
        controller = new SyncGameController();
    }

    return controller;
}
