import { GameFlowContext, MerchantLogoutResult } from "./context/gamecontext";
import { ManagementAPISupport } from "./managementapihelper";
import { measures, publicId } from "@skywind-group/sw-utils";
import config from "../config";
import * as Errors from "../errors";
import measure = measures.measure;

export enum LogoutGameState {
    /**
     * Has unfinished payment (broken payment)
     */
    BROKEN_PAYMENT = "brokenPayment",
    /**
     * round is not finished
     */
    UNFINISHED = "unfinished",

    /**
     *  Last round was successfully finished
     */
    FINISHED = "finished"
}

export interface LogoutRequest {
    roundId: string;
    roundPID: string;
    logoutId: string;
    gameContextId: string;
    gameToken: string;
    state: LogoutGameState;
}

export interface LoginRequest {
    roundId: string;
    logoutId: string;
    roundPID: string;
    gameContextId: string;
    gameToken: string;
}

export interface LoogoutResponse {
    requireLogin?: boolean;
}

/**
 *  Service is responsible to logging/logout player to/from game session on merchant side
 *
 *  It's used for merchant broken games and regulation session management
 */
export class MerchantGameSessionService extends ManagementAPISupport {
    private static LOGOUT_URL = "/v2/play/game/logout";
    private static LOGIN_URL = "/v2/play/game/login";

    constructor() {
        super(config.managementAPI.url);
    }

    /**
     * Send logout message:
     *  - mark context "pending logout"
     *  - do logout
     *  - mark context with finite state (skip login, require login)
     */
    @measure({ name: "MerchantGameSessionService.logout", isAsync: true })
    public async logout(ctx: GameFlowContext, ignorePayments: boolean = false): Promise<LoogoutResponse> {
        if (ctx.brokenPayment && !ignorePayments) {
            return Promise.reject(new Errors.LogoutForbiddenError());
        }
        await ctx.setLogoutResult(MerchantLogoutResult.PENDING_LOGOUT);

        const req: LogoutRequest = {
            roundId: ctx.roundId,
            roundPID: publicId.instance.encode(+ctx.roundId),
            logoutId: ctx.logoutId,
            gameContextId: ctx.id.asString(),
            gameToken: ctx.gameData.gameTokenData.token,
            state: this.getState(ctx)
        };
        const result = await this.post<LoogoutResponse>(MerchantGameSessionService.LOGOUT_URL, req);
        await ctx.setLogoutResult(result.requireLogin ?
                                  MerchantLogoutResult.REQUIRE_LOGIN :
                                  MerchantLogoutResult.SKIP_LOGIN);

        return result;
    }

    /**
     * Re-login player to the game session on merchant side.
     * Cleanup logout status
     *
     */
    @measure({ name: "MerchantGameSessionService.login", isAsync: true })
    public async login(ctx: GameFlowContext, increaseLogoutId?: boolean): Promise<void> {
        let newLogoutId = null;
        if (ctx.logoutResult === MerchantLogoutResult.REQUIRE_LOGIN) {
            const req: LoginRequest = {
                roundId: ctx.roundId,
                logoutId: ctx.logoutId,
                roundPID: publicId.instance.encode(+ctx.roundId),
                gameContextId: ctx.id.asString(),
                gameToken: ctx.gameData.gameTokenData.token,
            };
            await this.post<any>(MerchantGameSessionService.LOGIN_URL, req);
            if (increaseLogoutId) {
               newLogoutId = this.getIncreasedValueForLogoutId(ctx.logoutId);
            }
        }
        await ctx.setLogoutResult(null, newLogoutId);
    }

    private getState(ctx: GameFlowContext): LogoutGameState {
        if (ctx.brokenPayment) {
            return LogoutGameState.BROKEN_PAYMENT;
        } else if (ctx.unfinished) {
            return LogoutGameState.UNFINISHED;
        } else {
            return LogoutGameState.FINISHED;
        }
    }

    private getIncreasedValueForLogoutId(logoutId: string): string {
        const separator = "_";
        const separatorIndex = logoutId.indexOf(separator);
        if (separatorIndex === -1) {
            return `${logoutId}${separator}0`;
        }
        const originalLogoutId = logoutId.substr(0, separatorIndex);
        const currentCounter = logoutId.substr(separatorIndex + separator.length, logoutId.length);
        return `${originalLogoutId}${separator}${+currentCounter + 1}`;
    }
}

export default new MerchantGameSessionService();
