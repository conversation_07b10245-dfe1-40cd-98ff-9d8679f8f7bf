import * as http from "http";

export interface Probs {
    readonly alive: boolean;

    readonly ready: boolean;

    markDead();

    markReady();

    markDecomissioned();

    install(server: http.Server, url?: string): void;

    subscribeAlive(listener: ProbeStateCallback): void;

    subscribeReady(listener: ProbeStateCallback): void;
}

export type ProbeStateCallback = (value: boolean) => any;

class ProbsImpl implements Probs {
    public alive: boolean = true;
    public ready: boolean = false;

    private livenessCallbacks: Array<ProbeStateCallback> = [];
    private readinessCallbacks: Array<ProbeStateCallback> = [];

    public install(server: http.Server, url: string = "/probs") {
        const listeners = server.listeners("request").slice(0);
        server.removeAllListeners("request");
        server.on("request", (req: http.IncomingMessage, res: http.ServerResponse) => {
            if (req.url.startsWith(url)) {
                res.writeHead(200, {
                    "x-alive": `${this.alive}`,
                    "x-ready": `${this.ready}`
                });
                res.end();
            } else {
                listeners.forEach(l => l.call(server, req, res));
            }
        });

        process.on("SIGTERM", () => {
            this.markDecomissioned();
        });
    }

    public markDead() {
        this.alive = false;
        this.notifyChangeLiveness(false);
    }

    public markDecomissioned() {
        this.ready = false;
        this.notifyChangeReadiness(false);
    }

    public markReady() {
        this.ready = true;
        this.notifyChangeReadiness(true);
    }

    public subscribeReady(listener: ProbeStateCallback): void {
        this.readinessCallbacks.push(listener);
    }

    public subscribeAlive(listener: ProbeStateCallback): void {
        this.livenessCallbacks.push(listener);
    }

    private notifyChangeLiveness(value: boolean): void {
        this.livenessCallbacks.forEach(c => c(value));
    }

    private notifyChangeReadiness(value: boolean): void {
        this.readinessCallbacks.forEach(c => c(value));
    }
}

export const ServerProbs: Probs = new ProbsImpl();
