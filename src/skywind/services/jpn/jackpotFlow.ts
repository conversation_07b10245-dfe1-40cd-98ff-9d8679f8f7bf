import { Balance } from "../wallet";
import {
    Jack<PERSON><PERSON>ontext, Jack<PERSON>Info, Jackpot<PERSON>perationResult, JackpotTrxResult
} from "./jackpot";
import { JackpotService } from "./jackpotService";
import { EngineGameFlow } from "../gameflow";
import { BaseRequest, JackpotResult, JackpotResults, TickersResponse } from "@skywind-group/sw-game-core";
import { deepClone } from "../../utils/cloner";
import { JackpotUtil } from "./jackpotUtil";
import { AuthResponse as JPNAuthResponse } from "@skywind-group/sw-jpn-core";
import { getJPNServer } from "./jpnserver";

export interface JackpotPendingProcessResult {
    balance?: Balance;
    jackpotContext?: JackpotContext;
    jpnResult?: JackpotOperationResult;
}

export class JackpotFlow {
    private currentJackpotTickers: TickersResponse;
    private currentJackpotResult: JackpotTrxResult;

    constructor(public readonly flow: EngineGameFlow<BaseRequest>,
                public readonly jackpotService?: JackpotService) {
    }

    public async processJackpotOperation(): Promise<JackpotPendingProcessResult> {
        if (this.flow.jackpotPending && !JackpotUtil.isConfirmOperation(this.flow.jackpotPending.jackpotOperation)) {
            JackpotUtil.removeDuplicatesFromPending(this.flow.jackpotPending);
            const [jackpotOperationToken, jpContext] = await this.authDisabledJackpotsIfNeeded();
            if (!jpContext) {
                // jackpot is not available anymore, skip operation
                return {};
            }
            const jpnResult = await this.executePendingOperations(jackpotOperationToken);
            const jackpot = jpnResult.jackpot;
            const result: JackpotPendingProcessResult = { jpnResult };
            const isWinResult: boolean = JackpotUtil.isWinResult(jpnResult);
            const isStartMiniGame = JackpotUtil.isStartMiniGameResult(result.jpnResult);
            const isStartInstantJpMiniGame = JackpotUtil.isStartInstantJpMiniGameResult(result.jpnResult);

            if (isWinResult || isStartMiniGame || isStartInstantJpMiniGame) {
                result.jackpotContext = {
                    token: jpContext.token,
                    jackpotsInfo: jpContext.jackpotsInfo,
                    contributionEnabled: jpContext.contributionEnabled,
                    jackpot: jackpot
                };
                if (isStartInstantJpMiniGame) {
                    result.jackpotContext.instantJackpotAwarded = true;
                }
                if (isWinResult) {
                    const jpPendingFactory = this.flow.jackpotPendingFactory;
                    const confirm = await jpPendingFactory.createConfirmWinModification(jackpot, jpContext, jpnResult);
                    await this.flow.updateJackpotPendingModification(confirm, result, result.jackpotContext);
                }
            } else if (JackpotUtil.allJackpotsDisabled(jpContext)) {
                result.jackpotContext = null;
            } else if (JackpotUtil.isContributionOperation(this.flow.jackpotPending.jackpotOperation) ||
                JackpotUtil.isMiniGameOperation(this.flow.jackpotPending.jackpotOperation) ||
                JackpotUtil.isCheckWinOperation(this.flow.jackpotPending.jackpotOperation)) {
                result.jackpotContext = {
                    token: jpContext.token,
                    jackpotsInfo: jpContext.jackpotsInfo,
                    contributionEnabled: jpContext.contributionEnabled,
                };
            }

            this.currentJackpotTickers = jpnResult.tickers || this.currentJackpotTickers;
            this.currentJackpotResult = jackpot;

            return result;
        } else {
            return {};
        }
    }

    public async resolveJackpotWin(prevWinResult: JackpotOperationResult): Promise<JackpotPendingProcessResult> {
        if (this.flow.jackpotPending) {
            const [currentToken, jpContext] = await this.authDisabledJackpotsIfNeeded();
            const jpnResult = await this.executePendingOperations(currentToken);
            const result: JackpotPendingProcessResult = { jpnResult };

            if (prevWinResult) {
                result.jpnResult = prevWinResult;
            } else {
                const jpResult = this.flow?.jackpotPending?.jackpotOperation?.previousResult;
                if (jpResult) {
                    result.jpnResult = jpResult;
                }
            }

            if (JackpotUtil.allJackpotsDisabled(jpContext)) {
                result.jackpotContext = null;
            } else {
                result.jackpotContext = this.createJackpotContextWithPreviousMiniGameResult(jpContext, result);
            }

            this.currentJackpotTickers = jpnResult.tickers || this.currentJackpotTickers;
            this.currentJackpotResult = jpContext.jackpot;
            return result;
        }
    }

    private createJackpotContextWithPreviousMiniGameResult(jpContext,
                                                           result: JackpotPendingProcessResult): JackpotContext {
        const prevResults: JackpotResult[] = result.jpnResult.jackpot.result;
        if (prevResults) {
            const miniGameResult: JackpotResult[] = prevResults.filter(r => [
                "start-mini-game",
                "start-instant-jp-mini-game"
            ].includes(r.event));
            if (miniGameResult && miniGameResult.length > 0) {
                return {
                    token: jpContext.token,
                    jackpotsInfo: jpContext.jackpotsInfo,
                    contributionEnabled: jpContext.contributionEnabled,
                    jackpot: {
                        transactionId: result.jpnResult.jackpot.transactionId,
                        result: miniGameResult
                    }
                };
            }
        }
        return {
            token: jpContext.token,
            jackpotsInfo: jpContext.jackpotsInfo,
            contributionEnabled: jpContext.contributionEnabled
        };
    }

    private async executePendingOperations(token: string): Promise<JackpotOperationResult> {
        const jackpotPending = this.flow.jackpotPending;
        if (jackpotPending) {
            const jackpotOperationPayload = this.flow?.jackpotPending?.jackpotOperation?.payload;
            if (!jackpotOperationPayload?.externalId && jackpotPending?.jackpotOperation?.payload) {
                const externalTrxId = this.flow?.pendingModification?.walletOperation?.transactionId;
                jackpotOperationPayload.externalId = externalTrxId;
            }
            return await this.jackpotService.commitOperation(jackpotPending.jackpotOperation, token);
        }
    }

    public jackpotResult(): JackpotResults {
        if (!this.currentJackpotResult && !this.flow.jackpotContext) {
            return;
        }
        return JackpotUtil.getJackpotResult(this.currentJackpotResult || this.flow.jackpotContext.jackpot,
            this.flow.jackpotContext);
    }

    public isMiniGameTriggered(result: JackpotPendingProcessResult): boolean {
        const jackpotResults: JackpotResult[] = result?.jpnResult?.jackpot?.result;
        if (jackpotResults) {
            return jackpotResults.some(r => r.event === "start-mini-game");
        }
        return false;
    }

    public isInstantJpMiniGameTriggered(result?: JackpotPendingProcessResult): boolean {
        const jackpotResults: JackpotResult[] = result?.jpnResult?.jackpot?.result;
        if (jackpotResults) {
            return jackpotResults.some(r => r.event === "start-instant-jp-mini-game");
        }
        if (this.flow.jackpotContext) {
            return this.flow.jackpotContext.instantJackpotAwarded;
        }
        return false;
    }

    public async jackpotTickers(): Promise<TickersResponse> {
        if (!this.flow.jackpotContext) {
            return;
        }
        if (!this.currentJackpotTickers) {
            const jpContext = this.flow.jackpotContext;
            const rates = {};
            if (jpContext.jackpotsInfo) {
                for (const jp of jpContext.jackpotsInfo) {
                    rates[jp.currency] = this.flow.getExchangeRate(jp.currency);
                }
            }
            const tickers: TickersResponse = await this.jackpotService.getTickers(rates);
            if (tickers) {
                this.currentJackpotTickers = tickers;
            }
        }
        return deepClone(this.currentJackpotTickers);
    }

    private async authDisabledJackpotsIfNeeded(): Promise<[string, JackpotContext]> {
        let jpContext = this.flow.jackpotContext;
        let token = jpContext && jpContext.token;
        const jackpotIds = JackpotUtil.getJackpotIds(this.flow.jackpotPending.jackpotOperation);
        if (!jackpotIds) {
            return [token, jpContext];
        }

        const allAuthorized = this.flow.jackpotContext && jackpotIds.every((id) => {
            return this.flow.jackpotContext.jackpotsInfo.find((jp) => jp.id === id) !== undefined;
        });
        if (allAuthorized) {
            return [token, jpContext];
        }

        const gameData = this.flow.gameData;
        const jpnAuth: JPNAuthResponse = await getJPNServer().auth({
            playerCode: gameData.gameTokenData.playerCode,
            brandId: gameData.gameTokenData.brandId,
            currency: gameData.gameTokenData.currency,
            jackpotIds: jackpotIds,
            gameCode: gameData.gameTokenData.gameCode,
            region: gameData.region,
            includeDisabled: true
        });

        token = jpnAuth.token;

        if (jpContext) {
            const authorizedJackpots = new Set(jpnAuth.jackpots.map(jp => jp.id));
            jpContext.jackpotsInfo = jpContext.jackpotsInfo.filter(jp => !authorizedJackpots.has(jp.id));
            jpContext.jackpotsInfo.push(...jpnAuth.jackpots as JackpotInfo[]);
        } else {
            jpContext = {
                token: token,
                jackpotsInfo: jpnAuth.jackpots as JackpotInfo[],
                contributionEnabled: true
            };
        }

        this.jackpotService.context = jpContext;

        return [token, jpContext];
    }
}
