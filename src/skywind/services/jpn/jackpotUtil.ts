import { BaseRequest, <PERSON><PERSON><PERSON><PERSON>, JackpotR<PERSON>ult, JackpotResults } from "@skywind-group/sw-game-core";
import { deepClone } from "../../utils/cloner";
import { EngineGameFlow } from "../gameflow";
import { JackpotContext, <PERSON><PERSON><PERSON><PERSON>ation, JackpotOperationResult, JackpotTrxResult } from "./jackpot";
import { CheckWinRequest, WinJackpotRequest } from "@skywind-group/sw-jpn-core";
import { JackpotTypeStatistic } from "../context/jpStatistics";
import { GameFlowContext, JackpotPendingModification } from "../context/gamecontext";

export class JackpotUtil {

    private static readonly FORBIDDEN_ACTIONS_DURING_MINI_GAME = ["spin", "bet", "deferred-update"];

    private static readonly MINI_GAME_ACTIONS = [
        "start-mini-game",
        "play-mini-game"
    ];

    private static readonly INSTANT_JP_MINI_GAME_ACTIONS = [
        "start-instant-jp-mini-game",
        "play-instant-jp-mini-game"
    ];

    public static isStartMiniGameResult(result: { jackpot?: JackpotTrxResult }): boolean {
        return result.jackpot && result.jackpot.result.some(r => r.event === "start-mini-game");
    }

    public static isStartInstantJpMiniGameResult(result: { jackpot?: JackpotTrxResult }): boolean {
        return result.jackpot && result.jackpot.result.some(r => r.event === "start-instant-jp-mini-game");
    }

    public static isWinResult(result: JackpotOperationResult): boolean {
        return result.jackpot && result.jackpot.result.some(r => r.event === "win");
    }

    public static isContributionOperation(operation: JackpotOperation): boolean {
        return operation.type === "contribution";
    }

    public static isCheckWinOperation(operation: JackpotOperation): boolean {
        return operation.type === "check-win";
    }

    public static isMiniGameOperation(operation: JackpotOperation): boolean {
        return operation.type === "mini-game";
    }

    public static isWinOperation(operation: JackpotOperation): boolean {
        return operation.type === "win-jackpot";
    }

    public static isConfirmOperation(operation: JackpotOperation): boolean {
        return operation && operation.type === "win-confirm";
    }

    public static isRollbackOperation(operation: JackpotOperation): boolean {
        return operation && operation.type === "win-rollback";
    }

    public static validateJackpotState(flow: EngineGameFlow<BaseRequest>): boolean {

        const req = flow.request();
        const jpContext = flow.jackpotContext;
        if (jpContext && jpContext.jackpot) {
            if (this.isStartMiniGameResult(jpContext)) {
                return JackpotUtil.MINI_GAME_ACTIONS.includes(req.request) ||
                    !JackpotUtil.FORBIDDEN_ACTIONS_DURING_MINI_GAME.includes(req.request);
            }
            if (this.isStartInstantJpMiniGameResult(jpContext)) {
                const flowContext = flow.flowContext;
                const round = flowContext?.round;
                const lastRound = flowContext?.lastRound;
                const roundEnded = flowContext?.roundEnded;
                if ((round && round.instantJackpot) || (roundEnded && lastRound && lastRound.instantJackpot)) {
                    return JackpotUtil.INSTANT_JP_MINI_GAME_ACTIONS.includes(req.request) ||
                        !JackpotUtil.FORBIDDEN_ACTIONS_DURING_MINI_GAME.includes(req.request);
                }
            }
        }
        return ![...JackpotUtil.MINI_GAME_ACTIONS, ...JackpotUtil.INSTANT_JP_MINI_GAME_ACTIONS].includes(req.request);
    }

    public static isInstantJackpot(flow: EngineGameFlow): boolean {
        return flow.jackpotContext && flow.jackpotContext.jackpotsInfo.some(jp => jp.baseType === "sw-instant-jp");
    }

    public static isInstantJackpotMiniGame(context: GameFlowContext): boolean {
        return context?.round?.instantJackpot;
    }

    public static isInstantJackpotMiniGameFinished(history?: GameHistory) {
        return history?.type === "jackpot-mini-game" && history?.data?.request === "play-instant-jp-mini-game";
    }

    public static getJackpotResult(jackpot: JackpotTrxResult, jpContext?: JackpotContext): JackpotResults {

        if (!jackpot) {
            return;
        }
        let filtered = jackpot.result;
        if (jpContext) {
            filtered = jackpot.result.filter(jp => {
                const jackpotInfo = jpContext.jackpotsInfo.find(jpInfo => jpInfo.id === jp.jackpotId);
                return !!jackpotInfo?.isGameOwned || jackpotInfo?.baseType === "sw-instant-jp";
            });
        }
        if (filtered.length === 0) {
            return;
        }

        const results: JackpotResult[] = [];
        for (const result of filtered) {
            const win = deepClone(result);
            win.transactionId = undefined;
            results.push(win);
        }
        return results.length === 1 ? results[0] : results;
    }

    public static normalizeAmountByPrecision(precision: number, value: number) {

        if (!Number.isFinite(value)) {
            return value;
        }

        if (!precision) {
            return value;
        }

        return +(value.toFixed(precision));
    }

    public static safeAddWithPrecision(precision: number, a: number, b: number) {

        if (!Number.isFinite(a)) {
            return this.normalizeAmountByPrecision(precision, b);
        }

        if (!Number.isFinite(b)) {
            return this.normalizeAmountByPrecision(precision, a);
        }

        if (!precision) {
            return a + b;
        }

        const normalizedA = this.normalizeAmountByPrecision(precision, a);
        const normalizedB = this.normalizeAmountByPrecision(precision, b);
        return this.normalizeAmountByPrecision(precision, normalizedA + normalizedB);
    }

    public static getJackpotIds(jackpotOperation: JackpotOperation): string[] {
        const payload = jackpotOperation && jackpotOperation.payload;
        if (!payload) {
            return;
        }
        let jackpotIds;
        if ((payload as CheckWinRequest).jackpotIds) {
            jackpotIds = (payload as CheckWinRequest).jackpotIds;
        } else if ((payload as WinJackpotRequest).jackpotId) {
            jackpotIds = (payload as WinJackpotRequest).jackpotId;
        }
        return jackpotIds && !Array.isArray(jackpotIds) ? [jackpotIds] : jackpotIds;
    }

    // Fix pending contribution action
    public static removeDuplicatesFromPending(jackpotPending: JackpotPendingModification): void {
        const jackpotIds = JackpotUtil.getJackpotIds(jackpotPending.jackpotOperation);
        if (jackpotIds && jackpotIds.length > 1) {
            const uniqueJackpots = new Set();
            const fixedUniqueJackpotIds = jackpotIds.filter(jpId => {
                if (!uniqueJackpots.has(jpId)) {
                    uniqueJackpots.add(jpId);
                    return true;
                }
                return false;
            });
            (jackpotPending.jackpotOperation.payload as CheckWinRequest).jackpotIds = fixedUniqueJackpotIds;
        }
    }

    public static allJackpotsDisabled(jpContext: JackpotContext) {
        if (!jpContext) {
            return true;
        }
        return !jpContext.jackpotsInfo.find((jp) => !jp.isDisabled);
    }

    public static getJackpotTypeStatistic(context: JackpotContext): JackpotTypeStatistic {
        if (!context?.jackpotsInfo) {
            return {};
        }

        const jackpotTypeStatistic: JackpotTypeStatistic = {};

        for (const { type, id } of context.jackpotsInfo.filter(info => !info.isDisabled)) {
            if (jackpotTypeStatistic[type]) {
                jackpotTypeStatistic[type].push(id);
            } else {
                jackpotTypeStatistic[type] = [id];
            }
        }

        return jackpotTypeStatistic;
    }

}
