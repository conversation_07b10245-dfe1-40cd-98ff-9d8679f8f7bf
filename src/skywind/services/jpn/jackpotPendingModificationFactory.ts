import { WalletOperation } from "../wallet";
import { <PERSON><PERSON><PERSON>ontex<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Jackpot<PERSON><PERSON>ationR<PERSON>ult, JackpotTrxR<PERSON>ult } from "./jackpot";
import { JackpotService } from "./jackpotService";
import { EngineGameFlow } from "../gameflow";
import {
    BaseRequest,
    CheckJackpotWin,
    Contribution,
    GameHistory,
    JackpotAction,
    JackpotResult,
    MiniGame,
    WinJackpot
} from "@skywind-group/sw-game-core";
import { ExchangeRates, MiniGameRequest, WinConfirmRequest, WinJackpotRequest } from "@skywind-group/sw-jpn-core";
import { deepClone } from "../../utils/cloner";
import { getCheats } from "../random";
import { JackpotPendingModification } from "../context/gamecontext";
import * as Errors from "../../errors";

const MARKETING_JACKPOT_GAME_ID = "sw-marketing";

export class JackpotPendingModificationFactory {
    constructor(public flow: <PERSON><PERSON>ame<PERSON>low<BaseRequest>, public readonly jackpotService?: JackpotService) {
    }

    public async create(action: JackpotAction,
                        walletOperation?: WalletOperation): Promise<JackpotPendingModification> {
        if (!this.flow.isJackpotEnabled || walletOperation?.freeBetCoin || walletOperation?.freeBetMode) {
            return undefined;
        }
        const actionType = action && action.type;
        if (actionType === "win-jackpot") {
            return this.createWinJackpotModification(action as WinJackpot, walletOperation);
        } else if (actionType === "mini-game") {
            return this.createMiniGameModification(action as MiniGame);
        } else if (actionType === "checkWin" || actionType === "check-win") {
            return this.createCheckWinModification(action as CheckJackpotWin);
        } else {
            return this.createContribModification(action as Contribution, walletOperation);
        }
    }

    public createMiniGameModification(miniGame: MiniGame): JackpotPendingModification {
        if (!this.flow.isJackpotEnabled) {
            return undefined;
        }
        const jpContext = this.flow.jackpotContext;
        const jackpot = jpContext.jackpot ? jpContext.jackpot.result[0] : undefined;
        if (!jackpot || !["start-mini-game", "start-instant-jp-mini-game"].includes(jackpot.event)) {
            throw new Errors.JackpotStatusNotValid();
        }

        const request: MiniGameRequest = miniGame as any;
        request.transactionId = jpContext.jackpot.transactionId;
        request.jackpotId = jackpot.jackpotId;
        request.exchangeRates = this.getExchangeRates();
        request.roundId = this.flow.flowContext.roundId;
        request.betAmount = this.flow?.flowContext?.round?.currentBet;

        const jackpotOperation: JackpotOperation = {
            type: "mini-game",
            payload: request,
        };

        return { jackpotOperation };
    }

    public async createContribModification(contribution: Contribution,
                                           walletOperation: WalletOperation): Promise<JackpotPendingModification> {
        if (!this.flow.isJackpotEnabled || walletOperation?.freeBetCoin || walletOperation?.freeBetMode) {
            return undefined;
        }

        contribution = this.addGlobalContributions(contribution, walletOperation);

        const jpContext = this.flow.jackpotContext;

        if (jpContext && jpContext.contributionEnabled && contribution) {
            const jpActionType: string = contribution.type || "contribution";

            const contributionPrecision = this.flow.settings().contributionPrecision;
            const payload = deepClone(contribution) as any;
            payload.externalId = walletOperation ? walletOperation.transactionId : undefined;

            payload.exchangeRates = this.getExchangeRates();
            payload.amount = this.flow.fromGameAmount((contribution).amount);
            payload.roundId = this.flow.flowContext.roundId;
            payload.contributionPrecision = contributionPrecision;
            const cheats = getCheats(this.flow.rng());
            if (cheats) {
                payload.cheats = cheats;
            }

            if (this.flow.settings().instantJpEnabled) {
                payload.transactionId = await this.jackpotService.generateTransactionId();
                payload.deferredContribution = undefined;
            } else if (this.flow.settings().deferredContribution || contribution.deferredWinsEnabled) {
                if (contribution.deferredWinsEnabled) {
                    payload.deferredWinsEnabled = true;
                }
                const response = await this.jackpotService.deferredContribute(payload);

                payload.deferredContribution = response.result;
                payload.transactionId = response.transactionId;
            } else {
                payload.transactionId = await this.jackpotService.generateTransactionId();
                payload.deferredContribution = undefined;
            }

            const jackpotOperation: JackpotOperation = {
                type: jpActionType,
                payload
            } as JackpotOperation;
            return { jackpotOperation };
        }
    }

    public async createCheckWinModification(checkWin: CheckJackpotWin): Promise<JackpotPendingModification> {
        if (!this.flow.isJackpotEnabled) {
            return undefined;
        }

        const payload = deepClone(checkWin) as any;

        payload.exchangeRates = this.getExchangeRates();
        payload.roundId = this.flow.flowContext.roundId;

        payload.transactionId = await this.jackpotService.generateTransactionId();
        payload.betAmount = this.flow?.flowContext?.round?.currentBet;

        const cheats = getCheats(this.flow.rng());
        if (cheats) {
            payload.cheats = cheats;
        }
        const jackpotOperation: JackpotOperation = { type: "check-win", payload } as JackpotOperation;
        return { jackpotOperation };
    }

    public async createWinJackpotModification(winJackpot: WinJackpot,
                                              walletOperation?: WalletOperation): Promise<JackpotPendingModification> {
        if (!this.flow.isJackpotEnabled || walletOperation?.freeBetCoin || walletOperation?.freeBetMode) {
            return undefined;
        }

        const request = deepClone(winJackpot as any) as WinJackpotRequest;
        request.transactionId = await this.jackpotService.generateTransactionId();
        request.externalId = walletOperation ? walletOperation.transactionId : undefined;
        request.amount = winJackpot.amount;
        request.exchangeRates = this.getExchangeRates();
        request.roundId = this.flow.flowContext.roundId;
        request.betAmount = this.flow?.flowContext?.round?.currentBet;

        const jackpotOperation: JackpotOperation = {
            type: "win-jackpot",
            payload: request,
        };

        return { jackpotOperation };
    }

    public async createConfirmWinModification(jackpot: JackpotTrxResult,
                                              jackpotContext: JackpotContext,
                                              prevJpResult?: JackpotOperationResult,
                                              deferredWinsEnabled?: boolean):
        Promise<JackpotPendingModification> {

        const walletFactory = this.flow.walletOperationFactory;
        const walletOperation = await walletFactory
            .createJackpotWinPaymentOperation(jackpot, jackpotContext, prevJpResult);
        const filtered: JackpotResult[] = jackpot.result.filter(jp => jp.event === "win");
        const flowContext = this.flow.flowContext;
        const payload: WinConfirmRequest = {
            transactionId: jackpot.transactionId,
            jackpotId: filtered.map(jp => jp.jackpotId),
            roundId: flowContext?.roundId
        };
        if (deferredWinsEnabled) {
            payload.deferredWinsEnabled = true;
        }
        const jackpotOperation: JackpotOperation = {
            type: "win-confirm",
            payload: payload,
            previousResult: prevJpResult
        };
        const history: GameHistory = {
            type: "jackpot-win",
            roundEnded: false,
            data: {
                transactionId: jackpot.transactionId,
                result: filtered
            }
        };
        return { jackpotOperation, walletOperation, history };
    }

    public addGlobalContributions(jackpotAction: Contribution, walletOperation: WalletOperation): Contribution {
        if (jackpotAction && jackpotAction.type && jackpotAction.type !== "contribution") {
            return jackpotAction;
        }

        const bet = walletOperation && walletOperation.bet;
        if (!bet) {
            return jackpotAction;
        }

        const globalJackpots = this.flow.jackpotContext && this.flow.jackpotContext.jackpotsInfo ?
            this.flow.jackpotContext.jackpotsInfo.filter((jp) => !jp.isGameOwned) : undefined;
        if (!globalJackpots || !globalJackpots.length) {
            return jackpotAction;
        }

        let contribution: Contribution = jackpotAction as Contribution;
        if (!contribution) {
            contribution = {
                type: "contribution",
                jackpotIds: [],
                amount: bet
            };
        }
        if (contribution.jackpotIds) {
            contribution.jackpotIds = contribution.jackpotIds.concat(globalJackpots.map((jp) => jp.id));
        } else {
            contribution.jackpotIds = this.flow.jackpotContext.jackpotsInfo.map((jp) => jp.id);
        }
        for (const jp of globalJackpots) {
            if (jp.jpGameId === MARKETING_JACKPOT_GAME_ID) {
                contribution["mrktContributionAmount_" + jp.id] = bet * jp.contribution / 100;
            }
        }
        return contribution;
    }

    private getExchangeRates(): ExchangeRates {
        const jpContext = this.flow.jackpotContext;
        const rates: ExchangeRates = {};
        if (jpContext.jackpotsInfo) {
            for (const jp of jpContext.jackpotsInfo) {
                rates[jp.currency] = this.flow.getExchangeRate(jp.currency);
            }
        }
        return rates;
    }
}
