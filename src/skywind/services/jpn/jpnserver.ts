import * as request from "superagent";
import config from "../../config";
import * as Errors from "../../errors";
import { keepalive, logging, retry } from "@skywind-group/sw-utils";
import {
    AuthRequest,
    AuthResponse,
    CheckWinRequest,
    CheckWinResponse,
    ContributionRequest,
    ContributionResponse,
    DeferredContributionResponse,
    MiniGameRequest,
    MiniGameResponse,
    TickerRequest,
    TickerResponse,
    TransactionIdResponse,
    WinConfirmRequest,
    WinConfirmResponse,
    WinJackpotRequest,
    WinJackpotResponse
} from "@skywind-group/sw-jpn-core";
import { retryCondition } from "../../utils/common";
import HttpsAgent = keepalive.HttpsAgent;

const log = logging.logger("sw-slot-engine:jpn-server");

const agent = keepalive.createAgent(config.jpn.keepAlive, config.jpn.api) as HttpsAgent;

export class JPNServer {

    private static AUTH_REQUEST = "/api/v2/jpn/auth";
    private static TICKER_REQUEST = "/api/v2/jpn/ticker";
    private static CONTRIBUTION_REQUEST = "/api/v2/jpn/contribute";
    private static CHECK_WIN_REQUEST = "/api/v2/jpn/checkWin";
    private static TRX_ID_REQUEST = "/api/v2/jpn/contribute/transactionId";
    private static MINI_GAME_REQUEST = "/api/v2/jpn/minigame";
    private static WIN_CONFIRMATION_REQUEST = "/api/v2/jpn/win/confirm";
    private static WIN_ROLLBACK_REQUEST = "/api/v2/jpn/win/rollback";
    private static WIN_JACKPOT_REQUEST = "/api/v2/jpn/win";
    private static CONTRIBUTE_DEFERRED_REQUEST = "/api/v2/jpn/contribute/deferred";

    private static TRANSACTION_IS_PROCESSING_CODE = 8;

    constructor(private baseUrl: string) {
    }

    public auth(req: AuthRequest): Promise<AuthResponse> {
        return this.post<AuthResponse>(JPNServer.AUTH_REQUEST, req);
    }

    public getTickers(req: TickerRequest, token: string): Promise<TickerResponse> {
        return this.get<TickerResponse>(JPNServer.TICKER_REQUEST,
            { exchangeRates: JSON.stringify(req.exchangeRates) }, token);
    }

    public generateTransactionId(token: string): Promise<TransactionIdResponse> {
        return this.get<TransactionIdResponse>(JPNServer.TRX_ID_REQUEST, undefined, token);
    }

    public contribute(req: ContributionRequest, token: string): Promise<ContributionResponse> {
        return this.post<ContributionResponse>(JPNServer.CONTRIBUTION_REQUEST, req, token);
    }

    public deferredContribute(req: ContributionRequest, token: string): Promise<DeferredContributionResponse> {
        return this.post<DeferredContributionResponse>(JPNServer.CONTRIBUTE_DEFERRED_REQUEST, req, token);
    }

    public checkWin(req: CheckWinRequest, token: string): Promise<CheckWinResponse> {
        return this.post<CheckWinResponse>(JPNServer.CHECK_WIN_REQUEST, req, token);
    }

    public updateMiniGame(req: MiniGameRequest, token: string): Promise<MiniGameResponse> {
        return this.post<MiniGameResponse>(JPNServer.MINI_GAME_REQUEST, req, token);
    }

    public confirmWin(req: WinConfirmRequest, token: string): Promise<WinConfirmResponse> {
        return this.post<WinConfirmResponse>(JPNServer.WIN_CONFIRMATION_REQUEST, req, token);
    }

    public rollbackWin(req: WinConfirmRequest, token: string): Promise<WinConfirmResponse> {
        return this.post<WinConfirmResponse>(JPNServer.WIN_ROLLBACK_REQUEST, req, token);
    }

    public winJackpot(req: WinJackpotRequest, token: string): Promise<WinJackpotResponse> {
        return this.post<WinJackpotResponse>(JPNServer.WIN_JACKPOT_REQUEST, req, token);
    }

    protected post<T>(url: string, req?: any, token?: string): Promise<T> {
        const requestLog = {
            method: "post",
            url,
            baseUrl: this.baseUrl,
            request: req,
            token
        };
        log.info({ requestJpn: requestLog }, "Jpn Request");

        return retry(config.retries, () => {
            return this.processResponse(
                request
                    .post(`${this.baseUrl}${url}`)
                    .agent(agent)
                    .set(this.getHeaders(token))
                    .send(req));
        }, retryCondition);
    }

    protected get<T>(url: string, qs?: any, token?: string): Promise<T> {
        const requestLog = {
            method: "get",
            url,
            qs: qs,
            baseUrl: this.baseUrl,
            token
        };
        log.info({ requestJpn: requestLog }, "Jpn Request");

        return retry(config.retries, () => {
            return this.processResponse(
                request
                    .get(`${this.baseUrl}${url}`)
                    .agent(agent)
                    .query(qs)
                    .set(this.getHeaders(token)));
        }, retryCondition);
    }

    protected processResponse(result: request.SuperAgentRequest): Promise<any> {
        return result.then((res) => this.process(res)).catch((err) => this.process(err.response, err));
    }

    private process(response, error?: Error): Promise<any> {
        if (error && !response) {
            log.warn(error, "Error sending request");
            return Promise.reject(new Errors.JPNConnectionError(error));
        } else if (response.status !== 200) {
            log.warn({ responseCode: response.status, responseBody: response.body }, "Error response");
            if (response.status >= 400 && response.status < 500) {
                return Promise.reject(new Errors.JPNBadRequestError(response.status, response.body));
            } else if (this.isTransactionInProgress(response)) {
                return Promise.reject(new Errors.TransactionIsInProgressError(response.status, response.body));
            } else {
                return Promise.reject(new Errors.JPNInternalServerError(response.status, response.body));
            }
        } else {
            log.info({ responseCode: response.status, responseBody: response.body }, "Response");
            return response.body;
        }
    }

    private isTransactionInProgress(response): boolean {
        return response.status === 500 && response?.body?.code === JPNServer.TRANSACTION_IS_PROCESSING_CODE;
    }

    protected getHeaders(token?: string) {
        const headers: any = {
            "X-GS": config.environmentId || ""
        };

        if (token) {
            headers["X-Access-Token"] = token;
        }

        return headers;
    }
}

const JPN_SERVER: JPNServer = new JPNServer(config.jpn.api);

export function getJPNServer(): JPNServer {
    return JPN_SERVER;
}
