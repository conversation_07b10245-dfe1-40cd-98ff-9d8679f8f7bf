import { EngineGameFlow } from "./gameflow";
import { RandomGenerator } from "@skywind-group/sw-random-cs";

let randomGeneratorFactory: RandomGeneratorFactory;

export function setRandomGeneratorFactory(factory: RandomGeneratorFactory) {
    if (!randomGeneratorFactory) {
        randomGeneratorFactory = factory;
    }
}

export function getCheats(rng: RandomGenerator): number[] {
    return randomGeneratorFactory.getCheats(rng);
}

export function injectRandomGenerator(flow: EngineGameFlow<any>) {
    const rng = randomGeneratorFactory.createRandomGenerator(flow.request());
    flow.setRng(rng);
}

export function isAllowedSetPositionsByClient() {
    return randomGeneratorFactory.isAllowedSetPositionsByClient();
}

export interface RandomGeneratorFactory {
    createRandomGenerator(req: any): RandomGenerator;
    getCheats(rng: RandomGenerator): number[];
    isAllowedSetPositionsByClient(): boolean;
}
