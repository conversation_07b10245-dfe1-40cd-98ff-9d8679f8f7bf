import { BaseRequest, PushService, SomeGame, SomeGameFlow } from "@skywind-group/sw-game-core";
import { ExtendedGameInitRequest, GameFlowContext } from "./context/gamecontext";
import { AbstractGameController } from "./gamecontroller";
import { EngineGameFlow } from "./gameflow";
import * as Errors from "../errors";
import KeepAliveTimer from "./keepalive";
import { logging, measures } from "@skywind-group/sw-utils";
import { GameFlowFactory } from "./gameFlowFactory";
import { StartGameTokenData } from "./tokens";
import { SocketExtraHeaders } from "./socketExtraHeaders";
import MerchantGameSessionService from "./merchantGameSessionService";
import { ContextVariables } from "../utils/contextVariables";
import measure = measures.measure;
import { getGameFlowContextManager } from "./contextmanager/contextManagerImpl";
import { AsyncRequest } from "./asyncconnection";
import measureProvider = measures.measureProvider;
import { load } from "./game/game";

const log = logging.logger("sw-slot-engine");

export class AsyncGameController extends AbstractGameController {
    private game: SomeGame;
    private context: GameFlowContext;
    private keepAliveTimer: KeepAliveTimer;

    constructor(pushService?: PushService) {
        super(pushService);
    }

    public get gameFlowContext(): GameFlowContext {
        return this.context;
    }

    protected setUpContext(context: GameFlowContext, game: SomeGame) {
        this.game = game;
        this.context = context;
    }

    protected setUpGame(flowContext: GameFlowContext, game: SomeGame) {
        this.game = game;
        this.context = flowContext;
        if (flowContext.settings.keepAliveSec) {
            this.keepAliveTimer = new KeepAliveTimer(flowContext.gameData.gameTokenData.token,
                flowContext.settings.keepAliveSec);
            this.keepAliveTimer.start();
            this.keepAliveTimer.on("error", async (err) => {
                await this.pushService.notifyError(err);
            });
        }
    }

    @measure({ name: "AsyncGameController.finish", isAsync: true })
    public async finish(checkPending: boolean, req?: AsyncRequest): Promise<void> {
        // do not checkPending when entity is set to ignore pendings on logout
        if (this.context?.settings?.logoutControl?.ignorePayments?.gameClosure) {
            checkPending = false;
        }
        // clean resources
        if (this.keepAliveTimer) {
            this.keepAliveTimer.stop();
        }
        if (this.context && !this.context.corrupted) {
            ContextVariables.setUpWithContext(this.context);
            try {
                if (checkPending) {
                    log.info("Check pending on disconnect");
                }
                /*
                In case of minPaymentRetryTimeout is small for operator we can have case when cleanup job already
                started for this game-context - we cannot use cached game-context, because it may be modified.
                So, we need to refresh it befor disconnect handling.
                */
                const flow: EngineGameFlow<BaseRequest> = await this.createFlowToProcessDisconnect(checkPending);
                await this.tryToDisconnect(flow);
                await flow.transferAllOut();
            } finally {
                if (req) {
                    await req.continueTransaction(async () => {
                        await this.tryToLogout();
                    });
                } else {
                    await measureProvider.runInTransaction("logout-after-disconnect", async () => {
                        await this.tryToLogout();
                    });
                }
            }
        }
    }

    protected async createFlowForRequest<T extends BaseRequest>(req: T,
                                                                checkConcurrent?: boolean,
                                                                checkPending?: boolean): Promise<EngineGameFlow<T>> {
        return GameFlowFactory.createFromContext(
            this.context, req, this.game, this.pushService, checkConcurrent, checkPending);
    }

    private async createFlowToProcessDisconnect(checkPending?: boolean): Promise<EngineGameFlow> {
        if (!this.game && this.context?.gameData?.gameId) {
            const gameLoaded = await load(this.context.gameData.gameId, true);
            this.game = gameLoaded?.game;
        }
        const flow = await GameFlowFactory.createFromContextWithRefresh(
            this.context, undefined, this.game, this.pushService, false, checkPending
        );
        this.context = flow.flowContext;
        return flow;
    }

    protected async validateInitRequest(request: ExtendedGameInitRequest): Promise<StartGameTokenData> {
        const result = await super.validateInitRequest(request);
        SocketExtraHeaders.validate(result, request);
        return result;
    }

    private async tryToDisconnect(flow: SomeGameFlow<any>) {
        if (this.game?.disconnect) {
            try {
                await this.game.disconnect(flow);
            } catch (err) {
                log.warn(err, "Error try to disconnect");
                if (err instanceof Errors.ConcurrentAccessToGameSession) {
                    return Promise.reject(err);
                }
            }
        }
    }

    private async tryToLogout() {
        if (!this.gameFlowContext.corrupted && this.gameFlowContext.requireLogout) {
            const ignorePayments = this.context?.settings?.logoutControl?.ignorePayments?.gameClosure;
            const contextManager = getGameFlowContextManager(this.gameFlowContext.playMode);
            const updatedContext = await contextManager.findGameContextById(this.gameFlowContext.id);
            if (!updatedContext) {
                log.warn(`Game context not found: ${this.context.id}. Skipping logout.`);
            } else {
                await MerchantGameSessionService.logout(updatedContext, ignorePayments);
            }
        }
    }
}

export function createAsyncGameController(pushService: PushService): AsyncGameController {
    return new AsyncGameController(pushService);
}
