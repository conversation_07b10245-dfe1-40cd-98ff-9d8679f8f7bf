import {
    Balance,
    JackpotDetails,
    JackpotDetailsHolder,
    PaymentOperation,
    PlayerWalletManager,
    SplitPaymentOperation,
    WalletOperation
} from "./wallet";
import * as Errors from "../errors";
import {
    isBNSInsufficientBalance,
    isCannotCompletePaymentError,
    ManagementAPIBrokenIntegration,
    OperationRefundedError
} from "../errors";
import { SWError } from "@skywind-group/sw-wallet-adapter-core";
import { GameFlowContext, JackpotPendingModification, PendingModification, SpecialState } from "./context/gamecontext";
import { logging } from "@skywind-group/sw-utils";
import { JackpotFlow, JackpotPendingProcessResult } from "./jpn/jackpotFlow";
import { BaseRequest, GameHistory, PaymentInfo } from "@skywind-group/sw-game-core";
import { deepClone } from "../utils/cloner";
import { JackpotUtil } from "./jpn/jackpotUtil";
import { EngineGameFlow } from "./gameflow";
import { decodeDeferredContribution } from "./tokens";
import { JackpotIdDetails, JackpotPoolDetails, JackpotStatistic } from "./context/jpStatistics";
import { ContributionRequest } from "@skywind-group/sw-jpn-core";
import { JackpotOperationResult } from "./jpn/jackpot";
import { ExtendedGameHistory } from "../history/history";

const log = logging.logger("sw-slot-engine:gameflow:payment-delegate");

export function createPaymentDelegate(flow: EngineGameFlow): PaymentDelegate {
    const operation = flow?.flowContext?.pendingModification?.walletOperation?.operation;
    if (CombinedPaymentDelegate.OPERATIONS.includes(operation)) {
        return new CombinedPaymentDelegate(flow);
    } else if (flow.isSplitPayment()) {
        return new SplitPaymentDelegate(flow);
    } else {
        return new CombinedPaymentDelegate(flow);
    }
}

export interface PaymentDelegateResult {
    readonly balance: Balance;

    readonly jackpotResult: JackpotPendingProcessResult;

    readonly finalBalanceForHistory?: Balance;
}

class RequireRefundError extends Error {
}

export abstract class PaymentDelegate<T extends WalletOperation = WalletOperation> {
    private static REQUIRE_REFUND_ERROR_CODE: number = 800;

    constructor(protected readonly flow: EngineGameFlow<BaseRequest>) {
    }

    protected get pendingModification(): PendingModification {
        return this.gameFlowContext?.pendingModification;
    }

    protected get walletOperation(): T {
        return this.pendingModification?.walletOperation as T;
    }

    protected get walletManager(): PlayerWalletManager {
        return this.flow.walletManager;
    }

    protected get gameFlowContext() {
        return this.flow.flowContext;
    }

    protected get jackpotFlow(): JackpotFlow {
        return this.flow.jackpotFlow;
    }

    protected get jackpotOperation() {
        return this.flow?.jackpotPending?.jackpotOperation;
    }

    protected get history(): GameHistory {
        return this.pendingModification?.history;
    }

    protected get roundEnded(): boolean {
        return this.pendingModification?.history?.roundEnded === true;
    }

    public abstract commitPending(): Promise<PaymentDelegateResult>;

    protected async commitWalletOperation(paymentOperation: WalletOperation,
                                          allowRollback: boolean = true): Promise<Balance> {
        return this.doWalletOperation(paymentOperation, this.updateWalletRetries.bind(this), allowRollback);
    }

    protected async commitJackpotWalletOperation(): Promise<Balance> {
        return this.doWalletOperation(this.flow?.jackpotPending?.walletOperation,
            this.updateJackpotWalletRetries.bind(this), false);
    }

    protected async commitRefund(walletOperation: T): Promise<void> {
        walletOperation.markForRefund = true;
        await this.gameFlowContext.updatePending();
        const refundOperation = this.flow.walletOperationFactory.createRefundBetOperation(
            walletOperation,
            this.flow.gameData.settings.addBetAmountOnFreeBetRollback
        );
        let balance: Balance;
        try {
            balance = await this.walletManager.commitOperation(refundOperation);
        } catch (e) {
            log.warn("Refund failed", e);
            await this.updateWalletRetries(refundOperation);
            throw e;
        }
        await this.gameFlowContext.rollbackPendingModification();

        const operationRefundedError = new OperationRefundedError(balance?.extraData);
        return Promise.reject(operationRefundedError);
    }

    private async updateWalletRetries(paymentOperation: WalletOperation): Promise<void> {
        const walletOperation = this.walletOperation || paymentOperation;
        walletOperation.retry = (walletOperation.retry || 0) + 1;
        return this.gameFlowContext.updatePending();
    }

    private async updateJackpotWalletRetries(paymentOperation: WalletOperation): Promise<void> {
        const walletOperation = this.flow?.jackpotPending?.walletOperation || paymentOperation;
        walletOperation.retry = (walletOperation.retry || 0) + 1;
        return this.gameFlowContext.updateJackpotPending();
    }

    private async doWalletOperation(paymentOperation: WalletOperation,
                                    processError: (paymentOperation: WalletOperation) => Promise<void>,
                                    allowRollback: boolean = true): Promise<Balance> {

        if (!paymentOperation) {
            return undefined;
        }

        try {
            return await this.walletManager.commitOperation(paymentOperation);
        } catch (err) {
            if (Errors.isSWError(err) && this.isServiceError(err) && allowRollback) {
                // Do jp rollback if conditions apply
                const deferredWinsEnabled = this.jackpotOperation?.payload?.deferredWinsEnabled;
                if (deferredWinsEnabled && JackpotUtil.isConfirmOperation(this.jackpotOperation)) {
                    log.warn(err, "Rollback JP win");
                    this.jackpotOperation.type = "win-rollback";
                    this.jackpotOperation.payload.externalId = paymentOperation?.transactionId;
                    await this.jackpotFlow.resolveJackpotWin(undefined);
                }
                if (this.checkIfRefundError(err)) {
                    return Promise.reject(new RequireRefundError());
                }
                await this.doRollback(err, this.gameFlowContext, this.pendingModification);
            } else if (PaymentDelegate.isBrokenIntegrationError(err)) {
                await this.gameFlowContext.setSpecialState(SpecialState.BROKEN_INTEGRATION);
            } else if (isCannotCompletePaymentError(err)) {
                await this.gameFlowContext.setSpecialState(SpecialState.CANNOT_COMPLETE_PAYMENT);
            } else {
                await processError(paymentOperation);
            }
            return Promise.reject(err);
        }
    }

    protected async confirmJackpotWin(jpnResult: JackpotOperationResult): Promise<JackpotPendingProcessResult> {
        const balanceAfterJp = await this.commitJackpotWalletOperation();

        const jackpotResult: JackpotPendingProcessResult = await this.jackpotFlow.resolveJackpotWin(jpnResult);
        jackpotResult.balance = balanceAfterJp;
        return jackpotResult;
    }

    private isServiceError(swError: SWError): boolean {
        return swError.responseStatus >= 400 && swError.responseStatus < 500;
    }

    public static isBrokenIntegrationError(swError: SWError): swError is ManagementAPIBrokenIntegration {
        return swError instanceof ManagementAPIBrokenIntegration;
    }

    private checkIfRefundError(error: SWError): boolean {
        return error.code === PaymentDelegate.REQUIRE_REFUND_ERROR_CODE;
    }

    /**
     * Roll back payment. Returns true if rollback processed, otherwise false in rollback do not required
     * @param err
     * @param context
     * @param pending
     * @returns {boolean}
     */
    private async doRollback(err, context: GameFlowContext, pending: PendingModification) {
        if (isBNSInsufficientBalance(err)) {
            log.warn(err,
                "Rollback pending modification and closing round: " + JSON.stringify(pending));
            await context.rollbackPendingModificationAndCloseRound();
        } else {
            log.warn(err, "Rollback pending debit modification: " + JSON.stringify(pending));
            await context.rollbackPendingModification();
        }
    }

}

class CombinedPaymentDelegate extends PaymentDelegate<PaymentOperation> {
    public static OPERATIONS = ["finalize-game", "transfer-in", "transfer-out", "deferred-payment"];
    public static REFUND_OPERATIONS = ["transfer-in"];

    public async commitPending(): Promise<PaymentDelegateResult> {
        const walletOperation = this.walletOperation;
        let balanceAfterSpin: Balance;

        if (walletOperation?.markForRefund) {
            await this.commitRefund(walletOperation);
            return Promise.reject(new OperationRefundedError());
        }
        if ((walletOperation as WalletOperation)?.operation === "finalize-game") {
            const allowRollback = false;
            balanceAfterSpin = await this.commitWalletOperation(walletOperation, allowRollback);
        } else {
            try {
                balanceAfterSpin = await this.commitWalletOperation(walletOperation);
            } catch (err) {
                if (this.requireRefund(err, walletOperation)) {
                    log.warn(err, "Got RequireRefundError. Schedule refund operation", walletOperation);
                    await this.commitRefund(walletOperation);
                } else {
                    log.error(err, err.message, walletOperation);
                    throw err;
                }
            }
        }

        const jackpotFlow = this.jackpotFlow;
        let jackpotResult = await jackpotFlow.processJackpotOperation();
        const jpPending: JackpotPendingModification = jackpotFlow.flow.jackpotPending;
        if (jpPending && JackpotUtil.isConfirmOperation(jpPending.jackpotOperation)) {
            const jpWalletOperation: WalletOperation = jpPending.walletOperation;
            if (walletOperation) {
                jpWalletOperation.roundEnded = walletOperation.roundEnded;
                if (walletOperation.gameStatus) {
                    jpWalletOperation.gameStatus = walletOperation.gameStatus;
                }
            }
            jackpotResult = await this.confirmJackpotWin(jackpotResult.jpnResult);
        }

        const balanceAfterJp = jackpotResult.balance;
        const currentBalance = this.toCurrentBalance(balanceAfterJp, balanceAfterSpin);

        const historyBalance = deepClone(balanceAfterSpin || balanceAfterJp);
        if (balanceAfterJp && balanceAfterSpin) {
            historyBalance.previousValue = balanceAfterSpin.previousValue || balanceAfterJp.previousValue;
        }

        return { balance: currentBalance, jackpotResult, finalBalanceForHistory: historyBalance };
    }

    private requireRefund(err: Error, walletOperation: PaymentOperation): boolean {
        return err instanceof RequireRefundError &&
            CombinedPaymentDelegate.REFUND_OPERATIONS.includes(walletOperation.operation);
    }

    private toCurrentBalance(balanceAfterJp: Balance, balanceAfterSpin: Balance): Balance {
        const currentBalance = balanceAfterJp || balanceAfterSpin;

        // ensure extraData gets to response
        const jpExtraData = balanceAfterJp && balanceAfterJp.extraData;
        const spinExtraData = balanceAfterSpin && balanceAfterSpin.extraData;

        if (jpExtraData || spinExtraData) {
            currentBalance.extraData = { ...spinExtraData, ...jpExtraData };
        }

        return currentBalance;
    }
}

class SplitPaymentDelegate extends PaymentDelegate<SplitPaymentOperation> {

    public async commitPending(): Promise<PaymentDelegateResult> {
        const walletOperation: SplitPaymentOperation = this.walletOperation;
        // Process auto-redeem
        if ((walletOperation as WalletOperation)?.operation === "redeem-bns") {
            const balance = await this.commitWalletOperation(walletOperation);
            return { balance: balance, jackpotResult: {}, finalBalanceForHistory: balance };
        }

        if (walletOperation?.markForRefund) {
            await this.commitRefund(walletOperation);
            return Promise.reject(new OperationRefundedError());
        }

        if (walletOperation && !walletOperation.balanceAfterDebit) {
            this.injectDeferredJackpotDetails(walletOperation, this.gameFlowContext);
            await this.commitBet(walletOperation);
        }

        const jackpotFlow = this.jackpotFlow;
        const history = this.history;
        const roundEnded = this.roundEnded;

        let jackpotResult: JackpotPendingProcessResult = await jackpotFlow.processJackpotOperation();

        const creditPayment = this.createCreditWalletOperation(walletOperation);
        const miniGameTriggered = jackpotFlow.isMiniGameTriggered(jackpotResult);
        const instantJpMiniGameTriggered = jackpotFlow.isInstantJpMiniGameTriggered(jackpotResult);
        const jpPaymentExist: boolean = JackpotUtil.isConfirmOperation(this.jackpotOperation);

        if (roundEnded && creditPayment) {
            if (miniGameTriggered || jpPaymentExist) {
                creditPayment.roundEnded = false;
                creditPayment.gameStatus = "bonusgame";
            } else {
                this.injectJackpotDetails(creditPayment, jackpotResult);
            }

            if (miniGameTriggered && history) {
                history.roundEnded = false;
            }

            if (instantJpMiniGameTriggered) {
                creditPayment.roundEnded = false;
                creditPayment.gameStatus = "bonusgame";
                if (history) {
                    const extHistory = history as ExtendedGameHistory;
                    extHistory.instantJackpot = true;
                    history.roundEnded = false;
                }
            }
        }

        if (walletOperation && !walletOperation.balanceAfterCredit) {
            walletOperation.balanceAfterCredit = await this.commitWalletOperation(creditPayment, false);
        }

        if (jpPaymentExist) {
            await this.gameFlowContext.updatePending();
            this.jackpotOperation.payload.externalId = walletOperation?.transactionId;
            jackpotResult = await this.confirmJackpotWin(jackpotResult.jpnResult);
        }

        return this.buildSplitPaymentResult(jackpotResult, walletOperation);
    }

    private async commitBet(walletOperation: SplitPaymentOperation) {
        try {
            walletOperation.balanceAfterDebit = await this.commitWalletOperation(
                this.createDebitWalletOperation(walletOperation));
            await this.gameFlowContext.updatePending();
        } catch (err) {
            if (err instanceof RequireRefundError) {
                walletOperation.markForRefund = true;

                log.warn(err, "Got RequireRefundError. Schedule refund operation", walletOperation);

                await this.commitRefund(walletOperation);
            } else {
                return Promise.reject(err);
            }
        }
    }

    private injectJackpotDetails(creditPayment, jackpotResult: JackpotPendingProcessResult) {
        Object.assign(creditPayment,
            this.flow.walletOperationFactory.getJackpotDetails(jackpotResult.jpnResult, jackpotResult.jackpotContext));
    }

    private buildSplitPaymentResult(jackpotResult: JackpotPendingProcessResult,
                                    walletOperation: SplitPaymentOperation = {} as any): PaymentDelegateResult {
        const balanceAfterJackpotWin = jackpotResult.balance;

        const currentBalance = balanceAfterJackpotWin ||
            walletOperation.balanceAfterCredit ||
            walletOperation.balanceAfterDebit;

        // we need to ensure that extra data does not get lost in split payment
        const jpExtraData = balanceAfterJackpotWin && balanceAfterJackpotWin.extraData;
        const creditExtraData = walletOperation?.balanceAfterCredit?.extraData;
        const debitExtraData = walletOperation?.balanceAfterDebit?.extraData;
        if (jpExtraData || creditExtraData || debitExtraData) {
            currentBalance.extraData = { ...debitExtraData, ...creditExtraData, ...jpExtraData };
            if (debitExtraData?.messageArray && creditExtraData?.messageArray) {
                currentBalance.extraData.messageArray =
                    debitExtraData.messageArray.concat(creditExtraData.messageArray);
            }
        }

        let previousValue = walletOperation?.balanceAfterDebit?.previousValue;
        if (previousValue === undefined) {
            previousValue = balanceAfterJackpotWin?.previousValue;
        }

        if (previousValue !== undefined) {
            currentBalance.previousValue = previousValue;
        }

        const finalBalanceForHistory = this.recalculateFinalBalanceForHistory(walletOperation, balanceAfterJackpotWin);
        if (finalBalanceForHistory) {
            finalBalanceForHistory.extraData = {
                ...finalBalanceForHistory.extraData,
                ...currentBalance.extraData
            };
        }
        jackpotResult = this.recalculateJackpotBalanceForHistory(jackpotResult, walletOperation);

        return { balance: currentBalance, jackpotResult, finalBalanceForHistory };
    }

    private recalculateJackpotBalanceForHistory(jackpotResult: JackpotPendingProcessResult,
                                                walletOperation: SplitPaymentOperation): JackpotPendingProcessResult {

        const balanceAfterCredit = walletOperation?.balanceAfterCredit;
        if (!jackpotResult) {
            return undefined;
        }

        if (!jackpotResult.balance || !balanceAfterCredit) {
            return jackpotResult;
        }
        const result = deepClone(jackpotResult);
        result.balance.previousValue = balanceAfterCredit.main;
        return result;
    }

    private recalculateFinalBalanceForHistory(walletOperation: SplitPaymentOperation,
                                              balanceAfterJpWin: Balance): Balance {
        if (walletOperation?.balanceAfterDebit && walletOperation?.balanceAfterCredit) {
            const result: Balance = deepClone(walletOperation.balanceAfterCredit);
            result.previousValue = walletOperation.balanceAfterDebit.previousValue;
            return result;
        }

        return balanceAfterJpWin;
    }

    private createCreditWalletOperation(splitPaymentOperation: SplitPaymentOperation): WalletOperation & PaymentInfo {
        if (!splitPaymentOperation) {
            return undefined;
        }
        const copy: SplitPaymentOperation = deepClone<SplitPaymentOperation>(splitPaymentOperation);
        copy.actions = copy.creditActions;

        if (copy?.debitActions?.length) {
            const [{ amount }] = copy.debitActions;
            copy.betBeforeWin = amount;
        }

        copy.creditActions = undefined;
        copy.debitActions = undefined;
        copy.balanceAfterDebit = undefined;

        copy.ts = splitPaymentOperation.ts;

        const walletOperation = copy as WalletOperation;
        walletOperation.operation = "win";

        this.copySmResultTransactions(splitPaymentOperation, copy);

        return walletOperation;
    }

    private createDebitWalletOperation(splitPaymentOperation: SplitPaymentOperation): WalletOperation & PaymentInfo {
        if (!splitPaymentOperation) {
            return undefined;
        }
        const copy = deepClone<SplitPaymentOperation>(splitPaymentOperation);
        copy.actions = copy.debitActions;

        copy.creditActions = undefined;
        copy.debitActions = undefined;

        copy.ts = splitPaymentOperation.ts;

        const walletOperation = copy as WalletOperation;
        walletOperation.operation = "bet";
        walletOperation.totalWin = undefined;
        walletOperation.totalBet = undefined;
        walletOperation.roundEnded = false;

        this.copySmResultTransactions(splitPaymentOperation, copy);

        return walletOperation;
    }

    private copySmResultTransactions(source: SplitPaymentOperation, target: SplitPaymentOperation) {
        if (source.smResultExtraData && source.smResultExtraData.transactions) {
            const sourceTransactions = source.smResultExtraData.transactions;
            const targetTransactions = target.smResultExtraData.transactions;
            for (let i = 0; i < targetTransactions.length; i++) {
                targetTransactions[i].ts = sourceTransactions[i].ts;
            }
        }
    }

    private getDeferredJackpotDetails(context: GameFlowContext): JackpotDetailsHolder {
        const operationData = context?.jackpotPending?.jackpotOperation?.payload as ContributionRequest;
        const payload = operationData?.deferredContribution;
        if (!payload) {
            return undefined;
        }
        const contributions = decodeDeferredContribution(payload);
        if (!contributions || contributions.length === 0) {
            return undefined;
        }

        const contributionPrecision = this.gameFlowContext.settings.contributionPrecision;

        const jackpotStatistic: JackpotStatistic = {};

        let total = 0;

        for (const contribution of contributions) {
            const jackpotId = contribution.jackpotId;
            const totalJpContribution = contribution.totalPlayerContribution;

            total = JackpotUtil.safeAddWithPrecision(contributionPrecision, total, totalJpContribution);

            const pools = contribution.playerContributions;
            const jackpotIdDetails: JackpotIdDetails = {};
            const jpInfo = context?.jpContext?.jackpotsInfo &&
                context.jpContext.jackpotsInfo.find((jp) => jp.id === jackpotId);

            for (const pool of pools) {
                const jackpotPoolDetails: JackpotPoolDetails = {
                    contribution: { seed: pool.seed, progressive: pool.progressive },
                    win: 0,
                };
                if (jpInfo && jpInfo.isLocal !== undefined) {
                    jackpotPoolDetails.isLocal = jpInfo.isLocal;
                }
                jackpotIdDetails[pool.pool] = jackpotPoolDetails;
            }

            jackpotStatistic[jackpotId] = jackpotIdDetails;
        }

        const jackpotDetails: JackpotDetails = {
            contributionPrecision: contributionPrecision,
            jackpotTypes: JackpotUtil.getJackpotTypeStatistic(this.gameFlowContext.jpContext),
            jackpots: jackpotStatistic
        };

        return {
            totalJpContribution: total,
            totalJpWin: undefined,
            jackpotDetails: jackpotDetails,
        };
    }

    private injectDeferredJackpotDetails(debitPayment, context: GameFlowContext) {
        const details = this.getDeferredJackpotDetails(context);
        if (details) {
            Object.assign(debitPayment, details);
        }
    }
}
