import { ManagementAPISupport } from "./managementapihelper";
import { lazy, measures } from "@skywind-group/sw-utils";
import config from "../config";
import { ChangeNicknameActionRequest } from "./requests";
import { GameFlowContext } from "./context/gamecontext";
import measure = measures.measure;

interface ChangeNicknameResponse {
    code?: number;
    message?: string;
    nickname?: string;
    nicknameChangeAttemptsLeft?: number;
}

export class ChangeNicknameActionService extends ManagementAPISupport {
    private static CHANGE_NICKNAME_ACTION_URL = "/v1/player/change-nickname";

    constructor(private readonly url: string) {
        super(url);
    }

    @measure({ name: "ChangeNicknameActionService.performChangeNicknameAction", isAsync: true })
    public async performChangeNicknameAction(req: ChangeNicknameActionRequest,
                                             context: GameFlowContext): Promise<ChangeNicknameResponse> {
        return this.put<any>(ChangeNicknameActionService.CHANGE_NICKNAME_ACTION_URL, {
            gameToken: context.gameData.gameTokenData.token,
            nickname: req?.payload?.nickname || "",
            increaseNicknameChangeAttempts:
                typeof req?.payload?.decreaseNicknameChangeAttemptsLeft !== "undefined"
                    ? req?.payload?.decreaseNicknameChangeAttemptsLeft
                    : true
        });
    }
}

const changeNicknameActionServiceManager = lazy(() => new ChangeNicknameActionService(config.managementAPIGameAuth));

export function getChangeNicknameActionService(): ChangeNicknameActionService {
    return changeNicknameActionServiceManager.get();
}
