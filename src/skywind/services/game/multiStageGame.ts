import {
    BalanceResponse,
    BaseRequest,
    ContextUpdate,
    ConversionService,
    FreeBetsInfo,
    GameContext,
    GameContextPersistencePolicy,
    GameFlowInfo,
    GameHistory,
    GameInfo,
    GameInitRequest,
    GameInitResponse,
    GamePlayRequest,
    GamePlayResponse,
    GameStateInfoRequest,
    GameStateInfoResponse,
    JackpotAction,
    JackpotGame,
    JackpotResults, LiveGamePlayResponse,
    LogLevel,
    MiniGame,
    PaymentInfo,
    PushService,
    RandomGenerator,
    SomeGame,
    SomeGameFlow,
    SomeStage,
    StageFlow,
    TickersResponse,
    WinJackpot
} from "@skywind-group/sw-game-core";
import { EngineGameFlow } from "../gameflow";
import { AbstractGameProxy } from "./gameProxy";
import { WalletOperationFactory } from "../walletOperationFactory";
import { lazy } from "@skywind-group/sw-utils";
import { deepClone } from "../../utils/cloner";
import { isAllowedSetPositionsByClient } from "../random";

const uuid = require("uuid");

export interface MultiStageContext extends GameContext {
    flowId: string;
    request: GamePlayRequest;
    flowType: FlowType;
    gameContext: GameContext;
    stagesContexts: GameContext[];
    winAmount: number;
    totalBet: number;
    policy: GameContextPersistencePolicy;
    stagePolicies: number[];
    step: number;
}

export enum FlowType {
    INIT = 1, PLAY = 2, STATE = 3
}

export class FlowIsNotFinishedError extends Error {
    constructor() {
        super("Flow was broken. Reinitialize the game!");
    }
}

/**
 *  MultiStageGame can include several stages.
 *  Each request is passed to the methods of each stage in the loop.
 *
 *  MultiGameStage is responsible for:
 * 1) storing context of each stage
 * 2) repeat processing from the previous stage if the game was broken
 */
export class MultiStageGame extends AbstractGameProxy {

    constructor(game: SomeGame & ConversionService<any> & JackpotGame<any, any>,
                private readonly stages: SomeStage[]) {
        super(game);
    }

    public async init(flow: EngineGameFlow<GameInitRequest>): Promise<GameInitResponse> {
        const context: MultiStageContext = await this.getInitialGameContext(flow);

        let stagesResponse;
        if (context.flowId) {
            if (context.request) {
                stagesResponse = await this.processRepeat(context, flow);
                context.request = undefined;
            }
            await this.finalizeStages(context, flow);
        }

        this.prepareFlow(context, FlowType.INIT);

        const initResponse = await this.initGame(flow, context);

        for (let id = 0; id < this.stages.length; id++) {
            const ctx: MultiStageContext = await flow.gameContext() as MultiStageContext;
            const stage = this.stages[id];
            const stageFlow = new StageFlowImpl(flow, id, ctx, false, isAllowedSetPositionsByClient());
            const result = await stage.init(stageFlow);

            if (result) {
                stagesResponse = this.merge(result, stagesResponse);
            }

            if (stageFlow.update) {
                await this.updateStageContext(stageFlow);
            }
        }

        await this.finalizeFlow(flow);

        return this.merge(initResponse, stagesResponse);
    }

    private async processRepeat(context: MultiStageContext, flow: EngineGameFlow<GameInitRequest>) {
        if (context.flowType === FlowType.PLAY) {
            return this.processPlay(flow, true, isAllowedSetPositionsByClient(), context.step);
        } else if (context.flowType === FlowType.STATE) {
            return this.processStateInfo(flow, true, isAllowedSetPositionsByClient(), context.step);
        }
    }

    public async play(flow: EngineGameFlow<GamePlayRequest>,
                      allowCheating?: boolean): Promise<GamePlayResponse | LiveGamePlayResponse> {
        const context: MultiStageContext = await flow.gameContext() as MultiStageContext;

        if (context.flowId) {
            return Promise.reject(new FlowIsNotFinishedError());
        }

        this.prepareFlow(context, FlowType.PLAY);
        context.request = flow.request();

        const response = await this.playGame(flow, context, allowCheating);

        const stageResponses = await this.processPlay(flow, false, allowCheating);

        await this.finalizeFlow(flow);

        let result;
        if (this.isLiveResponse(response)) {
            (response as LiveGamePlayResponse).response =
                this.merge(stageResponses, (response as LiveGamePlayResponse).response);
            result = response;
        } else {
            result = this.merge(stageResponses, response);
        }
        return result;
    }

    public async internalEvent(
        flow: EngineGameFlow<GamePlayRequest>): Promise<GamePlayResponse | LiveGamePlayResponse> {
        const context: MultiStageContext = await flow.gameContext() as MultiStageContext;

        if (context.flowId) {
            return Promise.reject(new FlowIsNotFinishedError());
        }

        this.prepareFlow(context, FlowType.PLAY);
        context.request = flow.request();

        const response = await this.proxyInternalEvent(flow, context);

        const stageResponses = await this.processInternalEvent(flow, false, false);

        await this.finalizeFlow(flow);

        let result;
        if (this.isLiveResponse(response)) {
            (response as LiveGamePlayResponse).response =
                this.merge(stageResponses, (response as LiveGamePlayResponse).response);
            result = response;
        } else {
            result = this.merge(stageResponses, response);
        }
        return result;
    }

    public async stateInfo(flow: EngineGameFlow<GameStateInfoRequest>): Promise<GameStateInfoResponse> {
        const context: MultiStageContext = await flow.gameContext() as MultiStageContext;

        if (context.flowId) {
            return Promise.reject(new FlowIsNotFinishedError());
        }

        this.prepareFlow(context, FlowType.STATE);
        context.request = flow.request();

        const response = await this.gameStateInfo(flow, context);

        const stageResponses = await this.processStateInfo(flow, false, isAllowedSetPositionsByClient());

        await this.finalizeFlow(flow);

        return this.merge(stageResponses, response);
    }

    private async processPlay(flow: EngineGameFlow<BaseRequest>,
                              repeat: boolean,
                              allowCheating: boolean,
                              lastSuccessfulStep?: number): Promise<any> {
        return this.processRequest((s, f) => s.play(f), flow, repeat, allowCheating, lastSuccessfulStep);
    }

    private async processInternalEvent(flow: EngineGameFlow<BaseRequest>,
                                       repeat: boolean,
                                       allowCheating: boolean,
                                       lastSuccessfulStep?: number): Promise<any> {
        return this.processRequest((s, f) => s.internalEvent(f), flow, repeat, allowCheating, lastSuccessfulStep);
    }

    private async processStateInfo(flow: EngineGameFlow<BaseRequest>,
                                   repeat: boolean,
                                   allowCheating: boolean,
                                   lastSuccessfulStep?: number): Promise<any> {
        return this.processRequest((s, f) => s.stateInfo(f), flow, repeat, allowCheating, lastSuccessfulStep);
    }

    private async processRequest(action: (stage: SomeStage, stageFlow: StageFlowImpl<any>) => Promise<any>,
                                 flow: EngineGameFlow<BaseRequest>,
                                 repeat: boolean,
                                 allowCheating: boolean,
                                 step?: number): Promise<any> {

        let result;
        const lastSuccessfulStep = step === undefined ? -1 : step;
        for (let id = 0; id < this.stages.length; id++) {
            const context: MultiStageContext = await flow.gameContext() as MultiStageContext;
            const stage = this.stages[id];
            const stageFlow = new StageFlowImpl(flow, id, context, repeat, allowCheating);
            const response = await action(stage, stageFlow);

            if (response) {
                result = this.merge(response, result);
            }

            if (id > lastSuccessfulStep && stageFlow.update) {
                context.step = id;
                await this.updateStageContext(stageFlow);
            }
        }

        return result;
    }

    private prepareFlow(context: MultiStageContext, type: FlowType) {
        context.flowId = uuid.v4();
        context.flowType = type;
        context.step = undefined;
        if (!context.stagesContexts) {
            context.stagesContexts = [];
        }
        if (!context.stagePolicies) {
            context.stagePolicies = new Array(this.stages.length);
            context.stagePolicies.fill(GameContextPersistencePolicy.NORMAL);
        }
    }

    private async finalizeFlow<RES>(flow: EngineGameFlow<BaseRequest>) {
        const context: MultiStageContext = await flow.gameContext() as MultiStageContext;
        await this.finalizeStages(context, flow);
        context.flowId = undefined;
        context.request = undefined;
        context.flowType = undefined;
        flow.setPersistencePolicy(this.getPersistencePolicy(context));
        context.policy = undefined;
        context.winAmount = undefined;
        context.totalBet = undefined;
        context.step = undefined;
        await flow.updateGameContext(context);
    }

    private async finalizeStages(context: MultiStageContext,
                                 flow: EngineGameFlow<BaseRequest>): Promise<void> {
        for (let id = 0; id < this.stages.length; id++) {
            const stage = this.stages[id];
            const stageFlow = new StageFlowImpl(flow, id, context, false);
            await stage.finalize(stageFlow);
        }
    }

    private async initGame(flow: EngineGameFlow<GameInitRequest>, context: MultiStageContext) {
        const gameFlow = new GameFlowWrapper(flow, context);
        await super.beforeInit(gameFlow);
        const result = await this.game.init(gameFlow).catch((err) => this.checkGameError(err, flow));
        await super.afterInit(gameFlow, result, gameFlow.contextUpdate);
        await this.updateGameContext(gameFlow);

        return result;
    }

    private async gameStateInfo(flow: EngineGameFlow<GameStateInfoRequest>,
                                context: MultiStageContext): Promise<GameStateInfoResponse> {
        if (this.game.stateInfo) {
            const gameFlow = new GameFlowWrapper(flow, context);
            await super.beforeStateInfo(gameFlow);
            const result = await this.game.stateInfo(gameFlow);
            await super.afterStateInfo(gameFlow, result);
            await this.updateGameContext(gameFlow);
            return result;
        }

        return undefined;
    }

    private async playGame(flow: EngineGameFlow<GamePlayRequest>,
                           context: MultiStageContext,
                           allowCheating: boolean = false) {
        const gameFlow = new GameFlowWrapper(flow, context);

        await super.beforePlay(gameFlow);
        const result: GamePlayResponse | LiveGamePlayResponse =
            await this.game.play(gameFlow, allowCheating).catch((err) => this.checkGameError(err, flow));
        await super.afterPlay(gameFlow, this.getLiveResponse(result), gameFlow.contextUpdate);
        await this.updateGameContext(gameFlow);

        return result;
    }

    private async proxyInternalEvent(flow: EngineGameFlow<GamePlayRequest>,
                                     context: MultiStageContext) {
        const gameFlow = new GameFlowWrapper(flow, context);

        await this.beforePlay(gameFlow);
        const result: GamePlayResponse | LiveGamePlayResponse = await this.game.internalEvent(gameFlow);
        await this.afterPlay(gameFlow, this.getLiveResponse(result), gameFlow.contextUpdate);
        await this.updateGameContext(gameFlow);

        return result;
    }

    private async updateStageContext(stageFlow: StageFlowImpl<BaseRequest>) {
        const context: MultiStageContext = stageFlow.multiStageContext;
        const flow = stageFlow.originalFlow;
        const update = stageFlow.update;
        context.stagePolicies[stageFlow.id] = stageFlow.persistencePolicy;
        if (update.hasOwnProperty("context")) {
            context.stagesContexts[stageFlow.id] = update.context;
        }
        if (update.hasOwnProperty("gameContext")) {
            context.gameContext = this.merge(context.gameContext, update.gameContext);
        }
        update.context = context;

        flow.setPersistencePolicy(this.getPersistencePolicy(context));
        flow.deferredUpdate(update);
        await flow.commitDeferredUpdate();
    }

    private async updateGameContext(gameFlow: GameFlowWrapper<BaseRequest>) {
        const context: MultiStageContext = gameFlow.context;
        const flow = gameFlow.originalFlow;
        context.policy = gameFlow.policy;
        const update = gameFlow.contextUpdate;
        if (update.context !== undefined) {
            context.gameContext = update.context;
        }

        if (update.payment) {
            context.winAmount = WalletOperationFactory.getWinAmount(update.payment);
            context.totalBet = WalletOperationFactory.getBetAmount(update.payment);
        }

        await flow.update(context, update.payment, update.history, update.jackpotAction, update.analytics);
        gameFlow.contextUpdate = {};
    }

    private async getInitialGameContext(flow: EngineGameFlow<GameInitRequest>): Promise<MultiStageContext> {
        const result = await flow.gameContext();
        return (result ? result : {}) as MultiStageContext;
    }

    private getPersistencePolicy(ctx: MultiStageContext): number {
        return Math.max(...ctx.stagePolicies, ctx.policy || GameContextPersistencePolicy.NORMAL);
    }

    private merge(obj1, obj2) {
        return { ...(obj1 || {}), ...(obj2 || {}) };
    }
}

export class StageFlowImpl<REQ extends BaseRequest> implements StageFlow<REQ, any, GameContext, GameContext> {
    public update: ContextUpdate<GameContext>;
    public persistencePolicy: number = GameContextPersistencePolicy.NORMAL;
    private stageContext = lazy<GameContext>(() => {
        const ctx = this.multiStageContext && this.multiStageContext.stagesContexts;
        return ctx && deepClone(ctx[this.id]);
    });

    constructor(public readonly originalFlow: EngineGameFlow<REQ>,
                public readonly id: number,
                public readonly multiStageContext: MultiStageContext,
                public readonly repeat: boolean,
                public readonly allowCheating?: boolean) {
    }

    public get request(): REQ {
        return this.repeat ? (this.multiStageContext.request as REQ) : this.originalFlow.request();
    }

    public get info(): GameFlowInfo {
        return this.originalFlow.info();
    }

    public get rng(): RandomGenerator {
        return this.originalFlow.rng();
    }

    public get settings(): any {
        return this.originalFlow.settings();
    }

    public get pushService(): PushService {
        return this.originalFlow.pushService();
    }

    public get flowId(): string {
        return this.multiStageContext.flowId;
    }

    public get balance(): Promise<BalanceResponse> {
        return this.originalFlow.getBalance();
    }

    public get gameContext(): GameContext {
        return this.multiStageContext.gameContext as GameContext;
    }

    public get winAmount(): number {
        return this.multiStageContext.winAmount;
    }

    public get totalBet(): number {
        return this.multiStageContext.totalBet;
    }

    public get context(): GameContext {
        return this.stageContext.get();
    }

    public deferredUpdate(update: ContextUpdate<GameContext>): void {
        this.update = { ... (this.update || {}), ...update };
    }

    public log(level, message: any, ...optionalParams): void {
        this.originalFlow.log(level, message, optionalParams);
    }

    public exchange(amount: number, targetCurrency: string, baseCurrency?: string): number {
        return this.originalFlow.exchange(amount, targetCurrency, baseCurrency);
    }

    public jackpotTickers(): Promise<TickersResponse> {
        return this.originalFlow.jackpotTickers();
    }

    public jackpotResult(): Promise<JackpotResults> {
        return this.originalFlow.jackpotResult();
    }
}

export class GameFlowWrapper<REQ extends BaseRequest> implements SomeGameFlow<REQ> {
    private updated: boolean = false;
    public policy: GameContextPersistencePolicy = GameContextPersistencePolicy.NORMAL;
    public contextUpdate: ContextUpdate<GameContext> = {};

    constructor(public readonly originalFlow: EngineGameFlow<REQ>,
                public readonly context: MultiStageContext) {
    }

    public request(): REQ {
        return this.originalFlow.request();
    }

    public info(): GameFlowInfo {
        return this.originalFlow.info();
    }

    public rng(): RandomGenerator {
        return this.originalFlow.rng();
    }

    public settings(): any {
        return this.originalFlow.settings();
    }

    public pushService(): PushService {
        return this.originalFlow.pushService();
    }

    public async gameContext(): Promise<GameContext> {
        return this.context.gameContext;
    }

    public persistencePolicy(): GameContextPersistencePolicy {
        return this.policy | this.originalFlow.persistencePolicy();
    }

    public setPersistencePolicy(value: GameContextPersistencePolicy): void {
        this.policy = value;
    }

    public getBalance(): Promise<BalanceResponse> {
        return this.originalFlow.getBalance();
    }

    public async payment(payment: PaymentInfo): Promise<BalanceResponse> {
        this.checkUpdated();
        this.contextUpdate.payment = payment;
        return this.getBalance();
    }

    public async storeHistory(history: GameHistory): Promise<void> {
        this.checkUpdated();
        this.contextUpdate.history = history;
    }

    public async updateGameContext(context: GameContext): Promise<GameContext> {
        this.checkUpdated();
        this.contextUpdate.context = context;
        return context;
    }

    public async update(context: GameContext,
                        payment: PaymentInfo,
                        history: GameHistory,
                        jackpotAction?: JackpotAction): Promise<void> {
        this.checkUpdated();
        this.contextUpdate.context = context;
        this.contextUpdate.payment = payment;
        this.contextUpdate.history = history;
        this.contextUpdate.jackpotAction = jackpotAction;
    }

    public async updateMiniGame(context: GameContext, jackpotAction: MiniGame): Promise<void> {
        this.checkUpdated();
        this.contextUpdate.context = context;
        this.contextUpdate.jackpotAction = jackpotAction;
    }

    public log(level: LogLevel, message: any, ...optionalParams): void {
        this.originalFlow.log(level, message, optionalParams);
    }

    public exchange(amount: number, targetCurrency: string): number {
        return this.originalFlow.exchange(amount, targetCurrency);
    }

    public async winJackpot(context: GameContext, jackpotAction: WinJackpot, history: GameHistory): Promise<void> {
        this.checkUpdated();
        this.contextUpdate.context = context;
        this.contextUpdate.history = history;
        this.contextUpdate.jackpotAction = jackpotAction;
    }

    public jackpotTickers(): Promise<TickersResponse> {
        return this.originalFlow.jackpotTickers();
    }

    public jackpotResult(): Promise<JackpotResults> {
        return this.originalFlow.jackpotResult();
    }

    private checkUpdated() {
        if (this.updated) {
            throw new Error("Don't allow multiple updates in staged games!");
        }

        this.updated = true;
    }

    public getFreeBets(info: GameInfo): Promise<FreeBetsInfo> {
        return this.originalFlow.getFreeBets(info);
    }

    public deferredUpdate(update: ContextUpdate<GameContext>) {
        this.contextUpdate = { ...this.contextUpdate, ...update };
    }
}
