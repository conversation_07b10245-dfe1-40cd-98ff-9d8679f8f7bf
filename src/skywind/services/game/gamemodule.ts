import { SomeGameModule } from "@skywind-group/sw-game-core";
import config from "../../config";

export interface CacheableGameModule extends SomeGameModule {
    moduleName: ModuleName;
    noWinResult?: any;
}

export interface ModuleName {
    nameWithVersion: string;
    id: string;
    name: string;
    version: string;
}

export interface GameModule {
    game: SomeGameModule;
    moduleName: ModuleName;
    noWinResult?: any;
}

export interface GameLoader {
    loadGame(gameCode: string): GameModule;
    gameList(): GameModule[];
    getFlavorRoute(): string;
}

let gameLoader: GameLoader;

export function setGameLoader(loader: GameLoader) {
    setAllowedRoute(loader);
    gameLoader = loader;
}

export function getGameLoader(): GameLoader {
    if (!gameLoader) {
        throw new Error("GameLoader is null");
    }
    return gameLoader;
}

function setAllowedRoute(loader: GameLoader) {
    if (!loader) {
        return;
    }

    if (loader.getFlavorRoute && !config.routing.allowedDeployment) {
        config.routing.allowedDeployment = loader.getFlavorRoute();
    }
}
