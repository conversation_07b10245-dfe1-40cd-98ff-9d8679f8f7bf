import { createExtension as createRtpExtension } from "@skywind-group/sw-rtp-ext";
import { createExtension as createFreeBetOverrideExtension } from "@skywind-group/sw_ex_fb_override";
import { createExtension as createValidationExtension } from "@skywind-group/sw_ex_req_validation";
import { createExtension as createFreeBetCoinsOverride } from "@skywind-group/sw_ex_fb_coins_override";
import { createExtension as createUnfinishedRoundCoinsOverride } from "@skywind-group/sw_ug_coins_override";
import { createExtension as createInstantJpExtension } from "@skywind-group/sw_ex_instant_jp";
import { createExtension as createFeatureValidationExtension } from "@skywind-group/sw_ex_feature_validation";
import { GameExtensionModule, SomeGame } from "@skywind-group/sw-game-core";
import { AbstractGameProxy, ExtensionPosition } from "./gameProxy";
import { BrandSettings, Settings } from "../tokens";
import { Lazy, lazy } from "@skywind-group/sw-utils";

const rtpExtension: Lazy<GameExtensionModule> = lazy<any>(() => createRtpExtension());
const freeBetsExtensionOverride: Lazy<GameExtensionModule> = lazy<any>(() => createFreeBetOverrideExtension());
const validationExtension: Lazy<GameExtensionModule> = lazy<any>(() => createValidationExtension());
const featureValidationExtension: Lazy<GameExtensionModule> = lazy<any>(() => createFeatureValidationExtension());
const freeBetsExtensionCoinsOverride: Lazy<GameExtensionModule> = lazy<any>(() => createFreeBetCoinsOverride());
const unfinishedRoundExtensionCoinsOverride: Lazy<GameExtensionModule> =
    lazy<any>(() => createUnfinishedRoundCoinsOverride());
export const instantJpExtension: Lazy<GameExtensionModule> = lazy<any>(() => createInstantJpExtension());

export function decorateWithGlobalExtensions(game: SomeGame & AbstractGameProxy,
                                             settings: Settings,
                                             brandSettings?: BrandSettings): SomeGame & AbstractGameProxy {
    if (settings.validateRequestsExtensionEnabled) {
        game.addGlobalExtensionModule(ExtensionPosition.BEFORE_ALL, validationExtension.get());
    }
    if (settings.rtpConfigurator) {
        game.addGlobalExtensionModule(ExtensionPosition.BEFORE_ALL, rtpExtension.get());
    }
    // We have no gameType in game server
    const slotGame: any = game.game;
    if (slotGame && slotGame.defaultScene && slotGame.scenes) {
        if (brandSettings?.maxAnteBetStake || brandSettings?.maxBuyInStake) {
            game.addGlobalExtensionModule(ExtensionPosition.BEFORE_ALL, featureValidationExtension.get());
        }
        game.addGlobalExtensionModule(ExtensionPosition.BEFORE_ALL, unfinishedRoundExtensionCoinsOverride.get());
    }
    const currentExtensions = game.getExtensionModules && game.getExtensionModules();
    const freeBetExtensionPresent = currentExtensions
        && currentExtensions.find(ext => ext.constructor.name === "FreeBetsExtension");

    if (freeBetExtensionPresent) {
        game.addGlobalExtensionModule(ExtensionPosition.BEFORE_ALL, freeBetsExtensionCoinsOverride.get());
        game.addGlobalExtensionModule(ExtensionPosition.AFTER_ALL, freeBetsExtensionOverride.get());
    }

    if (settings.instantJpEnabled) {
        game.addGlobalExtensionModule(ExtensionPosition.BEFORE_ALL, instantJpExtension.get());
    }

    return game;
}
