import { PlayerContext, PlayerContextImpl } from "../playercontext/playerContext";
import { SomeCommand } from "../command";
import { GameContextID, PlayerContextID } from "../contextIds";
import { GameMode } from "@skywind-group/sw-game-core";
import { getPlayerContextDecoder } from "../playercontext/encoding/playerContextEncoding";
import { DBPlayerContext } from "../offlinestorage/offlineCommands";
import config from "../../config";
import { logging, redis } from "@skywind-group/sw-utils";
import { RedisProc } from "../redisCommandExecutor";
import logger = logging.logger;

export interface PlayerContextCommands {

    load(id: PlayerContextID): SomeCommand<PlayerContextImpl>;

    activateGame(id: PlayerContextID, gameContextId: GameContextID, playmode: GameMode): SomeCommand<PlayerContextImpl>;

    trackActive(id: PlayerContextID, ts: number): SomeCommand;

    removeGame(id: PlayerContextID, gameContextId: GameContextID, broken: boolean): SomeCommand;

    save(id: PlayerContextID, data: DBPlayerContext): SomeCommand<PlayerContextImpl>;

    remove(context: PlayerContext, force?: boolean): SomeCommand<number>;
}

const scriptsPath = `${__dirname}/../../../../resources/lua`;

export class PlayerContextCommandsImpl implements PlayerContextCommands {
    private readonly loadContextProc = new RedisProc(
        `${scriptsPath}/playerContextManager.lua`,
        `${scriptsPath}/loadPlayerContext.lua`,
    );

    private readonly activateGameProc = new RedisProc(
        `${scriptsPath}/playerContextManager.lua`,
        `${scriptsPath}/activateGameInPlayerContext.lua`,
    );

    private readonly removeGameProc = new RedisProc(
        `${scriptsPath}/playerContextManager.lua`,
        `${scriptsPath}/removeGameFromPlayerContext.lua`,
    );

    private readonly removeContextProc = new RedisProc(
        `${scriptsPath}/playerContextManager.lua`,
        `${scriptsPath}/removePlayerContext.lua`,
    );

    private readonly saveContextProc = new RedisProc(
        `${scriptsPath}/playerContextManager.lua`,
        `${scriptsPath}/savePlayerContext.lua`,
    );

    private readonly trackActiveContextProc = new RedisProc(
        `${scriptsPath}/playerContextManager.lua`,
        `${scriptsPath}/trackActivePlayerContext.lua`,
    );

    public load(id: PlayerContextID): SomeCommand<PlayerContextImpl> {
        return this.loadContextProc.cmd<PlayerContextImpl>([id.asString()], undefined, false, this.parseData(id));
    }

    public activateGame(id: PlayerContextID,
                        gameContextId: GameContextID,
                        playmode: GameMode): SomeCommand<PlayerContextImpl> {
        return this.activateGameProc.cmd([id.asString(), gameContextId.asString()],
            [playmode],
            true,
            this.parseData(id));
    }

    public removeGame(id: PlayerContextID, gameContextId: GameContextID, broken: boolean): SomeCommand {
        return this.removeGameProc.cmd([id.asString(), gameContextId.asString()], [broken ? 1 : 0], true);
    }

    public save(id: PlayerContextID, data: DBPlayerContext): SomeCommand<PlayerContextImpl> {
        return this.saveContextProc.cmd([data.id], [
            (data.games && JSON.stringify(data.games)) || null,
            data.data && JSON.stringify(data.data) || null,
            data.version
        ], true, this.parseData(id));
    }

    private parseData(id: PlayerContextID) {
        return (result: string[]) => {
            if (!result || !result[0]) {
                return undefined;
            }
            const context = new PlayerContextImpl(id);
            return getPlayerContextDecoder().decodeRedisData(context, result[0], result);
        };
    }

    public remove(context: PlayerContext, force?: boolean): SomeCommand<number> {
        return this.removeContextProc.cmd(
            [config.namespaces.lastPlayerActivityPrefix, context.id.asString()],
            [context.version, force ? 1 : 0]);
    }

    public trackActive(id: PlayerContextID, ts: number): SomeCommand<void> {
        return this.trackActiveContextProc.cmd(
            [config.namespaces.lastPlayerActivityPrefix, id.asString()],
            [ts]);
    }
}

export class FunPlayerContextCommands implements PlayerContextCommands {
    public load(id: PlayerContextID): SomeCommand<PlayerContextImpl> {
        return undefined;
    }

    public activateGame(id: PlayerContextID, gameContextId: GameContextID, playmode): SomeCommand<PlayerContextImpl> {
        return undefined;
    }

    public removeGame(id: PlayerContextID, gameContextId: GameContextID, broken: boolean): SomeCommand {
        return undefined;
    }

    public save(id: PlayerContextID, data: DBPlayerContext): SomeCommand<PlayerContextImpl> {
        return undefined;
    }

    public remove(context: PlayerContext, force?: boolean): SomeCommand<number> {
        return undefined;
    }

    public trackActive(id: PlayerContextID): SomeCommand<void> {
        return undefined;
    }
}
