import { GameFlowContextImpl } from "../context/gameContextImpl";
import * as Errors from "../../errors";
import { GameContextID } from "../contextIds";
import { FunRedis, Redis } from "../../storage/redis";
import config from "../../config";
import { ANALYTICS_QUEUE_NAME, GAME_HISTORY_LOG, ROUND_HISTORY_LOG, SESSION_HISTORY_LOG } from "../queue/redisQueue";
import { GameEventHistory, RoundHistory, SessionHistory } from "../../history/history";
import { getGameContextDecoder, getGameContextEncoder } from "../encoder/contextEncoding";
import { Command, IDENTITY, PromisedCommand, SomeCommand } from "../command";
import { CtxData } from "../encoder/encoder";
import { AnalyticsData } from "../analytics/analyticsService";
import { RedisProc } from "../redisCommandExecutor";
import { HiLoIdGenerator } from "@skywind-group/sw-utils";
import { GameFlowContext } from "../context/gamecontext";

// export is just for unit tests purposes. We should flush roundId cached values.
// todo rename this after full migration to new platform
let roundIdGenerator = new HiLoIdGenerator("sw-slot-engine:roundId",
    1000,
    Redis,
    config.generators.round.min,
    config.generators.round.max);
let funRoundIdGenerator = new HiLoIdGenerator(
    config.funGame.redis.keyPrefix + ":" + "sw-slot-engine:roundId",
    1000, FunRedis);

export function setGenerators(realGenerator: HiLoIdGenerator, funGenerator: HiLoIdGenerator) {
    roundIdGenerator = realGenerator;
    funRoundIdGenerator = funGenerator;
}

export enum StoreOptions {
    INIT_INFO = 1,
    PENDING_MODIFICATION = 2,
    JACKPOT_PENDING = 4,
    ROUND_INFO = 8,
    GAME_STATE = 16,
    JP_CONTEXT = 32,
    RETRY_ATTEMPTS = 64,
    LOGOUT_RESULT = 128,
    SPECIAL_STATE = 256,
    ALL = Math.pow(2, 32) - 1
}

export interface StoreHistory {
    item?: GameEventHistory;
    jpItem?: GameEventHistory;
    round?: RoundHistory;
    sessionStart?: SessionHistory;
    sessionEnd?: SessionHistory;
    analytics?: AnalyticsData;
}

/**
 * Abstraction for storing "hot" game contexts
 */
export interface GameContextOnlineCommands {

    /**
     * Generates game id
     */
    generateRoundId(): Promise<string>;

    /**
     * Stores game context;
     * @param context game context
     * @param sync should we waite for replication of this command to slave
     * @param storeOptions store options
     * @param history history item
     * @param checkExists do we need check if game context exists
     */
    store(context: GameFlowContextImpl,
          sync: boolean,
          storeOptions: StoreOptions,
          history?: StoreHistory,
          checkExists?: boolean): SomeCommand<GameFlowContextImpl>;

    /**
     * Prolong GameContext activity marker
     *
     */
    trackActive(context: GameContextID, ts: number): SomeCommand<void>;

    /**
     * Loads context from storage
     *
     * @param id context identifier
     * @param exclusiveLock set up 'exclusive' lock on record.
     *        The record could be updated only by the process who setup this flag.
     * @param reactive  commit of  transaction reactivate game context in the activity queue
     */
    load(id: GameContextID, exclusiveLock?: boolean, reactive?: boolean): SomeCommand<GameFlowContextImpl>;

    /**
     *  Loads context by merchant session Id
     *
     */
    loadByMerchantSessionId(brandId: number, merchantSessionId: string): SomeCommand<GameFlowContextImpl>;

    /**
     * Remove context from storage
     * @param context context identifier
     */
    remove(context: GameFlowContextImpl, sync: boolean, history?: StoreHistory, force?: boolean): SomeCommand;

    /**
     * Update dependency between our gameContext and merchant game serssion
     * @param id game context id
     */
    updateMerchantSessionLink(id: GameContextID, prevValue: string, newValue: string): SomeCommand;

    /**
     * Remove the link between game server and
     * @param id game context id
     */
    removeMerchantSessionLink(id: GameContextID, sessionId: string): SomeCommand;

    /**
     * Check if we have on-going force-cleanup(migration) procedure for the brand.
     * In case if we have it, we will raise and error 'ENV_ID_CHANGED'
     *
     * @param id game context id
     */
    checkMigrationStatus(id: GameContextID): SomeCommand;

    checkVersion(context: GameFlowContext, sync: boolean, checkExists: boolean): SomeCommand;
}

const scriptDir = __dirname + "/../../../../resources/lua/";

/**
 * Base ancestor for storing context in Redis
 */
export abstract class AbstractGameContextOnlineCommands implements GameContextOnlineCommands {

    protected loadHashProc: RedisProc = new RedisProc(
        `${scriptDir}gameContextManager.lua`, `${scriptDir}loadGameContext.lua`);

    public abstract generateRoundId(): Promise<string>;

    public store(context: GameFlowContextImpl,
                 sync: boolean,
                 storeOptions: StoreOptions,
                 history?: StoreHistory,
                 checkExists: boolean = true): SomeCommand<GameFlowContextImpl> {

        if (context.corrupted) {
            return Promise.reject(new Errors.GameContextStateIsBroken());
        }

        context.updatedAt = new Date();

        return this.doStore(context, sync, storeOptions, history, checkExists);
    }

    public load(id: GameContextID,
                exclusiveLock: boolean = false,
                reactive: boolean = true): SomeCommand<GameFlowContextImpl> {
        return this.doLoad(id, undefined, exclusiveLock, reactive);
    }

    public loadByMerchantSessionId(brandId: number, merchantSessionId: string): SomeCommand<GameFlowContextImpl> {
        return this.doLoad(undefined, this.getMerchantSessionKey(brandId, merchantSessionId), false);
    }

    public remove(context: GameFlowContextImpl, sync: boolean, history?: StoreHistory, force?: boolean): SomeCommand {
        if (context.corrupted) {
            return Promise.reject(new Errors.GameContextStateIsBroken());
        }

        return this.doRemove(context, sync, history, force);
    }

    public abstract trackActive(id: GameContextID, ts: number): SomeCommand<void>;

    protected abstract doStore(context: GameFlowContextImpl,
                               sync: boolean,
                               storeOptions: StoreOptions,
                               history: StoreHistory,
                               checkExists?: boolean): SomeCommand<GameFlowContextImpl>;

    public abstract checkVersion(
        context: GameFlowContextImpl,
        sync: boolean,
        checkExists: boolean
    ): SomeCommand;

    protected abstract doRemove(context: GameFlowContextImpl,
                                sync: boolean,
                                history: StoreHistory,
                                force: boolean): SomeCommand;

    protected getSessionId(context: GameFlowContextImpl) {
        if (context.prevSession === undefined) {
            return context.session.id;
        }
        return context.prevSession.id;
    }

    protected toArray(data: any): string[] {
        const result = [];
        // tslint:disable-next-line:forin
        for (const d in data) {
            result.push(d);
            result.push(data[d]);
        }

        return result;
    }

    protected doLoad(gameID: GameContextID,
                     merchantSessionKey: string,
                     exclusiveLock: boolean,
                     reactive?: boolean): SomeCommand<GameFlowContextImpl> {
        return this.loadHashProc.cmd(
            [this.getKey(gameID), merchantSessionKey], [exclusiveLock ? 1 : 0], false,
            async (response: string[]) => {
                const ctx: CtxData = {} as CtxData;
                for (let i = 0; i < response.length; i += 2) {
                    ctx[response[i]] = response[i + 1];
                }
                if (!ctx || !ctx.dataVersion) {
                    return undefined;
                }

                if (!gameID) {
                    gameID = GameContextID.createFromString(ctx.id);
                }

                const result = new GameFlowContextImpl(gameID, exclusiveLock, reactive);
                await getGameContextDecoder().decode(result, ctx);
                if (getGameContextEncoder().getDataVersion() !== ctx.dataVersion) {
                    result.markForFullUpdate();
                }
                return result;
            });
    }

    protected getMerchantSessionKey(brandId: number, sessionId: string): string {
        return sessionId ? `${config.namespaces.merchantSessionLink}:${brandId}:${sessionId}` : null;
    }

    public abstract removeMerchantSessionLink(id: GameContextID, sessionId: string): SomeCommand;

    public abstract updateMerchantSessionLink(id: GameContextID, prevValue: string, newValue: string): SomeCommand;

    public abstract checkMigrationStatus(id: GameContextID): SomeCommand;

    protected getKey(id: GameContextID): string {
        return id ? id.asString() : null;
    }
}

/**
 *  Main implementation with supporting game history storage
 */
export class GameContextOnlineCommandsImpl extends AbstractGameContextOnlineCommands {
    private checkVersionProc = new RedisProc(
        `${scriptDir}gameContextManager.lua`,
        `${scriptDir}checkGameContextVersion.lua`
    );
    private storeProc = new RedisProc(
        `${scriptDir}gameContextManager.lua`,
        `${scriptDir}storeGameContext.lua`);
    private removeProc = new RedisProc(
        `${scriptDir}gameContextManager.lua`,
        `${scriptDir}removeGameContext.lua`);

    private tracActiveProc = new RedisProc(
        `${scriptDir}gameContextManager.lua`,
        `${scriptDir}trackGameContextActive.lua`);

    private checkMigrationStatusProc = new RedisProc(
        `${scriptDir}checkMigrationStatus.lua`);

    private updateMerchantLinkProc = new RedisProc(`${scriptDir}updateMerchantLink.lua`);

    public generateRoundId(): Promise<string> {
        return roundIdGenerator.nextId();
    }

    public trackActive(id: GameContextID, ts: number): SomeCommand<void> {
        return this.tracActiveProc.cmd([this.getKey(id)],
            [config.namespaces.lastGameActivityKey, ts],
            true);
    }

    public removeMerchantSessionLink(id: GameContextID, sessionId: string): SomeCommand {
        return this.updateMerchantLinkProc.cmd([this.getMerchantSessionKey(id.brandId, sessionId), null, null],
            [],
            true);
    }

    public updateMerchantSessionLink(id: GameContextID, prevSessionId: string, newSessionId: string): SomeCommand {
        return this.updateMerchantLinkProc.cmd([
            this.getMerchantSessionKey(id.brandId, prevSessionId),
            this.getMerchantSessionKey(id.brandId, newSessionId),
            this.getKey(id)
        ], [], true);
    }

    protected async doStore(context: GameFlowContextImpl,
                            sync: boolean,
                            storeOptions: StoreOptions,
                            history: StoreHistory,
                            checkExists: boolean): PromisedCommand<GameFlowContextImpl> {

        const args: any[] = [
            this.getSessionId(context),
            context.version.toString()
        ];
        context.version++;
        const fields: any[] = this.toArray(await getGameContextEncoder().encode(context, storeOptions));
        args[2] = fields.length / 2;
        args[3] = 0;
        args[4] = checkExists ? 1 : 0;
        args[5] = context.lockExclusively ? 1 : 0;

        for (const f of fields) {
            args.push(f);
        }

        args[3] = context.gameData.gameTokenData.playmode === "play_money" ? 0 : this.addHistory(args, history);

        return this.storeProc.cmd([this.getKey(context.id)], args, sync, (result) => {
            context.markSaved();
            return context;
        });
    }

    public checkVersion(context: GameFlowContextImpl, sync: boolean, checkExists: boolean): SomeCommand {
        const args = [
            this.getSessionId(context),
            context.version.toString(),
            checkExists ? 1 : 0,
            context.lockExclusively ? 1 : 0
        ];

        return this.checkVersionProc.cmd([this.getKey(context.id)], args, sync, IDENTITY, true);
    }

    protected async doRemove(context: GameFlowContextImpl,
                             sync: boolean,
                             history: StoreHistory,
                             force: boolean): Promise<Command> {
        const args: any[] = [this.getSessionId(context), context.version.toString()];
        context.version++;
        args[2] = 0;
        args[3] = config.namespaces.lastGameActivityKey;
        args[4] = force ? 1 : 0;
        args[5] = context.lockExclusively ? 1 : 0;

        args[2] = context.gameData.gameTokenData.playmode === "play_money" ? 0 : this.addHistory(args, history);

        return this.removeProc.cmd([this.getKey(context.id)], args, sync, (result) => {
            if (result === -1) {
                context.corrupt();
                return Promise.reject(new Errors.ConcurrentAccessToGameSession());
            }
            context.markSaved();
        });
    }

    protected addHistory(args: any[], history: StoreHistory): number {
        let eventsCount = 0;

        if (history) {
            if (history.round) {
                eventsCount++;
                args.push(ROUND_HISTORY_LOG);
                args.push(JSON.stringify(history.round));
            }
            if (history.item) {
                eventsCount++;
                args.push(GAME_HISTORY_LOG);
                args.push(JSON.stringify(history.item));
            }
            if (history.jpItem) {
                eventsCount++;
                args.push(GAME_HISTORY_LOG);
                args.push(JSON.stringify(history.jpItem));
            }
            if (history.sessionStart) {
                eventsCount++;
                args.push(SESSION_HISTORY_LOG);
                args.push(JSON.stringify(history.sessionStart));

            }
            if (history.sessionEnd) {
                eventsCount++;
                args.push(SESSION_HISTORY_LOG);
                args.push(JSON.stringify(history.sessionEnd));
            }

            if (history.analytics) {
                eventsCount++;
                args.push(ANALYTICS_QUEUE_NAME);
                args.push(JSON.stringify(history.analytics));
            }
        }

        return eventsCount;
    }

    public checkMigrationStatus(id: GameContextID): SomeCommand {
        return this.checkMigrationStatusProc.cmd(
            [config.namespaces.forceCleanupJob],
            [id.brandId],
            false,
            IDENTITY,
            true
        );
    }
}

export class PlayMoneyGameContextOnlineCommands extends GameContextOnlineCommandsImpl {

    protected addHistory(args: any[], history: StoreHistory): number {
        return 0;
    }
}

/**
 *  Main implementation for fun games without game history
 */
export class FunGameContextOnlineCommands extends AbstractGameContextOnlineCommands {
    private checkVersionProc = new RedisProc(
        `${scriptDir}gameContextManager.lua`,
        `${scriptDir}checkGameContextVersion.lua`
    );
    private storeProc = new RedisProc(
        `${scriptDir}gameContextManager.lua`,
        `${scriptDir}storeGameContextFun.lua`);
    private removeProc = new RedisProc(
        `${scriptDir}gameContextManager.lua`,
        `${scriptDir}removeGameContextFun.lua`);

    public generateRoundId(): Promise<string> {
        return funRoundIdGenerator.nextId();
    }

    public trackActive(id: GameContextID, ts: number): SomeCommand<void> {
        return undefined;
    }

    protected getKey(id: GameContextID): string {
        return config.funGame.redis.keyPrefix + ":" + id.asString();
    }

    protected async doStore(context: GameFlowContextImpl,
                            sync: boolean,
                            storeOptions: StoreOptions,
                            history: StoreHistory,
                            checkExists: boolean): PromisedCommand<GameFlowContextImpl> {

        const args: any[] = [
            this.getSessionId(context),
            context.version.toString()
        ];
        context.version++;
        const fields: any[] = this.toArray(await getGameContextEncoder().encode(context, storeOptions));
        args[2] = fields.length / 2;
        args[3] = config.funGame.TTL;
        args[4] = checkExists ? 1 : 0;

        for (const f of fields) {
            args.push(f);
        }
        return this.storeProc.cmd([this.getKey(context.id)], args, false, async (result: number) => {
            if (result === -1) {
                context.corrupt();
                return Promise.reject(new Errors.ConcurrentAccessToGameSession());
            }
            context.markSaved();
            return context;
        });
    }

    protected async doRemove(context: GameFlowContextImpl,
                             sync: boolean,
                             history: StoreHistory,
                             force: boolean): Promise<Command> {
        const args: any[] = [this.getSessionId(context), context.version.toString()];
        context.version++;
        return this.removeProc.cmd([this.getKey(context.id)], args, false, async (result: number) => {
            if (result === -1) {
                context.corrupt();
                return Promise.reject(new Errors.ConcurrentAccessToGameSession());
            }
            context.markSaved();
        });
    }

    public removeMerchantSessionLink(id: GameContextID, sessionId: string): SomeCommand {
        return undefined;
    }

    public updateMerchantSessionLink(id: GameContextID, prevValue: string, newValue: string): SomeCommand {
        return undefined;
    }

    public checkMigrationStatus(id: GameContextID): SomeCommand {
        return undefined;
    }

    public checkVersion(context: GameFlowContextImpl, sync: boolean, checkExists: boolean): SomeCommand {
        const args = [
            this.getSessionId(context),
            context.version.toString(),
            checkExists ? 1 : 0
        ];

        return this.checkVersionProc.cmd([this.getKey(context.id)], args, sync, IDENTITY, true);
    }

    protected addHistory(args: any[], history: StoreHistory): number {
        return 0;
    }
}

export function getRoundIdGenerator() {
    return roundIdGenerator;
}
