import {
    BaseRequest,
    BaseResult,
    ClientResponse,
    FunStartGameToken,
    GameInitRequest
} from "@skywind-group/sw-game-core";
import { InvalidStartGameToken } from "../errors";
import { StartGameTokenData, verifyStartGameToken } from "./tokens";
import config from "../config";

export interface GameReInitRequest extends BaseRequest {
    request: "reinit";
    gameSession: string;
    startGameToken: string | FunStartGameToken;
    gameId: string;
    deviceId: string;
}

export interface ReInitClientResponse extends ClientResponse {
    lastRequestId?: number;
}

export interface RedeemBNSRequest extends BaseRequest {
    request: "redeem-bns";
    promoId?: string;
    manual?: boolean;
}

export interface RedeemBNSResponse extends BaseResult {
    redeemBalance: number;
    redeemCurrency: string;
    promoId: string;
}

export interface PlayerActionRequest extends BaseRequest {
    action: string;
    params?: any;
}

export interface ChangeNicknameActionRequest extends BaseRequest {
    payload: {
        nickname: string;
        decreaseNicknameChangeAttemptsLeft: boolean; // the same as increaseNicknameChangeAttempts
    };
}

export async function extractStartGameTokenData(
    request: GameInitRequest | GameReInitRequest): Promise<StartGameTokenData> {
    // fun mode - expect  object FunStartGameToken
    // real mode - expect string startGameToken.
    let result: StartGameTokenData;
    if (typeof request.startGameToken === "string") {
        result = await verifyStartGameToken(request.startGameToken);

        if (result.playmode === "fun") {
            // TODO define jackpot id for fun mode
            request.startGameToken = {
                playerCode: result.playerCode,
                gameCode: result.gameCode,
                brandId: result.brandId,
                currency: result.currency,
                gameGroup: result.gameGroup
            } as any;
        }
    } else if (!config.funGame.startGameTokenRequired) {
        result = request.startGameToken as any;
        result.playmode = "fun";
    } else {
        throw new InvalidStartGameToken();
    }

    if (!request.gameId) {
        request.gameId = result.providerGameCode;
    }

    return result;
}
