import { ManagementAPISupport } from "./managementapihelper";
import { BaseRequest, FunStartGameToken } from "@skywind-group/sw-game-core";
import config from "../config";
import { RequestContext } from "./context/gamecontext";
import * as Errors from "../errors";
import { GameMode } from "@skywind-group/sw-game-core";
import { GameTokenData } from "./tokens";

export interface MerchantGameInitRequest {
    merchantType: string;
    merchantCode: string;
    gameCode: string;
    ip: string;
}

export interface ChooseGameRequest extends BaseRequest {
    startGameToken: string | FunStartGameToken;
    gameId: string;
    playmode: GameMode;
    request: "choose-game";
    gameTokenData?: GameTokenData;
    isLive?: boolean;
}

export interface PlayerGameURLInfo {
    url: string;
    token: string;
}

export interface GamesListRequest extends BaseRequest {
    request: "get-games";
    startGameToken: string;
    filter?: any;
}

export interface GamesListResponse {
    games: string[];
}

export interface PlayerGameInfoService {
    getGameURLInfo(url: string,
                   data: MerchantGameInitRequest | ChooseGameRequest & RequestContext): Promise<PlayerGameURLInfo>;

    getPlayerGameURLInfo(data: ChooseGameRequest & RequestContext): Promise<PlayerGameURLInfo>;

    getMerchantGameURLInfo(data: MerchantGameInitRequest): Promise<PlayerGameURLInfo>;

    getAvailableGames(data: GamesListRequest): Promise<GamesListResponse>;
}

class PlayerGameInfoServiceImpl extends ManagementAPISupport implements PlayerGameInfoService {
    private static GET_PLAYER_GAME_INFO_URL = "/v1/game/url";
    private static GET_MERCHANT_GAME_INFO_URL = "/v1/merchants/game/url";
    private static GET_AVAILABLE_GAMES_URL = "/v1/games";

    constructor(url: string) {
        super(url);
    }

    /**
     * Gets list of available games
     * @param {GamesListRequest} data
     * @returns {Promise<GamesListResponse>}
     */
    public async getAvailableGames(data: GamesListRequest): Promise<GamesListResponse> {
        return this.post<GamesListResponse>(PlayerGameInfoServiceImpl.GET_AVAILABLE_GAMES_URL, data);
    }

    /**
     * Gets new player game info for any modes and entity types
     * @param {ChooseGameRequest & RequestContext} data
     * @returns {Promise<PlayerGameURLInfo>}
     */
    public async getPlayerGameURLInfo(data: ChooseGameRequest & RequestContext): Promise<PlayerGameURLInfo> {
        return this.getGameURLInfo(PlayerGameInfoServiceImpl.GET_PLAYER_GAME_INFO_URL, data);
    }

    /**
     * Gets merchant game info
     * @param {MerchantGameInitRequest} data
     * @returns {Promise<PlayerGameURLInfo>}
     */
    public async getMerchantGameURLInfo(data: MerchantGameInitRequest): Promise<PlayerGameURLInfo> {
        return this.getGameURLInfo(PlayerGameInfoServiceImpl.GET_MERCHANT_GAME_INFO_URL, data);
    }

    public getGameURLInfo(
        url, data: MerchantGameInitRequest | ChooseGameRequest & RequestContext): Promise<PlayerGameURLInfo> {

        const customOptions = this.getCustomQueryOptions(data.ip);

        return this.post<PlayerGameURLInfo>(
            url, data, data.ip, undefined, customOptions);

    }

    protected getCustomQueryOptions(ip: string) {
        return ip &&  { ip: ip };
    }

}

let playerGameInfoService: PlayerGameInfoService;

export function getService(): PlayerGameInfoService {
    if (!playerGameInfoService) {
        playerGameInfoService = new PlayerGameInfoServiceImpl(config.managementAPIGameAuth);
    }

    return playerGameInfoService;
}

export async function initMerchantGame(merchantType: string, request: any): Promise<PlayerGameURLInfo> {
    if (!request.merchantCode) {
        return Promise.reject(new Errors.MerchantGameInitError("Missing merchantCode parameter"));
    }
    if (!request.gameCode) {
        return Promise.reject(new Errors.MerchantGameInitError("Missing gameCode parameter"));
    }

    return await getService().getMerchantGameURLInfo({ merchantType, ...request });
}
