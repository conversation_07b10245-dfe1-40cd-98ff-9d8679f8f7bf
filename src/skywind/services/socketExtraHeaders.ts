import * as Socket<PERSON> from "socket.io";
import { GameInitRequest } from "@skywind-group/sw-game-core";
import { StartGameTokenData } from "./tokens";
import { WrongSocketHeadersError } from "../errors";
import { ExtraHeaders } from "../utils/common";

export class SocketExtraHeaders {
    public static readonly SW_GAME = Symbol("sw-game");
    public static readonly SW_PLAYER = Symbol("sw-player");

    public static setUp(socket: SocketIO.Socket, req) {
        this.setUpField(socket, req, ExtraHeaders.X_SW_GAME, SocketExtraHeaders.SW_GAME);
        this.setUpField(socket, req, ExtraHeaders.X_SW_PLAYER, SocketExtraHeaders.SW_PLAYER);
    }

    public static validate(tokenData: StartGameTokenData, req: GameInitRequest) {
        const gameId = req[SocketExtraHeaders.SW_GAME];
        const player = req[SocketExtraHeaders.SW_PLAYER];
        if ((gameId !== undefined && gameId !== tokenData.providerGameCode)
            || (player !== undefined && player !== `${tokenData.brandId}:${tokenData.playerCode}`)) {
            throw new WrongSocketHeadersError();
        }
    }

    private static setUpField(socket: SocketIO.Socket, req, header: string, symbol: symbol) {
        const value = socket?.handshake?.headers?.[header];
        if (value) {
            req[symbol] = value;
        }
    }
}
