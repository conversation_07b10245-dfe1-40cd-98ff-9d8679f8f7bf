/**
 * Perform regulatory actions over player through 'sw-management-api' module.
 */
import { ManagementAPISupport } from "./managementapihelper";
import { lazy, measures } from "@skywind-group/sw-utils";
import config from "../config";
import { ContextUtil } from "./contextmanager/contextManagerImpl";
import measure = measures.measure;
import { ForbiddenRequest } from "../errors";
import { PlayerActionRequest } from "./requests";
import { GameFlowContext } from "./context/gamecontext";

export class RegulatoryActionService extends ManagementAPISupport {
    private static REGULATORY_ACTION_URL = "/v2/play/regulatory-action";

    constructor(private readonly url: string) {
        super(url);
    }

    @measure({ name: "RegulatoryActionService.performPlayerRegulatoryAction", isAsync: true })
    public async performPlayerRegulatoryAction(req: PlayerActionRequest, context: GameFlowContext): Promise<any> {
        if (context.gameData.gameTokenData.playmode === "fun") {
            return Promise.reject(new ForbiddenRequest());
        }
        return this.post<any>(RegulatoryActionService.REGULATORY_ACTION_URL, {
            gameToken: context.gameData.gameTokenData.token,
            action: req.action,
            params: req.params
        });
    }
}

const regulatoryActionServiceManager = lazy(() => new RegulatoryActionService(config.managementAPI.url));

export function getRegulatoryActionService(): RegulatoryActionService {
    return regulatoryActionServiceManager.get();
}
