import { Redis } from "../storage/redis";
import { Redis as RedisClient } from "ioredis";
import config from "../config";
import { GameContextID, PlayerContextID } from "./contextIds";
import { UNSUPPORTED } from "../utils/common";
import { RedisProc } from "./redisCommandExecutor";
import { measures } from "@skywind-group/sw-utils";
import { GameMode } from "@skywind-group/sw-game-core";
import measure = measures.measure;

/**
 * Tracks game context last activity , returns last recently used contexts
 */

export interface IDsCursor<T> {
    next(): Promise<T[]>;

    close(): void;
}

export interface ActivityTracker {
    /**
     * Gets least recently used game context identifies
     *
     * @param maxCount - max count of ids in result.
     * @param maxTimestamp - max timestamp. We don't get new ids, only thouse
     *     are stale enough.
     */
    getLruGameContextIDs(maxTimestamp: number, maxCount: number): Promise<string[]>;

    /**
     * Gets least recently used player context identifies
     *
     * @param maxCount - max count of ids in result.
     * @param maxTimestamp - max timestamp. We don't get new ids, only thouse
     *     are stale enough.
     */
    getLruPlayerContextIDs(maxTimestamp: number, maxCount: number): Promise<string[]>;

    /**
     * Get active game contexts elements for brand
     *
     * @param {number} brandId
     * @param {number} batchSize
     * @returns {Promise<string[]>}
     */
    findTopActiveGameContextIDsByBrand(brandId: number, batchSize: number): Promise<GameContextID[]>;

    /**
     * Get active player contexts elements for brand
     *
     * @param {number} brandId
     * @param {number} batchSize
     * @returns {Promise<string[]>}
     */
    findTopActivePlayerContextIDsByBrand(brandId: number, batchSize: number): Promise<PlayerContextID[]>;

    /**
     * Remove last activity if its current value equal with the ts
     */
    removeGameContextActivity(id: string, ts: number): Promise<void>;
}

/**
 *  Implementation through separate skip list that contains timestamps
 */

const scriptDir = __dirname + "/../../../resources/lua/";

export class GameActivityTrackerImpl implements ActivityTracker {
    private removeProc = new RedisProc(`${scriptDir}gameContextManager.lua`, `${scriptDir}removeActivity.lua`);
    private findLexiGameContextsProc = new RedisProc(`${scriptDir}gameContextManager.lua`,
        `${scriptDir}findTopLexiGameContexts.lua`);
    private findLexiPlayerContextsProc = new RedisProc(`${scriptDir}playerContextManager.lua`,
        `${scriptDir}findTopLexiPlayerContexts.lua`);
    private modes: GameMode[] = ["real", "bns"];

    public async getLruGameContextIDs(maxTimestamp: number, maxCount: number): Promise<string[]> {
        return this.getLru(config.namespaces.lastGameActivityKey, maxTimestamp, maxCount);
    }

    public async getLruPlayerContextIDs(maxTimestamp: number, maxCount: number): Promise<string[]> {
        return this.getLru(config.namespaces.lastPlayerActivityPrefix, maxTimestamp, maxCount);
    }

    private async getLru(key: string, maxTimestamp: number, maxCount: number): Promise<string[]> {
        const client: RedisClient = await Redis.get().get();
        try {
            return await client.zrangebyscore(key, 0, maxTimestamp, "LIMIT", 0, maxCount);
        } finally {
            await Redis.get().release(client);
        }
    }

    @measure({ name: "GameActivityTrackerImpl.findTopActiveGameContextIDsByBrand", isAsync: true })
    public async findTopActiveGameContextIDsByBrand(brandId: number, batchSize: number): Promise<GameContextID[]> {
        let result = [];
        const client: RedisClient = await Redis.get().get();
        let limit = batchSize;
        try {
            for (let i = 0; i < this.modes.length && limit > 0; i++) {
                const mode = this.modes[i];
                const [min, max] = GameContextID.byBrandMatch(brandId, mode);
                const buffer = await this.findLexiGameContextsProc.exec<string[]>(client,
                    [config.namespaces.lastGameActivityKey], [min, max, limit.toString()])
                    .then(res => res.map(item => GameContextID.createFromString(item)));
                result = result.concat(buffer);
                limit -= buffer.length;
            }

            return result;
        } finally {
            await Redis.get().release(client);
        }
    }

    @measure({ name: "GameActivityTrackerImpl.findTopActivePlayerContextIDsByBrand", isAsync: true })
    public async findTopActivePlayerContextIDsByBrand(brandId: number, batchSize: number): Promise<PlayerContextID[]> {
        const client: RedisClient = await Redis.get().get();

        try {
            const [min, max] = PlayerContextID.byBrandMatch(brandId);
            return await this.findLexiPlayerContextsProc.exec<string[]>(client,
                [config.namespaces.lastPlayerActivityPrefix], [min, max, "1000"])
                .then(res => res.map(item => PlayerContextID.createFromString(item)));
        } finally {
            await Redis.get().release(client);
        }
    }

    public async removeGameContextActivity(id: string, ts: number): Promise<void> {
        const client: RedisClient = await Redis.get().get();
        try {
            await this.removeProc.exec(client, [id], [config.namespaces.lastGameActivityKey, ts.toString()]);
        } finally {
            await Redis.get().release(client);
        }
    }
}

/**
 *  Implementation for fun games through redis ttl
 */

export class FunActivityTracker implements ActivityTracker {
    public async getLruPlayerContextIDs(maxTimestamp: number, maxCount: number): Promise<string[]> {
        return UNSUPPORTED();
    }

    public async findTopActivePlayerContextIDsByBrand(brandId: number, maxCount: number): Promise<PlayerContextID[]> {
        return undefined;
    }

    public async getLruGameContextIDs(maxTimestamp: number, limit: number): Promise<string[]> {
        return UNSUPPORTED();
    }

    public async findTopActiveGameContextIDsByBrand(brandId: number, maxCount: number): Promise<GameContextID[]> {
        return undefined;
    }

    public async removeGameContextActivity(id: string, ts: number): Promise<void> {
        return UNSUPPORTED();
    }
}
