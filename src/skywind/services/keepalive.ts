import { ManagementAPISupport } from "./managementapihelper";
import { logging, measures } from "@skywind-group/sw-utils";
import config from "../config";
import { EventEmitter } from "events";
import * as Errors from "../errors";
import measure = measures.measure;
import { SWError } from "@skywind-group/sw-wallet-adapter-core";

const log = logging.logger("sw-slot-engine:keep-alive");

class PingService extends ManagementAPISupport {
    private static DEFAULT_PING_URL = "/v1/play/balance";

    constructor(private pingUrl: string = PingService.DEFAULT_PING_URL,
                url: string = config.managementAPI.url) {

        super(url);
    }

    @measure({ name: "PingService.ping", isAsync: true })
    public ping(gameToken: string): Promise<void> {
        return this.get<void>(this.pingUrl, { gameToken: gameToken });
    }
}

const pingService: PingService = new PingService();

// TODO: remove this in future as wrapper supports it
export default class KeepAliveTimer extends EventEmitter {

    private interval: any;

    constructor(private gameToken: string, private keepAliveSec: number) {
        super();
    }

    public get isActive() {
        return !!this.interval;
    }

    public start() {
        this.interval = setInterval(() => {
            pingService.ping(this.gameToken)
                .then(() => {
                    log.debug({ gameToken: this.gameToken }, "Ping");
                })
                .catch(err => {
                    log.error(err, { gameToken: this.gameToken }, "Ping failed");
                    if (Errors.isSWError(err)) {
                        const swError: SWError = err;
                        if (swError.responseStatus >= 400 && swError.responseStatus < 500) {
                            this.stop();
                            this.emit("error", err);
                        }
                    }
                });
        }, this.keepAliveSec * 1000);
    }

    public stop() {
        if (this.interval) {
            clearInterval(this.interval);
            delete this.interval;
        }
    }
}
