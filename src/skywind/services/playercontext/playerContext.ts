import { GameMode } from "@skywind-group/sw-game-core";
import { lazy } from "@skywind-group/sw-utils";
import { GameContextID, PlayerContextID } from "../contextIds";
import { CommandExecutor } from "../command";
import { PlayerContextCommands } from "../onlinestorage/playerContextCommands";
import { GameFlowContextImpl } from "../context/gameContextImpl";
import config from "../../config";

export interface PlayerGameInfo {
    id: GameContextID;
    mode: GameMode;
}

export interface FindPlayerGameOption {
    active?: boolean;
    gameCode?: string;
    gameMode?: GameMode;
    deviceId?: string;
}

export interface PlayerContext {

    readonly id: PlayerContextID;

    readonly activeGames: PlayerGameInfo[];

    readonly brokenGames: PlayerGameInfo[];

    readonly dataVersion: number;

    readonly version: number;

    readonly keepOffline: boolean;

    remove(force?: boolean): Promise<boolean>;

    markActive(): Promise<void>;

    hasGame(options: FindPlayerGameOption): boolean;

    findGame(option: FindPlayerGameOption): PlayerGameInfo;
    /**
     *  ... will be implemented in future
     *
     * set(property: string, value: string): void
     *
     * compareAndSwap(property: string, oldValue: string, newValue: string): void
     *
     * incrBy(property: string, value?: number);
     *
     * get(property: string): string
     *
     * remove(property: string): void
     *
     * expireAt(timestamp: number): void
     */
}

export class PlayerContextImpl implements PlayerContext {
    public data: any;
    public readonly dataVersion: number = 1;
    public version: number;

    public activeGamesWithMode: any[];
    public brokenGamesWithMode: any[];

    public commands: PlayerContextCommands;
    public executor: CommandExecutor;

    public static readonly HAS_GAME_FILTER = (item: PlayerGameInfo, opts: FindPlayerGameOption) =>
            (!opts.gameCode || item.id.gameCode === opts.gameCode) &&
            (!opts.gameMode || item.mode === opts.gameMode) &&
            (!opts.deviceId || item.id.deviceId === opts.deviceId)

    constructor(public readonly id: PlayerContextID) {
    }

    private readonly lazyActiveGames = lazy(() => {
        const result = this.parseGames(this.activeGamesWithMode);
        this.activeGamesWithMode = undefined;
        return result;
    });

    private readonly lazyBrokenGames = lazy(() => {
        const result = this.parseGames(this.brokenGamesWithMode);
        this.brokenGamesWithMode = undefined;
        return result;
    });

    private parseGames(data: string[]): PlayerGameInfo[] {
        const result = [];
        if (data) {
            for (let i = 0; i < data.length; i += 2) {
                result.push({
                    id: GameContextID.createFromString(data[i]),
                    mode: data[i + 1]
                });
            }
        }

        return result;
    }

    public get activeGames(): PlayerGameInfo[] {
        return this.lazyActiveGames.get();
    }

    public get brokenGames(): PlayerGameInfo[] {
        return this.lazyBrokenGames.get();
    }

    public get keepOffline() {
        return this.brokenGames.length > 0 || this.activeGames.length > 0;
    }

    public hasGame(options: FindPlayerGameOption): boolean {
        return !!this.findGame(options);
    }

    public findGame(options: FindPlayerGameOption): PlayerGameInfo {
        if (options.active === true) {
            return this.activeGames.find((item) => PlayerContextImpl.HAS_GAME_FILTER(item, options));
        } else if (options.active === false) {
            return this.brokenGames.find((item) => PlayerContextImpl.HAS_GAME_FILTER(item, options));
        } else {
            return this.activeGames.find((item) => PlayerContextImpl.HAS_GAME_FILTER(item, options)) ||
                this.brokenGames.find((item) => PlayerContextImpl.HAS_GAME_FILTER(item, options));
        }
    }

    public async remove(force?: boolean): Promise<boolean> {
        const result = await this.executor.execute<number>(this.commands.remove(this, force));
        return result === 1;
    }

    public async markActive(): Promise<void> {
        return this.executor.execute(this.commands.trackActive(this.id,
            GameFlowContextImpl.getPlayerActivityTimeout()));
    }
}
