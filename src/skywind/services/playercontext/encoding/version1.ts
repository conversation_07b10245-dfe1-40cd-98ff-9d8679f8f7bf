import { PlayerContextImpl } from "../playerContext";
import { PlayerContextDecoder } from "./playerContextEncoding";
import { DBPlayerContext } from "../../offlinestorage/offlineCommands";

type PlayerContextDataV1 = [string, string[], string[], string[]];

export class PlayerContextDataDecoderV1 implements PlayerContextDecoder<PlayerContextDataV1> {
    public static VERSION = "1";

    public decodeRedisData(playerContext: PlayerContextImpl,
                           dataVersion: string,
                           data: PlayerContextDataV1): PlayerContextImpl {
        const [_, metaInf, activeGame, brokenGames] = data;
        for (let i = 0; i < metaInf.length; i += 2) {
            const name = metaInf[i];
            const value = metaInf[i + 1];
            if (name === "version") {
                playerContext.version = +value;
            }
        }
        playerContext.activeGamesWithMode = activeGame;
        playerContext.brokenGamesWithMode = brokenGames;

        return playerContext;
    }

    public decodeDBData(playerContext: PlayerContextImpl,
                        dbPlayerContext: DBPlayerContext): PlayerContextImpl {
        playerContext.brokenGamesWithMode = [];
        dbPlayerContext.games.forEach((info) => playerContext.brokenGamesWithMode.push(info.id, info.mode));
        playerContext.version = dbPlayerContext.version;

        return playerContext;
    }
}
