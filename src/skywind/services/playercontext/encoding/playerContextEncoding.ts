import { PlayerContextImpl } from "../playerContext";
import { PlayerContextDataDecoderV1 } from "./version1";
import { DBPlayerContext } from "../../offlinestorage/offlineCommands";

export interface PlayerContextDecoder<T> {
    decodeRedisData(playerContext: PlayerContextImpl,
                    dataVersion: string,
                    data: T): Promise<PlayerContextImpl> | PlayerContextImpl;

    decodeDBData(playContext: PlayerContextImpl,
                 dbPlayerContext: DBPlayerContext): Promise<PlayerContextImpl> | PlayerContextImpl;
}

interface DecoderRepository {
    [fieldName: string]: PlayerContextDecoder<any>;
}

const decodersByVersion: DecoderRepository = {};

decodersByVersion[PlayerContextDataDecoderV1.VERSION] = new PlayerContextDataDecoderV1();

const decoder: PlayerContextDecoder<any> = {
    decodeRedisData: async (context: PlayerContextImpl, dataVersion: string, data: any) => {
        return decodersByVersion[dataVersion].decodeRedisData(context, dataVersion, data);
    },

    decodeDBData(playContext: PlayerContextImpl,
                 dbPlayerContext: DBPlayerContext): Promise<PlayerContextImpl> | PlayerContextImpl {
        return decodersByVersion[dbPlayerContext.dataVersion.toString()].decodeDBData(playContext, dbPlayerContext);
    }
};

export function getPlayerContextDecoder(): PlayerContextDecoder<any> {
    return decoder;
}
