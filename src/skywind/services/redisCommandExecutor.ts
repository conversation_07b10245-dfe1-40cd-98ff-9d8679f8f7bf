import { BaseCommandExecutor, Command, IDENTITY } from "./command";
import { Redis as RedisClient } from "ioredis";
import { Lazy, logging, redis } from "@skywind-group/sw-utils";
import { ConcurrentAccessToGameSession, EnvIdChangedError } from "../errors";
import logger = logging.logger;

export class RedisCommand<T> implements Command<T> {
    constructor(public readonly proc: RedisProc,
                public readonly keys: any[],
                public readonly args: any[],
                public readonly sync: boolean,
                public readonly parser: (result: any) => Promise<T> | T = IDENTITY,
                public readonly executeIndependently?: boolean) {
    }

    public async init(client: RedisClient) {
        return this.proc.init(client);
    }

    public async parseResult(result: any): Promise<T> {
        try {
            return this.parser(result);
        } catch (err) {
            return Promise.reject(err);
        }
    }
}

export class RedisProc extends redis.RedisProc {
    constructor(...files: string[]) {
        super(logger("sw-slot-engine:redisProc"), ...files);
    }

    public cmd<T>(keys?: any[],
                  args?: any[],
                  sync: boolean = false,
                  parser: (result) => Promise<T> | T = IDENTITY,
                  executeIndependently?: boolean): Command<T> {
        return new RedisCommand<T>(this, keys, args, sync, parser, executeIndependently);
    }
}

export type AnyRedisCommand = RedisCommand<any>;

export class RedisCommandExecutor extends BaseCommandExecutor {
    constructor(private readonly redis: Lazy<redis.RedisPool<RedisClient>>) {
        super();
    }

    protected async processOne(command: AnyRedisCommand) {
        const client = await this.redis.get().get();
        let response;
        try {
            response = await command.proc.exec(client, command.keys, command.args);

            if (command.sync) {
                await this.redis.get().waitForSync(client);
            }
            return response;
        } catch (err) {
            return Promise.reject(this.processError(err));
        } finally {
            await this.redis.get().release(client);
        }
    }

    protected async processPipeline(cmds: AnyRedisCommand[]): Promise<any[]> {
        const client = await this.redis.get().get();

        try {
            return await this.doExecute(cmds, client);
        } catch (err) {
            if (err.message.includes("NOSCRIPT")) {
                await Promise.all(cmds.map(c => c.init(client)));
                return await this.doExecute(cmds, client);
            } else {
                return Promise.reject(this.processError(err));
            }
        } finally {
            await this.redis.get().release(client);
        }
    }

    private async doExecute(cmds: AnyRedisCommand[], client: RedisClient): Promise<any> {
        let sync = false;
        const responses = [];
        const multi = client.multi();
        try {
            for (const c of cmds) {
                if (c) {
                    const proc = c.proc;
                    if (!proc.handle) {
                        await proc.init(client);
                    }
                    sync = sync || c.sync;
                    if (c.keys?.length || c.args?.length) {
                        const keys = c.keys || [];
                        const args = c.args || [];
                        if (c.executeIndependently) {
                            // Execute right away, skipping multi-exec
                            responses.push(await proc.exec(client, keys, args));
                        } else {
                            multi.evalsha(proc.handle, c.keys.length, ...keys, ...args);
                        }
                    }
                }
            }

            responses.push(...await multi.exec());

            const errors = responses.filter(r => r[0] instanceof Error).flat();

            if (errors.length) {
                throw errors[0];
            }

            if (sync) {
                await this.redis.get().waitForSync(client);
            }
            return responses.map(r => r[1]);
        } catch (err) {
            multi.discard();
            throw err;
        }
    }

    private static readonly CONCURRENT_ACCESS = "CONCURRENT_ACCESS";
    private static readonly ENV_ID_CHANGED = "ENV_ID_CHANGED";

    private processError(error: Error): Error {
        if (error.message.includes(RedisCommandExecutor.CONCURRENT_ACCESS)) {
            return new ConcurrentAccessToGameSession();
        } else if (error.message.includes(RedisCommandExecutor.ENV_ID_CHANGED)) {
            return new EnvIdChangedError();
        }

        return error;
    }
}
