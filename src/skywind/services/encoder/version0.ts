/**
 * Decode data in old format, that doesn't contain dataVersion field
 *
 */
import { GameFlowContextImpl } from "../context/gameContextImpl";
import { GameSession } from "../gameSession";
import { ContextDataDecoder } from "./encoder";
import { decodeGameIdWithFallback, decodeJpContext } from "./util";

/**
 * Current fields that is used in redis
 */
export interface OldGameContextData {
    state: string;
    /**
     * The version of format, is used to find appropriate decoder
     */
    dataVersion: string;
    currentSessionId: string;
    requestId: string;
    version: string;
    historyCounter: string;
}

export class OldContextDataDecoder implements ContextDataDecoder<OldGameContextData> {
    public async decode(context: GameFlowContextImpl, data: OldGameContextData) {
        context.session = GameSession.create(data.currentSessionId);
        context.lastRequestId = data.requestId === undefined ? 0 : parseInt(data.requestId, 10);
        context.version = data.version === undefined ? 0 : parseInt(data.version, 10);
        const state = JSON.parse(data.state);
        this.updateOldPending(state);

        context.settings = state.settings;
        context.gameData = state.gameData;
        context.gameData.gameId = decodeGameIdWithFallback(context.id.gameCode);
        context.gameVersion = state.gameVersion;
        context.persistencePolicy = state.persistencePolicy;
        context.pendingModification = state.pendingModification;
        context.jackpotPending = state.pendingModification ? state.pendingModification.jackpotPending : undefined;
        context.gameContext = state.gameContext;
        context.roundId = state.roundId;
        context.roundEnded = state.roundEnded;
        context.jpContext = decodeJpContext(state.jpContext, context.settings);
        context.round = state.round;
        context.requestContext = state.requestContext;
        context.createdAt = state.createdAt;
        context.updatedAt = state.updatedAt;
        context.gameSerialNumber = !data.historyCounter ? 0 : parseInt(data.historyCounter, 10);
        context.totalEventId = context.gameSerialNumber;
    }

    public updateOldPending(state) {
        if (state.pendingModification &&
            state.pendingModification.walletOperation &&
            !state.pendingModification.walletOperation.actions) {
            const walletOperation = state.pendingModification.walletOperation;
            state.pendingModification.walletOperation.actions = [
                { action: "debit", attribute: "balance", amount: walletOperation.bet },
                { action: "credit", attribute: "balance", amount: walletOperation.win }
            ];
        }
    }
}
