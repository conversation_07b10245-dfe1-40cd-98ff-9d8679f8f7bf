import { <PERSON><PERSON><PERSON>utR<PERSON><PERSON>, RequestContext, RoundStatistics } from "../context/gamecontext";
import { ContextDataDecoder, ContextDataEncoder } from "./encoder";
import { GameData } from "../auth";
import { GameSettings } from "../tokens";
import { GameSession } from "../gameSession";
import { decodeJpContext } from "./util";
import { GameFlowContextImpl } from "../context/gameContextImpl";
import { StoreOptions } from "../onlinestorage/gameContextCommands";

/**
 * Current fields that is used in redis
 */
export interface GameContextDataV5 {
    gameState?: string;
    metaInf?: string;
    round?: string;
    pending?: string;
    jpPending?: string;
    dataVersion: string;
    sessionId?: string;
    policy: string;
    requestId?: string;
    version: string;
    jpContext?: string;
    historyCounter?: string;
    totalEventId?: string;
    retryAttempts?: string;
    logoutResult?: string;
    logoutId?: string;
    specialState?: number;
}

interface RoundState {
    roundId: string;
    roundEnded: boolean;
    data: RoundStatistics;
}

interface MetaInfState {
    gameData: GameData;
    gameVersion: string;
    settings: GameSettings;
    requestContext: RequestContext;
    createdAt: Date;
    sessionFinished?: boolean;
}

interface GameState {
    game: any;
    updatedAt: Date;
}

export class ContextDataDecoderV5 implements ContextDataDecoder<GameContextDataV5> {
    public static readonly VERSION = "5";

    public async decode(context: GameFlowContextImpl, data: GameContextDataV5) {
        context.version = parseInt(data.version, 10);
        context.persistencePolicy = parseInt(data.policy || "0", 10);
        if (data.round) {
            const roundState: RoundState = JSON.parse(data.round);
            context.roundId = roundState.roundId;
            context.roundEnded = roundState.roundEnded;
            context.round = roundState.data;
        }

        context.roundId = context.roundId || "0";

        context.lastRequestId = parseInt(data.requestId, 10);
        context.gameSerialNumber = parseInt(data.historyCounter, 10);
        context.totalEventId = parseInt(data.totalEventId || data.historyCounter, 10);
        context.retryAttempts = data.retryAttempts && parseInt(data.retryAttempts, 10);

        const gameState: GameState = await this.decodeString(data.gameState);
        if (gameState) {
            context.gameContext = gameState.game;
            context.updatedAt = gameState.updatedAt;
            context.pendingModification = await this.decodeString(data.pending);
            context.jackpotPending = await this.decodeString(data.jpPending);
        }

        context.session = GameSession.create(data.sessionId);

        if (data.metaInf) {
            const metaInf: MetaInfState = JSON.parse(data.metaInf);

            if (metaInf) {
                context.gameData = metaInf.gameData;
                context.gameVersion = metaInf.gameVersion;
                context.settings = metaInf.settings;
                context.requestContext = metaInf.requestContext;
                context.createdAt = metaInf.createdAt;
                if (metaInf.sessionFinished) {
                    context.session.markFinished();
                }
            }
        }

        if (data.logoutResult !== undefined) {
            context.logoutResult = (data.logoutResult || undefined) as MerchantLogoutResult;
        }

        if (data.logoutId !== undefined) {
            context.logoutId = (data.logoutId || undefined) as MerchantLogoutResult;
        }

        if (data.specialState) {
            context.specialState = +data.specialState;
        }

        context.jpContext = decodeJpContext(await this.decodeString(data.jpContext), context.settings);
    }

    private async decodeString(data: string): Promise<any> {
        if (!data) {
            return undefined;
        }
        return JSON.parse(data);
    }
}

export class ContextDataEncoderV5 implements ContextDataEncoder {
    public async encode(context: GameFlowContextImpl, options: StoreOptions = StoreOptions.ALL): Promise<any> {
        const data: GameContextDataV5 = {
            dataVersion: ContextDataDecoderV5.VERSION,
            version: (context.version || 0).toString(),
            policy: context.persistencePolicy.toString(),
            requestId: context.lastRequestId.toString()
        };

        if (options & StoreOptions.ROUND_INFO) {
            data.round = JSON.stringify({
                roundId: context.roundId,
                roundEnded: context.roundEnded,
                data: context.round,
            });
            data.historyCounter = context.gameSerialNumber.toString();
            data.totalEventId = (context.totalEventId || context.gameSerialNumber).toString();
        }

        if (options & StoreOptions.GAME_STATE) {
            data.gameState = await this.encodeObject({
                game: context.gameContext || undefined,
                updatedAt: context.updatedAt,
            });
        }

        if (options & StoreOptions.PENDING_MODIFICATION) {
            data.pending = await this.encodeObject(context.pendingModification);
        }

        if (options & StoreOptions.JACKPOT_PENDING) {
            data.jpPending = await this.encodeObject(context.jackpotPending);
        }

        if (options & StoreOptions.INIT_INFO) {
            data.sessionId = context.session.id;
            data.metaInf = JSON.stringify({
                gameData: context.gameData,
                gameVersion: context.gameVersion,
                settings: context.settings,
                requestContext: context.requestContext,
                createdAt: context.createdAt,
                sessionFinished: context.session.isFinished
            });
        }

        if (options & StoreOptions.JP_CONTEXT) {
            data.jpContext = await this.encodeObject(context.jpContext);
        }

        if ((options & StoreOptions.RETRY_ATTEMPTS) && context.retryAttempts !== undefined) {
            data.retryAttempts = context.retryAttempts.toString();
        }

        if ((options & StoreOptions.LOGOUT_RESULT) && context.logoutResult !== undefined) {
            data.logoutResult = context.logoutResult || "";
            data.logoutId = context.logoutId || "";
        }

        if ((options & StoreOptions.SPECIAL_STATE) && context.specialState !== undefined) {
            data.specialState = context.specialState;
        }

        return data;
    }

    private async encodeObject(obj: any): Promise<string> {
        if (!obj) {
            return "";
        }
        return JSON.stringify(obj);
    }

    public getDataVersion(): string {
        return ContextDataDecoderV5.VERSION;
    }
}
