import { RequestContext, RoundStatistics } from "../context/gamecontext";
import { ContextDataDecoder, ContextDataEncoder } from "./encoder";
import { PlayerInfo } from "../auth";
import { GameSettings, GameTokenData, Limits, Settings } from "../tokens";
import { GameSession } from "../gameSession";
import { decodeGameIdWithFallback, decodeJpContext } from "./util";
import { Balance } from "../wallet";
import { GameMode, JurisdictionSettings } from "@skywind-group/sw-game-core";
import { logging } from "@skywind-group/sw-utils";
import { GameFlowContextImpl } from "../context/gameContextImpl";
import { StoreOptions } from "../onlinestorage/gameContextCommands";

const log = logging.logger("skywind:slot-engine:version3decoder");

/**
 * Current fields that is used in redis
 */
export interface GameContextDataV3 {
    gameState?: string;
    metaInf?: string;
    round?: string;
    pending?: string;
    jpPending?: string;
    dataVersion: string;
    sessionId?: string;
    policy: string;
    requestId?: string;
    version: string;
    jpContext?: string;
    historyCounter?: string;
}

interface RoundState {
    roundId: string;
    roundEnded: boolean;
    data: RoundStatistics;
}

export interface StartGameDataV3 {
    balance?: Balance;
    player?: PlayerInfo;
    limits: Limits;
    settings?: Settings;
    jrsdSettings?: JurisdictionSettings;
    gameMode?: GameMode;  // real, fun
    playedFromCountry?: string; // country that should be resolved from player's ip
    operatorCountry?: string;
}

export interface GameDataV3 extends StartGameDataV3 {
    gameTokenData: GameTokenData;
}

interface MetaInfState {
    gameData: GameDataV3;
    gameVersion: string;
    settings: GameSettings;
    requestContext: RequestContext;
    createdAt: Date;
}

interface GameState {
    game: any;
    updatedAt: Date;
}

export class ContextDataDecoderV3 implements ContextDataDecoder<GameContextDataV3> {
    public static readonly VERSION = "3";

    public async decode(context: GameFlowContextImpl, data: GameContextDataV3) {
        context.version = parseInt(data.version, 10);
        context.persistencePolicy = parseInt(data.policy || "0", 10);
        if (data.round) {
            const roundState: RoundState = JSON.parse(data.round);
            context.roundId = roundState.roundId;
            context.roundEnded = roundState.roundEnded;
            context.round = roundState.data;
        }

        if (!context.roundId) {
            log.warn("Round id is missing in the decoded context data: %s", data);
        }

        context.lastRequestId = parseInt(data.requestId, 10);
        context.gameSerialNumber = parseInt(data.historyCounter, 10);
        context.totalEventId = context.gameSerialNumber;

        const gameState: GameState = await this.decodeString(data.gameState);
        if (gameState) {
            context.gameContext = gameState.game;
            context.updatedAt = gameState.updatedAt;
            context.pendingModification = await this.decodeString(data.pending);
            context.jackpotPending = await this.decodeString(data.jpPending);
        }

        context.session = GameSession.create(data.sessionId);

        if (data.metaInf) {
            const metaInf: MetaInfState = JSON.parse(data.metaInf);

            if (metaInf) {
                context.gameData = metaInf.gameData;
                context.gameData.gameTokenData.playmode = metaInf.gameData.gameMode || "real";
                context.gameData.gameId = decodeGameIdWithFallback(context.id.gameCode);
                context.gameVersion = metaInf.gameVersion;
                context.settings = metaInf.settings;
                context.requestContext = metaInf.requestContext;
                context.createdAt = metaInf.createdAt;
            }
        }

        context.jpContext = decodeJpContext(await this.decodeString(data.jpContext), context.settings);
    }

    private async decodeString(data: string): Promise<any> {
        if (!data) {
            return undefined;
        }
        return JSON.parse(data);
    }
}

export class ContextDataEncoderV3 implements ContextDataEncoder {
    public async encode(context: GameFlowContextImpl, options: StoreOptions = StoreOptions.ALL): Promise<any> {
        const data: GameContextDataV3 = {
            dataVersion: ContextDataDecoderV3.VERSION,
            version: (context.version || 0).toString(),
            policy: context.persistencePolicy.toString(),
            requestId: context.lastRequestId.toString()
        };

        if (options & StoreOptions.ROUND_INFO) {
            data.round = JSON.stringify({
                roundId: context.roundId,
                roundEnded: context.roundEnded,
                data: context.round,
            });
            data.historyCounter = context.gameSerialNumber.toString();
        }

        if (options & StoreOptions.GAME_STATE) {
            data.gameState = await this.encodeObject({
                game: context.gameContext,
                updatedAt: context.updatedAt,
            });
        }

        if (options & StoreOptions.PENDING_MODIFICATION) {
            data.pending = await this.encodeObject(context.pendingModification);
        }

        if (options & StoreOptions.JACKPOT_PENDING) {
            data.jpPending = await this.encodeObject(context.jackpotPending);
        }

        if (options & StoreOptions.INIT_INFO) {
            data.sessionId = context.session.id;
            data.metaInf = JSON.stringify({
                gameData: context.gameData,
                gameVersion: context.gameVersion,
                settings: context.settings,
                requestContext: context.requestContext,
                createdAt: context.createdAt,
            });
        }

        if (options & StoreOptions.JP_CONTEXT) {
            data.jpContext = await this.encodeObject(context.jpContext);
        }

        return data;
    }

    private async encodeObject(obj: any): Promise<string> {
        if (!obj) {
            return "";
        }
        return JSON.stringify(obj);
    }

    public getDataVersion(): string {
        return ContextDataDecoderV3.VERSION;
    }
}
