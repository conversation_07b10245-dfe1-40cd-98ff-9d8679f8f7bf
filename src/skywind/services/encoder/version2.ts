import { GameFlowContextImpl } from "../context/gameContextImpl";
import { GameSession } from "../gameSession";
import { ContextDataDecoder } from "./encoder";
import { GameContextPersistencePolicy } from "@skywind-group/sw-game-core";
import { decodeGameIdWithFallback, decodeJpContext } from "./util";

/**
 * Current fields that is used in redis
 */
export interface GameContextDataV2 {
    state: string;
    /**
     * The version of format, is used to find appropriate decoder
     */
    dataVersion: string;
    currentSessionId: string;
    requestId: string;
    version: string;
    historyCounter: string;
}

/**
 * Current version decoder
 */
export class ContextDataEncoderV2 implements ContextDataDecoder<GameContextDataV2> {
    public static readonly VERSION = "2";

    public async decode(context: GameFlowContextImpl, data: GameContextDataV2) {
        context.session = GameSession.create(data.currentSessionId);
        context.lastRequestId = data.requestId === undefined ? 0 : parseInt(data.requestId, 10);
        context.version = data.version === undefined ? 0 : parseInt(data.version, 10);
        const state = await JSON.parse(data.state);

        context.settings = state.settings;
        context.gameData = state.gameData;
        context.gameData.gameId = decodeGameIdWithFallback(context.id.gameCode);
        context.gameVersion = state.gameVersion;
        context.persistencePolicy = state.persistencePolicy ?
                                    state.persistencePolicy :
                                    GameContextPersistencePolicy.NORMAL;
        context.pendingModification = state.pendingModification;
        context.jackpotPending = state.pendingModification ? state.pendingModification.jackpotPending : undefined;
        context.gameContext = state.gameContext;
        context.roundId = state.roundId;
        context.roundEnded = state.roundEnded;
        context.jpContext = decodeJpContext(state.jpContext, context.settings);
        context.round = state.round;
        context.requestContext = state.requestContext;
        context.createdAt = state.createdAt;
        context.updatedAt = state.updatedAt;
        context.gameSerialNumber = !data.historyCounter ? 0 : parseInt(data.historyCounter, 10);
        context.totalEventId = context.gameSerialNumber;
    }
}
