import { BNSGameData } from "./auth";
import { GameFlowContext } from "./context/gamecontext";
import { addMinutes } from "../utils/common";
import config from "../config";
import { GameContextPersistencePolicy } from "@skywind-group/sw-game-core";

export class ContextFinalizationTimeCalculator {

    public static getExpiredAt(ctx: GameFlowContext): number | null {
        const finalizationWasBroken = ctx?.pendingModification?.walletOperation?.expireAtRevisionRequiredOnFail;
        const roundExpiration = ctx?.settings?.roundExpireAt;
        const bnsMode = ctx.gameData.gameTokenData.playmode === "bns";
        /*
        roundExpiration check is to filter PS and cases when finalization started from api call
        bns mode is for case when bns finalization failed
        */
        const {
            factor = config.expireJob.retryFactor,
            maxRetries = config.expireJob.maxPaymentOfflineRetryAttempts,
            initialRetryTimeout = config.expireJob.postponeInterval
        } = ctx.settings?.finalizationRetryPolicy || {};
        if ((bnsMode || roundExpiration) && finalizationWasBroken) {
            const pendingPaymentRetryCounter = ctx?.pendingModification?.walletOperation?.retry || 0;
            // stop infinite payment retries
            if (pendingPaymentRetryCounter >= maxRetries) {
                return null;
            }
            const minutes = Math.pow(factor, pendingPaymentRetryCounter) * initialRetryTimeout;
            return addMinutes(Date.now(), minutes);
        }
        if (bnsMode) {
            const currentData: BNSGameData = ctx.gameData as BNSGameData;
            return currentData.bnsPromotion.expireAt;
        }
        if (roundExpiration) {
            if (!ctx.broken && ctx.persistencePolicy === GameContextPersistencePolicy.LONG_TERM
                && config.tempListOfGameForFinalization.includes(ctx.gameData.gameId)) {
                return null;
            }
            const lastPaymentDate = ctx?.round?.lastSuccessfulPaymentDate || Date.now();
            return addMinutes(lastPaymentDate, roundExpiration);
        }
    }

    public static getNextExpiredAt(ctx: GameFlowContext): number | null {
        const {
            factor = config.expireJob.retryFactor,
            maxRetries = config.expireJob.maxPaymentOfflineRetryAttempts,
            initialRetryTimeout = config.expireJob.postponeInterval
        } = ctx?.settings?.finalizationRetryPolicy || {};
        const currentRetries = ctx?.pendingModification?.walletOperation?.retry || 0;
        if (currentRetries > maxRetries) {
            return null;
        }
        const minutes = Math.pow(factor, currentRetries) * initialRetryTimeout;
        return addMinutes(Date.now(), minutes);
    }
}
