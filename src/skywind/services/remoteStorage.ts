import {
    RemoteStorageConnectionError,
    RemoteStorageServiceError,
    RemoteStorageServiceTranssientError
} from "../errors";
import { keepalive, logging, retry } from "@skywind-group/sw-utils";
import * as request from "superagent";
import config from "../config";
import { retryCondition } from "../utils/common";
import HttpsAgent = keepalive.HttpsAgent;

const log = logging.logger("sw-context-service:storage");

const agent = keepalive.createAgent(config.managementAPI.keepAlive, config.offlineStorage.url);

export class RemoteStorage {

    constructor(private readonly baseURL: string) {
    }

    public post<T>(url: string, req: any): Promise<T> {
        return retry(config.retries, () => {
            return this.processResponse(
                request
                    .post(`${this.baseURL}${url}`)
                    .set({ "Content-Type": "application/json" })
                    .agent(agent as HttpsAgent)
                    .send(req));
        }, retryCondition);
    }

    public get<T>(url: string, query?: any): Promise<T> {
        return retry(config.retries, () => {
            return this.processResponse(
                request
                    .get(`${this.baseURL}${url}`)
                    .agent(agent as HttpsAgent)
                    .query(query)
                    .set({ "Content-Type": "application/json" }));
        }, retryCondition);
    }

    public put(url: string, req: any): Promise<void> {
        return retry(config.retries, () => {
            return this.processResponse(
                request
                    .put(`${this.baseURL}${url}`)
                    .set({ "Content-Type": "application/json" })
                    .agent(agent as HttpsAgent)
                    .send(req));
        }, retryCondition);
    }

    public delete(url: string, req: any): Promise<void> {
        return retry(config.retries, () => {
            return this.processResponse(
                request
                    .delete(`${this.baseURL}${url}`)
                    .set({ "Content-Type": "application/json" })
                    .agent(agent as HttpsAgent)
                    .send(req));
        }, retryCondition);
    }

    protected processResponse(result: request.SuperAgentRequest): Promise<any> {
        return result.then((res) => this.process(res)).catch((err) => this.process(err.response, err));
    }

    private process(response, error?: Error): Promise<any> {
        if (error && !response) {
            log.warn(error, "Error sending request");
            return Promise.reject(new RemoteStorageConnectionError(error));
        } else if (response.status !== 200 && response.statusCode !== 201) {
            log.warn({ responseCode: response.status, responseBody: response.body }, "Error response");
            if (response.status >= 400 && response.status < 500) {
                return Promise.reject(new RemoteStorageServiceError(response.status, response.body));
            } else {
                return Promise.reject(new RemoteStorageServiceTranssientError(response.status, response.body));
            }
        } else {
            return response.body;
        }
    }
}
