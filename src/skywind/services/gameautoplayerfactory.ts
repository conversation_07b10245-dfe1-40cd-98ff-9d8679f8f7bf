import { EngineGameFlow } from "./gameflow";
import { AutoPlayer, createAutoPlayer as create } from "@skywind-group/sw-game-autoplayer";
import { LoadedGameResult, loadGameModule } from "./game/game";
import { GameFlowContext } from "./context/gamecontext";
import { logging } from "@skywind-group/sw-utils";
import { ForbiddenRequest } from "../errors";
import { ExtensionPosition } from "./game/gameProxy";
import { instantJpExtension } from "./game/globalExtensions";
import { InstantJpGame } from "@skywind-group/sw-game-instant-jp";
import { ModuleName } from "./game/gamemodule";

const log = logging.logger("sw-slot-engine:game-autoplayer");

async function createAutoPlayerForSW(flow: EngineGameFlow, ctx: GameFlowContext): Promise<AutoPlayer> {
    const gameModule: any = await loadGameModule(ctx.gameData.gameId);
    const gameContext = await flow.gameContext();
    const instantJackpot = flow?.jackpotContext?.instantJackpotAwarded || ctx?.round?.instantJackpot;
    const autoPlayer = create(gameModule, flow.game, { ...gameContext, instantJackpot }, log);
    if (!autoPlayer) {
        log.error(`Game doesn't support autoplay, but marked as supported ${ctx.gameData.gameId}`);
        return Promise.reject(new ForbiddenRequest());
    }
    return autoPlayer;
}

async function createAutoPlayerForITG(flow: EngineGameFlow, ctx: GameFlowContext): Promise<AutoPlayer> {
    const game: any = new InstantJpGame();
    const moduleName: ModuleName = {
        nameWithVersion: "@skywind-group/sw-game-instant-jp@0.0.22",
        id: "sw-game-instant-jp",
        name: "@skywind-group/sw-game-instant-jp",
        version: "0.0.22"
    };
    const gameModule: any = { game, moduleName };
    const loadedGame: LoadedGameResult = new LoadedGameResult(game, moduleName);
    loadedGame.game.addGlobalExtensionModule(ExtensionPosition.BEFORE_ALL, instantJpExtension.get());

    const instantJackpot = flow?.jackpotContext?.instantJackpotAwarded || ctx?.round?.instantJackpot;
    let gameContext: any = await flow.gameContext();
    gameContext = { ...gameContext,
        currentScene: gameContext.currentScene || "jackpot",
        previousProcessSceneResult: gameContext.previousProcessSceneResult || {},
        scenesState: gameContext.scenesState || {},
        instantJackpot,
        stake: {}
    };

    const autoPlayer = create(gameModule, loadedGame.game, gameContext, log);
    if (!autoPlayer) {
        log.error("Can't autoplay ITG round with L7 feature");
        return Promise.reject(new ForbiddenRequest());
    }
    return autoPlayer;
}

export type CreateAutoPlayerResponse = (flow: EngineGameFlow, ctx: GameFlowContext) => Promise<AutoPlayer>;

export function createAutoPlayer(isItgGame: boolean = false): CreateAutoPlayerResponse {
    return isItgGame ? createAutoPlayerForITG : createAutoPlayerForSW;
}
