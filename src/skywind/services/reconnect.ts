import NotificationService from "./notification";
import config from "../config";
import { RECONNECT_NOTIFICATION } from "../utils/common";
import { logging } from "@skywind-group/sw-utils";
import { ServerProbs } from "./probs/serverProbs";
import { Server } from "socket.io";
import * as IoServer from "socket.io-v2";

const log = logging.logger("sw-slot-engine:reconnect");

export interface ReconnectNotification {
    cluster?: string;
}

export async function subscribeReconnect(io: Server | IoServer.Server) {
    ServerProbs.subscribeReady((value) => {
        if (!value) {
            broadcast(io, { ts: Date.now });
        }
    });

    return NotificationService.subscribe(RECONNECT_NOTIFICATION, async (event: string, data: ReconnectNotification) => {
        log.info("Get notification %j", data);
        const ts = Date.now();
        if (!data.cluster || config.clusterName === data.cluster) {
            log.info("Broadcast 'reinit' for %s", data.cluster || "all");
            broadcast(io, { ts, data });
        }
    });
}

export async function notifyReconnect(cluster?: string) {
    return NotificationService.notify(RECONNECT_NOTIFICATION, { cluster });
}

function broadcast(io: Server | IoServer.Server, data: any) {
    const broadcastNamespace = (name: string) => {
        const namespace = (io as any)?.nsps?.[name] || (io as any)?._nsps?.[name];
        if (namespace) {
            namespace.emit("reinit-claim", data);
        }
    };
    broadcastNamespace(config.gameEntryPoint);
    broadcastNamespace("/");
}
