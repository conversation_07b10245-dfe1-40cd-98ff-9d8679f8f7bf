import { measures } from "@skywind-group/sw-utils";
import { RequestReferrer } from "./context/gamecontext";
import { ManagementAPISupport } from "./managementapihelper";
import config from "../config";
import measure = measures.measure;
import { DefaultParams } from "fastify";
import { URLSearchParams } from "url";

export interface WithLaunchUrl {
    launcherToken: string;
}

export interface GameUrl {
    url: string;
}

export interface GameLauncher {
    launch(data: RequestReferrer): Promise<string>;
}

class GameLauncherImpl extends ManagementAPISupport implements GameLauncher {
    private static LAUNCH_GAME_URL = "/v1/game/launch";
    private queryParamsToSkip: string[] = config.forbidOverridingGameUrlQuery.split(",");

    constructor(url: string, private queryParams: DefaultParams) {
        super(url);
    }

    @measure({ name: "GameLauncherService.launch", isAsync: true })
    public async launch(data: RequestReferrer & WithLaunchUrl): Promise<string> {
        const response = await this.post<GameUrl>(
            GameLauncherImpl.LAUNCH_GAME_URL,
            {
                launcherToken: data.launcherToken,
                webSiteDomain: data.referrer || undefined
            });

        return this.decorateGameUrlWithQueryParams(response.url);
    }

    private decorateGameUrlWithQueryParams(url: string) {
        const [domain, existentParams] = url.split("?");
        const params = new URLSearchParams(existentParams);

        for (const key in this.queryParams) {
            if (this.queryParamsToSkip.includes(key)) {
                continue;
            }
            params.set(key, this.queryParams[key]);
        }

        return domain + "?" + params.toString();
    }
}

export function getGameLauncherService(queryParams: DefaultParams = {}): GameLauncherImpl {
    return new GameLauncherImpl(config.managementAPIGameAuth, queryParams);
}
