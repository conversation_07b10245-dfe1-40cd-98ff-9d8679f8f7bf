import {
    BonusCoins<PERSON><PERSON><PERSON>,
    GameHistory,
    GameWalletAction,
    JackpotWinResult,
    PaymentInfo
} from "@skywind-group/sw-game-core";
import { EngineGameFlow, TransferInfo } from "./gameflow";
import {
    DeferredPaymentOperation,
    FinalizeGameOperation,
    GameJackpotWinAction,
    JackpotDetails,
    JackpotDetailsHolder,
    JackpotWinSplitPaymentOperation,
    OperationType,
    PaymentOperation,
    PlayerWalletManager,
    RedeemBNSOperation,
    RefundBetOperation,
    SplitPaymentOperation,
    TransferOperation,
    WalletOperation
} from "./wallet";
import { deepClone } from "../utils/cloner";
import { JackpotContext, JackpotOperationResult, JackpotTrxResult } from "./jpn/jackpot";
import { JackpotUtil } from "./jpn/jackpotUtil";
import { Currencies, Currency, CurrencyNotFoundError } from "@skywind-group/sw-currency-exchange";
import { calculateJPStatistic, JackpotStatistic, JackpotTypeStatistic } from "./context/jpStatistics";
import { DeferredPayment } from "@skywind-group/sw-deferred-payment";
import { getCurrencyExchange } from "./currencyexchange";
import { FinalizeRequestData } from "./gamerecovery";
import { RecoveryType } from "./offlinestorage/offlineCommands";
import { GameWalletActionType } from "@skywind-group/sw-game-core/src/definition";

const TRANSFER_OUT = "transfer-out";

export function createWalletOperationFactory(flow: EngineGameFlow<any>): WalletOperationFactory {
    if (flow.isSplitPayment()) {
        return new WalletSplitOperationFactory(flow, flow.walletManager);
    } else {
        return new WalletCombinedOperationFactory(flow, flow.walletManager);
    }
}

export abstract class WalletOperationFactory {
    constructor(protected readonly flow: EngineGameFlow<any>,
                protected readonly walletManager: PlayerWalletManager) {
    }

    public abstract createPaymentOperation(gamePayment: PaymentInfo, history: GameHistory): Promise<WalletOperation>;

    protected async preparePaymentOperation<P extends WalletOperation>(gamePayment: PaymentInfo,
                                                                       history: GameHistory): Promise<P> {
        const gameFlowInfo = this.flow.info();

        const walletOperation: any = deepClone(gamePayment);
        walletOperation.bet = 0;
        walletOperation.win = 0;
        const currency: string = gameFlowInfo.currency;
        walletOperation.currency = currency;
        walletOperation.transactionId = await this.walletManager.generateTransactionId();
        walletOperation.deviceId = gameFlowInfo.deviceId;
        walletOperation.roundId = gameFlowInfo.roundId;
        walletOperation.roundPID = gameFlowInfo.roundPID;
        walletOperation.gameSessionId = this.flow.flowContext.session.sessionId;
        walletOperation.roundEnded = (history && "roundEnded" in history) ? history.roundEnded : false;
        walletOperation.actions = walletOperation.actions || [];

        // Payment with actions - slot games
        const paymentInfoHasActions = walletOperation.actions.length;
        walletOperation.actions.forEach((walletAction: GameWalletAction) => {
            if (walletAction.attribute === "balance") {
                walletAction.amount = this.flow.fromGameAmount(walletAction.amount);
                walletAction.amount = this.normalizeCurrencyAmount(currency, walletAction.amount);
                if (walletAction.action === "debit") {
                    walletOperation.bet += walletAction.amount;
                } else if (walletAction.action === "credit") {
                    walletOperation.win += walletAction.amount;
                }
            }
        });

        // Payment without actions - arcades, live games
        if (gamePayment.bet !== undefined || gamePayment.win !== undefined) {
            walletOperation.bet += gamePayment.bet === undefined ? 0 : this.flow.fromGameAmount(gamePayment.bet);
            walletOperation.win += gamePayment.win === undefined ? 0 : this.flow.fromGameAmount(gamePayment.win);

            if (paymentInfoHasActions) {
                // SRT games with bad deployment configuration SWS-25746
                walletOperation.actions = walletOperation.actions.filter(a => !(a.attribute === "balance" &&
                    ["debit", "credit"].includes(a.action)));
            }
            walletOperation.actions.push(
                {
                    action: "debit",
                    attribute: "balance",
                    amount: this.normalizeCurrencyAmount(currency, walletOperation.bet),
                    changeType: "bet"
                },
                {
                    action: "credit",
                    attribute: "balance",
                    amount: this.normalizeCurrencyAmount(currency, walletOperation.win),
                    changeType: "win"
                }
            );
        }

        walletOperation.bet = this.normalizeCurrencyAmount(currency, walletOperation.bet);
        walletOperation.win = this.normalizeCurrencyAmount(currency, walletOperation.win);

        return walletOperation as P;
    }

    public async createTransferOperation(transferInfo: TransferInfo, roundEnded: boolean): Promise<TransferOperation> {
        const gameFlowInfo = this.flow.info();
        const walletOperation: TransferOperation = deepClone(transferInfo) as TransferOperation;

        walletOperation.transactionId = await this.walletManager.generateTransactionId();
        walletOperation.deviceId = gameFlowInfo.deviceId;
        walletOperation.roundId = gameFlowInfo.roundId;
        walletOperation.roundPID = gameFlowInfo.roundPID;
        walletOperation.gameSessionId = this.flow.flowContext.session.sessionId;
        walletOperation.roundEnded = roundEnded;

        const currency: string = gameFlowInfo.currency;
        const amount: number = this.flow.fromGameAmount(transferInfo.amount);
        walletOperation.amount = this.normalizeCurrencyAmount(currency, amount);
        walletOperation.currency = currency;

        if (transferInfo.operation === TRANSFER_OUT) {
            walletOperation.totalBet = this.flow?.flowContext?.round?.totalBet || 0;
            walletOperation.totalWin = this.flow?.flowContext?.round?.totalWin || 0;
            walletOperation.betsCount = this.flow?.flowContext?.round?.betsCount || 0;

            if (roundEnded) {
                Object.assign(walletOperation,
                    this.getJackpotDetails(undefined, undefined));
            }
        }

        return walletOperation;
    }

    public async createRedeemBNSOperation(redeemInfo: BonusCoinsBalance): Promise<RedeemBNSOperation> {
        const gameFlowInfo = this.flow.info();
        return {
            operation: "redeem-bns",
            transactionId: await this.walletManager.generateTransactionId(),
            deviceId: gameFlowInfo.deviceId,
            roundId: gameFlowInfo.roundId,
            roundPID: gameFlowInfo.roundPID,
            gameSessionId: this.flow.flowContext.session.sessionId,
            amount: redeemInfo.redeemBalance,
            currency: this.flow.flowContext.gameData.gameTokenData.currency,
            promoId: redeemInfo.promoId,
            externalId: redeemInfo.externalId,
            roundEnded: true,
        };
    }

    public async createDeferredPaymentOperation(deferredPayment: DeferredPayment): Promise<DeferredPaymentOperation> {
        const gameFlowInfo = this.flow.info();
        const amount = this.getDeferredPaymentAmount(deferredPayment);
        return {
            operation: "deferred-payment",
            transactionId: await this.walletManager.generateTransactionId(),
            deviceId: gameFlowInfo.deviceId,
            roundId: gameFlowInfo.roundId,
            roundPID: gameFlowInfo.roundPID,
            gameSessionId: this.flow.flowContext.session.sessionId,
            deferredPayment: deferredPayment,
            amount,
            currency: gameFlowInfo.currency,
            actions: [
                {
                    action: "credit",
                    attribute: "balance",
                    changeType: "deferred-payment",
                    amount,
                }
            ],
            roundEnded: true
        };
    }

    private getDeferredPaymentAmount(deferredPayment: DeferredPayment) {
        const playerCurrency = this.flow.info().currency;
        if (playerCurrency === deferredPayment.currencyCode) {
            return deferredPayment.amount;
        } else {
            return getCurrencyExchange().exchange(deferredPayment.amount, deferredPayment.currencyCode, playerCurrency);
        }
    }

    public async createFinalizeGameOperation(request: FinalizeRequestData,
                                             recoveryType: RecoveryType): Promise<FinalizeGameOperation> {
        const gameFlowInfo = this.flow.info();

        return {
            operation: "finalize-game",
            transactionId: await this.walletManager.generateTransactionId(),
            deviceId: gameFlowInfo.deviceId,
            roundId: gameFlowInfo.roundId,
            roundPID: gameFlowInfo.roundPID,
            gameSessionId: this.flow.flowContext.session.sessionId,
            currency: gameFlowInfo.currency,
            roundEnded: true,
            roundStatistics: this.flow.flowContext.round,
            expireAtRevisionRequiredOnFail: true,
            finalizationType: request.brandFinalizationType,
            recoveryType: recoveryType,
            closeInSWWalletOnly: request.closeInSWWalletOnly
        };
    }

    public abstract createRefundBetOperation(
        paymentOperation: WalletOperation,
        shouldAddBetAmountOnFreeBetRollback?: boolean
    ): RefundBetOperation & PaymentInfo;

    public abstract createJackpotWinPaymentOperation(jackpot: JackpotTrxResult,
                                                     jackpotContext: JackpotContext,
                                                     prevJpnResult?: JackpotOperationResult): Promise<WalletOperation>;

    public getJackpotDetails(jackpotResult: JackpotOperationResult,
                             jackpotContext: JackpotContext): JackpotDetailsHolder {
        const currentContribution = jackpotResult?.totalJpContribution || 0;
        const currentJpWin = jackpotResult?.totalJpWin || 0;

        const contributionPrecision = this.flow.flowContext.settings.contributionPrecision;
        const round = this.flow.flowContext.round;

        const currency = Currencies.get(this.flow.info().currency);
        const totalJpWin = JackpotUtil.safeAddWithPrecision(currency.exponent,
            (round?.totalJpWin || 0), currentJpWin);

        return {
            totalJpContribution: JackpotUtil.safeAddWithPrecision(contributionPrecision,
                round?.totalJpContribution || 0, currentContribution),
            totalJpWin: totalJpWin,
            jackpotDetails: this.createJackpotDetails(
                calculateJPStatistic(jackpotResult, round?.jpStatistic || {}, jackpotContext, true),
                contributionPrecision,
                JackpotUtil.getJackpotTypeStatistic(jackpotContext))
        };
    }

    public static getWinAmount(payment: PaymentInfo): number {
        return WalletOperationFactory.getBalanceChange(payment, payment.win, "credit");
    }

    public static getBetAmount(payment: PaymentInfo): number {
        return WalletOperationFactory.getBalanceChange(payment, payment.bet, "debit");
    }

    public static getBalanceChange(payment: PaymentInfo, amount: number, type: string): number {
        let result: number = amount || 0;
        if (payment.actions) {
            for (const a of payment.actions) {
                if (a.action === type && a.attribute === "balance") {
                    result += a.amount;
                }
            }
        }

        return result;
    }

    private createJackpotDetails(jpStatistic: JackpotStatistic,
                                 contributionPrecision: number,
                                 jackpotTypes: JackpotTypeStatistic): JackpotDetails {
        return {
            jackpots: jpStatistic,
            contributionPrecision: contributionPrecision,
            jackpotTypes
        };
    }

    private normalizeCurrencyAmount(currencyCode: string, amount: number): number {
        let currency: Currency;
        try {
            currency = Currencies.get(currencyCode);
        } catch (err) {
            throw new CurrencyNotFoundError(currencyCode);
        }
        const minorUnits = currency.toMinorUnits(amount);
        return currency.toMajorUnits(minorUnits);
    }

    public static isSplitPaymentOperation(operation: WalletOperation): operation is SplitPaymentOperation {
        return operation.operation === "split-payment";
    }
}

class WalletSplitOperationFactory extends WalletOperationFactory {

    public async createPaymentOperation(gamePayment: PaymentInfo,
                                        history: GameHistory): Promise<SplitPaymentOperation> {
        const walletOperation: SplitPaymentOperation = await this.preparePaymentOperation<SplitPaymentOperation>(
            gamePayment,
            history);
        walletOperation.operation = "split-payment";
        walletOperation.debitActions = walletOperation.debitActions || [];
        walletOperation.creditActions = walletOperation.creditActions || [];
        walletOperation.actions.forEach((walletAction: GameWalletAction) => {
            if (walletAction.action === "debit") {
                walletOperation.debitActions.push(walletAction);
            } else if (walletAction.action === "credit") {
                walletOperation.creditActions.push(walletAction);
            } else { // Freebets comes here
                walletOperation.debitActions.push(walletAction);
            }
        });
        walletOperation.actions = undefined;
        return walletOperation;
    }

    public async createJackpotWinPaymentOperation(jackpot: JackpotTrxResult,
                                                  jackpotContext: JackpotContext,
                                                  prevJpnResult?: JackpotOperationResult): Promise<WalletOperation> {
        const gameFlowInfo = this.flow.info();
        let win: number = 0;
        const actions: GameJackpotWinAction[] = jackpot.result.filter(jp => jp.event === "win")
            .map((jp: JackpotWinResult) => {
                win += jp.amount;
                const jpSettings = jackpotContext.jackpotsInfo.find((info) => info.id === jp.jackpotId);
                return {
                    action: "credit",
                    attribute: "balance",
                    amount: jp.amount,
                    changeType: (jpSettings && jpSettings.winPaymentType) || "jackpot_win",
                    jackpotId: jp.jackpotId,
                    pool: jp.pool
                } as GameJackpotWinAction;
            });
        const roundEnded = this.flow?.pendingModification?.history?.roundEnded;
        const walletOperation: JackpotWinSplitPaymentOperation = {
            operation: "payment",
            currency: gameFlowInfo.currency,
            transactionId: await this.walletManager.generateTransactionId(),
            deviceId: gameFlowInfo.deviceId,
            roundId: gameFlowInfo.roundId,
            roundPID: gameFlowInfo.roundPID,
            gameSessionId: this.flow.flowContext.session.sessionId,
            roundEnded: roundEnded,
            extTransactionId: jackpot.transactionId,
            actions,
            win,
            ...this.getJackpotDetails(prevJpnResult, jackpotContext)
        };
        if (roundEnded) {
            Object.assign(walletOperation, this.getJackpotDetails(prevJpnResult, jackpotContext));
        }

        return walletOperation;
    }

    public createRefundBetOperation(
        paymentOperation: SplitPaymentOperation,
        shouldAddBetAmountOnFreeBetRollback?: boolean
    ): RefundBetOperation & PaymentInfo {
        if (!paymentOperation) {
            return undefined;
        }
        const copy = deepClone<SplitPaymentOperation>(paymentOperation);
        copy.actions = copy.debitActions;

        copy.creditActions = undefined;
        copy.debitActions = undefined;

        copy.ts = paymentOperation.ts;

        const walletOperation = copy as WalletOperation;
        walletOperation.operation = "refund-bet";

        if (OperationType.isTransferIn(paymentOperation)) {
            walletOperation.bet = (paymentOperation as TransferOperation).amount;
        } else {
            walletOperation.bet = paymentOperation.bet;
            // adjust the "bet" field in case of free bet refund (SWS-47042)
            if (shouldAddBetAmountOnFreeBetRollback && !walletOperation.bet && walletOperation.freeBetCoin) {
                let totalBet = 0;
                for (const item of copy.actions) {
                    if (item.action === "freebet" as GameWalletActionType) {
                        totalBet += item.amount;
                    }
                }
                walletOperation.bet = totalBet;
            }
        }

        walletOperation.totalWin = undefined;
        walletOperation.totalBet = undefined;
        walletOperation.win = undefined;
        walletOperation.jackpotDetails = undefined;
        walletOperation.roundEnded = false;

        return walletOperation as RefundBetOperation;
    }
}

class WalletCombinedOperationFactory extends WalletOperationFactory {

    public async createPaymentOperation(gamePayment: PaymentInfo,
                                        history: GameHistory): Promise<PaymentOperation> {
        const walletOperation: PaymentOperation = await this.preparePaymentOperation<PaymentOperation>(gamePayment,
            history);
        walletOperation.operation = "payment";
        return walletOperation;
    }

    public async createJackpotWinPaymentOperation(jackpot: JackpotTrxResult,
                                                  jackpotContext: JackpotContext,
                                                  prevJpnResult?: JackpotOperationResult): Promise<WalletOperation> {
        const gameFlowInfo = this.flow.info();
        let win: number = 0;
        const actions: GameJackpotWinAction[] = jackpot.result.filter(jp => jp.event === "win").map(jp => {
            win += jp.amount;
            const jpSettings = jackpotContext.jackpotsInfo.find((info) => info.id === jp.jackpotId);
            return {
                action: "credit",
                attribute: "balance",
                amount: jp.amount,
                changeType: (jpSettings && jpSettings.winPaymentType) || "jackpot_win",
                jackpotId: jp.jackpotId,
                pool: jp.pool
            } as GameJackpotWinAction;
        });
        const walletOperation: PaymentOperation = {
            operation: "payment",
            currency: gameFlowInfo.currency,
            transactionId: await this.walletManager.generateTransactionId(),
            deviceId: gameFlowInfo.deviceId,
            roundId: gameFlowInfo.roundId,
            roundPID: gameFlowInfo.roundPID,
            gameSessionId: this.flow.flowContext.session.sessionId,
            roundEnded: true,
            extTransactionId: jackpot.transactionId,
            actions,
            win
        };
        if (prevJpnResult) {
            Object.assign(walletOperation, this.getJackpotDetails(prevJpnResult, jackpotContext));
        }

        return walletOperation;
    }

    public createRefundBetOperation(paymentOperation: WalletOperation): RefundBetOperation & PaymentInfo {
        if (!paymentOperation) {
            return undefined;
        }
        const result: WalletOperation = deepClone<WalletOperation>(paymentOperation);

        result.ts = paymentOperation.ts;
        if (OperationType.isTransferIn(paymentOperation)) {
            result.bet = paymentOperation.amount;
        } else {
            result.bet = paymentOperation.bet;
        }

        result.operation = "refund-bet";

        result.totalWin = undefined;
        result.totalBet = undefined;
        result.win = undefined;
        result.jackpotDetails = undefined;
        result.roundEnded = false;

        return result as RefundBetOperation;
    }
}
