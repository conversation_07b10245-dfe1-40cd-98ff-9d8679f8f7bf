import * as jwt from "jsonwebtoken";
import config from "../config";
import { DeferredContributionResult, JackpotShortInfo } from "@skywind-group/sw-jpn-core";
import { GameMode } from "@skywind-group/sw-game-core";
import { RtpConfigurator } from "@skywind-group/sw-rtp-ext";
import * as Errors from "../errors";

interface TokenConfig {
    secret: string;
    algorithm: string;
    issuer: string;
    expiresIn?: number;
}

export interface StartGameTokenData {
    /**
     * token value
     */
    token: string;
    playerCode: string;
    gameCode: string;
    providerCode: string;
    providerGameCode: string;
    brandId: number;
    module: string;
    settings?: Settings;
    playmode?: GameMode;
    merchantSessionId?: string;
    merchantSessionStateful?: string;
    currency?: string;
    test?: boolean;
    envId?: string;
    isNewSession?: boolean;
    deployment?: string;
    referrer?: string;
    gameGroup?: string;
    lobbySessionId?: string;
    operatorCountry?: string;
}

export interface ReplayTokenData {
    roundId: string;
    brandId: string;
}

export interface AAMSData {
    regulatoryData?: { // regulator data of Italian jurisdiction
        aamsSessionCode: string;
        participationStartDate?: string;
        ropCode?: string; // id assigned by regulator on buy-in
    };
}

export interface GameTokenData extends AAMSData {
    // Field that we add for our convenience
    token?: string; // token value

    gameCode: string;
    playerCode: string;
    brandId: number;
    currency: string;
    providerCode?: string;
    transferEnabled?: boolean;
    walletPerGame?: boolean;
    test?: boolean;
    merchantSessionId?: string;
    playmode: GameMode;
    platform?: string;
    defaultBalance?: number;
    nickname?: string;
    disablePlayerPhantomFeatures?: boolean; // Phantom uses this flag from kafka event to disable features for a player
}

export interface Settings {
    transferEnabled?: boolean;
    keepAliveSec?: number;
    jackpotId?: string;
    jackpots?: JackpotShortInfo[];
    contributionPrecision?: number; // Value precision: Number of digits after dot.
    splitPayment?: boolean; // Indicates that games transactions bet + win must be split on 2 phases bet and win.
    maxPaymentRetryAttempts?: number;
    minPaymentRetryTimeout?: number;
    rtpConfigurator?: RtpConfigurator;
    roundExpireAt?: number; // Time when round context should be expired (in hours)
    finalizationRetryPolicy?: {
        factor?: number;
        maxRetries?: number;
        initialRetryTimeout?: number;
    };
    deferredContribution?: boolean; // Should be true if you need contribution amount before bet payment
    autoCreateTestJackpot?: boolean; // Auto create test jackpot instances
    validateRequestsExtensionEnabled?: boolean; // Game supports ValidateRequestsExtension (it was tested),
    zeroBetCheckEnabled?: boolean; // Enable zero bet check for this game
    logoutControl?: {
        ignorePayments: {
            gameClosure: boolean;
            gameRelaunch: boolean;
            offlineRetry: boolean;
        }
    };
    instantJpEnabled?: boolean;
    smResultEnabled?: boolean; // Enable smResult for a specific operator
    fixedExtraData?: any;
    // ITG specific settings
    yamlVersion?: string;
    yamlPath?: number;
    staticSpinButton?: boolean;
    addBetAmountOnFreeBetRollback?: boolean;
    [field: string]: any;
}

export interface Limits {
}

export interface GameSettings extends Settings, Limits {
}

export interface BrandSettings {
    fastPlay: boolean;
    turboPlus: boolean;
    turbo: boolean;
    maxAnteBetStake?: number;
    maxBuyInStake?: number;

    [field: string]: any;
}

export interface Marketing {
    contributions: MarketingContribution[];
}

export interface MarketingContribution {
    jackpotId: string;
    contribution: number;
}

export interface SiteRefTokenData {
    siteReferrer: string;
}

export class TokenVerifyException {
}

export class TokenExpiredException {
}

export interface GameSessionData {
    sessionId: string;
    id: string;
    gameMode: GameMode;
}

export function parseStartToken(token: string): StartGameTokenData {
    const result: StartGameTokenData = jwt.decode(token) as any;
    if (!result) {
        return null;
    }
    result.token = token;
    return result;
}

export function parseGameToken(token: string): GameTokenData {
    const result: GameTokenData = jwt.decode(token) as any;
    if (!result) {
        return null;
    }
    result.token = token;
    return result;
}

export function parseToken<T>(token: string): T {
    const result: T = jwt.decode(token) as any;
    if (!result) {
        return null;
    }
    return result;
}

export async function generateSessionToken(sessionData: GameSessionData): Promise<string> {
    return generateToken<GameSessionData>(sessionData, config.gameSessionToken);
}

/**
 * For fun mode only
 *
 * @param data
 * @returns {Promise<string>}
 */
export function generateFunGameToken(data: GameTokenData): Promise<string> {
    return generateToken<GameTokenData>(data, config.gameSessionToken);
}

export function generateGameToken(data: GameTokenData): Promise<string> {
    return generateToken<GameTokenData>(data, config.gameSessionToken);
}

export function generateSiteRefToken(data: SiteRefTokenData): Promise<string> {
    return generateToken<SiteRefTokenData>(data, config.siteRefToken);
}

export async function verifySiteRefToken(token: string): Promise<SiteRefTokenData> {
    try {
        return await verifyToken<SiteRefTokenData>(token, config.siteRefToken);
    } catch (err) {
        if (err instanceof TokenExpiredException) {
            return Promise.reject(new Errors.SiteRefTokenExpiredError());
        }
        if (err instanceof TokenVerifyException) {
            return Promise.reject(new Errors.SiteRefTokenError());
        }
        return Promise.reject(err);
    }
}

export async function verifySessionToken(token: string): Promise<GameSessionData> {
    if (!token) {
        return Promise.reject(new Errors.SessionTokenIsMissingError());
    }
    try {
        return await verifyToken<GameSessionData>(token, config.gameSessionToken);
    } catch (err) {
        if (err instanceof TokenExpiredException) {
            return Promise.reject(new Errors.SessionExpiredError());
        }
        if (err instanceof TokenVerifyException) {
            return Promise.reject(new Errors.SessionTokenIsInvalidError());
        }
        return Promise.reject(err);
    }
}

export async function verifyStartGameToken(token: string): Promise<StartGameTokenData> {
    if (!token) {
        return Promise.reject(new Errors.InvalidStartGameToken());
    }
    try {
        return await verifyToken<StartGameTokenData>(token, config.startGameToken);
    } catch (err) {
        if (err instanceof TokenExpiredException) {
            return Promise.reject(new Errors.SessionExpiredError());
        }
        if (err instanceof TokenVerifyException) {
            return Promise.reject(new Errors.InvalidStartGameToken());
        }
        return Promise.reject(err);
    }
}

export async function verifyInternalToken<T>(token: string): Promise<T> {
    try {
        return await verifyToken<T>(token, config.internalServerToken);
    } catch (err) {
        if (err instanceof TokenExpiredException) {
            return Promise.reject(new Errors.InternalServerTokenExpiredError());
        }
        if (err instanceof TokenVerifyException) {
            return Promise.reject(new Errors.InternalServerTokenError());
        }
        return Promise.reject(err);
    }
}

export async function verifyReplayToken(token: string): Promise<ReplayTokenData> {
    try {
        return await verifyToken<ReplayTokenData>(token, config.replayToken);
    } catch (err) {
        if (err instanceof TokenExpiredException) {
            return Promise.reject(new Errors.InternalServerTokenExpiredError());
        }
        if (err instanceof TokenVerifyException) {
            return Promise.reject(new Errors.InternalServerTokenError());
        }
        return Promise.reject(err);
    }
}

export async function generateInternalToken<T>(data: T): Promise<string> {
    return generateToken(data, config.internalServerToken);
}

export async function generateReplayToken<T>(data: T): Promise<string> {
    return generateToken(data, config.replayToken);
}

async function generateToken<T>(data: T, cfg: TokenConfig): Promise<string> {
    return new Promise<string>((resolve, reject) => {
        const options: jwt.SignOptions = {
            algorithm: cfg.algorithm as jwt.Algorithm,
            issuer: cfg.issuer,
        };
        if ("expiresIn" in cfg) {
            options["expiresIn"] = cfg.expiresIn;
        }
        jwt.sign(data as any, cfg.secret, options, function(err, token) {
            return err ? reject(err) : resolve(token);
        });
    });
}

function verifyToken<T>(token: string, cfg: TokenConfig): Promise<T> {
    return new Promise<T>((resolve, reject) => {
        const ignoreExpiration: boolean = !cfg.expiresIn;
        jwt.verify(token, cfg.secret, {
            algorithms: [cfg.algorithm as jwt.Algorithm],
            issuer: cfg.issuer,
            ignoreExpiration: ignoreExpiration,
        }, function(err, decoded: any) {
            if (err) {
                // catch unique error
                if (err.name === "TokenExpiredError") {
                    return reject(new TokenExpiredException());
                }

                if (err.name === "JsonWebTokenError") {
                    return reject(new TokenVerifyException());
                }

                return reject(err);
            }
            return resolve(decoded);
        });
    });
}

// test purposes only!
export interface TestGameTokenData {
    playerCode: string;
    gameCode: string;
    brandId: number;
    currency: string;
    test?: boolean;
    playmode: string;
}

export async function generateStartGameToken(data: Partial<TestGameTokenData>): Promise<string> {
    return generateToken<Partial<TestGameTokenData>>(data, config.startGameToken);
}

export function decodeDeferredContribution(data: string): DeferredContributionResult[] {
    try {
        const payload = jwt.decode(data) as any;
        return payload.payload as DeferredContributionResult[];
    } catch (err) {
        return undefined;
    }
}
