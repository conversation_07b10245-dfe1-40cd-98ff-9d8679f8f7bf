import { GameSessionData, generateSessionToken, parseToken } from "./tokens";
import { Redis } from "../storage/redis";
import { GameMode } from "@skywind-group/sw-game-core";
import config from "../config";
import { GameContextID } from "./contextIds";
import { HiLoIdGenerator } from "@skywind-group/sw-utils";

let sessionIdGenerator = new HiLoIdGenerator("sw-slot-engine:sessionId", 1000, Redis,
    config.generators.session.min, config.generators.session.max);

// override generator in unit tests
export function setSessionIdGenerator(realGenerator: HiLoIdGenerator) {
    sessionIdGenerator = realGenerator;
}

export function getSessionIdGenerator() {
    return sessionIdGenerator;
}

export class GameSession {
    private finished: boolean;

    private constructor(public readonly id: string,
                        // tslint:disable-next-line:variable-name
                        private _sessionId?: string,
                        // tslint:disable-next-line:variable-name
                        private _gameMode?: GameMode) {

    }

    public get sessionId() {
        if (!this._sessionId) {
            this.parse();
        }
        return this._sessionId;
    }

    public get gameMode() {
        if (!this._sessionId) {
            this.parse();
        }
        return this._gameMode;
    }

    public get isFinished() {
        return this.finished;
    }

    public markFinished() {
        this.finished = true;
    }

    private parse() {
        const data = parseToken<GameSessionData>(this.id);
        this._sessionId = data.sessionId;
        this._gameMode = data.gameMode;
    }

    public static async generate(id: GameContextID, gameMode: GameMode): Promise<GameSession> {
        const sessionId: string = gameMode !== "fun" ? await sessionIdGenerator.nextId() : "0";

        const token = await generateSessionToken({
            sessionId: sessionId,
            id: id.asString(),
            gameMode: gameMode
        });
        return new GameSession(token, sessionId, gameMode);
    }

    public static create(token, data?: GameSessionData): GameSession {
        const [sessionId, mode] = data === undefined ? [undefined, undefined] : [data.sessionId, data.gameMode];
        return new GameSession(token, sessionId, mode);
    }
}
