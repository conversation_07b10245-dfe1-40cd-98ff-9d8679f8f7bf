import { ContextManager } from "./contextmanager/contextManager";
import { logging, measures } from "@skywind-group/sw-utils";
import config from "../config";
import { DBContext } from "./offlinestorage/offlineCommands";
import { ManagementAPISupport } from "./managementapihelper";
import { generateInternalToken } from "./tokens";
import measureProvider = measures.measureProvider;
import { ReactivateRequestData } from "./cleanup/reactivateGameService";

const log = logging.logger("skywind:slot-engine:reactivate-context");

export class ReactivateContextJob extends ManagementAPISupport {
    public static readonly REACTIVATE_GAME_URL = "/reactivate-game";

    constructor(private readonly manager: ContextManager) {
        super(config.managementAPI.internalServerUrl);
    }

    public async fire() {
        await measureProvider.runInTransaction("Reactivate game context", async () => {
            const cfg = config.reactivateJob;
            try {
                while (await this.doWork(cfg.batch)) {
                    log.debug("Processed %s items", cfg.batch);
                }
            } catch (err) {
                measureProvider.saveError(err);
                log.error(err, "Error reactivating contexts");
            }
        });

    }

    public async doWork(batchSize: number): Promise<boolean> {
        const items = await this.manager.findGameContextsForCompletion(batchSize);

        await Promise.all(items.map(ctx => this.reactivateGameContext(ctx)));

        return items.length >= batchSize;
    }

    private async reactivateGameContext(ctx: DBContext) {
        const request: ReactivateRequestData = {
            gameContextId: ctx.id,
            brandId: ctx.brandId
        };
        try {
            const token = await generateInternalToken(request);
            await this.post(ReactivateContextJob.REACTIVATE_GAME_URL, { token });
        } catch (err) {
            measureProvider.saveError(err);
            log.error(err, "Failed to reactivate game context", request);
        }
    }
}
