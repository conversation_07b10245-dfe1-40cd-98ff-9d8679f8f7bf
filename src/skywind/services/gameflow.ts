import {
    Analytics,
    BalanceResponse,
    BaseRequest,
    BonusCoinsBalance,
    ContextUpdate,
    ConversionService,
    FreeBetsInfo,
    GameContext,
    GameContextPersistencePolicy,
    GameFlowInfo,
    GameHistory,
    GameInfo,
    JackpotAction,
    JackpotResults,
    LogLevel,
    MiniGame,
    PaymentInfo,
    PushService,
    RandomGenerator,
    SomeGame,
    SomeGameFlow,
    TickersResponse,
    WinJackpot,
} from "@skywind-group/sw-game-core";
import { GameFlowContext, JackpotPendingModification, PendingModification } from "./context/gamecontext";
import { GameSettings, GameTokenData } from "./tokens";
import * as Errors from "../errors";
import { NeedToRestartTheGame, OperationRefundedError } from "../errors";
import {
    Balance,
    PlayerWalletManager,
    TransferOperationType,
    WalletOperation
} from "./wallet";
import { JackpotContext } from "./jpn/jackpot";
import { JackpotService } from "./jpn/jackpotService";
import { lazy, logging } from "@skywind-group/sw-utils";
import { getCurrencyExchange } from "./currencyexchange";
import { createWalletOperationFactory, WalletOperationFactory } from "./walletOperationFactory";
import { deepClone } from "../utils/cloner";
import { JackpotFlow, JackpotPendingProcessResult } from "./jpn/jackpotFlow";
import { JackpotPendingModificationFactory } from "./jpn/jackpotPendingModificationFactory";
import { JackpotUtil } from "./jpn/jackpotUtil";
import { AbstractGameProxy } from "./game/gameProxy";
import { PlayMode } from "./playMode";
import { ContextManager } from "./contextmanager/contextManager";
import { PlayerContext } from "./playercontext/playerContext";
import { createPaymentDelegate } from "./paymentDelegate";
import { GameFlowInfoImpl } from "./context/gameFlowInfo";
import { GameData } from "./auth";
import { AnalyticsData, AnalyticsService } from "./analytics/analyticsService";
import config from "../config";
import { ExtendedGameHistory } from "../history/history";
import { decorateWithGlobalExtensions } from "./game/globalExtensions";
import { DeferredPayment } from "@skywind-group/sw-deferred-payment";
import { StoreHistory } from "./onlinestorage/gameContextCommands";
import { createMessaging, Messaging } from "@skywind-group/sw-messaging";
import { TransferFacade } from "./transferFacadeForGameFlow";

const log = logging.logger("sw-slot-engine:keep-alive:gameflow");

export type GameConversionService = ConversionService<any>;

export interface TransferInfo {
    operation: TransferOperationType;
    amount: number;
}

const analyticsService = lazy(() => new AnalyticsService());
const achievementsLogger = logging.logger("achievements");
const achievementsService = lazy<Messaging>(() => createMessaging(config.achievements.nats, achievementsLogger));

export interface PreparedPendingUpdate {
    readonly context: GameContext;
    readonly hasPending: boolean;
    readonly walletOperation?: WalletOperation;
    readonly history?: GameHistory;
    readonly analytics?: Analytics;
    readonly jackpotPending?: JackpotPendingModification;
}

export class EngineGameFlow<REQ extends BaseRequest = BaseRequest> implements SomeGameFlow<REQ> {
    // immutable data
    public game: SomeGame & AbstractGameProxy;
    private gameSettings: GameSettings;

    // states
    private currentGameContext: GameContext;
    // private currentJackpotTickers: TickersResponse;
    private gameTokenData: GameTokenData;
    public contextUpdate: ContextUpdate<GameContext>;

    // services
    public readonly walletOperationFactory: WalletOperationFactory;
    public readonly jackpotFlow: JackpotFlow;
    public readonly jackpotPendingFactory: JackpotPendingModificationFactory;
    private randomGenerator: RandomGenerator;
    private readonly flowInfo: GameFlowInfoImpl;

    constructor(game: SomeGame & AbstractGameProxy,
                private readonly currentRequest: REQ,
                private contextManager: ContextManager,
                private readonly engineGameFlowContext: GameFlowContext,
                private readonly conversionService: GameConversionService,
                public walletManager: PlayerWalletManager,
                jackpotService?: JackpotService,
                private readonly notificationService?: PushService,
                private currentBalance?: Balance) {
        this.game = game && decorateWithGlobalExtensions(
            game,
            engineGameFlowContext.settings,
            engineGameFlowContext.gameData?.brandSettings
        );
        this.gameSettings = { ...engineGameFlowContext.settings };
        this.flowInfo = new GameFlowInfoImpl(this.engineGameFlowContext);
        this.walletOperationFactory = createWalletOperationFactory(this);
        this.jackpotFlow = new JackpotFlow(this, jackpotService);
        this.jackpotPendingFactory = new JackpotPendingModificationFactory(this, jackpotService);
        this.refreshCurrentGameContext();
        this.gameTokenData = engineGameFlowContext.gameData.gameTokenData;
    }

    public request(): REQ {
        return this.currentRequest;
    }

    public info(): GameFlowInfoImpl {
        return this.flowInfo;
    }

    public getLogData(): any {
        const contextId = this.engineGameFlowContext.id;
        const gameData = this.engineGameFlowContext.gameData;
        return {
            request: this.currentRequest,
            payload: {
                gameId: gameData.gameId,
                deviceId: contextId.deviceId,
                brandId: contextId.brandId,
                playerCode: contextId.playerCode,
                roundId: this.engineGameFlowContext.roundId,
                gameSerialNumber: this.engineGameFlowContext.gameSerialNumber,
                totalEventId: this.engineGameFlowContext.totalEventId
            }
        };
    }

    public getItgReducedLogData(): any {
        const contextId = this.engineGameFlowContext.id;
        const gameData = this.engineGameFlowContext.gameData;
        return {
            request: this.filterItgLogs(this.currentRequest),
            payload: {
                gameId: gameData.gameId,
                deviceId: contextId.deviceId,
                brandId: contextId.brandId,
                playerCode: contextId.playerCode,
                roundId: this.engineGameFlowContext.roundId,
                gameSerialNumber: this.engineGameFlowContext.gameSerialNumber,
                totalEventId: this.engineGameFlowContext.totalEventId
            }
        };
    }

    private filterItgLogs(request: any): any {
        const { gameState, history, ...rest } = request;
        return rest;
    }

    public setRng(randomGenerator: RandomGenerator) {
        this.randomGenerator = randomGenerator;
    }

    public rng(): RandomGenerator {
        return this.randomGenerator;
    }

    public settings(): any {
        return this.gameSettings;
    }

    public pushService(): PushService {
        return this.notificationService;
    }

    public async gameContext(): Promise<GameContext> {
        if (!this.currentGameContext) {
            this.currentGameContext = deepClone(this.engineGameFlowContext.gameContext);
        }

        return this.currentGameContext;
    }

    public persistencePolicy(): GameContextPersistencePolicy {
        return this.engineGameFlowContext.persistencePolicy === undefined ?
               GameContextPersistencePolicy.NORMAL : this.engineGameFlowContext.persistencePolicy;
    }

    public setPersistencePolicy(value: GameContextPersistencePolicy): void {
        this.engineGameFlowContext.persistencePolicy = value;
    }

    public async getBalance(internalWalletBalanceOnly?: boolean): Promise<BalanceResponse> {
        const balance: Balance = await this.getPlayerBalance(internalWalletBalanceOnly);
        return this.asBalanceResponse(balance);
    }

    public getExtraData(): any {
        return this.currentBalance?.extraData;
    }

    public getFreeBets(info: GameInfo): Promise<FreeBetsInfo> {
        return this.walletManager.getFreeBetsInfo(info);
    }

    public async payment(paymentInfo: PaymentInfo): Promise<BalanceResponse> {
        await this.update(this.engineGameFlowContext.gameContext, paymentInfo, undefined);
        return this.getBalance();
    }

    public async transfer(transferInfo: TransferInfo,
                          raiseError: boolean = true,
                          initiatedByServer: boolean = false): Promise<BalanceResponse> {
        await new TransferFacade(this).makeTransfer(transferInfo, raiseError, initiatedByServer);
        return this.getBalance();
    }

    public async redeemBNS(redeemInfo: BonusCoinsBalance) {
        const walletOperation = await this.walletOperationFactory.createRedeemBNSOperation(redeemInfo);
        const history: ExtendedGameHistory = {
            type: "redeem-bns",
            roundEnded: this.engineGameFlowContext.roundEnded,
            separateRound: true,
            credit: redeemInfo.redeemBalance,
            data: {
                promoId: redeemInfo.promoId,
                expireAt: redeemInfo.expireAt,
                amount: redeemInfo.amount,
                rewardedAmount: redeemInfo.rewardedAmount,
                redeemedAmount: redeemInfo.redeemMaxAmount ? Math.min(redeemInfo.amount, redeemInfo.redeemMaxAmount) :
                                redeemInfo.amount,
                redeemBalance: redeemInfo.redeemBalance,
                redeemCurrency: redeemInfo.redeemCurrency
            },
        };

        if (redeemInfo.externalId) {
            history.data.externalId = redeemInfo.externalId;
        }

        await this.updatePending(this.engineGameFlowContext.gameContext, walletOperation, history);
        await this.commitPending();

        return this.getBalance();
    }

    private async commitDeferredPayment(deferredPayment: DeferredPayment): Promise<Balance> {
        const walletOperation = await this.walletOperationFactory.createDeferredPaymentOperation(deferredPayment);
        const history: ExtendedGameHistory = Object.assign(deferredPayment.history || {
            type: "deferred-payment",
            data: {},
        }, { separateRound: true, roundEnded: this.engineGameFlowContext.roundEnded, credit: walletOperation.amount });

        await this.updatePending(this.engineGameFlowContext.gameContext, walletOperation, history);
        return this.doCommitPending();
    }

    public async checkPending(balance?: Balance) {
        if (this.pendingModification || this.jackpotPending) {
            try {
                await this.commitPending();
            } catch (e) {
                if (e instanceof OperationRefundedError) {
                    // suppress error
                    return;
                }

                return Promise.reject(e);
            }
        } else {
            await this.checkDeferredPayments(balance);
        }
    }

    public async incrementAndCheckRequestId<REQ extends BaseRequest>(req: REQ): Promise<void> {
        this.engineGameFlowContext.lastRequestId++;
        if (req.requestId !== this.engineGameFlowContext.lastRequestId) {
            return Promise.reject(new Errors.ConcurrentAccessToGameSession());
        }
    }

    public async storeHistory(history: GameHistory): Promise<void> {
        return this.update(this.engineGameFlowContext.gameContext, undefined, history);
    }

    public async updateGameContext(gameContext: GameContext): Promise<GameContext> {
        await this.engineGameFlowContext.update(gameContext);
        this.currentGameContext = deepClone(this.engineGameFlowContext.gameContext);

        return this.currentGameContext;
    }

    public async update(context: GameContext,
                        payment: PaymentInfo,
                        history: GameHistory,
                        jackpotAction?: JackpotAction,
                        analytics?: Analytics): Promise<any> {
        const update = await this.preparePending({ context, payment, history, jackpotAction, analytics });
        if (update.hasPending) {
            await this.updatePending(
                update.context,
                update.walletOperation,
                update.history,
                update.analytics,
                update.jackpotPending
            );
            return this.commitPending();
        } else {
            return this.updateGameContext(update.context);
        }
    }

    public async preparePending(update: ContextUpdate<GameContext>): Promise<PreparedPendingUpdate> {
        let walletOperation: WalletOperation;
        if (update.payment) {
            walletOperation = await this.walletOperationFactory.createPaymentOperation(update.payment, update.history);
        }
        const jackpotPending = await this.jackpotPendingFactory.create(update.jackpotAction, walletOperation);
        if (walletOperation && this.gameSettings.zeroBetCheckEnabled) {
            const totalBet = this.engineGameFlowContext?.round?.totalBet || walletOperation.bet;
            if (!totalBet && walletOperation.win) {
                log.error("Win in round without bet", update);
                throw new Errors.GameFlowIllegalInvocationException("Win in round without bet");
            }
        }

        if (walletOperation || update.history || jackpotPending || update.analytics) {
            // SWS-1011: Validate history and protect from inconsistent data
            if (update.history && !update.history.data) {
                return Promise.reject(
                    new Errors.GameFlowIllegalInvocationException("GameEventHistory should contain event")
                );
            }

            return {
                context: update.context,
                hasPending: true,
                walletOperation,
                history: update.history,
                jackpotPending,
                analytics: update.analytics
            };
        } else {
            return { context: update.context, hasPending: false };
        }
    }

    public async updateMiniGame(gameContext: GameContext, miniGame: MiniGame, analytics?: Analytics): Promise<void> {
        const jpModification = this.jackpotPendingFactory.createMiniGameModification(miniGame);
        await this.updatePending(gameContext, undefined, undefined, analytics, jpModification);
        await this.commitPending();
    }

    public async winJackpot(gameContext: GameContext,
                            winJackpot: WinJackpot,
                            history: GameHistory,
                            analytics?: Analytics): Promise<void> {
        const jpModification = await this.jackpotPendingFactory.createWinJackpotModification(winJackpot);
        await this.updatePending(gameContext, undefined, history, analytics, jpModification);
        await this.commitPending();
    }

    public log(level: LogLevel, message: any, ...optionalParams: any[]): void {
        if (this.gameTokenData) {
            if (typeof message === "object") {
                message.gameId = this.gameTokenData.gameCode;
                message.brandId = this.gameTokenData.brandId;
                message.playerCode = this.gameTokenData.playerCode;
            } else {
                const token = this.gameTokenData;
                const pre = `gameId: ${token.gameCode}, brandId: ${token.brandId}, playerCode: ${token.playerCode}. `;
                message = pre + message;
            }
        }
        switch (level) {
            case LogLevel.Debug:
                log.debug(message, optionalParams);
                break;
            case LogLevel.Info:
                log.info(message, optionalParams);
                break;
            case LogLevel.Warn:
                log.warn(message, optionalParams);
                break;
            case LogLevel.Error:
                log.error(message, optionalParams);
                break;
            default:
                log.warn(message, optionalParams);
                break;
        }
    }

    public exchange(amount: number, targetCurrency: string, baseCurrency?: string): number {
        return PlayMode.exchange(this.flowContext.gameData, amount, targetCurrency, baseCurrency);
    }

    public getExchangeRate(targetCurrency: string): number {
        const baseCurrency = this.gameTokenData.currency;
        return getCurrencyExchange().getExchangeRate(baseCurrency, targetCurrency);
    }

    // this method is called on disconnect, on context cleanup or from game recovery service
    public async transferAllOut(): Promise<void> {
        if (!this.gameSettings.transferEnabled) {
            return;
        }
        const balance = await this.getBalance(true);

        await this.transfer({
            operation: "transfer-out",
            amount: balance.amount,
        }, false, true);
    }

    public async jackpotTickers(): Promise<TickersResponse> {
        return this.jackpotFlow.jackpotTickers();
    }

    public async jackpotResult(): Promise<JackpotResults> {
        const result = this.jackpotFlow.jackpotResult();
        if (JackpotUtil.isInstantJackpot(this)) {
            const round = this.engineGameFlowContext.round;
            const lastRound = this.engineGameFlowContext.lastRound;
            const roundEnded = this.engineGameFlowContext.roundEnded;
            if ((round && round.instantJackpot) || (roundEnded && lastRound && lastRound.instantJackpot)) {
                return result;
            } else {
                return;
            }
        }
        return result;
    }

    public validateJackpotState(): boolean {
        return JackpotUtil.validateJackpotState(this);
    }

    public get isJackpotEnabled() {
        return this.jackpotContext && this.jackpotContext.contributionEnabled;
    }

    public get jackpotContext(): JackpotContext {
        return this.engineGameFlowContext.jpContext;
    }

    public get pendingModification(): PendingModification {
        return this.engineGameFlowContext.pendingModification;
    }

    public get jackpotPending(): JackpotPendingModification {
        return this.engineGameFlowContext.jackpotPending;
    }

    public get gameData(): GameData {
        return this.engineGameFlowContext.gameData;
    }

    public get lastRequestId() {
        return this.engineGameFlowContext.lastRequestId;
    }

    /**
     * GameFlow has limited lifespan.
     * We enforce this rule by closing flow and cleanup resources,
     */
    public close(): void {
        this.contextManager = undefined;
        this.walletManager = undefined;
        this.currentBalance = undefined;
    }

    public get flowContext(): GameFlowContext {
        return this.engineGameFlowContext;
    }

    public deferredUpdate(update: ContextUpdate<GameContext>) {
        this.contextUpdate = { ...(this.contextUpdate || {}), ...update };
    }

    public async commitDeferredUpdate() {
        const update = this.contextUpdate;
        if (update) {
            try {
                await this.update(
                    update.context,
                    update.payment,
                    update.history,
                    update.jackpotAction,
                    update.analytics
                );
            } finally {
                this.contextUpdate = undefined;
            }
        }
    }

    public async commitPending(): Promise<void> {
        const balance = await this.doCommitPending();
        await this.checkDeferredPayments(balance);
    }

    private async checkDeferredPayments(balance: Balance): Promise<void> {
        const deferredPayments = balance?.deferredPayments;
        if (deferredPayments) {
            for (const deferredPayment of deferredPayments) {
                try {
                    await this.commitDeferredPayment(deferredPayment);
                } catch (e) {
                    if (!this.pendingModification) {
                        log.warn(deferredPayment, "Deferred payment has been started already. Skip it");
                    } else {
                        return Promise.reject(e);
                    }
                }
            }
        }
    }

    private async doCommitPending(): Promise<Balance> {
        const paymentDelegate = createPaymentDelegate(this);

        const { balance, jackpotResult, finalBalanceForHistory } = await paymentDelegate.commitPending();

        if (balance) {
            this.currentBalance = balance;
        }

        if (config.analytics.sendOnline && this.engineGameFlowContext.playMode !== "fun") {
            const analytics = this.engineGameFlowContext?.pendingModification?.analytics;
            if (analytics) {
                await analyticsService.get().send(AnalyticsData(this.engineGameFlowContext, analytics));
            }
        }

        const storeHistory: StoreHistory = await this.engineGameFlowContext.commitPendingModification(
            finalBalanceForHistory,
            jackpotResult,
            jackpotResult.jackpotContext);

        if (config.achievements.on) {
            await this.processAchievements(storeHistory);
        }

        this.refreshCurrentGameContext();
        return balance;
    }

    private async processAchievements({ item }: StoreHistory): Promise<void> {
        if (item) {
            try {
                await achievementsService.get().publish(config.achievements.channel, item);
            } catch (err) {
                achievementsLogger.error(err, "Cannot publish game history event to NATS");
            }
        }
    }

    public async updateJackpotPendingModification(jpPending: JackpotPendingModification,
                                                  jpResult?: JackpotPendingProcessResult,
                                                  jpContext?: JackpotContext) {
        return this.engineGameFlowContext.updateJackpotPendingModification(jpPending, jpResult, jpContext);
    }

    public async updatePending(gameContext: GameContext,
                               walletOperation: WalletOperation,
                               history: GameHistory,
                               analytics?: Analytics,
                               jackpotPending?: JackpotPendingModification): Promise<void> {

        if (this.engineGameFlowContext.pendingModification) {
            return Promise.reject(new NeedToRestartTheGame());
        }

        // storing pending wallet operation in context
        return this.engineGameFlowContext.updatePendingModification(
            walletOperation,
            gameContext,
            this.currentRequest,
            history,
            jackpotPending,
            analytics);
    }

    public async updateJackpotPending(jackpotPending: JackpotPendingModification): Promise<void> {
        if (this.engineGameFlowContext.pendingModification) {
            return Promise.reject(new NeedToRestartTheGame());
        }
        return this.engineGameFlowContext.updatePendingModification(
            undefined,
            undefined,
            this.currentRequest,
            undefined,
            jackpotPending
        );
    }

    public async getPlayerBalance(internalWalletBalanceOnly?: boolean): Promise<Balance> {
        if (!this.currentBalance) {
            this.currentBalance = await this.walletManager.getBalance(internalWalletBalanceOnly);
        }
        return this.currentBalance;
    }

    private asBalanceResponse(balance: Balance): BalanceResponse {
        const amount = this.toGameAmount(balance.main);
        const balanceResponse: BalanceResponse = {
            currency: PlayMode.getCurrency(this.gameTokenData),
            amount: amount,
            real: {
                amount: amount
            },
            bonus: {
                amount: 0,
            },
        };

        if (balance.external !== undefined) {
            const externalAmount = this.toGameAmount(balance.external);
            balanceResponse.external = {
                amount: externalAmount,
            };
        }

        if (balance.extraBalances) {
            balanceResponse.extraBalances = {};
            // tslint:disable-next-line:forin
            for (const accountBalance in balance.extraBalances) {
                balanceResponse.extraBalances[accountBalance] = {
                    amount: balance.extraBalances[accountBalance]
                };
            }
        }

        if (balance.freeBets) {
            balanceResponse.freeBets = {
                amount: balance.freeBets.amount,
            };
        }

        if (balance.bonusCoins) {
            const bonusCoins = balance.bonusCoins;
            const expireCountDown = Math.floor((Date.parse(bonusCoins.expireAt) - Date.now()) / 1000);
            const playerContext = this.engineGameFlowContext.playerContext;
            const info = this.info();
            if (info.gameMode === "bns" ||
                (bonusCoins.amount > 0 && expireCountDown > 0) ||
                bonusCoins.redeemBalance > 0 ||
                this.hasBNSGame(info, playerContext)) {
                balanceResponse.bonusCoins = {
                    ...bonusCoins,
                    expireCountdown: expireCountDown > 0 ? expireCountDown : 0
                } as BonusCoinsBalance;
            }
        }

        return balanceResponse;
    }

    private hasBNSGame(info: GameFlowInfo, playerContext: PlayerContext) {
        return playerContext.hasGame({ gameCode: info.gameId, gameMode: "bns", deviceId: info.deviceId });
    }

    public fromGameAmount(amount: number): number {
        return this.conversionService.fromGameAmount(this.info(), this.settings(), amount);
    }

    public toGameAmount(amount: number): number {
        return this.conversionService.toGameAmount(this.info(), this.settings(), amount);
    }

    private refreshCurrentGameContext(): void {
        this.currentGameContext = undefined;
    }

    /**
     * Indicates that game payment should be split on two phase: credit and debit.
     * @returns {boolean}
     */
    public isSplitPayment(): boolean {
        return this.settings().splitPayment === true && PlayMode.supportsSplitPayment(this.info().gameMode);
    }
}
