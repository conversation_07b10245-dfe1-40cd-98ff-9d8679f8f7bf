import { Redis as RedisClient } from "ioredis";
import { getFactory, Redis } from "../storage/redis";

export type NotificationHandler<T = any> = (event: string, data?: T) => Promise<any>;

class Subscription {
    private handlers: NotificationHandler[] = [];

    constructor(private readonly sub: RedisClient, private readonly channel: string) {
    }

    public addHandler(handler: NotificationHandler) {
        this.handlers.push(handler);
    }

    public async init() {
        await this.sub.subscribe(this.channel);
        this.sub.on("message", async (channel: string, message?: string) => {
            if (this.channel === channel) {
                const data = message && JSON.parse(message);
                await Promise.all(this.handlers.map(handler => handler(this.channel, data)));
            }
        });
    }
}

export class NotificationService {
    private readonly subscriptions = new Map<string, Subscription>();

    public async subscribe(event: string, handler: NotificationHandler) {
        let subscription = this.subscriptions.get(event);
        if (!subscription) {
            const redisClient = await getFactory().createClient();
            subscription = new Subscription(redisClient, event);
            try {
                await subscription.init();
            }catch (err) {
                await redisClient.quit();
                return Promise.reject(err);
            }
            this.subscriptions.set(event, subscription);

        }
        subscription.addHandler(handler);
    }

    public async notify<T = any>(event: string, data: T) {
        const client = await Redis.get().get();
        try {
            await client.publish(event, JSON.stringify(data));
        } finally {
            await Redis.get().release(client);
        }
    }
}

export default new NotificationService();
