import { logging } from "@skywind-group/sw-utils";
import { PLATFORM_TYPE } from "@skywind-group/sw-wallet-adapter-core";
import { splitEnvParameters } from "./utils/envParse";

const commonPrefix = process.env.GAMESERVER_PREFIX || "games";
const defaultStartAmount = 50;

function logQueries(sql: string, timing?: number) {
    logging.logger("sequelize").info(sql);
}

const config = {
    socketV4: {
        enableNewSocketTerminal: process.env.IS_NEW_SOCKET_TERMINAL_ENABLED === "true",
        path: process.env.SOCKET_V4_PATH || "/socket-v4",
    },
    appPort: +process.env.APP_PORT || 4000,
    internalPort: +process.env.APP_INTERNAL_PORT || 4004,
    gameContextApiForQAsPort: +process.env.APP_GC_QA_API_PORT || 4444,

    environment: process.env.NODE_ENV || "development",
    environmentId: process.env.NODE_ENV_ID,
    clusterName: process.env.CLUSTER_NAME || "default",

    server: {
        timeout: +process.env.HTTP_SERVER_TIMEOUT || 12000,
        keepAliveTimeout: +process.env.HTTP_SERVER_KEEP_ALIVE_TIMEOUT || 0
    },

    gameEntryPoint: process.env.ENGINE_GAME_ENTRY_POINT || "/casino/game2",

    itgConcurrencyRequestCheckEnabled: process.env.ITG_CONCURRENCY_REQUEST_CHECK_ENABLED === "true",
    itgApiEnabled: process.env.ITG_API_ENABLED === "true",
    itgGameServerBaseUrl: process.env.ITG_GAME_SERVER_BASE_URL || "http://example.com",
    itgFinalizationUrl: process.env.ITG_FINALIZATION_URL || "/finalize",

    isProduction: (): boolean => {
        return config.environment === "production";
    },

    retries: {
        sleep: +(process.env.RETRIES_SLEEP_TIMEOUT || "50"),
        maxTimeout: +process.env.RETRIES_MAX_TIMEOUT || 1000
    },

    generators: {
        round: {
            min: process.env.ROUND_ID_MIN || "0",
            max: process.env.ROUND_ID_MAX || Number.MAX_SAFE_INTEGER.toString()
        },

        session: {
            min: process.env.SESSION_ID_MIN || "0",
            max: process.env.SESSION_ID_MAX || Number.MAX_SAFE_INTEGER.toString()
        }
    },

    newrelic: {
        appName: process.env.NEWRELIC_APP_NAME || "SW-GAME-SERVER",
        envName: process.env.NEWRELIC_ENV_NAME || "development",
        key: process.env.NEWRELIC_KEY || "395235de00803fcf5e69a2806cf655b7138918c3",
    },

    db: {
        database: process.env.PGDATABASE || "gameserver",
        user: process.env.PGUSER,
        password: process.env.PGPASSWORD,
        host: process.env.PGHOST || "db",
        port: +process.env.PGPORT || 5432,
        ssl: {
            isEnabled: process.env.PG_SECURE_CONNECTION === "true",
            ca: process.env.PG_CA_CERT || "./ca.pem",
        },
        maxConnections: +process.env.PG_MAX_CONNECTIONS || 10,
        maxIdleTime: +process.env.PG_MAX_IDLE_TIME_MS || 30000,
        syncOnStart: process.env.SYNC_ON_START === "true"
    },

    redis: {
        host: process.env.REDIS_HOST || "redis",
        port: +process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD,
        minConnections: +process.env.REDIS_MIN_CONNECTIONS || 2,
        maxConnections: +process.env.REDIS_MAX_CONNECTIONS || 10,
        maxIdleTime: +process.env.REDIS_MAX_IDLE_TIME_MS || 30000,
        connectionTimeout: +process.env.REDIS_CONNECTION_TIMEOUT || 1000,
        maxRetriesPerRequest: +process.env.REDIS_MAX_RETRIERS_PER_REQUEST || 0,
        sentinels: JSON.parse(process.env.REDIS_SENTINELS || null),
        sentinelUsername: process.env.REDIS_SENTINEL_USERNAME,
        sentinelPassword: process.env.REDIS_SENTINEL_PASSWORD,
        clusterName: process.env.REDIS_CLUSTER_NAME || "redis-ha",
        replicationFactor: +process.env.REDIS_REPLICATION_FACTOR || 0,
        replicationTimeout: +process.env.REDIS_REPLICATION_TIMEOUT || 100,
        showFriendlyErrorStack: process.env.REDIS_SHOW_FRIENDLY_ERROR_STACK === "true"
    },

    /**
     * a week default period for player history
     */
    playerHistoryPeriod: +process.env.PLAYER_HISTOTY_PERIOD_MS || 7 * 24 * 60 * 60 * 1000,

    /**
     *  Maximum count of history item.
     *  We cannot send more in reason of bad performance.
     *  Management API also has limit 200 lines and return error if this limit greater.
     */
    playerHistoryRecordsLimit: +process.env.PLAYER_HISTORY_RECORDS_LIMIT || 200,

    gameSessionToken: {
        algorithm: process.env.GAME_SESSION_TOKEN_ALGORITHM || "HS512",
        issuer: process.env.GAME_SESSION_TOKEN_ISSUER || "skywindgroup",
        secret: process.env.GAME_SESSION_TOKEN_SECRET ||
            "k06u_Lq8-DfxLNacIh14tiWfOLLsyRz5v-DGw-hrarnp3tIKE1QZSdaJahH5fJgh",
    },

    startGameToken: {
        algorithm: process.env.START_GAME_TOKEN_ALGORITHM || "HS512",
        issuer: process.env.START_GAME_TOKEN_ISSUER || "skywindgroup",
        expiresIn: +process.env.START_GAME_TOKEN_EXPIRES_IN || 7200, // 2 hours
        secret: process.env.START_GAME_TOKEN_SECRET ||
            "yLQDmzHLqmQrjp6Z8KvuwcTwQe7TM5qMdCj4w8k8AFBXkHHDEndhUdevTw6QYPKp",
    },

    /**
     * Management interaction url
     */
    managementAPI: {
        url: process.env.MANAGEMENT_API || "http://api:3006/",
        internalServerUrl: process.env.MANAGEMENT_API_INTERNAL_URL || "http://api:4004",
        keepAlive: {
            maxFreeSockets: +(process.env.MANAGEMENT_API_KEEP_ALIVE_FREE_SOCKET_COUNT || 100),
            freeSocketKeepAliveTimeout: +process.env.MANAGEMENT_API_KEEP_ALIVE_TIMEOUT || 30000,
            socketActiveTTL: +process.env.MANAGEMENT_API_SOCKET_ACTIVE_TTL || 60000,
        }
    },

    // tslint:disable-next-line:max-line-length
    managementAPIGameHistory: process.env.MANAGEMENT_API_GAME_HISTORY || process.env.MANAGEMENT_API_GAME_AUTH || "http://game-auth-api:3007/",
    managementAPIGameAuth: process.env.MANAGEMENT_API_GAME_AUTH || "http://game-auth-api:3007/",
    /**
     * Wallet access type: </br>
     *
     * true -  using managment api
     * false - direct to redis
     */
    walletThroughAPI: JSON.parse(process.env.WALLET_THROUGH_API || "true"),

    namespaces: {

        contextPrefix: {
            real: `${commonPrefix}:${process.env.GAME_CONTEXT_PREFIX || "context"}`,
            fun: `${commonPrefix}:${process.env.GAME_FUN_CONTEXT_PREFIX || "context"}`,
            bns: `${commonPrefix}:${process.env.GAME_BNS_CONTEXT_PREFIX || "bns"}`,
            play_money: `${commonPrefix}:${process.env.GAME_PLAY_MONEY_CONTEXT_PREFIX || "play_money"}`,
            fun_bonus: `${commonPrefix}:${process.env.GAME_FUN_BONUS_CONTEXT_PREFIX || "fun_bonus"}`,
        },
        playerContextPrefix: `${commonPrefix}:${process.env.PLAYER_CONTEXT_PREFIX || "player"}`,
        historyPrefix: `${commonPrefix}:${process.env.GAME_HISTORY_PREFIX || "history"}`,
        roundsPrefix: `${commonPrefix}:${process.env.ROUND_HISTORY_PREFIX || "rounds"}`,
        sessionsPrefix: `${commonPrefix}:${process.env.SESSION_HISTORY_PREFIX || "sessions"}`,
        cleanupGameContextPrefix: `${commonPrefix}:${process.env.CLEANUP_GAME_CONTEXT_PREFIX || "cleanup"}`,
        cleanupPlayerContextPrefix: `${commonPrefix}:${process.env.CLEANUP_GAME_CONTEXT_PREFIX || "players-cleanup"}`,
        analyticsPrefix: `${commonPrefix}:${process.env.ANALYTICS_PREFIX || "analytics"}`,
        lastGameActivityKey: `${commonPrefix}:${process.env.LAST_GAME_ACTIVITY_KEY || "last-activity"}`,
        lastPlayerActivityPrefix: `${commonPrefix}:${process.env.PLAYER_CONTEXT_PREFIX || "player-last-activity"}`,
        forceCleanupJob: `${commonPrefix}:${process.env.FORCE_CLEANUP_JOB || "force-cleanup"}`,
        merchantSessionLink: `${commonPrefix}:${process.env.MERCHANT_SESSION_LINK || "merchant-session"}`,
    },

    /**
     * Fun mode
     */
    funGame: {

        redis: {
            /**
             * Key prefix for all records which is associated with fun game
             */
            keyPrefix: process.env.FUN_KEYS_PREFIX || "fun",
            host: process.env.FUN_REDIS_HOST || "redis",
            port: +process.env.FUN_REDIS_PORT || 6379,
            password: process.env.FUN_REDIS_PASSWORD,
            minConnections: +process.env.FUN_REDIS_MIN_CONNECTIONS || 2,
            maxConnections: +process.env.FUN_REDIS_MAX_CONNECTIONS || 10,
            maxIdleTime: +process.env.FUN_REDIS_MAX_IDLE_TIME_MS || 30000,
            connectionTimeout: +process.env.FUN_REDIS_CONNECTION_TIMEOUT || 1000,
            maxRetriesPerRequest: +process.env.FUN_REDIS_MAX_RETRIERS_PER_REQUEST || 0,
            showFriendlyErrorStack: process.env.REDIS_SHOW_FRIENDLY_ERROR_STACK === "true"
        },

        /**
         * TTL in seconds
         */
        TTL: +process.env.FUN_GAME_STD_TTL || 900,  // 15 min
        /**
         *  Start amount for fun mode.
         */
        startAmount: +process.env.FUN_GAME_START_AMOUNT || +defaultStartAmount,
        defaultFunGameStartAmount: +defaultStartAmount,
        startGameTokenRequired: process.env.FUN_GAME_START_GAME_TOKEN_REQUIRED === "true"
    },

    offlineStorage: {
        port: +process.env.OFFLINE_STORAGE_PORT || 4006,
        useRemote: JSON.parse(process.env.OFFLINE_STORAGE_USE_REMOTE || "false"),
        url: process.env.OFFLINE_STORAGE_URL || "http://context-storage:4006/v1"
    },

    // finalization settings
    expireJob: {
        batch: +(process.env.EXPIRE_JOB_BATCH || "10"),
        checkInterval: +process.env.EXPIRE_JOB_CHECK_INTERVAL || 1,
        retryFactor: +process.env.EXPIRE_JOB_RETRY_FACTOR || 2,
        postponeInterval:  +process.env.POSTPONE_EXPIRATION_INTERVAL || 10,
        // stop finalization after N retries
        maxPaymentOfflineRetryAttempts: +process.env.STOP_FINALIZATION_AFTER_RETRIES || 10
    },

    reactivateJob: {
        batch: +(process.env.REACTIVATE_JOB_BATCH || "10"),
        checkInterval: +process.env.REACTIVATE_JOB_CHECK_INTERVAL || 1,
        postponeInterval:  +(process.env.POSTPONE_REACTIVATION_INTERVAL || "10")
    },

    cleanUp: {
        /**
         *  Game context will be considered 'cold' after this interval(minutes)
         */
        expireIn: +process.env.CLEAN_UP_EXPIRE_IN || 30,

        /**
         *  check expired contexts interval (seconds)
         */
        checkInterval: +process.env.CLEAN_UP_CHECK_INTERVAL || 10,
        /**
         * Max element which will be queried and processed in one job tick.
         */
        batchSize: +process.env.CLEAN_UP_BATCH_SIZE || 100,

        forceCleanupRepairInterval: +process.env.FORCE_CLENUP_REPAIR_INTERVAL || 10 * 60 * 1000,

        brokenPaymentMonitoringRequestTimeout: +process.env.BROKEN_PAYMENT_MONITORING_TIMEOUT || 1000
    },

    cleanUpPlayers: {
        /**
         *  Player context will be considered 'cold' after this summary of this
         *  interval(minutes) + game context expire interval (config.cleanUp.expireIn)
         */
        expireIn: +process.env.CLEAN_UP_PLAYERS_EXPIRE_IN || 10,
        /**
         *  check expired contexts interval (seconds)
         */
        checkInterval: +process.env.CLEAN_UP_CHECK_PLAYERS_INTERVAL || 60,
        /**
         * Max element which will be queried and processed in one job tick.
         */
        batchSize: +process.env.CLEAN_UP_PLAYERS_BATCH_SIZE || 100,

    },

    unloader: {
        unloaderType: process.env.UNLOADER_TYPE || "redis",

        // (minutes) max . Default is 15 minutes
        workerMaxOrphanTTL: +process.env.MAX_ORPHAN_TTL || 15,
        // cron to instantiate repairment of orphan worker lists
        repairWorkerCron: process.env.REPAIR_WORKER_CRON || "0/20 * * * *",
        // max batch size for unloading history,
        historyBatchSize: +process.env.UNLOADER_MAX_HISTORY_BATCH_SIZE || 1000,
        // max batch size for unloading rounds,
        roundBatchSize: +process.env.UNLOADER_MAX_ROUND_BATCH_SIZE || 100,

        /**
         * Alooma consumer parameters
         */
        aloomaConsumer: {
            /**
             * Access token for Alooma
             */
            // tslint:disable-next-line:max-line-length
            token: process.env.ALLOMA_TOKEN || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnROYW1lIjoic2t5d2luZGdyb3VwIiwiaW5wdXRMYWJlbCI6ImdhbWVoaXN0b3J5IiwiaW5wdXRUeXBlIjoiUkVTVEFQSSJ9.02arulVyATh-aGEUTSJSF7XE5JprpRSF-5JMzM0J4f0",
            /**
             * Mac count of tretires
             */
            retries: +process.env.ALLOMA_RETRIES || 10,
        },

        /**
         * Optional events consumer.
         *
         * It can be used  either for test environments or as a direct writer to Redshift
         */
        eventConsumer: {
            database: process.env.EVENT_CONSUMER_PGDATABASE || "gameserver",
            user: process.env.EVENT_CONSUMER_PGUSER,
            password: process.env.EVENT_CONSUMER_PGPASSWORD,
            host: process.env.EVENT_CONSUMER_PGHOST || "db",
            port: +process.env.EVENT_CONSUME_PGPORT || 5432,
            ssl: {
                isEnabled: process.env.EVENT_CONSUME_PG_SECURE_CONNECTION === "true",
            },
            maxConnections: +process.env.EVENT_CONSUMER_PG_MAX_CONNECTIONS || 10,
            maxIdleTime: +process.env.EVENT_CONSUMER_PG_MAX_IDLE_TIME_MS || 30000,
        },

        /**
         * Consumers for history events;
         *
         * "alooma": consuming events through Alooma service
         *
         * "gamehistory": storing events in local database in old format
         *
         * "events": storing events directly to local PostgreSQL. Just for test environments
         *
         * "redshift": consume data to Redshift through SQL interface
         */
        historyConsumers: JSON.parse(process.env.UNLOADER_HISTORY_CONSUMERS || "[\"gamehistory\"]"),

        kafkaToDBConfig: {
            rounds: {
                groupId: process.env.KAFKA_TO_PG_ROUND_CONSUMER_GROUP_ID || "RoundsToPGConsumer",
                sessionTimeout: +process.env.KAFKA_TO_PG_ROUND_CONSUMER_SESSION_TIMEOUT || 15000,
                fetchMaxWaitsMs: +process.env.KAFKA_TO_PG_ROUND_CONSUMER_FETCH_MAX_WAITS || 500,
                fetchMaxBytes: +process.env.KAFKA_TO_PG_ROUND_CONSUMER_FETCH_MAX_BYTES || 1024 * 1024,
                internalBufferSize: +process.env.KAFKA_TO_PG_ROUND_CONSUMER_BUFFER_SIZE || 1000
            },

            gameHistory: {
                groupId: process.env.KAFKA_TO_PG_GAME_HISTORY_CONSUMER_GROUP_ID || "GameHistoryToPGConsumer",
                sessionTimeout: +process.env.KAFKA_TO_PG_GAME_HISTORY_CONSUMER_SESSION_TIMEOUT || 15000,
                fetchMaxBytes: +process.env.KAFKA_TO_PG_GAME_HISTORY_CONSUMER_FETCH_MAX_BYTES || 1024 * 1024,
                fetchMaxWaitsMs: +process.env.KAFKA_TO_PG_GAME_HISTORY_CONSUMER_FETCH_MAX_WAITS || 500,
                internalBufferSize: +process.env.KAFKA_TO_PG_GAME_HISTORY_CONSUMER_BUFFER_SIZE || 1000
            },

            sessions: {
                groupId: process.env.KAFKA_TO_PG_SESSION_CONSUMER_GROUP_ID || "SessionsToPGConsumer",
                sessionTimeout: +process.env.KAFKA_TO_PG_SESSION_CONSUMER_SESSION_TIMEOUT || 15000,
                fetchMaxBytes: +process.env.KAFKA_TO_PG_SESSION_CONSUMER_FETCH_MAX_BYTES || 1024 * 1024,
                fetchMaxWaitsMs: +process.env.KAFKA_TO_PG_SESSION_CONSUMER_FETCH_MAX_WAITS || 500,
                internalBufferSize: +process.env.KAFKA_TO_PG_SESSION_CONSUMER_BUFFER_SIZE || 1000
            },
        },

        gameHistoryKafkaConsumer: {
            enabled: process.env.GAME_HISTORY_KAFKA_UNLOADER_ENABLED === "true",
            topicName: process.env.GAME_HISTORY_TOPIC_NAME || "game-history-raw",
            kafkaBrokerHostnames: process.env.GAME_HISTORY_KAFKA_BROKER_HOSTNAMES || "localhost:9092",
        },

        roundHistoryKafkaConsumer: {
            enabled: process.env.ROUND_HISTORY_KAFKA_UNLOADER_ENABLED === "true",
            topicName: process.env.ROUND_HISTORY_TOPIC_NAME || "round-history-raw",
            kafkaBrokerHostnames: process.env.ROUND_HISTORY_KAFKA_BROKER_HOSTNAMES || "localhost:9092",
        },

        sessionHistoryKafkaConsumer: {
            enabled: process.env.SESSION_HISTORY_KAFKA_UNLOADER_ENABLED === "true",
            topicName: process.env.SESSION_HISTORY_TOPIC_NAME || "session-history-raw",
            kafkaBrokerHostnames: process.env.SESSION_HISTORY_KAFKA_BROKER_HOSTNAMES || "localhost:9092",
        },

        maxSpinPopDuration: +process.env.MAX_SPIN_POP_DURATION || 500,
        spinPopSleepDuration: +process.env.SPIN_POP_SLEEP_DURATION || 100,

        // threshold in days, oldest sessions will be skipped by unloader
        oldSessionHistorySkipThreshold: +process.env.SESSION_HISTORY_SKIP_THRESHOLD || 120,

        // latest period (in days) to keep original game history ts
        gameHistoryLatestTsPeriod: +process.env.GAME_HISTORY_LATEST_TS_PERIOD || 120
    },

    kafka: {
        requireAck: +process.env.KAFKA_REQUIRE_ACK || -1,
        ackTimeoutMs: +process.env.KAFKA_ACK_TIMEOUT || 1000,
        clientCreationTimeout: +process.env.KAFKA_CONNECT_TIMEOUT || 6000,
        requestTimeout: +process.env.KAFKA_REQUEST_TIMEOUT || 5000,
        partitionerType: +process.env.KAFKA_PARTITIONER_TYPE || 0,
        maxSendAttemptTimeout: +process.env.KAFKA_MAX_SEND_ATTEMPT_TIMEOUT || 10000,
    },

    allowSetPositionsByClient: JSON.parse(process.env.ALLOW_SET_POSITIONS_BY_CLIENT || "false"),

    logLevel: process.env.LOG_LEVEL || "info",

    slowRequestLogThreshold: +process.env.SLOW_REQUEST_LOG_THRESHOLD_MS || 1000,

    graylog: {
        host: process.env.GRAYLOG_HOST || undefined,
        port: +process.env.GRAYLOG_PORT || undefined,
    },

    // false by default
    addMetricsEndpointToGameEngine: process.env.METRICS_ENDPOINT_IN_GAME_ENGINE === "true",

    measures: {
        /**
         * Turns on debug mode for gathering more measures.
         */
        includeDebugOnly: JSON.parse(process.env.MEASURES_INCLUDE_DEBUG_ONLY || "false"),

        /**
         * Interval of logging  metrics in history unload job
         */
        logUnloadHistoryInterval: +process.env.LOG_UNLOAD_HISTORY_INTERVAL || (5 * 60 * 1000),
    },

    // tslint:disable-next-line:no-empty
    // logging all POSTGRES queries to console
    queryLogging: process.env.POSTGRES_QUERY_LOGGING === "true" ? logQueries : () => {
    }, // tslint:disable-line

    npm: {
        user: process.env.NPM_USER,
        password: process.env.NPM_PASSWORD,
        email: process.env.NPM_EMAIL,
        moduleGroup: process.env.NPM_MODULE_GROUP || "@skywind-group",
        gameModules: process.env.NPM_GAME_MODULES,
    },

    /**
     * JPN Server
     */
    jpn: {
        api: process.env.JPN_SERVER_API || "http://jackpot:5000",
        keepAlive: {
            maxFreeSockets: +(process.env.JPN_KEEP_ALIVE_FREE_SOCKET_COUNT || 100),
            freeSocketKeepAliveTimeout: +process.env.JPN_KEEP_ALIVE_TIMEOUT || 30000,
            socketActiveTTL: +process.env.JPN_SOCKET_ACTIVE_TTL || 60000,
        }
    },

    publicId: { // !NB, MUST be equal to the same config in MAPI
        password: process.env.PCID_PASSWORD || "PjvMCu7AmUuhNTzYFqLHrYTctKxUQEpcygDJ7qePxjhWDahCQ2PqSynf93wt8ndW",
        minHashLength: process.env.PCID_MIN_HASH_LENGTH || 8
    },

    siteRefToken: {
        algorithm: process.env.GAME_SESSION_TOKEN_ALGORITHM || "HS256",
        issuer: process.env.GAME_SESSION_TOKEN_ISSUER || "skywindgroup",
        secret: process.env.GAME_SESSION_TOKEN_SECRET || "aWMy2t_AEdX9fjTeVW-PDaDDf5Vq59JGWkzSv4mw_NCJJLszY"
    },

    internalServerToken: {
        expiresIn: +process.env.INTERNAL_SERVER_TOKEN_EXPIRES_IN || 300, // 5 minutes
        algorithm: process.env.INTERNAL_SERVER_TOKEN_ALGORITHM || "HS256",
        issuer: process.env.INTERNAL_SERVER_TOKEN_ISSUER || "skywindgroup",
        secret: process.env.INTERNAL_SERVER_SECRET || "TU8N9oP4pPfrUMaRYkjwBsOyw0hgg39sPsTjONrgnN1ErJbn2"
    },

    replayToken: {
        algorithm: process.env.REPLAY_TOKEN_ALGORITHM || "HS256",
        issuer: process.env.REPLAY_TOKEN_ISSUER || "skywindgroup",
        expiresIn: +process.env.REPLAY_TOKEN_ISSUER_EXPIRES_IN || 3600, // 1 hour
        secret: process.env.REPLAY_TOKEN_SECRET ||
            "zloh1RE0bmx21rGPzaJoZUYjJMqClv-bcEAo7-VMyNqfiR2DwOdnkmL6oanj5Ktr",
    },

    corsWhitelist: {
        game: process.env.GAME_CORS_WHITELIST // comma-separated list of allowed origins for /game endpoint requests
    },
    accessControlAllowCredentials: JSON.parse(process.env.ACCESS_CONTROL_ALLOW_CREDENTIALS || "true"),

    bodyParserJsonLimit: +process.env.BODY_PARSER_JSON_LIMIT || 5242880,
    bodyParserUrlLimit: +process.env.BODY_PARSER_URL_LIMIT || 5242880,
    compressionThreshold: +process.env.COMPRESSION_THRESHOLD || 1024,

    newRoundsConsumer: process.env.NEW_ROUNDS_CONSUMER === "true",

    analytics: {
        sendOnline: process.env.ANALYTICS_SEND_ONLINE === "true",
        kafka: {
            topicName: process.env.ANALYTICS_TOPIC_NAME || "analytics-raw",
            kafkaBrokerHostnames: process.env.ANALYTICS_KAFKA_BROKER_HOSTNAMES || "localhost:9092",
            requireAck: +process.env.ANALYTICS_KAFKA_REQUIRE_ACK || -1,
            ackTimeoutMs: +process.env.ANALYTICS_KAFKA_ACK_TIMEOUT || 1000,
            clientCreationTimeout: +process.env.ANALYTICS_KAFKA_CONNECT_TIMEOUT || 6000,
            requestTimeout: +process.env.ANALYTICS_KAFKA_REQUEST_TIMEOUT || 5000,
            partitionerType: +process.env.ANALYTICS_KAFKA_PARTITIONER_TYPE || 2,
            maxSendAttemptTimeout: +process.env.ANALYTICS_KAFKA_MAX_SEND_ATTEMPT_TIMEOUT || 10000,
        }
    },
    routing: {
        validateDeployment: process.env.ROUTING_ENABLE_ROUTE_VALIDATION === "true",
        allowedDeployment: process.env.ROUTING_ALLOWED_ROUTE, // It can be overridden at runtime
    },

    // tslint:disable-next-line:max-line-length
    forbidOverridingGameUrlQuery: process.env.FORBID_OVERRIDING_GAME_URL_QUERY || "token,startGameToken,playmode",

    achievements: {
        on: process.env.ACHIEVEMENTS_ENABLE === "true",
        nats: {
            url: process.env.ACHIEVEMENTS_NATS_URL,
            notificationRecoveryInterval: +process.env.ACHIEVEMENTS_NATS_RECOVERY_INTERVAL || 60000
        },
        channel: `${process.env.ACHIEVEMENTS_NATS_ENV || "dev"}#spin#event`
    },

    smResult: {
        enabled: process.env.SM_RESULT_ENABLED === "true",
        maxLength: +process.env.SM_RESULT_MAX_LENGTH || 4000,
    },

    roundDetailedResult: {
        enabled: process.env.ROUND_DETAILED_RESULT_ENABLED === "true",
    },

    tempListOfGameForFinalization: process.env.LIST_OF_GAMES_FOR_TEMP_SOLUTION || [
        "sw_cl_965", "sw_cl_910", "sw_cl_945", "sw_hl_965", "sw_hl_910", "sw_hl_945"
    ],

    showAllEvents: process.env.SHOW_ALL_EVENTS === "true",

    acceptedDeviceIds: JSON.parse(
        process.env.ACCEPTED_DEVICE_IDS || `["${PLATFORM_TYPE.WEB}","${PLATFORM_TYPE.MOBILE}"]`
    ),
    allowedHTTPMethods: splitEnvParameters(
        process.env.ALLOWED_HTTP_METHODS,
        ["GET", "POST", "OPTIONS", "DELETE", "PATCH", "PUT"]
    ),
};

export default config;
