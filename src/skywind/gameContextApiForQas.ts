import http = require("http");
import gameContextApiForQas from "./api/gameContextApiForQAs";
import { errorHandler, notFoundHandler } from "./api/middleware";
import { lazy, logging } from "@skywind-group/sw-utils";
import * as fastify from "./fastify";
import { FastifyInstance } from "fastify";
import config from "./config";
import health from "./api/health";
import version from "./api/version";

const log = logging.logger("sw-slot-engine-testing-api");

const container = lazy(() => {
    const app: FastifyInstance = fastify.create();

    app.register(gameContextApiForQas, { prefix: "/qa-context-api" });
    app.register(health, { prefix: "/v1" });
    app.register(version, { prefix: "/v1/version" });

    // error handling
    app.setNotFoundHandler(notFoundHandler);
    app.setErrorHandler(errorHandler(log));
    return app;
});

export function getApplication(): FastifyInstance {
    return container.get();
}

export async function startGameContextForQasApi(): Promise<http.Server> {
    return new Promise<http.Server>((resolve, reject) => {
        const app = getApplication();
        app.listen(config.gameContextApiForQAsPort, "::", function(err) {
            if (err) {
                return reject(err);
            }
            resolve(app.server);

            log.info("Game server Testing API listening on: " + config.gameContextApiForQAsPort);
        });
    });
}
