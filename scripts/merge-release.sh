#!/bin/bash

release_version=$1
merged=${2:-false}

echo "Merge $release_version to develop"

if [ "$merged" = "false" ]; then
    git checkout develop
    git pull
    git checkout -b "merge_${release_version}_to_develop"
    git merge "origin/release/${release_version}"
else
    git add .
    git commit -m "Merge ${release_version} to develop"
    git push -u origin "merge_${release_version}_to_develop"
fi
