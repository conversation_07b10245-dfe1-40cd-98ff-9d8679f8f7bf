doctype html
html
  head
    style(type="text/css").
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: 'Arial';
      }
      html, body {
        height: 100%;
      }
      .error-container, .error__section {
        display: flex;
        justify-content: center;
      }
      .error-container {
        align-items: center;
        height: 100%;
        background-color: #eee;
      }
      .error {
        max-width: 50%;
        border-radius: 8px;
        padding: 16px 32px;
        background-color: #fff;
        box-shadow: 0 0 8px rgba(0, 0, 0, 0.05);
      }
      .error__section {
        text-align: center;
        padding: 8px;
      }
      .error-info {
        margin: 0 16px;
        color: #777;
        font-weight: bold;
      }
      .error-info_header {
        color: #444;
        font-size: 24px;
        font-weight: bold;
      }

  body
    div(class='error-container')
      div(class='error')
        div(class='error__section')
          span(class='error-info error-info_header')= message
        div(class='error__section')
          span(class='error-info') Status: #{status}
          span(class='error-info') Code: #{code}
        div(class='error__section')
          span(class='error-info') Trace ID: #{traceId || "N/A"}
