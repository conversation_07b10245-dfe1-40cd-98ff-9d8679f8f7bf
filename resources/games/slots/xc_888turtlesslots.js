const core = require('sw-slot-game-core');

function calculateAllWaysSingleLineConfig(lineIndex, columnsCount, rowsCount) {
    var symbolPositions = [];
    var tempLineIndex;
    var tempIndexNumber = lineIndex;
    for (var symbolIndex = 0; symbolIndex < columnsCount; symbolIndex++) {
        tempLineIndex = Math.floor(tempIndexNumber % rowsCount);
        symbolPositions.push(tempLineIndex+1);
        tempIndexNumber = Math.floor(tempIndexNumber / rowsCount);
    }

    return symbolPositions;
}

function calculateAllWaysLineConfigs(linesCount, columnsCount, rowsCount) {
    var result = [];
    for (var lineIndex = 0; lineIndex < linesCount; lineIndex++) {
        result.push(calculateAllWaysSingleLineConfig(lineIndex, columnsCount, rowsCount));
    }

    return result;
}

var config = {

    scenes: {
        default: 'main',
        
        list: {
            main: {
                symbols: {
                    Nine:    { id: 11, payId: 0, multiplier: [0,1,1,1,1]},
                    Ten:     { id: 10, payId: 0, multiplier: [0,1,1,1,1]},
                    J:       { id:  9, payId: 1, multiplier: [0,0,1,1,1]},
                    Q:       { id:  8, payId: 1, multiplier: [0,0,1,1,1]},
                    K:       { id:  7, payId: 1, multiplier: [0,0,1,1,1]},
                    A:       { id:  6, payId: 1, multiplier: [0,0,1,1,1]},
                    T5:      { id:  5, payId: 2, multiplier: [0,0,1,1,1]},
                    T4:      { id:  4, payId: 2, multiplier: [0,0,1,1,1]},
                    T3:      { id:  3, payId: 2, multiplier: [0,0,1,1,1]},
                    T2:      { id:  2, payId: 3, multiplier: [0,0,1,1,1]},
                    T1:      { id:  1, payId: 4, multiplier: [0,0,1,1,1]},
                    Scatter: { id: 12, payId: 5, multiplier: [0,0,1,1,1], scatter: true },
                    Wild:    { id:  0, wild: true }
                },

                reels: {
                    rows: [3, 3, 3, 3, 3],

                    list: [
                        [2, 10, 7, 2, 6, 5, 8, 12, 7, 11, 3, 8, 5, 10, 3, 11, 8, 3, 6, 11, 12, 6, 11, 3, 6, 2, 11, 7, 3, 6, 5, 7, 4, 6, 5, 8, 3, 6, 5, 7, 4, 11, 7, 4, 6, 5, 7, 4, 8, 11, 4, 12, 8, 11, 4, 8, 1, 11, 9, 4, 11, 9, 4, 10, 5, 8],
                        [3, 11, 9, 1, 11, 5, 8, 11, 4, 7, 0, 10, 9, 4, 7, 0, 10, 9, 4, 7, 0, 10, 9, 4, 7, 0, 10, 9, 3, 7, 5, 4, 7, 3, 6, 2, 9, 1, 12, 7, 1, 9, 5, 7, 0, 10, 9, 5, 7, 0, 10, 9, 5, 7, 0, 10, 9],
                        [1, 8, 11, 1, 8, 3, 10, 1, 8, 4, 9, 1, 7, 5, 8, 4, 7, 5, 8, 2, 9, 8, 12, 9, 8, 2, 9, 3, 8, 2, 11, 8, 1, 9, 8, 2, 6, 1, 9, 2, 6, 10, 12, 8, 6],
                        [4, 11, 5, 6, 3, 10, 5, 6, 1, 8, 3, 11, 5, 6, 3, 10, 4, 7, 8, 3, 12, 2, 7, 5, 8, 3, 6, 7, 0, 9, 10, 1, 6, 7, 0, 9, 10, 2, 6, 7, 0, 9, 10, 4, 6, 7, 0, 9, 10, 3, 8, 7, 3, 10, 7, 4, 10, 8, 5],
                        [1, 6, 10, 1, 9, 10, 4, 6, 11, 3, 6, 10, 12, 9, 10, 1, 6, 3, 1, 6, 3, 1, 6, 11, 1, 6, 5, 7, 1, 8, 2, 11, 8, 2, 11, 4, 9, 2, 7, 3, 11, 5, 7]
                    ]
                },

                rules: [
                    new core.WildRule({
                        ids: [0],
                        symbols: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
                        multiplier: [1, 1, 1, 1, 1]
                    })/* ,
                    new core.ScatterRule({
                        ids: [12],
                        scene: 'freeSpins',
                        rewards: [
                            { freeSpins: 0, multiplier: 0 },
                            { freeSpins: 0, multiplier: 0 },
                            { freeSpins: 12, multiplier: 3 },
                            { freeSpins: 12, multiplier: 3 },
                            { freeSpins: 12, multiplier: 3 },
                        ],
                        betMultiplier: [0, 0, 0, 0, 0]
                    })
                    */
                ]
            },
            freeSpins: {
                symbols: {
                    Nine:    { id: 11, payId: 0, multiplier: [0,1,1,1,1]},
                    Ten:     { id: 10, payId: 0, multiplier: [0,1,1,1,1]},
                    J:       { id:  9, payId: 1, multiplier: [0,0,1,1,1]},
                    Q:       { id:  8, payId: 1, multiplier: [0,0,1,1,1]},
                    K:       { id:  7, payId: 1, multiplier: [0,0,1,1,1]},
                    A:       { id:  6, payId: 1, multiplier: [0,0,1,1,1]},
                    T5:      { id:  5, payId: 2, multiplier: [0,0,1,1,1]},
                    T4:      { id:  4, payId: 2, multiplier: [0,0,1,1,1]},
                    T3:      { id:  3, payId: 2, multiplier: [0,0,1,1,1]},
                    T2:      { id:  2, payId: 3, multiplier: [0,0,1,1,1]},
                    T1:      { id:  1, payId: 4, multiplier: [0,0,1,1,1]},
                    Scatter: { id: 12, payId: 5, multiplier: [0,0,1,1,1], scatter: true },
                    Wild:    { id:  0, wild: true }
                },

                reels: {
                    rows: [3, 3, 3, 3, 3],

                    list: [
                        [2, 10, 7, 2, 6, 5, 8, 12, 7, 11, 3, 8, 5, 10, 3, 11, 8, 3, 6, 11, 12, 6, 11, 3, 6, 2, 11, 7, 3, 6, 5, 7, 4, 6, 5, 8, 3, 6, 5, 7, 4, 11, 7, 4, 6, 5, 7, 4, 8, 11, 4, 12, 8, 11, 4, 8, 1, 11, 9, 4, 11, 9, 4, 10, 5, 8],
                        [3, 11, 9, 1, 11, 5, 8, 11, 4, 7, 0, 10, 9, 4, 7, 0, 10, 9, 4, 7, 0, 10, 9, 4, 7, 0, 10, 9, 3, 7, 5, 4, 7, 3, 6, 2, 9, 1, 12, 7, 1, 9, 5, 7, 0, 10, 9, 5, 7, 0, 10, 9, 5, 7, 0, 10, 9],
                        [1, 8, 11, 1, 8, 3, 10, 1, 8, 4, 9, 1, 7, 5, 8, 4, 7, 5, 8, 2, 9, 8, 12, 9, 8, 2, 9, 3, 8, 2, 11, 8, 1, 9, 8, 2, 6, 1, 9, 2, 6, 10, 12, 8, 6],
                        [4, 11, 5, 6, 3, 10, 5, 6, 1, 8, 3, 11, 5, 6, 3, 10, 4, 7, 8, 3, 12, 2, 7, 5, 8, 3, 6, 7, 0, 9, 10, 1, 6, 7, 0, 9, 10, 2, 6, 7, 0, 9, 10, 4, 6, 7, 0, 9, 10, 3, 8, 7, 3, 10, 7, 4, 10, 8, 5],
                        [1, 6, 10, 1, 9, 10, 4, 6, 11, 3, 6, 10, 12, 9, 10, 1, 6, 3, 1, 6, 3, 1, 6, 11, 1, 6, 5, 7, 1, 8, 2, 11, 8, 2, 11, 4, 9, 2, 7, 3, 11, 5, 7]
                    ]
                },

                rules: [
                    new core.WildRule({
                        ids: [0],
                        symbols: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
                        multiplier: [1, 1, 1, 1, 1]
                    })/* ,
                    new core.ScatterRule({
                        ids: [12],
                        scene: 'freeSpins',
                        rewards: [
                            { freeSpins: 0, multiplier: 0 },
                            { freeSpins: 0, multiplier: 0 },
                            { freeSpins: 12, multiplier: 3 },
                            { freeSpins: 12, multiplier: 3 },
                            { freeSpins: 12, multiplier: 3 },
                        ],
                        betMultiplier: [0, 0, 0, 0, 0]
                    })
                    */
                ]
            }
        }
    },
    paytable: [
        [0, 2,   5,  10,   50],
        [0, 0,   5,  10,   50],
        [0, 0,  25, 100,  200],
        [0, 0,  50, 200,  400],
        [0, 0, 100, 500, 1000],
        [0, 0,   5,  20,   50]
    ],
    lines: calculateAllWaysLineConfigs(243, 5, 3)
};

function getGameInformation() {
    return config;
}

exports.getGameInformation = getGameInformation;