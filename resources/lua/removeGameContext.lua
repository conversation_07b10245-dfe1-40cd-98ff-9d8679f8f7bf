local key = KEYS[1]

local sessionId = ARGV[1]
local version = ARGV[2]
local eventsCount = tonumber(ARGV[3])
local activitySet = ARGV[4]
local force = tonumber(ARGV[5])
local exclusiveLock = tonumber(ARGV[6])

if force == 1 then
    GameContextManager.removeActivity(activitySet, key)
elseif GameContextManager.isActiveContext(activitySet, key) then
    return -1
end

redis.call('DEL', key)
GameContextManager.saveEvents(7, eventsCount, ARGV)

return 0
