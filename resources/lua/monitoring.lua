-- Return json-encoded that contains:

-- game_active_contexts - how many game context currently in Redis
-- player_active_contexts - how many player context currently in Redis
-- spins_history  - the size of spins history queue
-- spins_history_workers  - the size of spins history worker queues
-- rounds_history  - the size of rounds history queue
-- rounds_history_workers  - the size of rounds history worker queues
-- sessions_history  - the size of sessions history queue
-- sessions_history_workers  - the size of sessions history worker queues
-- game_cleanup_queue  - the size of cleanup queue
-- game_cleanup_queue_workers  - the size of cleanup worker queues
-- player_cleanup_queue  - the size of player cleanup queue
-- player_cleanup_queue_workers  - the size of player cleanup worker queues

local function getWorkers(prefix)
    local size = 0
    for  _, key in ipairs(redis.call('HKEYS', prefix .. '-worker')) do
        size = size + redis.call('LLEN', prefix .. '-worker:' .. key)
    end

    return size
end

local result = {
    game_active_contexts = redis.call('ZCARD', 'games:last-activity'),
    player_active_contexts = redis.call('ZCARD', 'games:player-last-activity'),

    spins_history = redis.call('LLEN', 'games:history'),
    spins_history_workers = getWorkers('games:history'),

    rounds_history = redis.call('LLEN', 'games:rounds'),
    rounds_history_workers = getWorkers('games:rounds'),

    sessions_history = redis.call('LLEN', 'games:sessions'),
    sessions_history_workers = getWorkers('games:sessions'),

    game_cleanup_queue = redis.call('LLEN', 'games:cleanup'),
    game_cleanup_queue_workers = getWorkers('games:cleanup'),

    player_cleanup_queue = redis.call('LLEN', 'games:players-cleanup'),
    player_cleanup_queue_workers = getWorkers('games:players-cleanup'),
}

return cjson.encode(result)
