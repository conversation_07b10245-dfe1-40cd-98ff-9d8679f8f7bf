local PlayerContextManager = {}

function PlayerContextManager.activateGame(playerContextKey, gameContextKey, mode)
    local brokenGameKey = PlayerContextManager.brokenGame(playerContextKey)
    local activeGameKey = PlayerContextManager.activeGame(playerContextKey)
    local metaInfKey = PlayerContextManager.metaInf(playerContextKey)

    -- remove from broken game registry
    if redis.call('HEXISTS', brokenGameKey, gameContextKey) == 1 then
        redis.call('HDEL', brokenGameKey, gameContextKey)
    end

    -- add to active game resistry
    if redis.call('HEXISTS', activeGameKey, gameContextKey) == 0 then
        redis.call('HSET', activeGameKey, gameContextKey, mode)
        redis.call('HINCRBY', metaInfKey, 'version', 1)
        redis.call('HSET', metaInfKey, 'dataVersion', '1')
    end
end

function PlayerContextManager.trackActive(activityKey, playerContextKey, ts)
    redis.call('ZADD', activityKey, ts, playerContextKey);
    redis.call('ZADD', PlayerContextManager.lexiActivityKey(activityKey), 0, playerContextKey);
end

function PlayerContextManager.removeGame(playerContextKey, gameContextKey, broken)
    local metaInfKey = PlayerContextManager.metaInf(playerContextKey)

    local activeGameKey = PlayerContextManager.activeGame(playerContextKey)
    local brokenGameKey = PlayerContextManager.brokenGame(playerContextKey)
    local mode = redis.call('HGET', activeGameKey, gameContextKey)
    redis.call('HDEL', activeGameKey, gameContextKey)
    redis.call('HINCRBY', metaInfKey, 'version', 1)

    if broken == 1 and mode then
        redis.call('HSET', brokenGameKey, gameContextKey, mode)
    else
        redis.call('HDEL', brokenGameKey, gameContextKey)
    end
end

function PlayerContextManager.load(playerContextKey)
    local metaInfKey = PlayerContextManager.metaInf(playerContextKey)
    local activeGames = redis.call('HGETALL', PlayerContextManager.activeGame(playerContextKey))
    local brokenGames = redis.call('HGETALL', PlayerContextManager.brokenGame(playerContextKey))
    local metaInf = redis.call('HGETALL', metaInfKey)
    local dataVersion = redis.call('HGET', metaInfKey, 'dataVersion');

    return { dataVersion, metaInf, activeGames, brokenGames }
end

function PlayerContextManager.removeActivity(set, key)
    redis.call('ZREM', set, key)
    redis.call('ZREM', PlayerContextManager.lexiActivityKey(set), key);
end

function PlayerContextManager.getLruContexts(set, ts, limit)
    return redis.call('ZRANGEBYSCORE', set, 0, ts, 'LIMIT', 0, limit)
end

function PlayerContextManager.findTopLexiActivities(set, min, max, limit)
    return redis.call('ZRANGEBYLEX', PlayerContextManager.lexiActivityKey(set), min, max, 'LIMIT', 0, limit)
end

function PlayerContextManager.enqueueCleanupEvent(queue, id)
    return redis.call('LPUSH', queue, cjson.encode({ id = id }))
end

function PlayerContextManager.isActive(activitySet, playerContextKey)
    local result = redis.call('ZSCORE', activitySet, playerContextKey)
    if result == nil or not result then
        return false
    else
        return true
    end
end

function PlayerContextManager.remove(activitySet, playerContextKey, lastVersion, force)
    local metaInfKey = PlayerContextManager.metaInf(playerContextKey)
    local activeGameKey = PlayerContextManager.activeGame(playerContextKey)
    local brokenGameKey = PlayerContextManager.brokenGame(playerContextKey)
    local hasActiveGame = redis.call('EXISTS', activeGameKey) == 1;

    if force == 0 and (PlayerContextManager.isActive(activitySet, playerContextKey) == true or hasActiveGame) then
        return -1;
    end

    local version = redis.call('HGET', metaInfKey, 'version')
    if force == 0 and version ~= nil and version ~= '' and version ~= false and version ~= lastVersion then
        return -1
    end

    redis.call('DEL', metaInfKey, activeGameKey, brokenGameKey)

    PlayerContextManager.removeActivity(activitySet, playerContextKey)

    return 1
end

function PlayerContextManager.save(playerContextKey, games, data, lastVersion)
    local metaInfKey = PlayerContextManager.metaInf(playerContextKey)
    local brokenGameKey = PlayerContextManager.brokenGame(playerContextKey)
    local version = redis.call('HGET', metaInfKey, 'version')
    if version ~= nil and version ~= '' and version ~= false and version ~= lastVersion then
        return -1
    end

    if games ~= nil then
        for _, game in ipairs(games) do
            redis.call('HSET', brokenGameKey, game.id, game.mode)
        end
    end

    redis.call('HSET', metaInfKey, 'version', lastVersion)
    redis.call('HSET', metaInfKey, 'dataVersion', '1')
    return PlayerContextManager.load(playerContextKey)
end

function PlayerContextManager.brokenGame(playerContextKey)
    return playerContextKey .. ':broken-games'
end

function PlayerContextManager.activeGame(playerContextKey)
    return playerContextKey .. ':active-games'
end

function PlayerContextManager.metaInf(playerContextKey)
    return playerContextKey .. ':metaInf'
end

function PlayerContextManager.lexiActivityKey(activityKey)
    return activityKey .. '-lexi'
end
