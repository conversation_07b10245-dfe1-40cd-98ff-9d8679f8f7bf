local activitySet = KEYS[1]
local queueName = KEYS[2]
local ts = tonumber(ARGV[1])
local limit = tonumber(ARGV[2])

local contextIds = PlayerContextManager.getLruContexts(activitySet, ts, limit)

local count = 0
for _, id in ipairs(contextIds) do
    PlayerContextManager.enqueueCleanupEvent(queueName, id)
    PlayerContextManager.removeActivity(activitySet, id)
    count = count + 1
end

return count
