local GameContextManager = {}
local exclusiveLockField = '$el'

function GameContextManager.checkVersion(key, sessionId, version, checkExists, exclusive)
    if exclusive == 0 and redis.call('HGET', key, exclusiveLockField) == "1" then
        return error("CONCURRENT_ACCESS");
    end

    local currentVersion = redis.call('HGET', key, 'version')
    local currentSessionId = redis.call('HGET', key, 'sessionId')

    -- remove in later versions
    if not currentSessionId then
        currentSessionId = redis.call('HGET', key, 'currentSessionId')
    end

    if not currentVersion and not currentSessionId and checkExists == 0 then
        return
    end

    if currentSessionId == sessionId and version == currentVersion then
        return
    end

    return error("CONCURRENT_ACCESS");
end

function GameContextManager.saveFields(key, from, count, values)
    local i = from
    while count > 0 do
        redis.call('HSET', key, values[i], values[i + 1])
        i = i + 2
        count = count - 1
    end
    redis.call('HDEL', key, exclusiveLockField);
end

function GameContextManager.trackActive(set, key, ts)
    redis.call('ZADD', set, ts, key)
    redis.call('ZADD', GameContextManager.lexiActivityKey(set), 0, key)
end

function GameContextManager.removeActivity(set, key)
    redis.call('ZREM', set, key)
    redis.call('ZREM', GameContextManager.lexiActivityKey(set), key)
end

function GameContextManager.isActiveContext(set, key)
    local result = redis.call('ZSCORE', set, key)
    if result == nil or not result then
        return false
    else
        return true
    end
end

function GameContextManager.getLruContexts(set, ts, limit)
    return redis.call("ZRANGEBYSCORE", set, 0, ts, "LIMIT", 0, limit)
end

function GameContextManager.findTopLexiActivities(set, min, max, limit)
    return redis.call("ZRANGEBYLEX", GameContextManager.lexiActivityKey(set), min, max, 'LIMIT', 0, limit)
end

function GameContextManager.enqueueCleanupEvent(queue, id)
    return redis.call("LPUSH", queue, cjson.encode({ id = id }))
end

function GameContextManager.saveEvents(from, count, events)
    local i = from
    while count > 0 do
        redis.call('LPUSH', events[i], events[i + 1])
        i = i + 2
        count = count - 1
    end
end

function GameContextManager.load(contextKey, merchantSessionKey, exclusive)
    local id = contextKey
    if id == "" or id == nil then
        id = redis.call('GET', merchantSessionKey);
    end

    if id == "" or not id then
        return {}
    end

    local result = redis.call('HGETALL', id);
    if exclusive == 1 and next(result) ~= nil then
        redis.call('HSET', id, exclusiveLockField, exclusive);
    end

    if contextKey == '' then
        table.insert(result, 'id');
        table.insert(result, id);
    end

    return result;
end

function GameContextManager.lexiActivityKey(activityKey)
    return activityKey .. '-lexi'
end

